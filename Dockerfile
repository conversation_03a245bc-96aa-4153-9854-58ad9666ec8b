# pin Debian 12 which is required for Playwright
FROM php:8.2-apache-bookworm

# Install intl PHP extension
ADD --chmod=0755 https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions /usr/local/bin/
RUN install-php-extensions intl

RUN apt-get -y update
# - git for explorer.php to display the current commit ID
# - npm is to launch the TypeScript Playwright server
# - unzip is required for composer to extract the downloaded dependencies
# - qpdf to merge PDFs
RUN apt-get -y install libicu-dev git npm unzip qpdf

# Install Xdebug
# RUN pecl install xdebug && docker-php-ext-enable xdebug

# Install wkhtmltopdf with patched Qt
RUN apt-get -y install wget libxrender1 libfontconfig libxtst6 xz-utils fontconfig xfonts-75dpi xfonts-base libjpeg62-turbo libssl3

# TARGETPLATFORM is set when building the Docker container automatically
ARG TARGETPLATFORM
RUN if [ "$TARGETPLATFORM" = "linux/amd64" ]; then ARCHITECTURE=amd64; elif [ "$TARGETPLATFORM" = "linux/arm64" ]; then ARCHITECTURE=arm64; else ARCHITECTURE="arm64"; fi \
    && wget "https://github.com/wkhtmltopdf/packaging/releases/download/********-3/wkhtmltox_********-3.bookworm_${ARCHITECTURE}.deb" \
    && apt install "./wkhtmltox_********-3.bookworm_${ARCHITECTURE}.deb" \
    &&  rm "./wkhtmltox_********-3.bookworm_${ARCHITECTURE}.deb"

# for workarounds so the index.php file runs locally and inside the Docker container
ENV INSIDE_DOCKER_ENV=1

# install Arial font since many printouts use it as their preferred font
COPY fonts/ /usr/share/fonts/truetype
RUN fc-cache -f

# WARNING: if the Playwright version changes in package.json, take care to wipe all Docker caches for the deployment!
# copy only the package.json to install the specified Playwright version from package.json
COPY playwright-pdf/package.json .
RUN npm install
# download into /root/.cache/ms-playwright/chromium_headless_shell-1148
RUN npx playwright install --with-deps chromium-headless-shell
# remove now unused file
RUN rm package.json

WORKDIR /var/www/html
# see https://getcomposer.org/doc/faqs/how-to-install-composer-programmatically.md
RUN wget https://raw.githubusercontent.com/composer/getcomposer.org/76a7060ccb93902cd7576b67264ad91c8a2700e2/web/installer -O - -q | php -- --quiet
COPY . /var/www/html
# avoid this:
# fatal: detected dubious ownership in repository at '/var/www/html'
RUN git config --global --add safe.directory /var/www/html
RUN php composer.phar install
RUN rm -f config.php
# copy the Xdebug configuration file
# COPY docker-php-ext-xdebug.ini /usr/local/etc/php/conf.d/

# provide access to Apache
RUN chown -R www-data:www-data /var/www/html

# restrict access to files
COPY apache-restrict.conf /etc/apache2/conf-enabled/000-restrict.conf

CMD ["sh", "/var/www/html/docker-init.sh"]
EXPOSE 80
