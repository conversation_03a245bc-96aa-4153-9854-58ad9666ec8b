Those files are used to suppress all warnings in `playwright-pdf.ts` and to allow to jump to the source files.

- `package.json`,
- `package-lock.json`
- `tsconfig.json`

Install the packages with the following command:

```bash
npm install
```

Note that you need a new version of Node like `23.1.0` installed.

# Start server

To compile the Playwright printouts, the TypeScript server needs to be running. Start it like this in this directory.

```bash
npx tsx playwright-pdf.ts
```