import {parseArgs} from "util";
import {Err, isErr} from "./err";
import {chromium} from "playwright";
import * as fs from "node:fs";
import * as http from "node:http";

function quitOnNonDigits(key: string, str: string) {
    const digitsOnly = /^\d+$/.test(str);
    if (!digitsOnly) {
        throw new Error(`${key}: ${str} is not a number!`);
    }
}

function isDigitsOnly(str: string): boolean {
    return /^\d+$/.test(str);
}

/**
 * @return null on no error, otherwise the error string
 */
function hasMissing(keys: string[], json: any): string | null {
    for (let key of keys) {
        if (!json[key]) {
            return `Missing required argument: ${key}`
        }
    }
    return null
}

function formatMargin(raw: string | undefined | number) {
    if (raw === undefined) {
        return undefined;
    }
    if (typeof raw == "number") {
        raw = String(raw)
    }
    return raw + "mm"
}

async function readFile(filePath: string): Promise<string | Err> {
    try {
        return fs.readFileSync(filePath, "utf-8")
    } catch (e: unknown) {
        return Err(`File '${filePath}' is not readable: ${e}`)
    }
}

const {values: serverConfig} = parseArgs({
    args: process.argv,
    options: {
        port: {
            type: "string",
            default: "3000"
        }
    },
    strict: true,
    allowPositionals: true,
})

quitOnNonDigits("port", serverConfig.port)

const server = http.createServer(async (req, res) => {
    let body = '';
    req.on('data', chunk => {
        body += chunk.toString();
    });
    req.on('end', async () => {
        let json: any
        try {
            json = JSON.parse(body);
        } catch (e: unknown) {
            res.writeHead(400)
            res.end("Request body JSON decoding failed: " + String(e))
            return
        }
        const missing = hasMissing(["bodyFile", "outputFile"], json)
        if (missing != null) {
            res.writeHead(400)
            res.end("Request body JSON is missing either bodyFile or outputFile")
            return
        }
        let header: string | undefined = undefined
        let footer: string | undefined = undefined
        try {
            if (json["headerFile"]) {
                const headerResult = await readFile(json["headerFile"]);
                if (isErr(headerResult)) {
                    res.writeHead(400)
                    res.end("Error reading header file: " + (headerResult as Err).error)
                    return
                }
                header = headerResult
            }
            if (json["footerFile"]) {
                const footerResult = await readFile(json["footerFile"]);
                if (isErr(footerResult)) {
                    res.writeHead(400)
                    res.end("Error reading footer file: " + (footerResult as Err).error)
                    return
                }
                footer = footerResult
            }
            if (json["marginTopMm"]) {
                if (!isDigitsOnly(json["marginTopMm"])) {
                    res.writeHead(400)
                    res.end("marginTopMm should be digits only!")
                    return
                }
            }
            if (json["marginRightMm"]) {
                if (!isDigitsOnly(json["marginRightMm"])) {
                    res.writeHead(400)
                    res.end("marginRightMm should be digits only!")
                    return
                }
            }
            if (json["marginBottomMm"]) {
                if (!isDigitsOnly(json["marginBottomMm"])) {
                    res.writeHead(400)
                    res.end("marginBottomMm should be digits only!")
                    return
                }
            }
            if (json["marginLeftMm"]) {
                if (!isDigitsOnly(json["marginLeftMm"])) {
                    res.writeHead(400)
                    res.end("marginLeftMm should be digits only!")
                    return
                }
            }
            // console.log("Creating PDF through headless chromium with options:", json);
            const browser = await chromium.launch({
                // lower timeout 10 seconds to debug rare crashes
                timeout: 10_000,
                // executablePath: "/Users/<USER>/playwright/ms-playwright/chromium_headless_shell-1148/chrome-mac/headless_shell"
                // executablePath: "/Users/<USER>/vero/api-printouts/playwright-pdf/ms-playwright/chromium_headless_shell-1148/chrome-mac/headless_shell"
            })
            try {
                const page = await browser.newPage()
                page.setDefaultNavigationTimeout(60_000)
                page.setDefaultTimeout(60_000)
                await page.goto("file://" + json["bodyFile"])
                // use empty dummy div because as soon as any margins are set, header and footer are displayed
                // with their default content of url, title, etc.
                const emptyHtml = "<div></div>"
                await page.pdf({
                    path: json["outputFile"],
                    displayHeaderFooter: !!header || !!footer,
                    headerTemplate: header ?? emptyHtml,
                    footerTemplate: footer ?? emptyHtml,
                    format: json.format ?? "A4",
                    landscape: json.landscape ?? false,
                    margin: {
                        top: formatMargin(json["marginTopMm"]),
                        right: formatMargin(json["marginRightMm"]),
                        bottom: formatMargin(json["marginBottomMm"]),
                        left: formatMargin(json["marginLeftMm"]),
                    },
                    printBackground: true
                })
                await browser.close()
                res.writeHead(200)
                res.end()
            } catch (e: unknown) {
                try {
                    await browser.close()
                } catch (e2: unknown) {
                    res.writeHead(500)
                    res.end("Failed to close headless chromium: " + String(e) + " " + String(e2))
                    return
                }
                res.writeHead(500)
                res.end(String(e))
                return
            }
        } catch (e: unknown) {
            res.writeHead(500)
            res.end(String(e))
            return
        }
    });
})

const port = Number.parseInt(serverConfig.port);
server.listen(port, () => {
    console.log("Playwright server is running on http://localhost:" + serverConfig.port)
});