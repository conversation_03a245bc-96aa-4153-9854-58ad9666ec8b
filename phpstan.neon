includes:
    - vendor/phpstan/phpstan/conf/bleedingEdge.neon

parameters:
    phpVersion: 80200
    level: 6
    paths:
        - api-routes/
        - core/
        - lang/
        - principal-handling/
        - scripts/
        - tests/
        - utils/
        - api-routes.php
        - explorer.php
        - index.php
        - printout.helper.php
        - Pdf.php
        - PrintoutCompiler.php
        - PrintoutCurl.php
    ignoreErrors:
        - '#Call to function method_exists\(\) with .* will always evaluate to true#'

