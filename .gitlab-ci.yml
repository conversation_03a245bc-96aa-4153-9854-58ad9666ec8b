default:
  tags:
    - "docker"

stages:
  - syntax-check
  - lint
  - build
  - unit-test
  - integration-test
  - deploy

php-syntax-check:
  stage: syntax-check
  image: php:8.2-cli
  script:
    # exclude _old_non_curl/ which contains incorrect files for PHP 8.2 and vendor/ which should contain correct files
    - |
      find . -type f -name "*.php" \
        -not -path "./_old_non_curl/*" \
        -not -path "./vendor/*" \
        -print0 | xargs -0 -n1 -P "$(nproc || echo 4)" php --syntax-check
    # run explorer.php once to detect any simple PHP issues, like Cannot redeclare class... errors
    - php explorer.php

build-dev:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [ "" ]
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination "${CI_REGISTRY_IMAGE}:develop-${CI_COMMIT_SHORT_SHA}"
      --destination "${CI_REGISTRY_IMAGE}:develop-latest"
      --build-arg=TARGETPLATFORM=$CI_RUNNER_EXECUTABLE_ARCH
  only:
    - develop

build-prod:
  stage: build
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [ "" ]
  script:
    - /kaniko/executor
      --context "${CI_PROJECT_DIR}"
      --dockerfile "${CI_PROJECT_DIR}/Dockerfile"
      --destination ${CI_REGISTRY_IMAGE}:temp-${CI_PIPELINE_ID}
      --build-arg=TARGETPLATFORM=$CI_RUNNER_EXECUTABLE_ARCH
  only:
    - tags

phpstan:
  stage: lint
  image: php:8.2-cli
  before_script:
    - apt-get update && apt-get install -y git unzip libicu-dev
    - docker-php-ext-install intl
    - curl -fsSL https://raw.githubusercontent.com/composer/getcomposer.org/f3108f64b4e1c1ce6eb462b159956461592b3e3e/web/installer | php -- --quiet
    - php composer.phar install --prefer-dist --no-progress --no-interaction --optimize-autoloader
  script:
    - php composer.phar lint

phpunit:
  image: php:8.2-cli
  stage: unit-test
  before_script:
    # install git and unzip for composer, libicu-dev is for the intl extension
    - apt-get update && apt-get install -y git unzip libicu-dev
    - docker-php-ext-install intl
    - curl -fsSL https://raw.githubusercontent.com/composer/getcomposer.org/f3108f64b4e1c1ce6eb462b159956461592b3e3e/web/installer | php -- --quiet
    - php composer.phar install --prefer-dist --no-progress --no-interaction --optimize-autoloader
    - apt-get clean && rm -rf /var/lib/apt/lists/*
  script:
    - vendor/bin/phpunit tests/

generate-sample-pdfs:
  stage: integration-test
  # pin Debian 12 which is required for Playwright, the others can still use image: php:8.2-apache since they do not use Playwright
  image: php:8.2-apache-bookworm
  timeout: 2h
  allow_failure: true
  before_script:
    - apt-get update
    # unzip is for composer, git for the explorer
    - curl -fsSL https://deb.nodesource.com/setup_24.x | bash -
    - curl -SsL https://packages.httpie.io/deb/KEY.gpg | gpg --dearmor -o /usr/share/keyrings/httpie.gpg
    - echo "deb [arch=amd64 signed-by=/usr/share/keyrings/httpie.gpg] https://packages.httpie.io/deb ./" > /etc/apt/sources.list.d/httpie.list
    - apt-get install -y libicu-dev git unzip nodejs gnupg libxrender1 libfontconfig libxtst6 xz-utils fontconfig xfonts-75dpi xfonts-base libjpeg62-turbo libssl3 httpie
    - docker-php-ext-install intl
    # install wkhtmltopdf
    - curl -fsSL -o wkhtmltox.deb "https://github.com/wkhtmltopdf/packaging/releases/download/********-3/wkhtmltox_********-3.bookworm_amd64.deb"
    - apt install -y ./wkhtmltox.deb && rm ./wkhtmltox.deb
    - cp apache-restrict-ci.conf /etc/apache2/conf-available/apache-restrict.conf
    - a2enconf apache-restrict
    - echo -e "DocumentRoot \"/builds/BauBuddy/api-printouts\"\n<Directory \"/builds/BauBuddy/api-printouts\">\n\tAllowOverride All\n</Directory>" > /etc/apache2/sites-available/000-default.conf
    - a2enmod rewrite
    - chown -R www-data:www-data /builds/BauBuddy/api-printouts
    - chmod -R a+rX /builds /builds/BauBuddy /builds/BauBuddy/api-printouts
    - echo "ServerName localhost" >> /etc/apache2/apache2.conf
    - apachectl configtest
    - service apache2 restart
    # install composer via pinned installer using curl
    - curl -fsSL https://raw.githubusercontent.com/composer/getcomposer.org/f3108f64b4e1c1ce6eb462b159956461592b3e3e/web/installer | php -- --quiet
    - php composer.phar install --no-interaction --prefer-dist --no-progress --optimize-autoloader
    - cd playwright-pdf
    - npm ci
    - npx playwright install --with-deps chromium-headless-shell
    # clean up apt cache to reduce layer size
    - apt-get clean && rm -rf /var/lib/apt/lists/*
  script:
    - npx tsx playwright-pdf.ts &
    - cd ..
    - 'timeout 10 sh -c "until curl -s http://localhost:3000; do sleep 1; done"'
    - php tests/ci/generate_sample_pdfs_ci.php

deploy:
  stage: deploy
  image:
    name: gcr.io/go-containerregistry/crane:debug
    entrypoint: [ "" ]
  variables:
    GIT_STRATEGY: none
  script:
    - crane auth login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - crane cp "${CI_REGISTRY_IMAGE}:temp-${CI_PIPELINE_ID}" "${CI_REGISTRY_IMAGE}:${CI_COMMIT_TAG}"
    - crane cp "${CI_REGISTRY_IMAGE}:temp-${CI_PIPELINE_ID}" "${CI_REGISTRY_IMAGE}:master-latest"
    - crane delete "${CI_REGISTRY_IMAGE}:temp-${CI_PIPELINE_ID}"
  only:
    - tags