<?php /** @noinspection PhpMultipleClassDeclarationsInspection */
declare(strict_types=1);
require_once __DIR__ . '/core/Response.php';
require_once __DIR__ . '/core/Request.php';
require_once __DIR__ . '/core/OutputType.php';
require_once __DIR__ . '/core/functions.php';
require_once __DIR__ . '/api-routes.php';

/**
 * To abort with 500 on fatal or syntax errors.
 */
function setupErrorHandling(): void
{
    set_error_handler(function ($severity, $message, $file, $line) {
        /** @noinspection PhpUnhandledExceptionInspection */
        throw new ErrorException($message, 0, $severity, $file, $line);
    });

    set_exception_handler(function ($e) {
        http_response_code(500);
        die("Internal Server Error: $e");
    });

    register_shutdown_function(function () {
        $error = error_get_last();
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            http_response_code(500);
            $message = "Internal Server Error: $error[message] in $error[file] on line $error[line]";
            die("Internal Server Error: $message");
        }
    });
}

/** @noinspection PhpUnhandledExceptionInspection */
setupErrorHandling();

$configFile = __DIR__ . '/config.php';
if (file_exists($configFile)) {
    require_once($configFile);
}

$method = Request::method();
if (!in_array($method, ['GET', 'POST'])) {
    die_with_response_code(Response::BAD_REQUEST, "Unsupported request method: $method. Only GET and POST are allowed.");
}

$headers = getallheaders();

/**
 * @param array<string, mixed> $headers
 */
function getCaseInsensitiveHeader(array $headers, string $name): string|null
{
    $headers = array_change_key_case($headers);
    $name = strtolower($name);

    if (array_key_exists($name, $headers)) {
        return $headers[$name];
    }
    return null;
}

$principal = getCaseInsensitiveHeader($headers, 'principal');
if ($principal === null) {
    die_with_response_code(Response::BAD_REQUEST, "The required HTTP header Principal is not set");
}

$authorization = getCaseInsensitiveHeader($headers, 'authorization');
if ($authorization === null) {
    die_with_response_code(Response::BAD_REQUEST, "The required HTTP header Authorization is not set");
}

$uri = current_url();
$route = parse_route_and_type($uri);
Request::setRouteAndType($route);

$routeSegments = explode('/', $route->apiRoute);
$requestApiRoute = $routeSegments[0];
unset($routeSegments[0]);

$basePathApiRoutes = __DIR__ . '/api-routes/';
Request::$basePathApiRoutes = $basePathApiRoutes;
$dirMapping = getRouteDirectoryMapping();
$foundProperCaseDir = false;
foreach ($dirMapping as $properCaseDir => $lowerCaseDir) {
    if (strtolower($requestApiRoute) === $lowerCaseDir) {
        $foundProperCaseDir = $properCaseDir;
        break;
    }
}
if ($foundProperCaseDir === false || !is_dir($basePathApiRoutes . $foundProperCaseDir)) {
    $toPrint = $requestApiRoute;
    if ($foundProperCaseDir !== false) {
        $toPrint = $foundProperCaseDir;
    }
    $message = "The directory: .../api-routes/$toPrint for the passed API route ($toPrint) is not existing!";
    die_with_response_code(Response::SERVER_ERROR, $message);
}

// Include here for further usage in the printouts.
// Once the Docker container is running, the require statements can be deleted from the model files.
//
// This also includes PrintoutCurl, so it does not need to be included as well.
require_once __DIR__ . '/printout.helper.php';

$urlParams = [];
parse_str($_SERVER['QUERY_STRING'], $urlParams);
$urlParamsNames = array_keys($urlParams);

$routeConfigPath = $basePathApiRoutes . $foundProperCaseDir . '/config.php';
$config = new RouteConfig();
if (file_exists($routeConfigPath)) {
    /**
     * @var RouteConfig $config
     */
    $config = require_once $routeConfigPath;
}

Request::setConfig($config);

if (strtolower($method) !== strtolower($config->requestMethod->name)) {
    die_with_response_code(Response::METHOD_NOT_ALLOWED, "The $method method is not allowed.");
}

if (is_post_request()) {
    $keys = array_map(fn($s) => $s->name, $config->getRouteSegments());

    if (count($keys) !== count($routeSegments)) {
        die_with_response_code(Response::BAD_REQUEST, "The passed route segments do not match with the route. " .
            "You passed " . count($routeSegments) . " segment(s), but the route requires " . count($keys) . " segment(s): " .
            implode(", ", $keys));
    }

    $segments = array_combine($keys, array_values($routeSegments));

    $config->validateRouteSegments($segments);
} else {
    $config->validateThatAllRequiredParametersAreSet($urlParamsNames, $foundProperCaseDir);
}

if (isset($config->redirectToRoute)) {
    $routes = array_keys($dirMapping);

    if (!in_array($config->redirectToRoute, $routes)) {
        die_with_response_code(Response::BAD_REQUEST, "Unknown route: $config->redirectToRoute");
    }

    $foundProperCaseDir = $config->redirectToRoute;
}

/**
 * @param array<string, mixed> $params
 * @return array<string, mixed> $params array filled with optional parameters set to null, so the getData() call on the model can succeed
 */
function fillOptionalParametersToNull(RouteConfig $config, array $params): array
{
    $result = $params;
    /**
     * @var RouteConfigParameter $param
     */
    foreach ($config->getParameters() as $param) {
        if (!$param->required && !array_key_exists($param->name, $result)) {
            $result[$param->name] = null;
        }
    }
    return $result;
}

$urlParams = fillOptionalParametersToNull($config, $urlParams);
Request::$urlParams = $urlParams;

/**
 * @var RouteConfigParameter $param
 */
foreach ($config->getParameters() as $param) {
    if ($param->required) {
        $requiredParameterNames[] = $param->name;
    }
}

function returnParametersAsString(array $configParameters): string
{
    $all = [];
    /**
     * @var RouteConfigParameter $param
     */
    foreach ($configParameters as $param) {
        $current = $param->name;
        if ($param->required) {
            $current .= ' (required)';
        }
        $all[] = $current;
    }
    return implode(", ", $all);
}

function checkForUnknownParameter(array $configParameters, array $urlParamsNames): void
{
    foreach ($urlParamsNames as $name => $_) {
        $found = false;
        /**
         * @var RouteConfigParameter $param
         */
        foreach ($configParameters as $param) {
            if ($param->name === $name) {
                $found = true;
                break;
            }
        }

        if ($found === false && $name !== PrintoutHelper::AppendPdfsToPrintoutQueryParameter) {
            if (count($configParameters) === 0) {
                $message = "Invalid parameter: '$name'. This endpoint does not accept any query parameters.";
            } else {
                $knownParameters = returnParametersAsString($configParameters);
                $message = "Unknown parameter: '$name'. Available parameters: $knownParameters.";
            }
            die_with_response_code(Response::BAD_REQUEST, $message);
        }
    }
}

checkForUnknownParameter($config->getParameters(), $urlParams);
$config->validateUrlParamTypesVia_GET();

Request::setQuery(array_merge($urlParams, $segments ?? []));

PrintoutHelper::validateApiBaseUrl();

// hardcode for the localhost file links for the PDF generation
if (getenv('INSIDE_DOCKER_ENV') === '1') {
    $_SERVER['REQUEST_SCHEME'] = 'http';
    $_SERVER['HTTP_HOST'] = 'localhost';
}

function returnGetDataResult($basePathApiRoutes, $apiRoute, $urlParams): array
{
    require_once $basePathApiRoutes . $apiRoute . '/model.php';
    $className = "C_" . $apiRoute;
    $model = new $className();
    return call_user_func_array([$model, 'getData'], $urlParams);
}

function handleJson($basePathApiRoutes, $apiRoute, $urlParams): void
{
    header('Content-Type: application/json');
    header('Content-Disposition: attachment; filename="output.json"');
    echo json_encode(returnGetDataResult($basePathApiRoutes, $apiRoute, $urlParams), JSON_PRETTY_PRINT);
}

function handleHtml($basePathApiRoutes, $apiRoute): void
{
    global $config;

    $templateDir = $config->getTemplateDir(TemplatePart::content, $apiRoute);

    header('Content-Type: text/html');
    header('Content-Disposition: attachment; filename="output.html"');
    ob_start();
    require_once $basePathApiRoutes . $templateDir . '/template.content.php';
    $output = ob_get_contents();
    ob_end_clean();
    echo $output;
}

function generateUidFilePathWithTimestamp(string $fileExtensionWithDot): string
{
    $uid = uniqid(date('YmdHis') . '_', true);
    $tempDir = sys_get_temp_dir();
    if (!str_ends_with($tempDir, DIRECTORY_SEPARATOR)) {
        $tempDir .= DIRECTORY_SEPARATOR;
    }
    return $tempDir . "printouts-" . $uid . $fileExtensionWithDot;
}

function handlePdfWithPlaywright(RouteConfig $config, string $apiRoute, array $urlParams): void
{
    $outputPath = tempnam(sys_get_temp_dir(), 'printouts-') . '.pdf';
    $requestBody = constructPlaywrightServerRequestBody($config, $outputPath, $apiRoute, $urlParams);
    callPlaywrightServerToGeneratePdf($requestBody);
    $config->callAfterPdfGenerationCallback($outputPath);

    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename=output.pdf');
    readfile($outputPath);
}

function handlePdfWithWkhtmltopdf(RouteConfig $config, string $apiRoute, array $urlParams): void
{
    $args = [];
    if (defined("PRINTOUT_WKHTMLTOPDF_PATH")) {
        $args[] = PRINTOUT_WKHTMLTOPDF_PATH;
    } else {
        $args[] = "wkhtmltopdf";
    }

    if (count($config->wkhtmltopdfArguments) >= 1) {
        $args = array_merge($args, $config->wkhtmltopdfArguments);
    }

    saveGetDataResultAsTemporaryJsonFile($apiRoute, $urlParams, Request::$basePathApiRoutes);

    // CONTENT
    $contentDir = $config->getTemplateDir(TemplatePart::content, $apiRoute);
    $contentTpl = Request::$basePathApiRoutes . $contentDir . '/template.content.php';
    $args[] = executePhpFileAndReturnFilePathHtml($contentTpl);

    // HEADER
    $headerDir = $config->getTemplateDir(TemplatePart::header, $apiRoute);
    $headerTpl = Request::$basePathApiRoutes . $headerDir . '/template.header.php';
    if (file_exists($headerTpl)) {
        $args[] = '--header-html';
        $args[] = executePhpFileAndReturnFilePathHtml($headerTpl);
    }

    // FOOTER
    $footerDir = $config->getTemplateDir(TemplatePart::footer, $apiRoute);
    $footerTpl = Request::$basePathApiRoutes . $footerDir . '/template.footer.php';
    if (file_exists($footerTpl)) {
        $args[] = '--footer-html';
        $args[] = executePhpFileAndReturnFilePathHtml($footerTpl);
    }

    $filename = tempnam(sys_get_temp_dir(), 'printouts-') . '.pdf';
    $args[] = "--encoding";
    $args[] = "utf-8";
    $args[] = $filename;

    $descriptorSpec = array(
        // stdin is a pipe that the child will read from
        0 => array("pipe", "r"),
        // stdout is a pipe that the child will write to
        1 => array("pipe", "w"),
        // stderr is a pipe that the child will write to
        2 => array("pipe", "w")
    );

    $pipes = [];
    $process = proc_open($args, $descriptorSpec, $pipes);
    if (!is_resource($process)) {
        $argsString = print_r($args, true);
        $message = "Unable to start wkhtmltopdf process. Does the executable exist at the given path?\n$argsString";
        die_with_response_code(Response::SERVER_ERROR, $message);
    }

    $stdout = stream_get_contents($pipes[1]);
    $stderr = stream_get_contents($pipes[2]);
    fclose($pipes[0]);
    fclose($pipes[1]);
    fclose($pipes[2]);

    $statusCode = proc_close($process);
    if ($statusCode !== 0) {
        $message = "wkhtmltopdf exited with status code $statusCode and output:\n$stdout $stderr";
        die_with_response_code(Response::SERVER_ERROR, $message);
    }

    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename=output.pdf');

    if (PrintoutHelper::$documentChildrenForPdfMerging &&
        ($_GET[PrintoutHelper::AppendPdfsToPrintoutQueryParameter] ?? "") === 'true') {

        $fids = [];
        foreach (PrintoutHelper::$documentChildrenForPdfMerging as $child) {
            $type = strtoupper($child['type']);
            if ($type == SchemaTypes::PHOTO) {
                foreach ($child['reportedValues'] as $fid) {
                    $fids[] = $fid;
                }
            }
        }
        $urls = [];
        $baseUrl = PrintoutHelper::getApiBaseUrl();
        $realPdfUrls = [];
        foreach (array_unique($fids) as $fid) {
            if (array_key_exists($fid, PrintoutHelper::$downloadedFilesForDocumentMerging)) {
                $file = PrintoutHelper::$downloadedFilesForDocumentMerging[$fid];
                if ($file['mimeType'] === 'application/pdf') {
                    $realPdfUrls[] = $file['filePath'];
                }
            } else {
                $urls[] = "$baseUrl/v1/files/single/$fid";
            }
        }
        $curl = new PrintoutCurl($baseUrl);
        if (count($urls) >= 1) {
            // note that all fids are downloaded here, although some printouts only consider images
            // one could add a fid image blacklist array to PrintoutHelper, to not download all
            $responses = $curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
            for ($i = 0; $i < count($responses); $i++) {
                $statusCode = $responses[$i]['statusCode'];
                // ignore errors
                if ($statusCode == 200) {
                    $response = $responses[$i]['response'];
                    if ($response['mime_type'] == 'application/pdf') {
                        $realPdfUrls[] = $response['filepath'];
                    }
                }
            }
        }
        $paths = [];
        foreach ($realPdfUrls as $pdfUrl) {
            $s = file_get_contents($pdfUrl);
            $path = generateUidFilePathWithTimestamp(".pdf");
            file_put_contents($path, $s);
            $paths[] = $path;
        }
        $out = generateUidFilePathWithTimestamp(".pdf");
        $joinedPaths = implode(" ", $paths);

        /** @noinspection PhpUnhandledExceptionInspection */
        $mergedPdf = mergePdfs([$filename, $joinedPaths], $out, $config->afterPdfGenerationCallback);
        readfile($mergedPdf);
    } else {
        $config->callAfterPdfGenerationCallback($filename);
        readfile($filename);
    }
}

if (isset($config->beforeRouteCallback)) {
    call_user_func_array($config->beforeRouteCallback, [&$urlParams]);
}

if (isset($urlParams[PrintoutHelper::AppendPdfsToPrintoutQueryParameter])) {
    unset($urlParams[PrintoutHelper::AppendPdfsToPrintoutQueryParameter]);
}

switch ($route->type) {
    case OutputType::Json:
        handleJson($basePathApiRoutes, $foundProperCaseDir, $urlParams);
        break;

    case OutputType::Html:
        handleHtml($basePathApiRoutes, $foundProperCaseDir);
        break;

    case OutputType::Pdf:
        switch ($config->getRenderEngine()) {
            case RenderEngine::wkhtmltopdf:
                handlePdfWithWkhtmltopdf($config, $foundProperCaseDir, $urlParams);
                break;
            case RenderEngine::playwright:
                handlePdfWithPlaywright($config, $foundProperCaseDir, $urlParams);
                break;
        }
        break;
}
