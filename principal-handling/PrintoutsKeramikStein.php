<?php

class PrintoutsKeramikStein
{
    /**
     * custom hack for different logos depending on project-number
     *
     * @param $externalProjectNo
     * @param $projectManager
     * @return string
     */
    public static function selectCorrectLogo($externalProjectNo, $projectManager): string
    {
        $principal = PrintoutHelper::getPrincipal();

        if ($externalProjectNo && ($principal === 'ksbg' || $principal === 'ksbg_test')) {

            // containing an W -> Fliesen Wurm
            if (str_contains($externalProjectNo, 'W')) {
                return '<img width="100%" src="https://api.baubuddy.de/infomaterial/Dokumente_fliesen_wurm_gmbh_k/common/20200805122034-Wurm-3.png" alt="">';
            }

            // containing a F -> <PERSON><PERSON>berger
            if (str_contains($externalProjectNo, 'F')) {
                return '<img width="100%" src="https://api.baubuddy.de/branches/api-develop/infomaterial/Dokumente_fuchsberger_gmbh/common/20210107093515-Logo-final-Tafel-mit-QR-gelbgrün.jpg" alt="">';
            }

            // containing a D -> digu
            if (str_contains($externalProjectNo, 'DG')) {
                return '<img width="100%" src="https://intern.digithebutler.at:8443/api/infomaterial/Dokumente_ksbg/common/digu.png" alt="">';
            }

            if (str_contains($externalProjectNo, 'D')) {
                return "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/logo-hb.jpg') . "' width='100%'  alt=''/>";
            }

            // containing a N -> nimmervoll
            if (str_contains($externalProjectNo, 'N')) {
                return '<img width="100%" src="https://intern.digithebutler.at:8443/api/infomaterial/Dokumente_ksbg/common/nimmervoll2.png" alt="">';
            }

            // containing a K -> KEOB
            // this also checks for a list of random employeeIds, if any of them is project manager it should belong to KEOB
            if (str_contains($externalProjectNo, 'K') || in_array($projectManager, [5, 79, 270, 302, 348, 1329, 1429])) {
                return '<img width="100%" src="https://intern.digithebutler.at:8443/api/infomaterial/Dokumente_ksbg/common/keob.png" alt="">';
            }

            // containing a T or -R -> Trixner
            if (str_contains($externalProjectNo, 'T') || str_contains($externalProjectNo, '-R')) {
                return '<img width="100%" src="https://intern.digithebutler.at:8443/api/infomaterial/Dokumente_ksbg/common/trixner.png" alt="">';
            }

        } else if ($externalProjectNo && ($principal === 'perchtold')) {

            $subString = substr($externalProjectNo, 0, 2);

            if ($subString == "11" || $subString == "12" || $subString == "16" || $subString == "19" || $subString == "26") {
                return "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/Perchtold_Logo_Gmunden.jpg') .
                    "' width='100%'  alt=''/>";
            }

            if ($subString == "33" || $subString == "35" || $subString == "41" || $subString == "43") {
                return "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/Perchtold_Logo_Wien.jpg') .
                    "' width='100%'  alt=''/>";
            }

            if ($subString == "55") {
                return "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/Perchtold_Logo_Akustik.jpg') .
                    "' width='100%'  alt=''/>";
            }

            if ($subString == "71") {
                return "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/Perchtold_Logo_Schlosserei.jpg') .
                    "' width='100%'  alt=''/>";
            }
        }

        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $logo = PrintoutHelper::downloadSettings($curl)['logo'];
        return '<img width="50%" src="' . $logo . '" alt="">';
    }

    public static function selectCorrectStamp($externalProjectNo, $schemaPositionTitle): string
    {
        if ($schemaPositionTitle == 'Unterschrift Auftragnehmer') {
            $html = "<div class='stamp'>";

            // containing an W -> Fliesen Wurm
            if ($externalProjectNo && str_contains($externalProjectNo, 'W')) {
                $html .= '<img width="100%" ' .
                    'src="https://intern.digithebutler.at:8443/api/infomaterial/Dokumente_ksbg/common/wurm-stempel.png">';

            } // containing a F -> Fuchsberger
            elseif ($externalProjectNo && str_contains($externalProjectNo, 'F')) {
                $html .= '<img width="100%" ' .
                    'src="https://intern.digithebutler.at:8443/api/infomaterial/Dokumente_ksbg/common/fuchsberger-stempel.png">';

            } // break out since DG gets no stamp, but D has one
            else if ($externalProjectNo && str_contains($externalProjectNo, 'DG')) {
                return '';
            } // containing a D -> digu
            elseif ($externalProjectNo && str_contains($externalProjectNo, 'D')) {
                $html .= "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/stamp-hb-fliesen-germany.jpg') .
                    "' width='100%'  alt=''/>";

            } // containing a K -> KEOB
            elseif ($externalProjectNo && str_contains($externalProjectNo, 'K')) {
                $html .= "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/stamp-keob.jpg') .
                    "' width='100%'  alt=''/>";

            } // containing an R -> Trixner
            elseif ($externalProjectNo && str_contains($externalProjectNo, 'R')) {
                $html .= "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/stamp-trixner.jpg') .
                    "' width='100%'  alt=''/>";

            } // containing an N -> Nimmervoll
            elseif ($externalProjectNo && str_contains($externalProjectNo, 'N')) {
                $html .= "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot('img/stamp-nimmervoll.jpg') .
                    "' width='100%'  alt=''/>";

            } // fallback to KsbgHbStamp.png
            else {
                if (defined('WEB_ROOT_API')) {
                    $html .= "<img src='" . WEB_ROOT_API . "vendor\printouts\KsbgHbStamp.png'/>";
                } else {
                    $html .= "<img src='" . PrintoutHelper::translateLocalPathToServerPathFromRoot("img/KsbgHbStamp.png") . "'/>";
                }
            }

            $html .= "</div>";
            return $html;
        }
        return '';
    }
}
