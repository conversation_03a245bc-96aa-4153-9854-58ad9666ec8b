<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection DuplicatedCode */

class PrintoutsHakakuecheCloud
{
    public static function KsbgChecklist_combineData_getExternalNo($workingOrder)
    {
        // falling back to ktr-aanr if externalWorkingOrderNo is empty string
        return $workingOrder['externalWorkingOrderNo'] == '' ? $workingOrder['uniqueKey'] : $workingOrder['externalWorkingOrderNo'];
    }

    public static function KsbgChecklist_template_process_positionsTitle($reportedValue)
    {
        if (isset($reportedValue['reportedValues'])) {
            $extractedText = self::extractFromText('articleNo', $reportedValue['reportedValues'][0]);
            if ($extractedText) {
                return $extractedText;
            }
        }
        return $reportedValue['name'];
    }

    /**
     * @param $reportedValue
     * @param $strings
     * @return string content of reported value without
     */
    public static function KsbgChecklist_template_process_positionsValue($reportedValue, $strings): string
    {
        if (isset($reportedValue['reportedValue'])) {
            $articleNoAndPosNo = self::extractFromText('fullString', $reportedValue['reportedValue']);
            return str_replace("[" . $articleNoAndPosNo . "]", "", $reportedValue['reportedValue']);
        }

        if (isset($reportedValue['reportedValues'])) {
            $articleNoAndPosNo = self::extractFromText('fullString', $reportedValue['reportedValues'][0]);
            return str_replace("[" . $articleNoAndPosNo . "]", "", $reportedValue['reportedValues'][0]);
        }

        return $strings['no'];
    }

    public static function KsbgChecklist_template_processPositionsHeadline($reportedValue)
    {
        $headlinePosition = $reportedValue;
        $childPositionId = $headlinePosition['id'] + 1;
        $childPosition = $reportedValue['children'][$childPositionId];
        $posNo = self::extractFromText('posNo', $childPosition['reportedValues'][0]);
        // backward compatibility
        if ($posNo == "")
            $posNo = $headlinePosition['name'];
        return $posNo;
    }

    /***
     * @param $type string articleNo,posNo,fullString
     * @param $text string extracted string from format [MOPAK-500 # Pos500]
     * @return mixed|string
     */
    private static function extractFromText($type, $text)
    {
        $matches = array();
        preg_match("/\[[^\^]+]/mu", $text, $matches);
        if (count($matches) > 0) {
            // drop brackets
            $articleNoAndPosNoString = str_replace(["[", "]"], "", $matches[0]);
            // separate articleNo from Position No (structure: [MOPAK-500 # Pos500])
            $articleNoAndPosNoArray = explode(' # ', $articleNoAndPosNoString);
            if ($type === 'articleNo')
                return $articleNoAndPosNoArray[0];
            elseif ($type === 'posNo')
                return str_replace('Pos', 'Position ', $articleNoAndPosNoArray[1]);
            elseif ($type === 'fullString')
                return $articleNoAndPosNoString;
        }
        return null;
    }

    public static function C_KsbgChecklist_getStrings(&$strings): void
    {
        $strings['projectNameTitle'] = 'Projekt';
        $strings['projectSiteAddressTitle'] = 'Projektadresse';
        $strings['projectManagerTitle'] = 'Projektleitung';
        $strings['sectionTitle'] = null;
    }
}
