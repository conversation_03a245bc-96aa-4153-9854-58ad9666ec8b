<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_MitteilungFertigstellung())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!doctype html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Mitteilung Fertigstellung</title>
</head>
<body>
<div class="header">
    <img src="<?= $data['logo'] ?>" alt="">
    <div class="contact">
        <p><?= $data['addressWithPhone'] ?></p>
    </div>
</div>
<table class="info-table sender-info-table">
    <tr>
        <td>Absender</td>
    </tr>
    <tr>
        <td><?= $data['companyTitle'] ?> <?= $data['companyName'] ?></td>
    </tr>
    <tr>
        <td><?= $data['companyAddress'] ?></td>
    </tr>
    <tr>
        <td><?= $data['companyPostCode'] ?> <?= $data['companyCity'] ?></td>
    </tr>
</table>
<table class="info-table client-info-table">
    <tr>
        <td>Auftraggeber</td>
    </tr>
    <tr>
        <td><?= $data['principalDisplayName'] ?></td>
    </tr>
    <tr>
        <td><?= $data['principalRealAddress'] ?></td>
    </tr>
    <tr>
        <td>
            <?= $data['principalAddressCountryCode'] ?>
            <?= $data['principalPostcode'] ?>
            <?= $data['principalAddressCity'] ?>
        </td>
    </tr>
</table>
<table class="info-table project-info-table">
    <tr>
        <td>Bauvorhaben</td>
    </tr>
    <tr>
        <td><?= $data['projectName'] ?></td>
    </tr>
    <tr>
        <?= $data['projectSiteAddress'] ?>
        <?= $data['projectSiteCity'] ?>
        <?= $data['projectSiteZipCode'] ?>
    </tr>
    <tr>
        <td>
            Auftrag vom <b><?= PrintoutHelper::dateConverter($data['projectValidStartDate'], 'd.m.Y') ?></b>
        </td>
    </tr>
</table>
<table class="w-full">
    <tr>
        <td>
            <h3>Mitteilung über die Fertigstellung gem. § 12 VOB/B</h3>
        </td>
    </tr>
</table>
<table class="w-full mt-50">
    <tr>
        <td>
            <p>
                Sehr geehrte Damen und Herren,
            </p>
            <p>
                in vorbezeichneter Angelegenheit möchten wir Ihnen mitteilen, das die von uns auszuführenden
                Gerüstbauleistungen für das oben näher bezeichnete Bauvorhaben fertiggestellt worden sind. Wir möchten
                Sie daher bitten, entsprechend dem anliegenden Prüfprotokoll die förmliche Abnahme durchzuführen. Als
                Termin schlagen wir den
            </p>
        </td>
    </tr>
</table>
<table class="w-full mt-50">
    <tr>
        <td style="width: 250px"></td>
        <td style="width: 200px; text-align: center; font-weight: bold">
            <?= $data['VorgeDatum'] ?>
        </td>
        <td style="width: 50px"></td>
        <td style="width: 100px; font-weight: bold">
            <?= $data['Uhrzeit'] ?>
        </td>
        <td>Uhr</td>
    </tr>
    <tr>
        <td>vor.</td>
    </tr>
</table>
<table class="appointment-table w-full mt-50">
    <tr>
        <td>
            <p>
                Bitte teilen Sie uns bis zum
                <b>
                    <span>
                        <?php if (!empty($data['Confirmation'])) { ?>
                            <?= $data['Confirmation'] ?>
                        <?php } ?>
                    </span>
                </b>
                mit, ob dieser Termin für Sie akzeptabel ist.
            </p>
        </td>
    </tr>
</table>
<table class="w-full mt-50">
    <tr>
        <td>Mit freundlichen Grüßen,</td>
    </tr>
</table>
<table class="w-full mt-60 signature-table">
    <tr>
        <td>
            <?php
                if ($data['Ort'] !== "" && $data['Datum'] !== "") {
                    echo $data['Ort'] . ", " . $data['Datum'];
                } else if ($data['Ort'] !== "") {
                    echo $data['Ort'];
                } else if ($data['Datum'] !== "") {
                    echo $data['Datum'];
                }
            ?>
        </td>
        <td></td>
        <td>
            <?php if (!empty($data['Auftragnehmer'])) { ?>
                <img src="<?= $data['Auftragnehmer'] ?>" alt="signature" class="signature">
            <?php } ?>
        </td>
    </tr>
    <tr>
        <td class="border-top"></td>
        <td></td>
        <td class="border-top"></td>
    </tr>
    <tr>
        <td>Ort/Datum</td>
        <td></td>
        <td>Auftragnehmer</td>
    </tr>
</table>
</body>
</html>