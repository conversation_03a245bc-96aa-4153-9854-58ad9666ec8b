body, html {
    margin: 0;
    padding: 0;
}

* {
    font-family: Arial, sans-serif;
}

.w-full {
    width: 100%;
}

.mt-50 {
    margin-top: 50px !important;
}

.mt-60 {
    margin-top: 60px !important;
}

.border-top {
    border-top: 1px solid black;
}

.header {
    width: 100%;
    display: -webkit-box;
    -webkit-box-pack: center;
    padding: 10px 0;
}

.header img {
    width: 150px;
    display: inline-block;
    clip-path: inset(0 10px 0 0);
}

.header .contact {
    display: -webkit-box;
    -webkit-box-pack: center;
    -webkit-box-orient: block-axis;
    margin-left: 15px;
}

.header .contact p {
    margin: 0;
    font-weight: lighter;
    display: inline-block;
    font-size: 0.9em;
    color: rgb(102, 102, 102);
}

.info-table {
    width: 48%;
    border-collapse: collapse;
    border: 2px solid black;
}

.info-table tr:first-child {
    background-color: #f5f5f5;
}

.info-table tr td {
    padding: 8px;
    border: 1px solid black;
}

.client-info-table {
    float: left;
    margin: 15px 0 40px;
}

.project-info-table {
    float: right;
    margin: 15px 0 40px;
}

.appointment-table span {
    margin: 0 10px;
    width: 120px;
    display: inline-block;
    text-align: center;
}

.signature-table tr:first-child td:nth-child(1),
.signature-table tr:first-child td:nth-child(3){
    width: 750px !important;
}

.signature-table tr:first-child td:nth-child(2) {
    width: 200px;
}

.signature-table tr:first-child td:first-child {
    padding-right: 10px;
    vertical-align: bottom;
}

.signature {
    width: 150px;
    height: auto;
}