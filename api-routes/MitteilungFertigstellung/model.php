<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';
require_once __DIR__ . '/../../utils/BundesInnungGeruestbauHelper.php';

class C_MitteilungFertigstellung
{
    /**
     * @return array<mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $checkBoxes = [];
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $mappedDocument = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);
        $data['rel_key1'] = $doc['fullDocument']['documentRelKey1'];
        $data['documentDate'] = $doc['fullDocument']['documentCreatedOn'];
        $data['checkboxes'] = $checkBoxes;
        $data['Ort'] = $this->getMappedData('Ort', $mappedDocument);
        $data['Auftragnehmer'] = $this->getMappedData('Auftragnehmer', $mappedDocument);
        $data['Uhrzeit'] = $this->getMappedData('Uhrzeit', $mappedDocument);
        $data['VorgeDatum'] = $this->getMappedDate('Vorgeschlagenes Datum...', $mappedDocument);
        $data['Datum'] = $this->getMappedDate('Datum', $mappedDocument);
        $data['Confirmation'] = $this->getMappedDate('Bitte teilen Sie uns bis zum ... mit, ob dieser Termin für Sie akzeptabel ist.', $mappedDocument);
        $projectNo = $doc['fullDocument']['documentRelKey1'];
        return array_merge(BundesInnungGeruestbauHelper::downloadCommonDataForHeader($curl, $projectNo), $data);
    }

    /**
     * @param array<mixed> $mappedData
     */
    private function getMappedData(string $title, array $mappedData): string
    {
        if (!isset($mappedData[$title])) {
            return '';
        }
        return $mappedData[$title];
    }

    /**
     * @param array<mixed> $mappedData
     */
    private function getMappedDate(string $title, array $mappedData): string
    {
        if (!isset($mappedData[$title])) {
            return '';
        }
        return PrintoutHelper::dateConverter($mappedData[$title], 'd.m.Y');
    }
}

