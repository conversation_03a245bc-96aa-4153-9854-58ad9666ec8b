{"name": "Kalkulationsdatenblatt : remove cm fix v10", "description": "", "status": "active", "createdBy": "4", "createdOn": "2023-04-26T00:00:00Z", "printOptions": ["8"], "positions": [{"id": 1, "parentId": 3, "title": "Datum", "type": "date"}, {"id": 2, "parentId": 3, "title": "Angebots Nr.", "type": "int"}, {"id": 3, "title": "Kalkulationsdatenblatt", "type": "headline"}, {"id": 4, "parentId": 3, "title": "Baustelle/Bauvorhaben", "type": "headline"}, {"id": 5, "parentId": 3, "title": "Bedarfskalkulation", "type": "checkbox"}, {"id": 6, "parentId": 3, "title": "Submissionskalkulation", "type": "checkbox"}, {"id": 7, "parentId": 3, "title": "Anschrift (Str, PLZ, Ort)", "type": "string"}, {"id": 8, "parentId": 3, "title": "Auftraggeber", "type": "string"}, {"id": 9, "parentId": 3, "title": "Ansprechpartner für Technische Fragen", "type": "string"}, {"id": 10, "parentId": 3, "title": "Telefonnummer", "type": "string"}, {"id": 11, "parentId": 3, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date"}, {"id": 12, "parentId": 3, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date"}, {"id": 13, "parentId": 3, "title": "Baustellenentfernung", "type": "string"}, {"id": 14, "parentId": 3, "title": "Ø Trageweg", "type": "string"}, {"id": 15, "parentId": 3, "title": "Anzahl der Anfahrt", "type": "int"}, {"id": 16, "parentId": 3, "title": "Jede zusätzliche Anfahrt (€)", "type": "float"}, {"id": 17, "parentId": 3, "title": "Min. Aufbauleistung zusammengehörend", "type": "string"}, {"id": 18, "parentId": 3, "title": "Min. Abbauleistung zusammengehörend", "type": "string"}, {"id": 19, "parentId": 3, "title": "Material-Lagerplatz", "type": "combobox", "values": ["<PERSON>a", "<PERSON><PERSON>", "mehrere"]}, {"id": 20, "parentId": 3, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "combobox-multi", "values": ["IFC Model", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bauzeitenplan", "GEAB", "Lageplan", "Baustelleneinrichtungsplan"]}, {"id": 21, "parentId": 3, "title": "Einsatz von Fahrzeugen/Technik", "type": "headline"}, {"id": 22, "parentId": 3, "title": "Kran-LKW 26To 18 m Hubhöhe", "type": "checkbox"}, {"id": 23, "displayInside": 22, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 24, "displayInside": 22, "title": "Aufbau", "type": "checkbox"}, {"id": 25, "parentId": 3, "title": "Kran-LKW 26To 29 m Hubhöhe", "type": "checkbox"}, {"id": 26, "displayInside": 25, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 27, "displayInside": 25, "title": "Aufbau", "type": "checkbox"}, {"id": 28, "parentId": 3, "title": "Autokran Klaas 18To 52m Hubhöhe", "type": "checkbox"}, {"id": 29, "displayInside": 28, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 30, "displayInside": 28, "title": "Aufbau", "type": "checkbox"}, {"id": 31, "parentId": 3, "title": "LKW Abroller 26To + Pritsche 7,00x2,5m", "type": "checkbox"}, {"id": 32, "displayInside": 31, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 33, "displayInside": 31, "title": "Aufbau", "type": "checkbox"}, {"id": 34, "parentId": 3, "title": "Solo LKW 7,5-18To 10 m", "type": "checkbox"}, {"id": 35, "displayInside": 34, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 36, "displayInside": 34, "title": "Aufbau", "type": "checkbox"}, {"id": 37, "parentId": 3, "title": "Sattel 40To Länge 17m", "type": "checkbox"}, {"id": 38, "displayInside": 37, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 39, "displayInside": 37, "title": "Aufbau", "type": "checkbox"}, {"id": 40, "parentId": 3, "title": "3,5 To <PERSON><PERSON>", "type": "checkbox"}, {"id": 41, "displayInside": 40, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 42, "displayInside": 40, "title": "Aufbau", "type": "checkbox"}, {"id": 43, "parentId": 3, "title": "Kran (bauseits) kostenlos", "type": "checkbox"}, {"id": 44, "displayInside": 43, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 45, "displayInside": 43, "title": "Aufbau", "type": "checkbox"}, {"id": 46, "parentId": 3, "title": "Aufzug GEDA 500 kg", "type": "checkbox"}, {"id": 47, "displayInside": 46, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 48, "displayInside": 46, "title": "Aufbau", "type": "checkbox"}, {"id": 49, "parentId": 3, "title": "Aufzug GEDA 1500 kg", "type": "checkbox"}, {"id": 50, "displayInside": 49, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 51, "displayInside": 49, "title": "Aufbau", "type": "checkbox"}, {"id": 52, "parentId": 3, "title": "Aufzug GEDA 200 kg", "type": "checkbox"}, {"id": 53, "displayInside": 52, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 54, "displayInside": 52, "title": "Aufbau", "type": "checkbox"}, {"id": 55, "parentId": 3, "title": "Alimak 300 kg", "type": "checkbox"}, {"id": 56, "displayInside": 55, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 57, "displayInside": 55, "title": "Aufbau", "type": "checkbox"}, {"id": 58, "parentId": 3, "title": "<PERSON><PERSON>", "type": "checkbox"}, {"id": 59, "displayInside": 58, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 60, "displayInside": 58, "title": "Aufbau", "type": "checkbox"}, {"id": 61, "parentId": 3, "title": "Teleskoplader bis 6m Hubhöhe", "type": "checkbox"}, {"id": 62, "displayInside": 61, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 63, "displayInside": 61, "title": "Aufbau", "type": "checkbox"}, {"id": 64, "parentId": 3, "title": "Teleskoplader bis 6-18m Hubhöhe", "type": "checkbox"}, {"id": 65, "displayInside": 64, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 66, "displayInside": 64, "title": "Aufbau", "type": "checkbox"}, {"id": 67, "parentId": 3, "title": "Teleskoplader bis 18-30m Hubhöhe", "type": "checkbox"}, {"id": 68, "displayInside": 67, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 69, "displayInside": 67, "title": "Aufbau", "type": "checkbox"}, {"id": 70, "parentId": 3, "title": "Gebäude-Eigenschaften", "type": "headline"}, {"id": 71, "parentId": 3, "title": "Bauzustand", "type": "combobox-multi", "values": ["Bestandsgebäude", "Neubau / Rohbau", "Rohbaubegleitend"]}, {"id": 72, "parentId": 3, "title": "Gebäudegeometrie", "type": "combobox-multi", "values": ["<PERSON><PERSON><PERSON>", "viele Fenster", "Eckige Geometrie"]}, {"id": 73, "parentId": 3, "title": "An<PERSON>g<PERSON><PERSON>", "type": "headline"}, {"id": 74, "parentId": 73, "title": "WDVS cm", "type": "float"}, {"id": 75, "parentId": 73, "title": "<PERSON><PERSON><PERSON>", "type": "float"}, {"id": 76, "parentId": 73, "title": "<PERSON><PERSON>", "type": "float"}, {"id": 77, "parentId": 73, "title": "<PERSON><PERSON>z", "type": "float"}, {"id": 78, "parentId": 3, "title": "Dachüberstände", "type": "headline"}, {"id": 79, "parentId": 78, "title": "<PERSON><PERSON><PERSON> cm", "type": "float"}, {"id": 80, "parentId": 78, "title": "Traufe cm", "type": "float"}, {"id": 81, "parentId": 3, "title": "Besonderheit", "type": "combobox-multi", "values": ["Elektrische Freileitung", "Öffentlicher Verkehrsraum (Sondernutzung)", "Passantenschutz", "Statik"]}, {"id": 82, "parentId": 3, "title": "Aufstandsfläche", "type": "combobox-multi", "values": ["tragfähige feste Fläche", "lastverteilende Unterlage"]}, {"id": 83, "parentId": 3, "title": "Gerüstart", "type": "combobox-multi", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Raumgerüst/Flächengerüst", "Treppenturm / Gerüsttreppe", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Dacharbeiten)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Maler/Putzer)", "Sandwichfassade", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 84, "parentId": 3, "title": "Gerüst-Eigenschaften", "type": "headline"}, {"id": 85, "parentId": 3, "title": "Lastklasse (gleichmäßig verteilte Last)", "type": "combobox", "values": ["1 (0,75 kN/m²)", "2 (1,50) kN/m²", "3 (2,00 kN/m²)", "4 (3,00 kN/m²)", "5 (4,50 kN/m²)", "6 (6,00 kN/m²)"]}, {"id": 86, "parentId": 3, "title": "Breitenklasse", "type": "combobox", "values": ["W 06", "W 09", "W"]}, {"id": 87, "parentId": 3, "title": "Zugang nur für Gerüstersteller", "type": "combobox", "values": ["Innenliegender Leitergang", "Podesttreppe", "<PERSON><PERSON>"]}, {"id": 88, "parentId": 3, "title": "Aufmass nach. (Abrechnung)", "type": "combobox", "values": ["Arbeitsgerüst", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 89, "parentId": 3, "title": "Technische Daten", "type": "headline"}, {"id": 90, "parentId": 3, "title": "Horizontaler Abstand vom Belag zum Gebäude (Wandabstand) (m)", "type": "float"}, {"id": 91, "parentId": 3, "title": "Horizontaler Abstand von der Traufe zum Seitenschutz/ <PERSON><PERSON><PERSON>wand (m)", "type": "float"}, {"id": 92, "parentId": 3, "title": "Vertikaler Abstand von der Bodenplatte Erdgeschoss zum ersten Belag (m)", "type": "float"}, {"id": 93, "parentId": 3, "title": "Vertikaler Abstand von der Traufe zum obersten Belag (m)", "type": "float"}, {"id": 94, "parentId": 3, "title": "Bekleidung / Anbauteile", "type": "combobox-multi", "values": ["Sands<PERSON>hlnetz", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "(Gitter)Plane", "Plane als B1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 95, "parentId": 3, "title": "<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 96, "parentId": 3, "title": "<PERSON><PERSON><PERSON> innen", "type": "combobox", "values": ["0,25m", "0,33m", "0,50m", "0,67m", "1,00m"]}, {"id": 97, "parentId": 3, "title": "<PERSON><PERSON><PERSON> außen", "type": "combobox", "values": ["0,25m", "0,33m", "0,50m", "0,67m", "1,00m"]}, {"id": 98, "parentId": 3, "title": "Verankerung / Verschluss", "type": "headline"}, {"id": 99, "parentId": 98, "title": "Hinweis: ab 160 mm statischer Nachweis/ Innendiagonale verwenden (außer bei Netzen und Plane)", "type": "headline"}, {"id": 100, "parentId": 98, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "values": ["120 mm", "160 mm *", "230 mm *", "300 mm *", "350 mm *"]}, {"id": 101, "parentId": 98, "title": "Abstützung", "type": "combobox-multi", "values": ["Abstützung / Rohre", "Abstutztürme", "V-Anker", "Gerüstkappen", "Freistehen<PERSON> Gerü<PERSON>", "Innendiagonale"]}, {"id": 102, "parentId": 98, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "values": ["durch Fa. Schäfer Gerüstbau", "bauseits", "Ankerlöcher bleiben offen", "<PERSON>ine Verankerung vorhanden"]}, {"id": 103, "parentId": 3, "title": "Kalkulationsbemerkungen / INFOS", "type": "string"}, {"id": 104, "parentId": 3, "title": "Infos aus dem Vergabegespräch", "type": "string"}, {"id": 105, "parentId": 3, "title": "Unterschrift Kalkulator", "type": "signatureField"}]}