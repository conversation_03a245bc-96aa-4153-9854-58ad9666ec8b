<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Kalkulationsdatenblatt())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!DOCTYPE html>
<html lang="de">
<head>
    <title>Kalkulationsdatenblatt</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>

<table id="main-body-bordered-table">
    <tr>
        <td class="no-border no-pad">
            <table id="bauvorhaben">
                <tr>
                    <td colspan="6" class="table-title">
                        <h2>Baustelle / Bauvorhaben</h2>
                    </td>
                </tr>
                <tr>
                    <td colspan="3" class="text-align-center" style="border-bottom: unset">
                        <b>Bedarfskalkulation <?= $data['Kalkulationsdatenblatt Bedarfskalkulation'] ? '<span style="font-size: 24px;">&#9745;</span>' : '<span style="font-size: 24px;">&#9744;</span>'; ?></b>
                    </td>
                    <td colspan="3" class="text-align-center" style="border-bottom: unset">
                        <b>Submissionskalkulation <?= $data['Kalkulationsdatenblatt Submissionskalkulation'] ? '<span style="font-size: 24px;">&#9745;</span>' : '<span style="font-size: 24px;">&#9744;</span>'; ?></b>
                    </td>
                </tr>
            </table>
            <table id="bauvorhaben-adresse">
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Anschrift (Str, PLZ, Ort):</b></td>
                    <td colspan="5"> <?= $data['Kalkulationsdatenblatt Anschrift (Str, PLZ, Ort)']; ?></td>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Auftraggeber:</b></td>
                    <td colspan="5"><?= $data['Kalkulationsdatenblatt Auftraggeber']; ?></td>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Ansprechpartner für Technische Fragen:</b></td>
                    <td colspan="2"><?= $data['Kalkulationsdatenblatt Ansprechpartner für Technische Fragen']; ?></td>
                    <td class="bauvorhaben-cell-titles" id="telefonnummer"><b>Telefonnummer:</b></td>
                    <td colspan="2"><?= $data['Kalkulationsdatenblatt Telefonnummer']; ?></td>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Geplanter Aufbautermin:</b></td>
                    <td colspan="2"><?= PrintoutHelper::dateConverter($data['Kalkulationsdatenblatt Geplanter Aufbautermin'], 'd.m.Y'); ?></td>
                    <td class="bauvorhaben-cell-titles" colspan="2"><b>Geplanter Abbautermin:</b></td>
                    <td><?= PrintoutHelper::dateConverter($data['Kalkulationsdatenblatt Geplanter Abbautermin'], 'd.m.Y'); ?></td>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles">
                        <b>Baustellenentfernung:</b>
                    </td>
                    <td colspan="2"><?= $data['Kalkulationsdatenblatt Baustellenentfernung']; ?></td>
                    <td class="bauvorhaben-cell-titles" colspan="2"><b>&oslash; Trageweg:</b></td>
                    <td><?= $data['Kalkulationsdatenblatt Ø Trageweg']; ?></td>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Anzahl der Anfahrt:</b></td>
                    <td colspan="2"><?= $data['Kalkulationsdatenblatt Anzahl der Anfahrt']; ?></td>
                    <td class="bauvorhaben-cell-titles" colspan="2"><b>Jede zusätzliche Anfahrt:</b></td>
                    <td style="text-align: center;">
                        <?= $data['Kalkulationsdatenblatt Jede zusätzliche Anfahrt (€)'] ?>
                        €
                    </td>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Min. Aufbauleistung zusammengehörend:</b></td>
                    <td colspan="2"><?= $data['Kalkulationsdatenblatt Min. Aufbauleistung zusammengehörend']; ?></td>
                    <td class="bauvorhaben-cell-titles" colspan="2"><b>Min. Abbauleistung zusammengehörend:</b></td>
                    <td><?= $data['Kalkulationsdatenblatt Min. Abbauleistung zusammengehörend']; ?></td>
                </tr>
                <tr>
                </tr>
                <tr>
                    <td class="bauvorhaben-cell-titles"><b>Material-Lagerplatz:</b></td>
                    <td><?= $data['Kalkulationsdatenblatt Material-Lagerplatz'] == "Ja" ? "&#9745;" : "&#9744;"; ?>
                        Ja
                    </td>
                    <td><?= $data['Kalkulationsdatenblatt Material-Lagerplatz'] == "Nein" ? "&#9745;" : "&#9744;"; ?>
                        Nein
                    </td>
                    <td colspan="3"><?= $data['Kalkulationsdatenblatt Material-Lagerplatz'] == "mehrere" ? "&#9745;" : "&#9744;"; ?>
                        mehrere
                    </td>
                </tr>
                <tr>
                    <td rowspan="2" class="bauvorhaben-cell-titles"><b>Vorhandene Anlagen:</b></td>
                    <td>
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'IFC Model'); ?>
                        IFC Model
                    </td>
                    <td class="yellow-highlighter">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'Ansichten'); ?>
                        Ansichten
                    </td>
                    <td class="yellow-highlighter">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'Schnitte'); ?>
                        Schnitte
                    </td>
                    <td class="yellow-highlighter">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'Grundrisse'); ?>
                        Grundrisse
                    </td>
                    <td>
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'Bauzeitenplan'); ?>
                        Bauzeitenplan
                    </td>
                </tr>
                <tr>
                    <td>
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'GEAB'); ?>
                        GEAB
                    </td>
                    <td>
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'Lageplan'); ?>
                        Lageplan
                    </td>
                    <td colspan="2">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained($data['Kalkulationsdatenblatt Vorhandene Anlagen'],
                            'Baustelleneinrichtungsplan'); ?>
                        Baustelleneinrichtungsplan
                    </td>
                    <td></td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<table id="einsatz">
    <tr>
        <td class="table-title" colspan="6">
            <h2>Einsatz von Fahrzeugen/Technik</h2>
        </td>
    </tr>
    <tr>
        <td colspan="2" style="background-color: #7d7369; "></td>
        <td class="bauvorhaben-cell-titles" colspan="2">Aufbau</td>
        <td class="bauvorhaben-cell-titles" colspan="2">Abbau</td>
    </tr>
    <tr>
        <td colspan="2"><?= $data["Kalkulationsdatenblatt Kran-LKW 26To 18 m Hubhöhe"] ? "&#9745;" : "&#9744;"; ?>
            Kran-LKW 26To 18 m Hubhöhe
        </td>
        <td colspan="2"><?= $data["Kran-LKW 26To 18 m Hubhöhe Aufbau"] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data["Kran-LKW 26To 18 m Hubhöhe Abbau"] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Kran-LKW 26To 29 m Hubhöhe'] ? "&#9745;" : "&#9744;"; ?>
            Kran-LKW 26To 29 m Hubhöhe
        </td>
        <td colspan="2"><?= $data['Kran-LKW 26To 29 m Hubhöhe Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Kran-LKW 26To 29 m Hubhöhe Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Autokran Klaas 18To 52m Hubhöhe'] ? "&#9745;" : "&#9744;"; ?>
            Autokran Klaas 18To 52m Hubhöhe
        </td>
        <td colspan="2"><?= $data['Autokran Klaas 18To 52m Hubhöhe Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Autokran Klaas 18To 52m Hubhöhe Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt LKW Abroller 26To + Pritsche 7,00x2,5m'] ? "&#9745;" : "&#9744;"; ?>
            LKW Abroller 26To + Pritsche 7,00x2,5m
        </td>
        <td colspan="2"><?= $data['LKW Abroller 26To + Pritsche 7,00x2,5m Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['LKW Abroller 26To + Pritsche 7,00x2,5m Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Solo LKW 7,5-18To 10 m'] ? "&#9745;" : "&#9744;"; ?>
            Solo LKW 7,5-18To 10 m
        </td>
        <td colspan="2"><?= $data['Solo LKW 7,5-18To 10 m Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Solo LKW 7,5-18To 10 m Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Sattel 40To Länge 17m'] ? "&#9745;" : "&#9744;"; ?>
            Sattel 40To Länge 17m
        </td>
        <td colspan="2"><?= $data['Sattel 40To Länge 17m Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Sattel 40To Länge 17m Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt 3,5 To Sprinter'] ? "&#9745;" : "&#9744;"; ?>
            3,5 To Sprinter
        </td>
        <td colspan="2"><?= $data['3,5 To Sprinter Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['3,5 To Sprinter Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Kran (bauseits) kostenlos'] ? "&#9745;" : "&#9744;"; ?>
            Kran (bauseits) kostenlos
        </td>
        <td colspan="2"><?= $data['Kran (bauseits) kostenlos Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Kran (bauseits) kostenlos Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Aufzug GEDA 500 kg'] ? "&#9745;" : "&#9744;"; ?>
            Aufzug GEDA 500 kg
        </td>
        <td colspan="2"><?= $data['Aufzug GEDA 500 kg Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Aufzug GEDA 500 kg Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Aufzug GEDA 1500 kg'] ? "&#9745;" : "&#9744;"; ?>
            Aufzug GEDA 1500 kg
        </td>
        <td colspan="2"><?= $data['Aufzug GEDA 1500 kg Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Aufzug GEDA 1500 kg Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Aufzug GEDA 200 kg'] ? "&#9745;" : "&#9744;"; ?>
            Aufzug GEDA 200 kg
        </td>
        <td colspan="2"><?= $data['Aufzug GEDA 200 kg Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Aufzug GEDA 200 kg Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Alimak 300 kg'] ? "&#9745;" : "&#9744;"; ?>
            Alimak 300 kg
        </td>
        <td colspan="2"><?= $data['Alimak 300 kg Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Alimak 300 kg Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Geda Seilwinde'] ? "&#9745;" : "&#9744;"; ?>
            Geda Seilwinde
        </td>
        <td colspan="2"><?= $data['Geda Seilwinde Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Geda Seilwinde Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Teleskoplader bis 6m Hubhöhe'] ? "&#9745;" : "&#9744;"; ?>
            Teleskoplader bis 6m Hubhöhe
        </td>
        <td colspan="2"><?= $data['Teleskoplader bis 6m Hubhöhe Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Teleskoplader bis 6m Hubhöhe Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Teleskoplader bis 6-18m Hubhöhe'] ? "&#9745;" : "&#9744;"; ?>
            Teleskoplader bis 6-18m Hubhöhe
        </td>
        <td colspan="2"><?= $data['Teleskoplader bis 6-18m Hubhöhe Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Teleskoplader bis 6-18m Hubhöhe Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2"><?= $data['Kalkulationsdatenblatt Teleskoplader bis 18-30m Hubhöhe'] ? "&#9745;" : "&#9744;"; ?>
            Teleskoplader bis 18-30m Hubhöhe
        </td>
        <td colspan="2"><?= $data['Teleskoplader bis 18-30m Hubhöhe Aufbau'] ? "&#9745;" : "&#9744;"; ?></td>
        <td colspan="2"><?= $data['Teleskoplader bis 18-30m Hubhöhe Abbau'] ? "&#9745;" : "&#9744;"; ?></td>
    </tr>
    <tr>
        <td colspan="2">
            <?= $data['Kalkulationsdatenblatt Kran-LKW 26To 18 m Hubhöhe'] ? "&#9745;" : "&#9744;"; ?>
            Kran-LKW 26To 18 m Hubhöhe
        </td>
        <td colspan="2">
            <?= $data['Kran-LKW 26To 18 m Hubhöhe Aufbau'] ? "&#9745;" : "&#9744;"; ?>
        </td>
        <td colspan="2">
            <?= $data['Kran-LKW 26To 18 m Hubhöhe Abbau'] ? "&#9745;" : "&#9744;"; ?>
        </td>
    </tr>
</table>

<table id="gebaeude-eigenschaften" class="container-table bordered">
    <tr>
        <td class="table-title" colspan="4">
            <h2>Gebäude-Eigenschaften</h2>
        </td>
    </tr>
    <tr>
        <td style="border-right: 1px solid black"><b>Bauzustand:</b></td>
        <td style="border-right: none">
            <?= $data['Kalkulationsdatenblatt Bauzustand'] ? "&#9745;" : "&#9744;"; ?>
            Bestandsgebäude
        </td>
        <td style="border-right: none; border-left: none">
            <?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bauzustand'], 'Neubau / Rohbau');
            ?>
            Neubau/Rohbau
        </td>
        <td style="border-left: none">
            <?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bauzustand'], 'Rohbaubegleitend');
            ?>
            Rohbaubegleitend
        </td>
    </tr>
    <tr>
        <td><b>Gebäudegeometrie:</b></td>
        <td style="border-right: none; border-left: none">
            <?= $data['Kalkulationsdatenblatt Gebäudegeometrie'] == 'Balkone' ? "&#9745;" : "&#9744;"; ?>
            Balkone
        </td>
        <td style="border-right: none; border-left: none">
            <?= $data['Kalkulationsdatenblatt Gebäudegeometrie'] == 'viele Fenster' ? "&#9745;" : "&#9744;"; ?>
            viele Fenster
        </td>
        <td style="border-right: none; border-left: none">
            <?= $data['Kalkulationsdatenblatt Gebäudegeometrie'] == 'Eckige Geometrie' ? "&#9745;" : "&#9744;"; ?>
            Eckige Geometrie
        </td>
    </tr>
    <tr>
        <td><b>Ankergrund:</b></td>
        <td colspan="3" class="container-cell">
            <table class="nested-table">
                <tr>
                    <td style="border: none !important;">
                        <?= isset($data['Ankergrund WDVS cm']) ? "&#9745;" : "&#9744;"; ?>
                        WDVS
                        <?= $data['Ankergrund WDVS cm'] ?? '' ?>
                        cm
                    </td>
                    <td>
                        <?= $data['Ankergrund Ziegel'] ? "&#9745;" : "&#9744;"; ?>
                        Ziegel
                    </td>
                    <td>
                        <?= $data['Ankergrund Beton'] ? "&#9745;" : "&#9744;"; ?>
                        Beton
                    </td>
                    <td>
                        <?= $data['Ankergrund Holz'] ? "&#9745;" : "&#9744;"; ?>
                        Holz
                    </td>
                    <td>
                        &#9744;
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td><b>Dachüberstände:</b></td>
        <td class="container-cell" colspan="3" style="border: none !important;">
            <table class="nested-table" style="border: none !important;">
                <tr>
                    <td colspan="2"
                        style="border: none !important;">
                        <?= $data['Dachüberstände Giebel cm'] ? "&#9745;" : "&#9744;"; ?>
                        Giebel <?= $data['Dachüberstände Giebel cm']; ?> cm
                    </td>
                    <td colspan="3"
                        style="border: none !important;">            <?= $data['Dachüberstände Traufe cm'] ? "&#9745;" : "&#9744;"; ?>
                        Traufe <?= $data['Dachüberstände Traufe cm']; ?> cm
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td><b>Besonderheit:</b></td>
        <td class="container-cell no-pad" colspan="3">
            <table class="nested-table" id="besonderheit">
                <tr style="border-top: none !important;">
                    <td style="border-top: none !important; border-right: 1px solid black !important">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained(
                            $data['Kalkulationsdatenblatt Besonderheit'], 'Elektrische Freileitung');
                        ?>
                        Elektrische Freileitung
                    </td>
                    <td style="border-right: 1px solid black !important;">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained(
                            $data['Kalkulationsdatenblatt Besonderheit'],
                            'Öffentlicher Verkehrsraum (Sondernutzung)');
                        ?>
                        Öffentlicher Verkehrsraum
                        (Sondernutzung)
                    </td>
                    <td>
                        <?php
                        PrintoutHelper::echoCheckboxIfContained(
                            $data['Kalkulationsdatenblatt Besonderheit'],
                            'Passantenschutz');
                        ?>
                        Passantenschutz
                    </td>
                </tr>
                <tr>
                    <td style="border-right: 1px solid black !important; border-top: 1px solid black !important">
                        <?php
                        PrintoutHelper::echoCheckboxIfContained(
                            $data['Kalkulationsdatenblatt Besonderheit'],
                            'Statik');
                        ?>
                        Statik
                    </td>
                    <td style="border-right: 1px solid black !important; border-top: 1px solid black !important">
                        &#9744;
                    </td>
                    <td style="border-top: 1px solid black !important">
                        &#9744;
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td><b>Aufstandsfläche:</b></td>
        <td style="border-left: none; border-right: none">
            <?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Aufstandsfläche'],
                'tragfähige feste Fläche');
            ?>
            tragfähige feste Fläche
        </td>
        <td colspan="2" style="border-left: none; border-right: none">
            <?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Aufstandsfläche'],
                'tragfähige feste lastverteilende Unterlage');
            ?>
            lastverteilende Unterlage
        </td>
    </tr>
</table>

<table id="geruestart" class="bordered">
    <tr>
        <td class="table-title" colspan="3">
            <h2>Gerüstart</h2>
        </td>
    </tr>
    <tr>
        <td>
            <?= in_array('Hängegerüst', $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Hängegerüst
        </td>
        <td>
            <?= in_array('Raumgerüst/Flächengerüst',
                $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Raumgerüst / Flächengerüst
        </td>
        <td>
            <?= in_array('Treppenturm / Gerüsttreppe',
                $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Treppenturm / Gerüsttreppe
        </td>
    </tr>
    <tr>
        <td>
            <?= in_array('Fahrgerüst', $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Fahrgerüst
        </td>
        <td>
            <?= in_array('Dachfanggerüst (Dacharbeiten)',
                $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Dachfanggerüst (Dacharbeiten)
        </td>
        <td>
            <?= in_array('Fassadengerüst (Maler/Putzer)',
                $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Fassadengerüst (Maler/Putzer)
        </td>
    </tr>
    <tr>
        <td>
            <?= in_array('Sandwichfassade',
                $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Sandwichfassade
        </td>
        <td>
            <?= in_array('Schutzdach',
                $data['Kalkulationsdatenblatt Gerüstart'] ?? []) ?
                "&#9745;" : "&#9744;"; ?>
            Schutzdach
        </td>
        <td>
            &#9744;
        </td>
    </tr>
</table>

<table id="geruest-eigenschaften" class="bordered">
    <tr>
        <td class="table-title" colspan="6" style="border-right: 3px solid black">
            <h2>Gerüst-Eigenschaften</h2>
        </td>
    </tr>
    <tr>
        <td colspan="2">
            <table class="eigenschaften-details">
                <tr>
                    <td colspan="6" style="">
                        <b>Lastklasse (gleichmäßig verteilte Last):</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <?= $data['Kalkulationsdatenblatt Lastklasse (gleichmäßig verteilte Last)'] ===
                        '1 (0,75 kN/m²)' ? "&#9745;" : "&#9744;"; ?>
                        1 (0,75 kN/m²)
                    </td>
                    <td colspan="2">
                        <?=
                        $data['Kalkulationsdatenblatt Lastklasse (gleichmäßig verteilte Last)'] ===
                        '2 (1,50 kN/m²)' ? "&#9745;" : "&#9744;"; ?>
                        2 (1,50 kN/m²)
                    </td>
                    <td colspan="2">
                        <?=
                        $data['Kalkulationsdatenblatt Lastklasse (gleichmäßig verteilte Last)'] ===
                        '3 (2,00 kN/m²)' ? "&#9745;" : "&#9744;"; ?>
                        3 (2,00 kN/m²)
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <?= $data['Kalkulationsdatenblatt Lastklasse (gleichmäßig verteilte Last)'] ===
                        '4 (3,00 kN/m²)' ? "&#9745;" : "&#9744;"; ?>
                        4 (3,00 kN/m²)
                    </td>
                    <td colspan="2">
                        <?= $data['Kalkulationsdatenblatt Lastklasse (gleichmäßig verteilte Last)'] ===
                        '5 (4,50 kN/m²)' ? "&#9745;" : "&#9744;"; ?>
                        5 (4, 50 kN/m²)
                    </td>
                    <td colspan="2">
                        <?= $data['Kalkulationsdatenblatt Lastklasse (gleichmäßig verteilte Last)'] ===
                        '6 (6,00 kN/m²)' ? "&#9745;" : "&#9744;"; ?>
                        6 (6,00 kN/m²)
                    </td>
                </tr>
            </table>
        </td>
        <td colspan="2" id="breitenklasse" style="border-right: 1px solid black">
            <table class="eigenschaften-details">
                <tr>
                    <td colspan="4">
                        <b>Breitenklasse:</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <?php PrintoutHelper::echoCheckbox(
                            $data['Kalkulationsdatenblatt Breitenklasse'] === 'W 06') ?>
                        W 06
                    </td>
                    <td colspan="2">
                        <?php PrintoutHelper::echoCheckbox(
                            $data['Kalkulationsdatenblatt Breitenklasse'] === 'W 09') ?>
                        W 09
                    </td>
                </tr>
                <tr>
                    <td>
                        <?php PrintoutHelper::echoCheckbox(
                            $data['Kalkulationsdatenblatt Breitenklasse'] === 'W') ?>
                        W
                    </td>
                </tr>
            </table>
        </td>
        <td class="zugang-details" colspan="2" style="border-right: 3px solid black !important">
            <table class="zugang-details">
                <tr>
                    <td style="border: none">
                        <b>Zugang nur für Gerüstersteller:</b>
                    </td>
                </tr>
                <tr>
                    <td style="border: 0 !important;"
                        colspan="2"><?= $data['Kalkulationsdatenblatt Zugang nur für Gerüstersteller']
                        == "Innenliegender Leitergang" ? "&#9745;" : "&#9744;"; ?>
                        Innenliegender Leitergang
                    </td>
                    <td style="border: 0 !important;" colspan="2"></td>
                    <td style="border: 0 !important;" colspan="2"></td>
                </tr>
                <tr>
                    <td style="border: 0 !important;"
                        colspan="2"><?= $data['Kalkulationsdatenblatt Zugang nur für Gerüstersteller']
                        == "Podesttreppe" ? "&#9745;" : "&#9744;"; ?>
                        Podesttreppe
                    </td>
                    <td style="border: 0 !important;" colspan="2"></td>
                    <td style="border: 0 !important;" colspan="2"></td>
                </tr>
                <tr>
                    <td style="border: 0 !important;"
                        colspan="2"><?= $data['Kalkulationsdatenblatt Zugang nur für Gerüstersteller'] == "Leiter" ? "&#9745;" : "&#9744;"; ?>
                        Leiter
                        <span style="margin-left: 90px">&#9744;</span>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<table class="aufmass-abrechnung bordered">
    <tr>
        <td rowspan="2" colspan="2" style="border-right: 1px solid black; width: 400px;">
            <b> Aufmass nach. (Abrechnung)</b>
        </td>
        <td colspan="4"
            style="width: 800px; border-right: 3px solid black">
            <?= $data['Kalkulationsdatenblatt Aufmass nach. (Abrechnung)'] == "Arbeitsgerüst" ? "&#9745;" : "&#9744;"; ?>
            Arbeitsgerüst
        </td>
    </tr>
    <tr>
        <td colspan="4" style="border-right: 3px solid black">
            <?= $data['Kalkulationsdatenblatt Aufmass nach. (Abrechnung)'] == "Schutzgerüst" ? "&#9745;" : "&#9744;"; ?>
            Schutzgerüst
        </td>
    </tr>
</table>

<table class="technische-daten bordered">
    <tr>
        <td class="table-title" colspan="6" style="border-right: 3px solid black">
            <h2>Technische Daten</h2>
        </td>
    </tr>
    <tr>
        <td colspan="4"
            style="border-right: 1px solid black; border-bottom: 1px solid black; border-top: 1px solid black;">
            Horizontaler Abstand vom Belag zum Gebäude (Wandabstand)
        </td>
        <td style="width: 350px; border-right: 3px solid black; border-bottom: 1px solid black;"
            colspan="2">
            <?= $data['Kalkulationsdatenblatt Horizontaler Abstand vom Belag zum Gebäude (Wandabstand) (m)']; ?>
            m
        </td>
    </tr>
    <tr>
        <td colspan="4"
            style="border-right: 1px solid black; border-bottom: 1px solid black; border-top: 1px solid black;">
            Horizontaler Abstand von der Traufe zum Seitenschutz/ Schutzwand
        </td>
        <td style="width: 350px; border-right: 3px solid black; border-bottom: 1px solid black;"
            colspan="2"> <?= $data['Kalkulationsdatenblatt Horizontaler Abstand von der Traufe zum Seitenschutz/ Schutzwand (m)']; ?>
            m
        </td>
    </tr>
    <tr>
        <td colspan="4"
            style="border-right: 1px solid black; border-bottom: 1px solid black; border-top: 1px solid black;">
            Vertikaler Abstand von der Bodenplatte Erdgeschoss zum ersten Belag
        </td>
        <td style="width: 350px; border-right: 3px solid black; border-bottom: 1px solid black;" colspan="2">
            <?= $data['Kalkulationsdatenblatt Vertikaler Abstand von der Bodenplatte Erdgeschoss zum ersten Belag (m)']; ?>
            m
        </td>
    </tr>
    <tr>
        <td colspan="4"
            style="border-right: 1px solid black; border-bottom: 1px solid black; border-top: 1px solid black;">
            Vertikaler Abstand von der Traufe zum obersten Belag
        </td>
        <td style="width: 350px; border-right: 3px solid black; border-bottom: 1px solid black;"
            colspan="2"><?= $data['Kalkulationsdatenblatt Vertikaler Abstand von der Traufe zum obersten Belag (m)']; ?>
            m
        </td>
    </tr>
</table>

<table id="bekleidung-table" class="bordered">
    <tr>
        <td rowspan="4" id="bekleidung-title">
            <h4>Bekleidung / Anbauteile:</h4>
        </td>
    </tr>
    <tr>
        <td><?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bekleidung / Anbauteile'], 'Sandstrahlnetz'); ?>
            Sandstrahlnetz
        </td>
        <td><?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bekleidung / Anbauteile'], 'Rupfennetz'); ?>
            Rupfennetz
        </td>
    </tr>
    <tr>
        <td><?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bekleidung / Anbauteile'], '(Gitter)Plane'); ?>
            (Gitter)Plane
        </td>
        <td><?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bekleidung / Anbauteile'], 'Plane als B1'); ?>
            Plane als B1
        </td>
    </tr>
    <tr>
        <td><?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bekleidung / Anbauteile'], 'Innengeländer'); ?>
            Innengeländer
        </td>
        <td><?php
            PrintoutHelper::echoCheckboxIfContained(
                $data['Kalkulationsdatenblatt Bekleidung / Anbauteile'], 'Gitterträger'); ?>
            Gitterträger
        </td>
    </tr>
</table>

<table id="konsolen-table" class="bordered">
    <tr>
        <td class="table-title" colspan="6">
            <h2>Konsolen</h2>
        </td>
    </tr>
    <tr>
        <td>
            <?= isset($data['Kalkulationsdatenblatt Konsolen innen']) ? "&#9745;" : "&#9744;"; ?>
            <b>Konsole innen:</b>
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen innen'] == "0,25m" ? "&#9745;" : "&#9744;"; ?>0,25m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen innen'] == "0,33m" ? "&#9745;" : "&#9744;"; ?>0,33m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen innen'] == "0,50m" ? "&#9745;" : "&#9744;"; ?>0,50m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen innen'] == "0,67m" ? "&#9745;" : "&#9744;"; ?>0,67m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen innen'] == "1,00m" ? "&#9745;" : "&#9744;"; ?>1,00m
        </td>
    </tr>
    <tr>
        <td>
            <?= isset($data['Kalkulationsdatenblatt Konsolen außen']) ? "&#9745;" : "&#9744;"; ?>
            <b>Konsole außen:</b>
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen außen'] == "0,25m" ? "&#9745;" : "&#9744;"; ?>0,25m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen außen'] == "0,33m" ? "&#9745;" : "&#9744;"; ?>0,33m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen außen'] == "0,50m" ? "&#9745;" : "&#9744;"; ?>0,50m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen außen'] == "0,67m" ? "&#9745;" : "&#9744;"; ?>0,67m
        </td>
        <td><?= $data['Kalkulationsdatenblatt Konsolen außen'] == "1,00m" ? "&#9745;" : "&#9744;"; ?>1,00m
        </td>
    </tr>
</table>

<table id="verankerung" class="bordered">
    <tr>
        <td class="table-title" colspan="6">
            <h2>Verankerung / Verschluss</h2>
        </td>
    </tr>
    <tr>
        <td class="align-center" colspan="6">
            <b class="align-center">
                * Hinweis: ab 160 mm statischer Nachweis/ Innendiagonale verwenden (außer bei Netzen und Plane)
            </b>
        </td>
    </tr>
    <tr>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckbox(isset(
                $data['Verankerung / Verschluss Ringschrauben']));
            ?>
            Ringschrauben
        </td>
        <td class="bordered">
            <?= $data['Verankerung / Verschluss Ringschrauben'] == "120 mm"
                ? "&#9745;" : "&#9744;"; ?>
            120mm
        </td>
        <td class="bordered">
            <?= $data['Verankerung / Verschluss Ringschrauben'] == "160 mm *" ? "&#9745;" : "&#9744;"; ?>
            160mm *
        </td>
        <td class="bordered">
            <?= $data['Verankerung / Verschluss Ringschrauben'] == "230 mm *" ? "&#9745;" : "&#9744;"; ?>
            230mm *
        </td>
        <td class="bordered">
            <?= $data['Verankerung / Verschluss Ringschrauben'] == "300 mm *" ? "&#9745;" : "&#9744;"; ?>
            300mm *
        </td>
        <td class="bordered">
            <?= $data['Verankerung / Verschluss Ringschrauben'] == "350 mm *" ? "&#9745;" : "&#9744;"; ?>
            350mm *
        </td>
    </tr>
    <tr>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckboxIfContained($data['Verankerung / Verschluss Abstützung'],
                'Abstützung / Rohre'); ?>
            Abstützung / Rohre
        </td>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckboxIfContained($data['Verankerung / Verschluss Abstützung'],
                'Abstutztürme'); ?>
            Abstütztürme
        </td>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckboxIfContained($data['Verankerung / Verschluss Abstützung'],
                'V-Anker'); ?>
            V-Anker
        </td>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckboxIfContained($data['Verankerung / Verschluss Abstützung'],
                'Gerüstkappen'); ?>
            Gerüstkappen
        </td>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckboxIfContained($data['Verankerung / Verschluss Abstützung'],
                'Freistehen. Gerüst'); ?>
            Freistehen. Gerüst
        </td>
        <td class="bordered">
            <?php
            PrintoutHelper::echoCheckboxIfContained($data['Verankerung / Verschluss Abstützung'],
                'Innendiagonale'); ?>
            Innendiagonale
        </td>
    </tr>
</table>

<table id="verschluss" class="bordered">
    <tbody style="border-left: 3px solid black; border-right: 3px solid black">
    <tr>
        <td rowspan="3">
            <b>Verschluss:</b>
        </td>
    </tr>
    <tr>
        <td><?= $data['Verankerung / Verschluss Verschluss'] == 'durch Fa. Schäfer Gerüstbau' ? "&#9745;" : "&#9744;"; ?>
            durch Fa. Schäfer Gerüstbau
        </td>
        <td><?= $data['Verankerung / Verschluss Verschluss'] == 'bauseits' ? "&#9745;" : "&#9744;"; ?>
            bauseits
        </td>
    </tr>
    <tr>
        <td><?= $data['Verankerung / Verschluss Verschluss'] == 'Ankerlöcher bleiben offen' ? "&#9745;" : "&#9744;"; ?>
            Ankerlöcher bleiben offen
        </td>
        <td><?= $data['Verankerung / Verschluss Verschluss'] == 'Keine Verankerung vorhanden' ? "&#9745;" : "&#9744;"; ?>
            Keine Verankerung vorhanden
        </td>
    </tr>
    </tbody>
</table>

<table class="bordered" style="border-bottom: 3px solid black">
    <tr>
        <td class="table-title">
            <h2> Kalkulationsbemerkungen / INFOS</h2>
        </td>
    </tr>
    <tr>
        <td>
            <section class="comment">
                <?= $data['Kalkulationsdatenblatt Kalkulationsbemerkungen / INFOS']; ?>
            </section>
        </td>
    </tr>
    <tr>
        <td class="table-title"><h2>Infos aus dem Vergabegespräch</h2></td>
    </tr>
    <tr>
        <td>
            <section class="comment">
                <?= $data['Kalkulationsdatenblatt Infos aus dem Vergabegespräch']; ?>
            </section>
        </td>
    </tr>
</table>

<table id="signature-table" class="no-border">
    <tr>
        <td>
            <b> Unterschrift Kalkulator:</b>
            <?php if (isset($data['Kalkulationsdatenblatt Unterschrift Kalkulator'])) { ?>
                <img src="<?= $data['Kalkulationsdatenblatt Unterschrift Kalkulator']; ?>" id="signature" alt="">
            <?php } ?>
        </td>
    </tr>
</table>
</body>
</html>
