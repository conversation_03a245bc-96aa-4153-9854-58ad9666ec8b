* {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 14px;
}

.bordered th, .bordered td {
    border: 1px solid black;
}

td {
    padding: 3px;
}

.no-pad {
    padding: 0;
}

table {
    max-width: 1110px;
    width: 100%;
    border-collapse: collapse;
}

#main-body-bordered-table {
    padding: 0;
    margin: 0;
    border-collapse: collapse;
    border: 2px solid black;
    border-bottom: none;
}

#signature {
    height: 120px;
}

.align-center {
    text-align: center;
}

.table-title {
    border-left: 2px solid black;
    border-right: 2px solid black;
    text-align: center;
    background-color: #ccc;
    width: 100%;
    border-top: 2px solid black !important;
    border-bottom: 0.0667em solid black !important;
    border-collapse: collapse;
    padding: 0 !important;
    margin: 0 !important;
}

h2 {
    margin: 5px 0;
    font-size: 19px;
}

.yellow-highlighter {
    background-color: #fefd3a;
}

.bordered {
    border: 1px solid black;
    border-left: 3px solid black;
    border-right: 3px solid black;
}

#verankerung td {
    border: 1px solid black;
}

#gebaeude-eigenschaften {
    border-collapse: collapse;
    padding: 0;
}

.eigenschaften-details td {
    padding-left: 5px !important;
}

.eigenschaften-details td {
    width: 150px !important;
}

#geruest-eigenschaften td {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.eigenschaften-details, .eigenschaften-details td {
    border: none;
}

#breitenklasse, #breitenklasse td {
    text-align: left;
    border: none;
}

#breitenklasse td {
    vertical-align: top;
}

#geruestart td {
    padding-right: 10px;
    padding-left: 10px;
    border-collapse: collapse;
}

.nested-table td {
    padding: 0;
    border-collapse: collapse;
    border: none !important;
}

#besonderheit td {
    padding: 3px;
}

.text-align-center {
    text-align: center;
}

#bauvorhaben-adresse td:nth-child(1) {
    width: 180px;
}

#bauvorhaben-adresse td {
    border: 1px solid black;
}

#bauvorhaben-adresse {
    border-collapse: collapse;
}

#bauvorhaben {
    width: 100%;
    border-collapse: collapse;
}

#bauvorhaben td {
    border: 1px solid black;
}

#bauvorhaben td {
    width: 600px !important;
}

#bauvorhaben td:first-child {
    width: 250px;
    border-top: none !important;
}

#einsatz tr td:last-child {
    border-right: 3px solid black;
}

#einsatz tbody {
    border-left: 3px solid black;
}

#einsatz {
    width: 100%;
}

#einsatz td {
    border: 1px solid black;
}

#einsatz td:nth-child(3) {
    text-align: center;
    width: 350px;
}

#einsatz td:nth-child(2) {
    text-align: center;
    width: 350px;
}

#einsatz td:nth-child(1) {
    width: 510px;
}

#gebaeude-eigenschaften {
    width: 100%;
    border-collapse: collapse;
}

#gebaeude-eigenschaften td {
    width: 300px;
}

#bekleidung-table {
    border-collapse: collapse;
}

#bekleidung-table td:nth-child(1) {
    width: 500px !important;
}

#bekleidung-title {
    border: 1px solid black;
}

#bekleidung-table td {
    border: 1px solid black;
    width: 350px;
}

#konsolen-table {
    width: 100%;
    border-collapse: collapse;
}

#konsolen-table td {
    border: 1px solid black;
    width: 300px;
}

#verankerung {
    width: 100%;
}

#verschluss {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
}

#verschluss td {
    width: 300px;
    border-collapse: collapse;
    border: 1px solid black;
}

#signature-table {
    width: 100%;
    border: none !important;
    font-size: 18px;
}

.no-border {
    border: none !important;
}

.full-width h2, .full-width h3 {
    margin: 0 !important;
}

.bauvorhaben-cell-titles td {
    width: 100px;
    min-width: 100px;
    max-width: 100px;
    font-weight: bold !important;
    white-space: nowrap;
    padding: 0 !important;
}

#geruest-eigenschaften {
    white-space: nowrap !important;
}

#bauvorhaben-adresse {
    table-layout: fixed;
}