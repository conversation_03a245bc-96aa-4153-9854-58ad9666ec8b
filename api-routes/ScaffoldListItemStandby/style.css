html {
    font-family: Arial, sans-serif;
}

.total-hours-vertical {
    /* the real column will always be larger because of the total hour cell contents */
    width: 40px;
}

.p-4 {
    padding: 4px !important;
}

.header-table, .main-table, .footer-table {
    width: 100%;
}

.table-row {
    height: 40px;
}

.header-table tr:first-child td:last-child,
.header-table tr:nth-child(2) td:last-child {
    border: 1px solid black;
    width: 20%;
}

.header-table td, .main-table td {
    padding: 3px 1px;
}

.header-table tr td:nth-child(2),
.header-table tr td:nth-child(3) {
    width: 27%
}

.header-title {
    width: 100%;
    padding-top: 20px;
}

.header-text {
    padding-right: 60%;
}

.text-align-center {
    text-align: center !important;
    padding-top: 50px
}

.font-bold {
    font-weight: bold;
}

.italic {
    font-style: italic;
}

.text-align-right {
    text-align: right !important;
}

.text-align-center {
    text-align: center;
}

.rental-container {
    width: 100%;
}

.standby-table {
    width: 100%;
    border-collapse: collapse;
}

.rental-table-first {
    margin-top: 60px;
    width: 100%;
    border-collapse: collapse;
}

.rental-table-first th, .rental-table-first td {
    border: 1px solid black;
    padding: 8px;
    text-align: center;
}

.rental-table-first th {
    background-color: #f2f2f2;
}

.area-box {
    border: 1px solid black;
    padding: 5px;
    display: inline-block;
    width: 150px;
    text-align: center;
    margin-left: 5px;
}

.remarks-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
}

.remarks-table td, .remarks-table th {
    border: 1px solid black;
    padding: 10px;
    vertical-align: top;
}

.remarks-table .header {
    border: none;
}

.remarks-table .calculation {
    font-style: italic;
    font-weight: bold;
    font-size: 18px;
    border: none;
    padding: 30px 25px 50px;
}

.footer {
    padding-top: 20px;
}

.footer-page {
    width: 100%;
}

.left-align {
    text-align: left;
}

.right-align {
    text-align: right;
}

.underlined {
    border-bottom: 2px solid black;
}