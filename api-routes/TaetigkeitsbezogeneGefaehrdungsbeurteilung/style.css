/*--------------------------------- begin generic styles ------------------------------*/
* {
    font-family: "Arial", sans-serif;
    font-size: 10px;
    margin: 0;
    padding: 0;
}

table {
    width: 100%;
    border: 1px solid black;
    border-collapse: collapse;
}

.container {
    border: 1px solid #333;
    padding: 0 15px;
    background: #fff;
    margin: 0 auto;
}

.section h2 {
    border-bottom: 1px solid #ccc;
    font-size: 12px;
    font-weight: bold;
    padding-bottom: 2px;
    margin-bottom: 2px;
}

.fields-container {
    font-size: 0;
}

.field {
    display: inline-block;
    vertical-align: top;
    width: 32%;
    margin-right: 1%;
    box-sizing: border-box;
    margin-bottom: 8px;
}

.field-one {
    display: inline-block;
    vertical-align: top;
    width: 100%;
    margin-right: 1%;
    box-sizing: border-box;
    margin-bottom: 8px;
}

.field-two {
    display: inline-block;
    vertical-align: top;
    width: 64%;
    margin-right: 1%;
    box-sizing: border-box;
    margin-bottom: 8px;
}

.field-four {
    display: inline-block;
    vertical-align: top;
    width: 18%;
    margin-right: 1%;
    box-sizing: border-box;
    margin-bottom: 8px;
}


.field:nth-child(3n) {
    margin-right: 0;
}

.field-four:nth-child(4n) {
    margin-right: 0;
}

.label {
    font-weight: bold;
    margin-bottom: 2px;
    display: block;
}

.combobox,
.combobox-multi,
.checkbox-group {
    margin-left: 8px;
}

.option {
    display: block;
}

.ascii-checkbox {
    margin-right: 5px;
}

table td {
    padding: 2px;
}

.bg-orange {
    background: rgb(244, 200, 146);
}

.orange-text {
    color: #f7931d;
}

.font-bold {
    font-weight: bold;
    font-size: 11px;
    padding-top: 4px;
    padding-bottom: 2px;
}

.font-bold-small {
    font-weight: bold;
    font-size: 9px;
    padding-right: 3px;
}

.font-14 {
    font-size: 14px;
}

.mt-10 {
    margin-top: 12px;
}

/*--------------------------------- end generic styles ------------------------------*/

/*--------------------------------- begin main content structure styles ------------------------------*/
.left-div {
    float: left;
    width: 55%;
}

.left-div__first-table td:last-child {
    width: 14%;
}

.right-div {
    float: right;
    width: 45%;
}

.bottom-left-div {
    float: left;
    width: 60%;
}

.bottom-right-div {
    float: right;
    padding-left: 2%;
    width: 38%;
}

.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

/*--------------------------------- end main content structure styles ------------------------------*/

/*--------------------------------- begin header-table styles ------------------------------*/
.header-table {
    border-bottom: none !important;
    width: 100%;
}

.header-table tr {
    height: 100px;
}

.header-table td {
    padding: 0 2px;
    border-collapse: collapse;
}

.header-table tr:first-child td:first-child {
    padding: 5px;
    text-align: center;
}

.header-table td:first-child {
    width: 10%;
}

.header-td-text {
    font-size: 18px;
    padding-bottom: 12px;
}

.header-td-text-last {
    font-size: 14px;
}

.header-highlight {
    font-weight: bold;
    font-size: 24px;
    vertical-align: middle;
    width: 38%;
    text-align: center;
}

.header-table td:last-child {
    border-left: 1px solid black;
    text-align: center;
}

/*--------------------------------- end header-table styles ------------------------------*/

/*--------------------------------- begin left-div and right-div styles ------------------------------*/
.left-div table {
    border: none;
    border-left: 1px solid black;
    border-bottom: 1px solid black;
}

.left-div__first-table {
    border-top: 1px solid black !important;
}

.left-div__second-table, .left-div__third-table {
    border-bottom: none !important;
}

.left-div__second-table td:nth-child(2) {
    border-bottom: 1px solid black;
    width: 70%;
    margin: 0 10px;
    height: 10px;
}

.left-div__second-table td:last-child {
    width: 5%;
}

.left-div__third-table,
.left-div__forth-table,
.right-div__first-table,
.right-div__second-table,
.right-div__third-table {
    border-top: none !important;
    padding-top: 10px;
}

.left-div__third-table tr:not(:first-child) td {
    border-bottom: 1px solid black;
    height: 15px;
    background: #fedab3;
}

.left-div__third-table tr:last-child td,
.right-div__first-table,
.right-div__second-table {
    border-bottom: none;
}

.bottom-left-div__table tr:last-child td:last-child,
.right-div__third-table tr:last-child td:last-child {
    padding-bottom: 5px;
}

.left-div__forth-table tr:first-child td:first-child {
    padding-top: 5px;
}

/*--------------------------------- end left-div and right-div styles ------------------------------*/

/*--------------------------------- begin bottom-left-div and bottom-right-div__table styles ------------------------------*/
.bottom-left-div__table {
    border-top: none;
    border-left: none;
    border-bottom: none;
}

.bottom-left-div__table td {
    padding: 0 2px;
}

.bottom-left-div__table tbody {
    padding: 5px;
}

.measure-hierarchy {
    display: table;
    width: 100%;
    margin: 1rem 0;
}

.measure-hierarchy-row {
    display: table-row;
}

.measure-hierarchy-cell {
    display: table-cell;
    vertical-align: middle;
    padding: 1rem;
}

.measure-hierarchy-left {
    width: 80%;
}

.measure-hierarchy-right {
    width: 20%;
    text-align: center;
}

.measure-hierarchy-title {
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 0.25rem;
    margin-top: 0.25rem;
}

.measure-hierarchy-list {
    list-style: none;
    font-size: 12px;
}

.measure-hierarchy-list li {
    padding-bottom: 2px;
}

.measure-hierarchy-list li:nth-child(2) {
    padding-left: 20px;
}

.measure-hierarchy-list li:nth-child(3) {
    padding-left: 40px;
}

.measure-hierarchy-list li:nth-child(4) {
    padding-left: 60px;
}

.measure-hierarchy-list li:nth-child(5) {
    padding-left: 80px;
}

.measure-hierarchy-right img {
    max-width: 90px;
    height: auto;
}

.bottom-right-div__first-table {
    border-bottom: none !important;
}

.bottom-right-div__first-table,
.bottom-right-div__second-table {
    border: none !important;
}

.height-80 {
    height: 100px;
}

.padding-18 {
    padding-left: 18px;
}

.second-page {
    page-break-before: always
}

.left-div__bottom-div {
    margin: 4px 4px 0 0;
    width: 55%;
}

.left-div__bottom-div-table {
    border: none !important;
}

.left-div__bottom-div-table td {
    padding: 0 10px;
}

.right-div table {
    border-left: none !important;
}

.right-div__first-table {
    border-top: 1px solid black !important;
}

.left-div table {
    border-right: 1px solid #070707 !important;
}

.right-div__bottom-table {
    border: none !important;
    background: #fab05f;
    width: 97%;
    margin-top: 10px;
    margin-left: 10px;
    border-radius: 8px;
}

.right-div__bottom-table td {
    font-size: 14px;
    color: #ffffff;
    font-weight: bold;
    padding: 5px;
}

.right-div__bottom-table td:first-child {
    width: 15%;
    font-size: 20px;
}

.right-div__bottom-table td:nth-child(3) {
    width: 100px;
    text-align: left;
}

.bg-white {
    background: #ffffff;
    height: 30px;
    width: 100px;
    border-radius: 5px;
}

.right-div__bottom-table td:last-child {
    width: 60%;
}

.underlined {
    border-bottom: 1px solid #000000;
}

.left-align {
    text-align: left;
}

.right-arrow {
    font-size: 16px;
    font-weight: bold;
}

/*--------------------------------- end bottom-left-div and bottom-right-div__table styles ------------------------------*/

.footer-container {
    border-top: 1px solid black;
    display: table;
    width: 100%;
}

.footer-img-container {
    padding-top: 2px;
    padding-bottom: 4px;
    display: table-cell;
    vertical-align: middle;
    width: 45px;
    text-align: center;
}

.footer-img {
    padding-top: 2px;
    width: 40px;
    height: 40px;
    display: block;
    margin: 0 auto;
}

.footer-text-container {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
}

.footer-text {
    margin: 0;
    font-size: 12px;
    font-weight: bold;
}

.signature {
    max-height: 30px;
    max-width: 80px;
    height: auto;
    width: auto;
}
