<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_TaetigkeitsbezogeneGefaehrdungsbeurteilung())->getData($_GET['projectNo'], $_GET['documentA'], $_GET['documentB'],
        $_GET['documentC'], $_GET['documentD'], $_GET['workingOrderId'] ?? -1);
}

/**
 * @param array<string, mixed> $data
 */
function renderCheckbox(string $documentKey, array $data, string $key, string $value): void
{
    echo '<span class="option">';
    echo ($data[$documentKey][$key] ?? '') === $value ? '&#9745;' : '&#9744;';
    echo ' ' . $value . '</span>';
}

/**
 * @param array<string, mixed> $data
 */
function getCheckbox(array $data, string $document, string $title): string
{
    if (!isset($data[$document][$title])) {
        return "<td>&#9744;</td><td>&#9744;</td>";
    }
    $reportedValue = $data[$document][$title];

    if (PrintoutHelper::isCheckboxTickedByReportedValue($reportedValue)) {
        return "<td>&#9745;</td><td>&#9744;</td>";
    } else {
        return "<td>&#9744;</td><td>&#9745;</td>";
    }
}

/**
 * @param array<string, mixed> $data
 */
function getMultiChecks(array $data, string $title, string $reportedValue): string
{
    if (!isset($data['document_a'][$title])) {
        return "&#9744;";
    }
    return in_array($reportedValue, $data['document_a'][$title]) ? "&#9745;" : "&#9744;";
}

function getFormattedValue(string $key): string
{
    global $data;
    $value = $data['document_a'][$key] ?? '';
    if (!empty($value) && $value != 0) {
        return PrintoutHelper::formatTimeNumberWithThousands($value);
    }
    return '';
}

/**
 * @param array<string, mixed> $data
 */
function renderCheckboxField(array $data, string $fieldKey, string $labelText): void
{
    $fieldValue = trim($data['document_b'][$fieldKey] ?? '');
    $checkbox = $fieldValue !== '' ? '&#9745;' : '&#9744;';
    echo $checkbox . ' ' . htmlspecialchars($labelText) . ': ';
    echo '<span class="underlined">' . htmlspecialchars($data['document_b'][$fieldKey] ?? '') . '</span>';
}

?>

<!DOCTYPE html>
<html lang="de">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title></title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<table class="header-table">
    <tr>
        <td rowspan="2" style="border: none!important;">
            <img src="<?= $data['logo'] ?>" alt="logo" width="80" height="80">
        </td>
        <td class="header-highlight">
            Nietiedt <br> Gerüstbau GmbH
        </td>
        <td class="header-highlight orange-text">
            Tätigkeitsbezogene <br> Gefährdungsbeurteilung
        </td>
        <td class="font-bold font-14" style="vertical-align: middle;">
            <div class="header-td-text">FB - GB</div>
            <div class="header-td-text-last">Rev. 1.6 <br> Stand 12/21</div>
        </td>
    </tr>
</table>
<div class="left-div">
    <table class="left-div__first-table">
        <tr>
            <td style="padding-left: 18px" class="font-bold font-14">Block (A) Auftragsbeschreibung/Arbeitsauftrag</td>
            <td style="padding-left: 9px; padding-right: 9px"
                class="font-bold bg-orange"><?= $data['documentNo'] ?></td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2" class="font-bold">Zuständigkeitsbereich / Niederlassung:</td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2"></td>
        </tr>
    </table>
    <table class="left-div__second-table">
        <tr>
            <td style="padding-left: 18px" colspan="3"><span class="font-bold">Angaben zum Bauvorhaben</span>
                (Auftragsnummer, Name des Kunden, Anschrift usw.)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">Bauteil:</td>
            <td><?= $data['workingOrderTitle'] ?></td>
            <td></td>
        </tr>
        <tr>
            <td style="padding-left: 18px">Auftragsnummer:</td>
            <td><?= $data['project']['projectNo'] ?? '' ?></td>
            <td></td>
        </tr>
        <tr>
            <td style="padding-left: 18px">Kunde:</td>
            <td><?= $data['project']['customerName'] ?? '' ?></td>
            <td></td>
        </tr>
        <tr>
            <td style="padding-left: 18px">Anschrift:</td>
            <td><?= $data['projectAddress'] ?></td>
            <td></td>
        </tr>
        <tr>
            <td style="padding-left: 18px"></td>
            <td></td>
            <td></td>
        </tr>
    </table>
    <table class="left-div__third-table">
        <tr>
            <td style="padding-left: 18px" colspan="7"><span class="font-bold">A.2 Ansprechpartner</span>
                (Name, Telefonnummer)
            </td>
        </tr>
        <?php
        if (isset($data['partners']) && is_array($data['partners']) && count($data['partners']) > 0) {
            foreach ($data['partners'] as $partner) {
                if (isset($partner['jobTitle']) && $partner['jobTitle'] == 'AP') {
                    ?>
                    <tr>
                        <td style="padding-left: 18px"></td>
                        <td></td>
                        <td></td>
                        <td style="padding-left: 18px">
                            <?php
                            echo $partner['firstName'] ?? '';
                            echo ' ';
                            echo $partner['lastName'] ?? '';
                            ?>
                        </td>
                        <td>
                        </td>
                        <td>
                            <?= !empty($partner['phone']) ? $partner['phone'] : (!empty($partner['cell']) ? $partner['cell'] : ''); ?>
                        </td>
                        <td></td>
                    </tr>
                    <?php
                    break;
                }
            }
        } else { ?>
            <tr>
                <td colspan="7">&nbsp;</td>
            </tr>
        <?php } ?>
        <tr>
            <td style="padding-left: 18px">
                <div>Kunde</div>
                <div>&nbsp;</div>
            </td>
            <td></td>
            <td></td>
            <td style="padding-left: 18px">
                <div>(Name)</div>
                <div>&nbsp;</div>
            </td>
            <td>
                <div></div>
                <div>&nbsp;</div>
            </td>
            <td>
                <div>(Telefonnummer)</div>
                <div>&nbsp;</div>
            </td>
            <td></td>
        </tr>
        <?php
        if (isset($data['partners']) && is_array($data['partners']) && count($data['partners']) > 0) {
            foreach ($data['partners'] as $partner) {
                if (isset($partner['jobTitle']) && $partner['jobTitle'] == 'SIGEKO') {
                    ?>
                    <tr>
                        <td style="padding-left: 18px"></td>
                        <td style="padding-left: 18px">
                            <?php
                            echo $partner['firstName'] ?? '';
                            echo ' ';
                            echo $partner['lastName'] ?? '';
                            ?>
                        </td>
                        <td>
                            <?= !empty($partner['phone']) ? $partner['phone'] : (!empty($partner['cell']) ? $partner['cell'] : ''); ?>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <?php break;
                }
            }
        } ?>
        <tr>
            <td style="padding-left: 18px; border: none">
                <div>SIGEKO</div>
                <div>&nbsp;</div>
            </td>
            <td style="border: none"></td>
            <td style="border: none">
                <div>(Name, Tel.)</div>
                <div>&nbsp;</div>
            </td>
            <td style="border: none"></td>
            <td style="border: none">
                <div>SIGE-Plan:</div>
                <div>&nbsp;</div>
            </td>
            <td style="border: none">
                <div>
                    <?= !empty($data['document_a']['SIGE-Plan'])
                        ? (($data['document_a']['SIGE-Plan'] === 'vorhanden')
                            ? '&#9745; vorhanden'
                            : '&#9744; vorhanden')
                        : ''; ?>
                </div>
                <div>&nbsp;</div>
            </td>
            <td style="border: none">
                <div>
                    <?= !empty($data['document_a']['SIGE-Plan'])
                        ? (($data['document_a']['SIGE-Plan'] === 'nicht vorhanden')
                            ? '&#9745; nicht vorhanden'
                            : '&#9744; nicht vorhanden')
                        : ''; ?>
                </div>
                <div>&nbsp;</div>
            </td>
        </tr>
        <tr>
            <td colspan="7" style="padding-left: 18px">
                <div>
                    <?= !empty($data['document_a']['Datum'])
                        ? date('d.m.Y', strtotime($data['document_a']['Datum']))
                        : ''; ?> &nbsp;
                    <?= !empty($data['document_a']['Name'])
                        ? $data['document_a']['Name']
                        : ''; ?> &nbsp;
                    <?php if (isset($data['document_a']['Unterschrift'])) { ?>
                        <img src="<?= $data['document_a']['Unterschrift'] ?>" alt="signature" class="signature">
                    <?php } ?>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="7" style="padding-left: 18px; border: none">
                <div>Datum, Name und Unterschrift des Aufsichtsführenden / der befähigten Person</div>
            </td>
        </tr>
        <tr>
            <td colspan="7" style="padding-left: 18px">
                <div>
                    <?= !empty($data['team']['human_resources_mapped'][0]['validFrom'])
                        ? date('d.m.Y', strtotime($data['team']['human_resources_mapped'][0]['validFrom']))
                        : ''; ?> &nbsp;
                    <?= !empty($data['team']['human_resources_mapped'][0]['firstName'] && $data['team']['human_resources_mapped'][0]['lastName'])
                        ? $data['team']['human_resources_mapped'][0]['firstName'] . ' ' . $data['team']['human_resources_mapped'][0]['lastName']
                        : ''; ?> &nbsp;

                </div>
            </td>
        </tr>
        <tr>
            <td colspan="7" style="padding-left: 18px">Datum, Name, Unterschrift <u>aller</u> ausführenden
                Mitarbeiter/-innen
            </td>
        </tr>
    </table>
    <div class="container">
        <!-- Gerüst Section -->
        <div class="section" id="geruest">
            <h2 style="margin-top: 10px">Gerüst</h2>
            <div class="fields-container">
                <div class="field-four">
                    <div class="label">Lastklasse</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Lastklasse', '1 (0,75 kN/m²)') ?> 1 (0,75 kN/m²)</span>
                        <span class="option"><?= getMultiChecks($data, 'Lastklasse', '2 (1,50 kN/m²)') ?> 2 (1,50 kN/m²)</span>
                        <span class="option"><?= getMultiChecks($data, 'Lastklasse', '3 (2,00 kN/m²)') ?> 3 (2,00 kN/m²)</span>
                        <span class="option"><?= getMultiChecks($data, 'Lastklasse', '4 (3,00 kN/m²)') ?> 4 (3,00 kN/m²)</span>
                        <span class="option"><?= getMultiChecks($data, 'Lastklasse', '5 (4,50 kN/m²)') ?> 5 (4,50 kN/m²)</span>
                        <span class="option"><?= getMultiChecks($data, 'Lastklasse', '6 (6,00 kN/m²)') ?> 6 (6,00 kN/m²)</span>
                    </div>
                </div>
                <div class="field-four">
                    <div class="label">Breitenklasse</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Breitenklasse', 'W 06') ?> W 06</span>
                        <span class="option"><?= getMultiChecks($data, 'Breitenklasse', 'W 09') ?> W 09</span>
                        <span class="option"><?= getMultiChecks($data, 'Breitenklasse', 'W 12') ?> W 12</span>
                        <span class="option">Sonstiges:
                            <u>
                                <?= $data['document_a']['Breitenklasse Sonstiges'] ?? '' ?>
                            </u>
                        </span>
                    </div>
                </div>
                <div class="field-four">
                    <div class="label">Konsolen innen</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Konsolen innen', '0,25m') ?> 0,25m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsolen innen', '0,33m') ?> 0,33m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsolen innen', '0,50m') ?> 0,50m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsolen innen', '0,67m') ?> 0,67m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsolen innen', '0,75m') ?> 0,75m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsolen innen', '1,00m') ?> 1,00m</span>
                    </div>
                </div>
                <div class="field-four">
                    <div class="label">Konsole außen</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Konsole außen', '0,25m') ?> 0,25m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsole außen', '0,33m') ?> 0,33m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsole außen', '0,50m') ?> 0,50m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsole außen', '0,67m') ?> 0,67m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsole außen', '0,75m') ?> 0,75m</span>
                        <span class="option"><?= getMultiChecks($data, 'Konsole außen', '1,00m') ?> 1,00m</span>
                    </div>
                </div>
                <div class="field-four">
                    <div class="label">Ankergrund</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Ankergrund', 'WDVS') ?> WDVS</span>
                        <span class="option"><?= getMultiChecks($data, 'Ankergrund', 'Ziegel') ?> Ziegel</span>
                        <span class="option"><?= getMultiChecks($data, 'Ankergrund', 'Beton') ?> Beton</span>
                        <span class="option"><?= getMultiChecks($data, 'Ankergrund', 'Holz') ?> Holz</span>
                        <span class="option">Sonstiges:
                            <u>
                                <?= $data['document_a']['Ankergrund Sonstiges'] ?? '' ?>
                            </u>
                        </span>
                    </div>
                </div>
                <div class="field">
                    <div class="label">Bekleidung</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Dachfangnetz') ?> Dachfangnetz</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Sandstrahlnetz') ?> Sandstrahlnetz</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Sandstrahlnetz B1') ?> Sandstrahlnetz B1</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Gitterverstärkte Plane') ?> Gitterverstärkte Plane</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Gitterverstärkte Plane als B1') ?> Gitterverstärkte Plane als B1</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Kederplane') ?> Kederplane</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Kederplane B1') ?> Kederplane B1</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Staubschutznetz / Rupfennetz') ?> Staubschutznetz / Rupfennetz</span>
                        <span class="option"><?= getMultiChecks($data, 'Bekleidung', 'Einhausungssystem / Cladding') ?> Einhausungssystem / Cladding</span>
                        <span class="option">Sonstiges:
                            <u>
                                <?= $data['document_a']['Bekleidung Sonstiges'] ?? '' ?>
                            </u>
                        </span>
                    </div>
                </div>
                <div class="field">
                    <div class="label">Anbauteile</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Anbauteile', 'Innengeländer') ?> Innengeländer</span>
                        <span class="option"><?= getMultiChecks($data, 'Anbauteile', 'Gitterträger') ?> Gitterträger</span>
                        <span class="option">Sonstiges:
                            <u>
                                <?= $data['document_a']['Anbauteile Sonstiges'] ?? '' ?>
                            </u>
                        </span>
                    </div>
                    <div class="label" style="margin-top: 8px;">Zugang für Gerüstersteller</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Zugang für Gerüstersteller', 'innenliegender Leitergang') ?> innenliegender Leitergang</span>
                        <span class="option"><?= getMultiChecks($data, 'Zugang für Gerüstersteller', 'Podesttreppe') ?> Podesttreppe</span>
                        <span class="option"><?= getMultiChecks($data, 'Zugang für Gerüstersteller', 'Leiter (bis 5m Aufstiegshöhe)') ?> Leiter (bis 5m Aufstiegshöhe)</span>
                    </div>
                    <div class="label" style="padding-top: 8px">Aufbau nach A+V oder Statik</div>
                    <div class="combobox">
                        <?php renderCheckbox('document_a', $data, 'Aufbau nach A+V oder Statik', 'Regelausführung / A+V des Herstellers'); ?>
                        <?php renderCheckbox('document_a', $data, 'Aufbau nach A+V oder Statik', 'Statik'); ?>
                    </div>
                </div>
                <div class="field">
                    <div class="label">Art der Verankerung</div>
                    <div class="combobox-multi">
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Ringschrauben 120mm') ?> Ringschrauben 120mm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Ringschrauben 160mm') ?> Ringschrauben 160mm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Ringschrauben 230mm') ?> Ringschrauben 230mm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Ringschrauben 300mm') ?> Ringschrauben 300mm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Ringschrauben 350mm') ?> Ringschrauben 350mm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Ringschrauben 400mm') ?> Ringschrauben 400mm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'V-Anker') ?> V-Anker</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Innendiagonale') ?> Innendiagonale</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Abstützung mit Rohren') ?> Abstützung mit Rohren</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Abstützturm') ?> Abstützturm</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Verankerung', 'Abstützturm ballastiert') ?> Abstützturm ballastiert</span>
                    </div>
                </div>
                <div class="field-one">
                    <div class="label">
                        Plan <small>(Verlinkung zu Plänen aus techn. Projektsupport)</small>
                        <?php if (isset($data['document_a']['Verlinkung zu Plänen aus techn. Projektsupport'])) { ?>
                            <img src="<?= $data['document_a']['Verlinkung zu Plänen aus techn. Projektsupport'][0]['filePath'] ?>"
                                 class="plan-image" alt="plan">
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
        <!-- Gebäude Section -->
        <div class="section" id="gebaeude">
            <h2>Gebäude</h2>
            <div class="fields-container">
                <div class="field">
                    <div class="label">Verwendungsart</div>
                    <div class="checkbox-group">
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Arbeitsgerüst') ?> Arbeitsgerüst</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Traggerüst') ?> Traggerüst</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Fanggerüst (≤ 20° Dachneigung)') ?> Schutzgerüst/ Fanggerüst (≤ 20° Dachneigung)</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Dachfanggerüst (Dacharbeiten, > 20° Dachneigung)') ?> Schutzgerüst/ Dachfanggerüst (Dacharbeiten, > 20° Dachneigung)</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Schutzdach') ?> Schutzgerüst/ Schutzdach</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Trümmerschutz mit Dämmung') ?> Schutzgerüst/ Trümmerschutz mit Dämmung</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Passantenschutz') ?> Schutzgerüst/ Passantenschutz</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Tunnelrahmen') ?> Schutzgerüst/ Tunnelrahmen</span>
                        <span class="option"><?= getMultiChecks($data, 'Verwendungsart', 'Schutzgerüst/ Fluchttreppe') ?> Schutzgerüst/ Fluchttreppe</span>
                    </div>
                    <div class="label" style="padding-top: 8px;">Art der Ausbildung</div>
                    <div class="checkbox-group">
                        <span class="option"><?= getMultiChecks($data, 'Art der Ausbildung', 'Fassadengerüst (Maler, Putzer)') ?> Fassadengerüst (Maler, Putzer)</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Ausbildung', 'Raumgerüst/ Flächengerüst') ?> Raumgerüst/ Flächengerüst</span>
                        <span class="option"><?= getMultiChecks($data, 'Art der Ausbildung', 'Fahrgerüst/ Rollgerüst') ?> Fahrgerüst/ Rollgerüst</span>
                    </div>
                </div>
                <div class="field">
                    <div class="label">Bauart</div>
                    <div class="checkbox-group">
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Tragsystem/ Hängegerüst') ?> Tragsystem/ Hängegerüst</span>
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Tragsystem/ Standgerüst') ?> Tragsystem/ Standgerüst</span>
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Tragsystem/ Konsolengerüst') ?> Tragsystem/ Konsolengerüst</span>
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Tragsystem') ?> Tragsystem</span>
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Ausführungsart/ Stahlrohrkupplung') ?> Ausführungsart/ Stahlrohrkupplung</span>
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Ausführungsart/ Rahmengerüst (70er/100er)') ?> Ausführungsart/ Rahmengerüst (70er/100er)</span>
                        <span class="option"><?= getMultiChecks($data, 'Bauart', 'Ausführungsart/ Modulgerüst') ?> Ausführungsart/ Modulgerüst</span>
                    </div>
                    <div class="label" style="padding-top: 8px;">kranbare Ausführung</div>
                    <div class="checkbox-group">
                        <span class="option"><?= getMultiChecks($data, 'kranbare Ausführung', 'Bewehrungsgerüst/ Flechtergerüste') ?> Bewehrungsgerüst/
                            Flechtergerüste</span>
                        <span class="option"><?= getMultiChecks($data, 'kranbare Ausführung', 'Treppenturm') ?> Treppenturm</span>
                    </div>
                </div>
                <div class="field">
                    <div class="label">Aufstandsfläche</div>
                    <div class="combobox">
                        <?php renderCheckbox('document_a', $data, 'Aufstandsfläche', 'tragfähige, feste Fläche') ?>
                        <?php renderCheckbox('document_a', $data, 'Aufstandsfläche', 'lastverteilende Unterlage (Maßnahmen dazu, unten aufführen)') ?>
                        <span><?php
                            if (isset($data['document_a']['Aufstandsfläche lastverteilende Maßnahme'])) {
                                echo '(' . $data['document_a']['Aufstandsfläche lastverteilende Maßnahme'] . ')';
                            } else {
                                echo '';
                            }
                            ?></span>
                    </div>
                    <div class="label" style="padding-top: 8px;">Horizontale Abstände vom Gerüst zum Gebäude (in m):
                        <u><?= getFormattedValue('Horizontale Abstände vom Gerüst zum Gebäude'); ?></u>
                    </div>
                    <div class="label" style="padding-top: 8px;">Vertikale Abstände zur ersten Belagsebene (in m):
                        <u><?= getFormattedValue('Vertikale Abstände zur ersten Belagsebene'); ?></u>
                    </div>
                    <div class="label" style="padding-top: 8px;">max. Höhe Gerüst (in m):
                        <u><?= getFormattedValue('max. Höhe Gerüst (in m)'); ?></u>
                    </div>
                    <div class="label" style="padding-top: 8px;">max. Höhe Objekt (in m):
                        <u><?= getFormattedValue('max. Höhe Objekt (in m)'); ?></u>
                    </div>
                </div>
            </div>
        </div>
        <!-- Transport Section -->
        <div class="section" id="transport">
            <h2>Transport</h2>
            <div class="fields-container">
                <div class="field">
                    <div class="label">Hilfsmittel auf Baustelle</div>
                    <div class="combobox">
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'Teleskoplader (klein o. groß)'); ?>
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'GEDA 200'); ?>
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'Aufzug 500 kg'); ?>
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'Aufzug 1500kg'); ?>
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'Geda Seilwinde'); ?>
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'Böcker-Aufzug'); ?>
                        <?php renderCheckbox('document_a', $data, 'Hilfsmittel auf Baustelle', 'Kran (bauseits)'); ?>
                    </div>
                </div>
                <div class="field-two">
                    <div class="label">Horizontaltransport zum Gerüst (in m):
                        <u><?= getFormattedValue('Horizontaltransport zum Gerüst (in m)'); ?></u>
                    </div>
                    <div class="label">Vertikaltransport am Gerüst (in m):
                        <u><?= getFormattedValue('Vertikaltransport am Gerüst (in m)'); ?></u>
                    </div>
                    <div class="label">Horizontaltransport im Gerüst (in m):
                        <u><?= getFormattedValue('Horizontaltransport im Gerüst (in m)'); ?></u>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="right-div">
    <table class="right-div__first-table">
        <tr>
            <td style="padding-left: 18px" class="font-bold font-14">Block (B) Vorrausschauende Gefährdungsermittlung
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" class="font-bold">B.1 Gefährdungsfaktoren <span class="orange-text"
                                                                                           style="float: right">Bitte Hinweise im Register "Seite 2" beachten!</span>
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['a.) Beleuchtung'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                a) Beleuchtung
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['b.) Gefährdung durch elektrischen Strom (gefährliche Körperströme, elektrostatische Vorgänge)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                b) Gefährdung durch elektrischen Strom
                <br>
                (gefährliche Körperströme, elektrostatische Vorgänge)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['c.)  Klima (Hitze, Kälte, Zugluft, Luftfeuchtigkeit)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c) Klima (Hitze, Kälte, Zugluft, Luftfeuchtigkeit)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['d.) Physische Faktoren (Schwere dynamische Arbeit, einseitige dynamische Arbeit, statische Arbeit)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                d) Physische Faktoren (Schwere dynamische Arbeit, einseitige dynamische Arbeit, statische Arbeiten)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['e.) Mechanische Schwingungen (Vibrationen, Hand-, Arm , Körperschwingungen)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                e) Mechanische Schwingungen (Vibrationen, Hand-, Arm-, Körperschwingungen)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['f.) Biologische Arbeitsstoffe (Infektionsgefahr durch Viren, Allergene, usw.)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                f) Biologische Arbeitsstoffe (Infektionsgefahr durch Viren, Allergene, usw.)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['g.) Licht und Farbe'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                g) Licht und Farbe
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['h.) Mechanische Faktoren (z.B. Quetschen, Schneiden, Stechen, Stoßen)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                h) Mechanische Faktoren (z.B. Quetschen, Schneiden, Stechen, Stoßen)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['i.) Strahlungen (elektromagn. Felder, infrarote-, ultraviolette-, ionisierende-, Laserstrahl.'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                i) Strahlungen (elektromagn. Felder, infrarote-, ultraviolette-, ionisierende-, Laserstrahlung)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['j.) Arbeiten in Überdruck und Unterdruck'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                j) Arbeiten in Überdruck und Unterdruck
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['k.) Faktoren der Brand- und Explosionsgefahr (Brandgefährdung durch Feststoffe, Flüssigkeiten, Gase, Ex-Atmosphäre)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                k) Faktoren der Brand- und Explosionsgefahr (Brandgefährdung durch Feststoffe, Flüssigkeiten, Gase,
                Ex-Atmosphäre)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['l.) Gefahrstoffe (Flüssigkeiten, Gase, Nebel, Dämpfe, Stäube)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                l) Gefahrstoffe (Flüssigkeiten, Gase, Nebel, Dämpfe, Stäube)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['m.) Schall (Lärm, Infra-/Ultraschall)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                m) Schall (Lärm, Infra-/Ultraschall)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['n.) Thermische Gefährdungsfaktoren (heiße oder kalte Medien/Oberflächen)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                n) Thermische Gefährdungsfaktoren (heiße oder kalte Medien/Oberflächen)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['o.) Multifaktorielle Gefährdungen (Zusammenwirken mehrerer Faktoren)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                o) Multifaktorielle Gefährdungen (Zusammenwirken mehrerer Faktoren)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['p.) Menschen'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                p) Menschen
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['q.) Tiere'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                q) Tiere
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['r.) Psychische Faktoren'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                r) Psychische Faktoren
            </td>
        </tr>
    </table>
    <table class="right-div__second-table">
        <tr>
            <td style="padding-left: 18px" class="font-bold">B.2 Art der Tätigkeit</td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['a.) Arbeiten mit Gefahrstoffen'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                a) Arbeiten mit Gefahrstoffen
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['b.) Arbeiten unter Lärm (ab 85 db(A))'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                b) Arbeiten unter Lärm (ab 85 db(A))
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['c.) Arbeiten mit kritischen Betriebsmitteln (Rührwerke, Kabeltrommel, Handlampe, etc.)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c) Arbeiten mit kritischen Betriebsmitteln (Rührwerke, Kabeltrommel, Handlampe etc.)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['d.) Arbeiten in Behältern, engen Räumen'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                d) Arbeiten in Behältern, engen Räumen
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['e.) Arbeiten in Zwangshaltung'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                e) Arbeiten in Zwangshaltung
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['f.) Arbeiten in Höhen mit Absturzgefahr (Gerüste, Leitern, Hebebühnen)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                f) Arbeiten in Höhen mit Absturzgefahr (Gerüste, Leitern, Hebebühnen)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['g.) Arbeiten mit Fahrzeugen'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                g) Arbeiten mit Fahrzeugen
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['h.) Hebearbeiten (technisch/manuell)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                h) Hebearbeiten (technisch/manuell)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['i.) Heißarbeiten (bohren, Einsatz nicht Ex-geschützter Geräte in Ex- Bereichen)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                i) Heißarbeiten (bohren, Einsatz nicht Ex-geschützter Geräte in Ex-Bereichen)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['j.) Dacharbeiten (nur nach Freigabe der Geschäftsleitung erlaubt)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                j) Dacharbeiten (nur nach Freigabe der Geschäftsleitung erlaubt)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['k.) Arbeiten mit Atemschutzgeräten'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                k) Arbeiten mit Atemschutzgeräten <b>➔</b> Ja?, dann <b>➔</b> j.1) G26.3 bescheinigt!
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['l.) Hochdruckwasserstrahlarbeiten über 250 bar (in der Nähe)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                l) Hochdruckwasserstrahlarbeiten über 250 bar (in der Nähe)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['m.) Gerüstdemontage nach Sandstrahlarbeiten'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                m) Gerüstdemontage nach Sandstrahlarbeiten
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php renderCheckboxField($data, 'n.) sonstiges', 'n) sonstiges'); ?>
            </td>
        </tr>
    </table>
    <table class="right-div__third-table">
        <tr>
            <td style="padding-left: 18px" colspan="2" class="font-bold">B.3 Schutzmaßnahmen</td>
        </tr>
        <tr>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['a.) techn. Absturzsicherung (I-Geländer/ MSG)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                a) techn. Absturzsicherung (I-Geländer/ MSG)
            </td>
            <td style="padding-left: 18px">
                <?php
                $reportedValue = $data['document_b']['b.) PSA gegen Absturz'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                b) PSA gegen Absturz
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['c.) Persönliche Schutzausrüstung (Standard: S3-Schuhe, Industrieschutzhelm nach EN 397, Handschuhe)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c) Persönliche Schutzausrüstung (Standard: S3-Schuhe, Industrieschutzhelm nach EN 397, Handschuhe)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c1) Gehörschutz (Gehörstöpsel, -kapseln)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c1) Gehörschutz (Gehörstöpsel, -kapseln)
            </td>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c5) Rettungsweste'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c5) Rettungsweste
            </td>
        </tr>
        <tr>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c2) Hautschutz-, Hautreinigungs-, Pflegemittel'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c2) Hautschutz-, Hautreinigungs-, Pflegemittel
            </td>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c6) Augenschutz  (Schutz-Korb-oder Bügelbrille)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c6) Augenschutz (Schutz-Korb- oder Bügelbrille)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c3) feuerhemmende/feuerfeste/antistatische Kleidung'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c3) feuerhemmende/feuerfeste/antistatische Kleidung
            </td>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c7) Kinnriemen (2)- oder (4)-Punkt'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c7) Kinnriemen (2)- oder (4)-Punkt
            </td>
        </tr>
        <tr>
            <td style="padding-left: 36px">
                <?php
                $reportedValue = $data['document_b']['c4) Atemschutz (Staubmaske, etc.)'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                c4) Atemschutz (Staubmaske, etc.)
            </td>
            <td style="padding-left: 36px">
                <?php renderCheckboxField($data, 'c8) sonstiges', 'c8) sonstiges'); ?>
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['d.) standsichere Lage aller Bauteile und Arbeitsmittel'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                d) standsichere Lage aller Bauteile und Arbeitsmittel
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['e.) Belüftung (manuell, technische Belüftung / Absaugung'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                e) Belüftung (manuell, technische Belüftung / Absaugung)
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['f.) Inaugenscheinnahme kritischer Betriebsmittel u. E- Check'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                f) Inaugenscheinnahme kritischer Betriebsmittel u. E-Check
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['g.) Arbeitsplatz/Baustelle absperren/sichern'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                g) Arbeitsplatz/Baustelle absperren/sichern
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['h.) tätigkeitsbez. Unterweisung ausführender MA durch den Aufsichtsführenden erfolgt'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                h) tätigkeitsbez. Unterweisung ausführender MA durch den Aufsichtsführenden erfolgt
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['i.) Einweisung durch die befähigte Person (extern) erforderlich'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                i) Einweisung durch die befähigte Person (extern) erforderlich
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['j.) Rettungskette bekannt und geklärt'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                j) Rettungskette bekannt und geklärt
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['k.) Ex-geschütztes Werkzeug/Telefon erforderlich'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                k) Ex-geschütztes Werkzeug/Telefon erforderlich
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['l.) Prüfung der Ladung (Ladungssicherung) v.d. Transport erfolgt'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                l) Prüfung der Ladung (Ladungssicherung) v. d. Transport erfolgt
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php
                $reportedValue = $data['document_b']['m.) Höhensicherungsgerät / Höhenrettungsgerät erforderlich'] ?? null;
                if ($reportedValue !== null) {
                    PrintoutHelper::echoCheckboxByReportedValue($reportedValue);
                } else {
                    echo '&#9744;';
                }
                ?>
                m) Höhensicherungsgerät / Höhenrettungsgerät erforderlich
            </td>
        </tr>
        <tr>
            <td style="padding-left: 18px" colspan="2">
                <?php renderCheckboxField($data, 'n.) sonstiges2', 'n) sonstiges'); ?>
            </td>
        </tr>
    </table>
    <table class="right-div__bottom-table">
        <tr>
            <td style="text-align: center">Notruf 112</td>
            <td>oder</td>
            <td>
                <div></div>
                <div class="bg-white"></div>
                <div></div>
            </td>
            <td style="font-size: 11px">
                Fachkräfte für Arbeitssicherheit<br>
                <span style="font-size: 13px">0 44 21 / 30 04-200</span>
            </td>
        </tr>
    </table>
</div>
<div class="second-page">
    <div class="left-div__bottom-div">
        <div class="measure-hierarchy bg-orange" role="table">
            <div class="measure-hierarchy-row" role="row">
                <div class="measure-hierarchy-cell measure-hierarchy-left" role="cell">
                    <p class="measure-hierarchy-title">
                        <span class="right-arrow">&#8594;</span> gemäß der Maßnahmenhierarchie vorwiegend ergriffene
                        Maßnahmen
                    </p>
                    <ul class="measure-hierarchy-list">
                        <li><?= getMultiChecks($data, 'gemäß der Maßnahmenhierarchie vorwiegend ergriffene Maßnahmen', '1. Gefahrenquelle vermeiden/beseitigen') ?>
                            1. Gefahrenquelle vermeiden/beseitigen
                        </li>
                        <li><?= getMultiChecks($data, 'gemäß der Maßnahmenhierarchie vorwiegend ergriffene Maßnahmen', '2. Sicherheitstechnische Maßnahmen') ?>
                            2. Sicherheitstechnische Maßnahmen
                        </li>
                        <li><?= getMultiChecks($data, 'gemäß der Maßnahmenhierarchie vorwiegend ergriffene Maßnahmen', '3. Organisatorische Maßnahmen') ?>
                            3. Organisatorische Maßnahmen
                        </li>
                        <li><?= getMultiChecks($data, 'gemäß der Maßnahmenhierarchie vorwiegend ergriffene Maßnahmen', '4. Nutzung von persönlicher Schutzausrüstung') ?>
                            4. Nutzung von persönlicher Schutzausrüstung
                        </li>
                        <li><?= getMultiChecks($data, 'gemäß der Maßnahmenhierarchie vorwiegend ergriffene Maßnahmen', '5. Verhaltensbezogene Maßnahmen') ?>
                            5. Verhaltensbezogene Maßnahmen
                        </li>
                    </ul>
                </div>
                <div class="measure-hierarchy-cell measure-hierarchy-right" role="cell">
                    <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/warning_sign.png") ?>"
                         alt="">
                </div>
            </div>
        </div>
    </div>
    <div class="bottom-left-div">
        <table class="bottom-left-div__table">
            <tr>
                <td style="padding-left: 18px" colspan="5" class="font-bold font-14">
                    Block (C) Untersuchung/Feststellung des Arbeitsumfeldes
                </td>
            </tr>
            <tr>
                <th style="padding-left: 18px" class="font-bold left-align" colspan="2">C.1 Arbeitsumgebung</th>
                <th class="font-bold-small">ja</th>
                <th class="font-bold-small">nein</th>
                <th class="font-bold-small left-align">Maßnahme(n)</th>
                <th class="font-bold-small">erledigt</th>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">a) Sicherer Zugang/Fluchtweg vorhanden?</td>
                <?= getCheckbox($data, 'document_c', 'a.) Sicherer Zugang/Fluchtweg vorhanden?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">b) Umfeld in Ordnung (Beleuchtung, Belüftung, Sauberkeit)?
                </td>
                <?= getCheckbox($data, 'document_c', 'b.) Umfeld in Ordnung(Beleuchtung, Belüftung, Sauberkeit)?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)2'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt2'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">c) besondere Rutsch- und Stolpergefahren vorhanden?</td>
                <?= getCheckbox($data, 'document_c', 'c.) besondere Rutsch und Stolpergefahr vorhanden?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)3'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt3'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">
                    d) Sammelplätze, Ansprechpartner und Notfalleinrichtungen vorhanden und bekannt? Ersthelfer vor Ort?
                    Ja? <strong>➔ Name: &#8595;</strong>
                </td>
                <?= getCheckbox($data, 'document_c', 'd.) Sammelplätze, Ansprechpartner und Notfalleinrichtungen vorhanden und bekannt? Ersthelfer vor Ort?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)4'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt4'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td style="padding-left: 18px">
                    e) sonstiges:
                    <span class="underlined">
                        <?= $data['document_c']['e.) sonstiges2'] ?? '' ?>
                    </span>
                </td>
                <td class="bg-orange">
                    <span><?= $data['document_c']['Ersthelfer vor Ort'] ?? '' ?></span>
                </td>
                <?= getCheckbox($data, 'document_c', 'e.) sonstiges') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)5'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt5'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <th style="padding-left: 18px" class="font-bold left-align" colspan="2">C.2 Planung und Koordinierung
                </th>
                <th class="font-bold-small left-align">ja</th>
                <th class="font-bold-small left-align">nein</th>
                <th class="font-bold-small left-align">Maßnahme(n)</th>
                <th class="font-bold-small left-align">erledigt</th>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">a) Untergrund ist fest und tragfähig?</td>
                <?= getCheckbox($data, 'document_c', 'a.) Untergrund ist fest und tragfähig?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)6'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt6'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">b) Verankerungsgrund ist in Ordnung?</td>
                <?= getCheckbox($data, 'document_c', 'b.) Verankerungsgrund ist in Ordnung?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)7'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt7'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            <tr>
                <td colspan="2" style="padding-left: 18px">c) Beeinflussung durch weitere Personen/Arbeitsgruppen?</td>
                <?= getCheckbox($data, 'document_c', 'C.) Beeinflussung durch weitere Personen/Arbeitsgruppen?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)8'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt8'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">d) Gefährdung im Umfeld von Krantätigkeiten?</td>
                <?= getCheckbox($data, 'document_c', 'd.) Gefährdung im Umfeld von Krantätigkeiten?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)9'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt9'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">e) Gefährdung im Umfeld durch
                    Hochdruckreinigung/Strahlarbeiten?
                </td>
                <?= getCheckbox($data, 'document_c', 'e.) Gefährdung im Umfeld durch Hochdruckreinigung/ Strahlarbeiten?') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)10'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt10'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding-left: 18px">
                    f) sonstiges (z.B. Asbest):
                    <span class="underlined">
                        <?= $data['document_c']['f.) sonstiges (z.B. Asbest)2'] ?? '' ?>
                    </span>
                </td>
                <?= getCheckbox($data, 'document_c', 'f.) sonstiges (z.B. Asbest)') ?>
                <td class="bg-orange"><?= $data['document_c']['Maßnahme(n)11'] ?? '' ?></td>
                <td style="text-align: center">
                    <?php
                    $val = $data['document_c']['erledigt11'] ?? null;
                    if ($val !== null) {
                        PrintoutHelper::echoCheckboxByReportedValue($val);
                    } else {
                        echo '&#9744;';
                    }
                    ?>
                </td>
            </tr>
        </table>
    </div>
    <div class="bottom-right-div">
        <table class="bottom-right-div__first-table">
            <tr>
                <td style="padding-left: 5px" colspan="2" class="font-bold font-14">
                    Block (D) Rückschauende Gefährdungsermittlung
                </td>
            </tr>
            <tr>
                <td style="padding-left: 5px" colspan="2" class="font-bold">
                    D.1 Wirksamkeitkontrolle zur Gefährdungermittlung
                </td>
            </tr>
            <tr>
                <td style="padding-left: 5px">
                    a.) Sind die unter Block<br>
                    B.3
                    <?php renderCheckbox('document_d', $data, 'Sind die unter Block?', 'Block B.3'); ?>
                </td>
                <td style="padding-left: 5px">
                    a2) Block C
                    <?php renderCheckbox('document_d', $data, 'Sind die unter Block?', 'Block C'); ?>
                </td>
            </tr>
        </table>
        <table class="bottom-right-div__second-table">
            <tr>
                <td>&nbsp;</td>
                <td class="font-bold">ja</td>
                <td class="font-bold">nein</td>
            </tr>
            <tr>
                <td style="padding-left: 5px">getroffenen Maßnahmen wirksam</td>
                <?= getCheckbox($data, 'document_d', 'getroffenen Maßnahmen wirksam?') ?>
            </tr>
            <tr class="height-80 bg-orange">
                <td colspan="3" style="padding: 5px; vertical-align: top">
                    <strong>Erforderliche Korrekturmaßnahme(n):</strong>
                    <br>
                    <?= $data['document_d']['Erforderliche Korrekturmaßnahmen'] ?? '' ?>
                </td>
            </tr>
            <tr class="bg-orange">
                <td>&nbsp;</td>
                <td><strong>ja</strong></td>
                <td><strong>nein</strong></td>
            </tr>
            <tr class="bg-orange">
                <td style="padding: 5px">
                    <strong>Korrekturmaßnahmen wirksam?</strong>
                </td>
                <?= getCheckbox($data, 'document_d', 'Korrekturmaßnahmen wirksam?') ?>
            </tr>
        </table>
    </div>
</div>
</body>
</html>
