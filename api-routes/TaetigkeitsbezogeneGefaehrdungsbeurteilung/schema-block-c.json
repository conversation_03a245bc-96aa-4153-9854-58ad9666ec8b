{"name": "Tätigkeitsbezogene Gefährdungsbeurteilung Block (C)", "description": "", "status": "active", "createdBy": "4", "createdOn": "2022-12-23T08:47:19Z", "editedOn": "2022-12-23T10:07:02Z", "positions": [{"id": 1, "title": "Block (C) Untersuchung/Feststellung des Arbeitsumfeldes", "type": "headline"}, {"id": 2, "parentId": 1, "title": "C.1 Arbeitsumgebung", "type": "headline"}, {"id": 3, "parentId": 2, "title": "a.) <PERSON><PERSON>er Zugang/Fluchtweg vorhanden?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 4, "displayInside": 3, "title": "Maßnahme(n)", "type": "string"}, {"id": 5, "displayInside": 3, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 6, "parentId": 2, "title": "b.) Umfeld in Ordnung(Beleuchtung, Belüftung, Sauberkeit)?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 7, "displayInside": 6, "title": "Maßnahme(n)", "type": "string"}, {"id": 8, "displayInside": 6, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 9, "parentId": 2, "title": "c.) besondere Rutsch und Stolpergefahr vorhanden?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 10, "displayInside": 9, "title": "Maßnahme(n)", "type": "string"}, {"id": 11, "displayInside": 9, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 12, "parentId": 2, "title": "d.) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ansprechpartner und Notfalleinrichtungen vorhanden und bekannt? Ersthelfer vor Ort?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 13, "displayInside": 12, "title": "Maßnahme(n)", "type": "string"}, {"id": 14, "displayInside": 12, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 15, "parentId": 2, "title": "e.) sonstiges", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 16, "parentId": 2, "title": "e.) sonstiges", "type": "string"}, {"id": 17, "displayInside": 15, "title": "Maßnahme(n)", "type": "string"}, {"id": 18, "displayInside": 15, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 19, "displayInside": 15, "title": "Ersthelfer vor Ort", "type": "string"}, {"id": 20, "parentId": 1, "title": "C.2 Planung und Koordinierung", "type": "headline"}, {"id": 21, "parentId": 20, "title": "a.) Untergrund ist fest und tragfähig?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 22, "displayInside": 21, "title": "Maßnahme(n)", "type": "string"}, {"id": 23, "displayInside": 21, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 24, "parentId": 20, "title": "b.) Verankerungsgrund ist in Ordnung?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 25, "displayInside": 24, "title": "Maßnahme(n)", "type": "string"}, {"id": 26, "displayInside": 24, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 27, "parentId": 20, "title": "C.) Beeinflussung durch weitere Personen/Arbeitsgruppen?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 28, "displayInside": 27, "title": "Maßnahme(n)", "type": "string"}, {"id": 29, "displayInside": 27, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 30, "parentId": 20, "title": "d.) Gefährdung im Umfeld von Krantätigkeiten?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 31, "displayInside": 30, "title": "Maßnahme(n)", "type": "string"}, {"id": 32, "displayInside": 30, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 33, "parentId": 20, "title": "e.) Gefährdung im Umfeld durch Hochdruckreinigung/ Strahlarbeiten?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 34, "displayInside": 33, "title": "Maßnahme(n)", "type": "string"}, {"id": 35, "displayInside": 33, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 36, "parentId": 20, "title": "f.) sonstiges (z.B. <PERSON>)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"]}, {"id": 37, "parentId": 20, "title": "f.) sonstiges (z.B. <PERSON>)", "type": "string"}, {"id": 38, "displayInside": 36, "title": "Maßnahme(n)", "type": "string"}, {"id": 39, "displayInside": 36, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}]}