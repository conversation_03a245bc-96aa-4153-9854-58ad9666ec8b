<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_TaetigkeitsbezogeneGefaehrdungsbeurteilung
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $projectNo, string $documentA, string $documentB, string $documentC, string $documentD, string $workingOrderId): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $projectPartial = 'customerName,projectSiteAddress,projectSiteZipCode,projectSiteCity,projectNo,partners';

        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        $data['document_a'] = PrintoutHelper::mapDocumentChildrenToValues(
            PrintoutHelper::downloadDocumentById((int)$documentA, $curl, considerForPdfMerging: false)['fullDocument']);
        $data['document_b'] = PrintoutHelper::mapDocumentChildrenToValues(
            PrintoutHelper::downloadDocumentById((int)$documentB, $curl, considerForPdfMerging: false)['fullDocument']);
        $data['document_c'] = PrintoutHelper::mapDocumentChildrenToValues(
            PrintoutHelper::downloadDocumentById((int)$documentC, $curl, considerForPdfMerging: false)['fullDocument']);
        $data['document_d'] = PrintoutHelper::mapDocumentChildrenToValues(
            PrintoutHelper::downloadDocumentById((int)$documentD, $curl, considerForPdfMerging: false)['fullDocument']);

        $data['project'] = PrintoutHelper::downloadProject($projectNo, $projectPartial, $curl);
        $data['documentNo'] = $data['project']['projectNo'] ?? '';
        $allPartners = $this->downloadPartner($curl);
        $projectPartnerIds = $data['project']['partners'] ?? '';
        $mappedPartners = [];
        if (!empty($projectPartnerIds)) {
            foreach ($allPartners as $partner) {
                if (isset($partner['partnerNo']) && in_array($partner['partnerNo'], $projectPartnerIds)) {
                    $mappedPartners[] = $partner;
                }
            }
        }
        $data['partners'] = $mappedPartners;

        if ($workingOrderId != -1) {
            $woPartial = "shortDescription,workingOrderNo,teamIdentification";
            $data['workingOrder'] = PrintoutHelper::downloadWorkingOrder((int)$projectNo, (int)$workingOrderId, $woPartial, $curl);
            $data['documentNo'] .= '(' . $workingOrderId . ')';
            $data['team'] = $this->downloadTeam($curl, $projectNo, $workingOrderId);
        }

        $data['workingOrderTitle'] = $data['workingOrder']['shortDescription'] ?? '';
        $data['projectAddress'] = $data['project']['projectSiteAddress'] . ' ' . $data['project']['projectSiteZipCode'] . ' ' . $data['project']['projectSiteCity'];
        $data['teamIdentification'] = $data['workingOrder']['teamIdentification']['displayText'] ?? '';
        return $data;
    }

    /**
     * @return array<mixed>
     */
    private function downloadPartner(PrintoutCurl $curl): array
    {
        $url = "v1/partners";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }

    /**
     * @return array<string, mixed>
     */
    private function downloadTeam(PrintoutCurl $curl, string $projectNo, string $workingOrderId): array
    {
        $teamUrl = "v1/team/select/wo/$projectNo/$workingOrderId";
        $teamResponse = $curl->_simple_call('get', $teamUrl, [], PrintoutHelper::getHeadersForApiCalls());
        $teamData = json_decode($teamResponse, true) ?? [];
        $employeesUrl = "v3/employees";
        $employeesResponse = $curl->_simple_call('get', $employeesUrl, [], PrintoutHelper::getHeadersForApiCalls());
        $employeesData = json_decode($employeesResponse, true) ?? [];

        $employeesIndex = [];
        foreach ($employeesData as $employee) {
            if (isset($employee['employeeNo'])) {
                $employeesIndex[$employee['employeeNo']] = $employee;
            }
        }
        $mappedHumanResources = [];
        if (isset($teamData['human_resources']) && is_array($teamData['human_resources'])) {
            foreach ($teamData['human_resources'] as $hr) {
                $employeeId = is_array($hr) && isset($hr['employeeNo']) ? $hr['employeeNo'] : $hr;
                if (isset($employeesIndex[$employeeId])) {
                    $mappedHumanResources[] = $employeesIndex[$employeeId];
                } else {
                    $mappedHumanResources[] = $hr;
                }
            }
        }
        $teamData['human_resources_mapped'] = $mappedHumanResources;
        return $teamData;
    }
}
