<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/SampleDocumentationCreator.php';

$creator = new SampleDocumentationCreator(__DIR__, forceDocumentationCreation: false);
$docs = $creator->createDocumentations([
    'schema-block-a.json',
    'schema-block-b.json',
    'schema-block-c.json',
    'schema-block-d.json',
]);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::TaetigkeitsbezogeneGefaehrdungsbeurteilung,
    params: [
        "projectNo" => "2408010",
        "workingOrderId" => "2",
        "documentA" => $docs[0]['documentId'],
        "documentB" => $docs[1]['documentId'],
        "documentC" => $docs[2]['documentId'],
        "documentD" => $docs[3]['documentId'],
		],
    cleanupCallback: function () use ($creator, $docs) {
        $creator->deleteSchemasByIds([
            $docs[0]['schemaId'],
            $docs[1]['schemaId'],
            $docs[2]['schemaId'],
            $docs[3]['schemaId']
        ]);
    }
);
