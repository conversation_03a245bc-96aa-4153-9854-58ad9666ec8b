<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "projectNo", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentA", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentB", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentC", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentD", required: true);
$config->addParameter(RouteConfigParameterType::int, "workingOrderId", required: false);
$config->wkhtmltopdfArguments = [
    "--margin-bottom", "40px",
    "--margin-top", "20px",
    "--margin-left", "20px",
    "--margin-right", "20px"
];
return $config;