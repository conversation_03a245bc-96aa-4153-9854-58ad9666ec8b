<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ZB2Druckzuordnung())->getData(
        $_GET['schemaId'] ?? null,
        $_GET['documentId'] ?? null
    );
}

/**
 * @param array<string, mixed> $data
 */
function displayResourceProperties(string $title, array $data): string
{
    return empty($data['vehicleResourceProperties'][$title]) ? '-' : $data['vehicleResourceProperties'][$title];
}

/**
 * @param array<string, mixed> $data
 */
function displayResource(string $title, array $data): string
{
    return empty($data['vehicleResource'][$title]) ? '-' : $data['vehicleResource'][$title];
}

?>

<!DOCTYPE html>
<html lang="de">
<head>
    <title>ZB2 Druckzuordnung</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <meta charset="UTF-8">
</head>
<body style="font-family: Arial, sans-serif">
<div class="page">
    <table style="width: 99%; border: 2px solid black; font-size: 80%">
        <tr>
            <td colspan="2" style="font-size: 150%; font-weight: bold; text-align: center">Datenbestätigung</td>
        </tr>
        <tr>
            <td colspan="2" style="text-align: center">für das nachfolgend beschriebene Fahrzeug zum Zwecke der
                Vorlage
            </td>
        </tr>
        <tr>
            <td style="width: 5%; text-align: center; vertical-align: top">-</td>
            <td style="width: 95%">bei der Zulassungbehörde für die Zulassung des Fahrzeugs, soweit ein
                Gutachten/Zusatzgutachten für die Zulassung nicht
                erforderlich ist¹
            </td>
        </tr>
        <tr>
            <td>oder</td>
        </tr>
        <tr>
            <td style=" text-align: center; vertical-align: top">-</td>
            <td>beim amtlich anerkannten Sachverständigen in den Fällen, in denen für die Erteilung der
                Betriebserlaubnis ein Gutachten/Zusatzgutachten erforderlich ist¹
            </td>
        </tr>
    </table>
    <table style="width: 99%; border-collapse: collapse; margin-top: 10px; font-size: 90%">
        <tr>
            <td style="border: 3px solid black; width: 7%; font-weight: bold">Feld ²</td>
            <td style="border: 3px solid black; width: 7%; font-weight: bold">Teil II ³</td>
            <td colspan="2" style="border: 3px solid black; font-size: 130%; font-weight: bold">Bezeichnung</td>
            <td style="border: 3px solid black; width: 32%; font-weight: bold">Daten ²</td>
        </tr>
        <tr>
            <td style="height: 5px"></td>
        </tr>
        <tr>
            <td class="border">D.1</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Marke</td>
            <td class="fourth-column"><?= displayResourceProperties('Make', $data) ?></td>
        </tr>
        <tr>
            <td class="border">D.2</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Typ</td>
            <td class="fourth-column"><?= displayResourceProperties('Type', $data) ?></td>
        </tr>
        <tr>
            <td class="border"></td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Variante</td>
            <td class="fourth-column"><?= displayResourceProperties('Variant', $data) ?></td>
        </tr>
        <tr>
            <td class="border"></td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Version</td>
            <td class="fourth-column"><?= displayResourceProperties('Version', $data) ?></td>
        </tr>
        <tr>
            <td class="border">D.3</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Handelsbezeichnung(en)</td>
            <td class="fourth-column"><?= displayResourceProperties('CommercialName', $data) ?></td>
        </tr>
        <tr>
            <td class="border">E</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Fahrzeug-Identifizierungsnummer</td>
            <td class="fourth-column"><?= displayResourceProperties('VehicleIdentificationNumber', $data) ?></td>
        </tr>
        <tr>
            <td class="border">F.1</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Technisch zulässige Gesamtmasse in kg</td>
            <td class="fourth-column"><?= displayResourceProperties('TechnPermMaxLadenMass', $data) ?></td>
        </tr>
        <tr>
            <td class="border">F.2</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Im Zulassungsmitgliedstaat zulässige Gesamtmasse in kg</td>
            <td class="fourth-column"><?= displayResourceProperties('MaxPermLadenMassNational', $data) ?></td>
        </tr>
        <tr>
            <td class="border">G</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Masse des in Betrieb befindlichen Fahrzeugs in kg (Leermasse)</td>
            <td class="fourth-column"><?= displayResourceProperties('MassOfTheVehicleInRunningOrder', $data) ?></td>
        </tr>
        <tr>
            <td class="border">J</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Fahrzeugklasse</td>
            <td class="fourth-column"><?= displayResourceProperties('VehicleCategoryCode', $data) ?></td>
        </tr>
        <tr>
            <td class="border">K</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Nummer der EG-Typgenehmigung oder ABE</td>
            <td class="fourth-column"><?= displayResourceProperties('TypeApprovalNumber', $data) ?></td>
        </tr>
        <tr>
            <td class="border">L</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Anzahl der Achsen</td>
            <td class="fourth-column"><?= displayResourceProperties('NumberOfAxles', $data) ?></td>
        </tr>
        <tr>
            <td class="border">O.1</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Technisch zulässige Anhängelast gebremst in kg</td>
            <td class="fourth-column"><?= displayResourceProperties('TechPermMaxTowMassCentAxTrail', $data) ?></td>
        </tr>
        <tr>
            <td class="border">O.2</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Technisch zulässige Anhängelast ungebremst in kg</td>
            <td class="fourth-column"><?= displayResourceProperties('TechPermMaxTowMassBrakedTrail', $data) ?></td>
        </tr>
        <tr>
            <td class="border">P.1</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Hubraum in cm³</td>
            <td class="fourth-column"><?= displayResourceProperties('EngineCapacity', $data) ?></td>
        </tr>
        <tr>
            <td style="border-left: 2px solid black; border-top: 2px solid black; border-top: 2px solid black">P.2</td>
            <td rowspan="2" class="second-column">X</td>
            <td colspan="2"
                style="border-left: 2px solid black; border-top: 2px solid black; border-top: 2px solid black">
                Nennleistung in kW
            </td>
            <td style="border-left: 2px solid black; border-top: 2px solid black; border-right: 2px solid black; vertical-align: center;">
                <?= displayResourceProperties('MaximumNetPower', $data) ?>
            </td>
        </tr>
        <tr>
            <td style="border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black">
                P.4
            </td>
            <td colspan="2"
                style="border-left: 2px solid black; border-bottom: 2px solid black; border-bottom: 2px solid black">
                Nenndrehzahl bei min-1
            </td>
            <td style="border-left: 2px solid black; border-bottom: 2px solid black; border-right: 2px solid black; vertical-align: center;">
                -
            </td>
        </tr>
        <tr>
            <td class="border">P.3</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Kraftstoff oder Energiequelle</td>
            <td class="fourth-column"><?= displayResourceProperties('FuelCode', $data) ?></td>
        </tr>
        <tr>
            <td class="border">Q</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Leistungsgewicht in kW/kg (nur bei Krädern)</td>
            <td class="fourth-column">-</td>
        </tr>
        <tr>
            <td class="border">R</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Farbe des Fahrzeugs</td>
            <td class="fourth-column"><?= displayResourceProperties('ColourDescription', $data) ?></td>
        </tr>
        <tr>
            <td class="border">S.1</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Sitzplätze einschließlich Fahrersitz</td>
            <td class="fourth-column"><?= displayResourceProperties('NrOfSeatingPositions', $data) ?></td>
        </tr>
        <tr>
            <td class="border">S.2</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Stehplätze</td>
            <td class="fourth-column"><?= displayResourceProperties('NumberOfStandingPlaces', $data) ?></td>
        </tr>
        <tr>
            <td class="border">T</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Höchstgeschwindigkeit in km/h</td>
            <td class="fourth-column"><?= displayResourceProperties('MaximumSpeed', $data) ?></td>
        </tr>
        <tr>
            <td class="border">U.1</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Standgeräusch in dB (A)</td>
            <td class="fourth-column"><?= displayResourceProperties('SoundLevelStationary', $data) ?></td>
        </tr>
        <tr>
            <td class="border">U.2</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Drehzahl in min-1 zu U.1</td>
            <td class="fourth-column"><?= displayResourceProperties('SoundLevelStatEngineSpeed', $data) ?></td>
        </tr>
        <tr>
            <td class="border">U.3</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Fahrgeräusch in dB (A)</td>
            <td class="fourth-column"><?= displayResourceProperties('SoundLevelDriveBy', $data) ?></td>
        </tr>
        <tr>
            <td class="border">V.7</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">CO<span style="vertical-align: bottom; font-size: 60%">2</span> (in g/km)
            </td>
            <td class="fourth-column"><?= displayResourceProperties('TestprocTypeVIICO2', $data) ?></td>
        </tr>
        <tr>
            <td class="border">V.9</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Angabe der für die EG-Typgenehmigung maßgebliche Schadstoffklasse</td>
            <td class="fourth-column"><?= displayResourceProperties('NrBaseRegulActLastAmendMotVeh', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(2)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Hersteller-Kurzbezeichnung</td>
            <td class="fourth-column"><?= $data['manufacturerShort'] ?></td>
        </tr>
        <tr>
            <td class="border">(2.1)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Code zu (2)</td>
            <td class="fourth-column"><?= displayResourceProperties('CodeOfManufacturer', $data) ?></td>
        </tr>
        <tr>
            <td rowspan="2"
                style="border-left: 2px solid black; border-top: 2px solid black; border-bottom: 2px solid black; vertical-align: center">
                (2.2)
            </td>
            <td rowspan="2" class="second-column">X</td>
            <td rowspan="2" class="border" style="width: 25%">Code zu (D.2) mit Prüfziffer</td>
            <td class="border">Typ/Variante/Version</td>
            <td class="fourth-column"><?= displayResourceProperties('CodeOfType', $data) ?>
                <span style="padding-left: 65px"><?= displayResourceProperties('CodeOfVariantVersion', $data) ?></span>
            </td>
        </tr>
        <tr>
            <td class="border" style="width: 23%">Prüfziffer</td>
            <td class="fourth-column"><?= displayResourceProperties('CheckDigitCodeOfVariantVersion', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(3)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Prüfziffer zur Fahrzeug-Identifizierungsnummer</td>
            <td class="fourth-column"><?= displayResourceProperties('CheckDigitCodeOfVehicleIdentificationNumber', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(4)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Art des Aufbaus</td>
            <td class="fourth-column"><?= displayResourceProperties('CodeForBodywork', $data) ?></td>
        </tr>
        <tr>
            <td rowspan="2" class="border">(5)</td>
            <td rowspan="2" class="second-column">X</td>
            <td rowspan="2" class="border">Bezeichnung der</td>
            <td class="border">Fahrzeugklasse</td>
            <td class="fourth-column"><?= displayResource('_VehicleCategoryDescription', $data) ?></td>
        </tr>
        <tr>
            <td class="border">Art des Aufbaus</td>
            <td class="fourth-column"><?= displayResource('_DescriptionOfBodywork', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(6)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Datum zu K</td>
            <td class="fourth-column">-<?php
                $dateValue = displayResourceProperties('TypeApprovalDateOfIssue', $data);
                if ($dateValue !== '-' && !empty($dateValue)) {
                    echo date('d-m-Y', strtotime($dateValue));
                }
                ?></td>
        </tr>
        <tr>
            <td style="border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; padding-left: 5px">
                (7.1)
            </td>
            <td rowspan="3"
                style="border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; text-align: center "></td>
            <td rowspan="3"
                style="width: 40%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; padding-left: 5px">
                Technisch zulässige maximale Achslast/Masse je Achsgruppe in kg
            </td>
            <td style="border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; padding-left: 5px">
                Achse 1
            </td>
            <td style="border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; vertical-align: center; padding-left: 5px">
                <?= displayResourceProperties('TechnicallyPermMassAxle_16_2X1', $data) ?>
            </td>
        </tr>
        <tr>
            <td class="border">(7.2)</td>
            <td class="border">Achse 2</td>
            <td class="fourth-column"><?= displayResourceProperties('TechnicallyPermMassAxle_16_2X2', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(7.3)</td>
            <td class="border">Achse 3</td>
            <td class="fourth-column"><?= displayResourceProperties('TechnicallyPermMassAxle_16_2X3', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(8.1)</td>
            <td rowspan="3" class="second-column"></td>
            <td rowspan="3" class="border">Zulässige maximale Achslast im Zulassungsmitgliedstaat in kg</td>
            <td class="border">Achse 1</td>
            <td class="fourth-column"><?= displayResourceProperties('MaxPermLadenMassAxleNational_17_2X1', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(8.2)</td>
            <td class="border">Achse 2</td>
            <td class="fourth-column"><?= displayResourceProperties('MaxPermLadenMassAxleNational_17_2X2', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(8.3)</td>
            <td class="border">Achse 3</td>
            <td class="fourth-column"><?= displayResourceProperties('MaxPermLadenMassAxleNational_17_2X3', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(9)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Anzahl der Antriebsachsen</td>
            <td class="fourth-column"><?= displayResourceProperties('NumberOfPoweredAxles', $data) ?></td>
        </tr>
        <tr>
            <td colspan="5" style="font-size: 87%; padding-left: 0 !important;">¹ Ob ein Gutachten/Teilgutachten
                erforderlich ist, ergibt sich aus
                der Bescheinigung der Angaben durch den Ausstellungsberechtigten
            </td>
        </tr>
        <tr>
            <td colspan="5" style="font-size: 87%; padding-left: 0!important;">² Für die Ausfüllung ist der Leitfaden
                zur Zulassungsbescheinigung
                Teil I und Teil II zu beachten
            </td>
        </tr>
        <tr>
            <td colspan="5" style="font-size: 87%; padding-left: 0 !important;">³ Soweit für das Fahrzeug eine
                Zulassungsbescheinigung Teil II
                ausgefüllt wurde, kann auf die Angabe der mit „X" gekennzeichneten Felder in der Datenbestätigung
                verzichtet werden
            </td>
        </tr>
    </table>
</div>
<div class="page">
    <table style="width: 99%; border: 2px solid black; font-size: 80%; margin-top: 20px">
        <tr>
            <td style="width: 35%; font-weight: bold">Fortsetzung:</td>
            <td style="width: 65%; font-weight: bold">Datenbestätigung für das Fahrzeug</td>
        </tr>
        <tr>
            <td style="padding-top: 6px; padding-bottom: 6px">(2) Hersteller Kurzbezeichnung</td>
            <td style="padding-top: 6px; padding-bottom: 6px"><?= $data['manufacturerShort'] ?></td>
        </tr>
        <tr>
            <td>(E) Fahrzeug-Identifizierungsnummer</td>
            <td style="font-size: 92%"><?= displayResourceProperties('VehicleIdentificationNumber', $data) ?></td>
        </tr>
    </table>
    <table style="width: 99%; border-collapse: collapse; margin-top: 10px; font-size: 90%">
        <tr>
            <td style="border: 3px solid black; width: 7%; font-weight: bold">Feld ²</td>
            <td style="border: 3px solid black; width: 7%; font-weight: bold">Teil II ³</td>
            <td colspan="2" style="border: 3px solid black; font-size: 130%; font-weight: bold; width: 54%">
                Bezeichnung
            </td>
            <td style="border: 3px solid black; width: 32%; font-weight: bold">Daten ²</td>
        </tr>
        <tr>
            <td style="height: 5px"></td>
        </tr>
        <tr>
            <td class="border">(10)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Code zu P.3</td>
            <td class="fourth-column"><?= displayResourceProperties('FuelCodePowerSource', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(11)</td>
            <td class="second-column">X</td>
            <td colspan="2" class="border">Code zu R</td>
            <td class="fourth-column"><?= displayResourceProperties('PrimaryColourCode', $data) ?>
                <span style="padding-left: 10px"><?= displayResourceProperties('SecondaryColourCode', $data) ?></span>
            </td>
        </tr>
        <tr>
            <td class="border">(12)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Rauminhalt des Tanks bei Tankfahrzeugen in m³</td>
            <td class="fourth-column"><?= displayResourceProperties('FuelTankCapacity', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(13)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Stützlast in kg</td>
            <td class="fourth-column"><?= displayResourceProperties('TechPermMaxStatVertMassCouplPt', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(14)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Bezeichnung der nationalen Emissionsklasse</td>
            <td class="fourth-column">-</td>
        </tr>
        <tr>
            <td class="border">(14.1)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Code zu V.9 oder (14)</td>
            <td class="fourth-column"><?= displayResourceProperties('CodeEmissionCategory', $data) ?></td>
        </tr>
        <tr>
            <td style="width: 7%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; padding-left: 5px">
                (15.1)
            </td>
            <td rowspan="3"
                style="width: 7%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; text-align: center "></td>
            <td rowspan="3"
                style="width: 38%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; padding-left: 5px">
                Bereifung
            </td>
            <td style="width: 16%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; padding-left: 5px">
                Achse 1
            </td>
            <td style="width: 32%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; vertical-align: center; padding-left: 5px">
                <?= displayResourceProperties('TyreSize_35X1', $data) ?>
            </td>
        </tr>
        <tr>
            <td class="border">(15.2)</td>
            <td class="border">Achse 2</td>
            <td class="fourth-column"><?= displayResourceProperties('TyreSize_35X2', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(15.3)</td>
            <td class="border">Achse 3</td>
            <td class="fourth-column"><?= displayResourceProperties('TyreSize_35X3', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(18)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Länge in mm</td>
            <td class="fourth-column"><?= displayResourceProperties('Length', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(19)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Breite in mm</td>
            <td class="fourth-column"><?= displayResourceProperties('Width', $data) ?></td>
        </tr>
        <tr>
            <td class="border">(20)</td>
            <td class="second-column"></td>
            <td colspan="2" class="border">Höhe in mm</td>
            <td class="fourth-column"><?= displayResourceProperties('Height', $data) ?></td>
        </tr>
        <tr>
            <td style="height: 65px" class="border">(22)</td>
            <td class="second-column"></td>
            <td colspan="3" class="border" style="vertical-align: top">
                Bemerkungen und Ausnahmen <span style="font-size: 92%">(Leitfaden beachten)</span>
                <br><span style="font-size: 92%"><?= displayResourceProperties('RemarksExceptions', $data) ?></span>
            </td>
        </tr>
        <tr>
            <td style="height: 65px" class="border">(22a)</td>
            <td class="second-column"></td>
            <td colspan="3" class="border" style="vertical-align: top">
                <br><span style="font-size: 92%"><?= displayResourceProperties('RemarksExceptions2', $data) ?></span>
            </td>
        </tr>
        <tr>
            <td style="height: 130px" class="border">(23)</td>
            <td class="second-column">X</td>
            <td colspan="3" class="border" style="vertical-align: top">
                Raum für interne Vermerke des Herstellers
                <br><span style="font-size: 92%"><?= displayResourceProperties('RemarksExceptions3', $data) ?></span>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="padding-left: 0; padding-top: 15px">Bescheinigung der Angaben durch den
                Ausstellungsberechtigten (Nichtzutreffendes bitte streichen):
            </td>
        </tr>
    </table>
    <table style="width: 99%; border-collapse: collapse; margin-top: 25px; table-layout: fixed;">
        <tr>
            <td style="width: 40px; padding-left: 20px;box-sizing: border-box;">➤</td>
            <td colspan="3" style="text-align: left; padding-left: 8px">Die Richtigkeit der vorstehenden Angaben wird
                heute bescheinigt.
            </td>
        </tr>
        <tr>
            <td style="width: 5%;padding-left: 20px; vertical-align: top">➤</td>
            <td colspan="3" style=" width: 95%;text-align: left; padding-left: 8px">Die Übereinstimmung mit der unter
                Feld K und 6
                angegebenen ABE und dem genehmigten Typ ggf. nebst Variante/Version bzw. Ausführung wird bestätigt.
            </td>
        </tr>
        <tr>
            <td style="padding-left: 20px;">➤</td>
            <td colspan="3" style="text-align: left; padding-left: 8px">Für die Zulassung ist ein
                Gutachten / Teilgutachten erforderlich.
            </td>
        </tr>
        <tr>
            <td style="height: 20px"></td>
        </tr>
    </table>
    <table style="width: 99%; border-collapse: collapse; margin-top: 25px;;">
        <tr>
            <td class="padding-top-bottom" style="width: 15%; font-size: 92%">Datum</td>
            <td class="padding-top-bottom" style="width: 50%; border-bottom: 1px solid #000000">
                <?= date('d.m.Y') ?>
            </td>
            <td class="padding-top-bottom" style="width: 30%"></td>
        </tr>
        <tr>
            <td class="padding-top-bottom" style="width: 15%; font-size: 92%; padding-top: 20px">Firma</td>
            <td class="padding-top-bottom" style="width: 50%; border-bottom: 1px solid #000000"></td>
            <td class="padding-top-bottom" style="width: 30%"></td>
        </tr>
        <tr>
            <td class="padding-top-bottom" style="width: 15%; font-size: 92%"></td>
            <td class="padding-top-bottom" style="width: 50%; border-bottom: 1px solid #000000"></td>
            <td class="padding-top-bottom" style="width: 30%"></td>
        </tr>
        <tr>
            <td class="padding-top-bottom" style="width: 15%; font-size: 92%"></td>
            <td class="padding-top-bottom" style="width: 50%; border-bottom: 1px solid #000000"></td>
            <td class="padding-top-bottom" style="width: 30%"></td>
        </tr>
        <tr>
            <td class="padding-top-bottom" style="width: 15%; font-size: 92%;"></td>
            <td class="padding-top-bottom" style="width: 50%; border-bottom: 1px solid #000000"></td>
            <td class="padding-top-bottom" style="width: 30%"></td>
        </tr>
        <tr>
            <td class="padding-top-bottom" style="width: 15%; font-size: 92%; padding-top: 20px">Unterschrift</td>
            <td class="padding-top-bottom" style="width: 50%; border-bottom: 1px solid #000000"></td>
            <td class="padding-top-bottom" style="width: 30%"></td>
        </tr>
    </table>
</div>
</body>
</html>