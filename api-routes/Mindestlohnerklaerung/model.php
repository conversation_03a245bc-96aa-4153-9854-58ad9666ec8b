<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Mindestlohnerklaerung
{
    /**
     * @return array<mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $hierarchicalDocument = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($hierarchicalDocument['fullDocument']);

        $projectFields = "projectName,projectSiteAddress,projectSiteZipCode,projectSiteCity,customerName";
        $projectId = (int)$hierarchicalDocument['documentRelKey1'];
        $project = PrintoutHelper::downloadProject($projectId, $projectFields, $curl);
        $settings = PrintoutHelper::downloadInfoSettings($curl);
        $data = array_merge($data, [
            'projectName' => $project['projectName'],
            'projectSiteAddress' => $project['projectSiteAddress'],
            'projectSiteCity' => $project['projectSiteCity'],
            'projectSiteZipCode' => $project['projectSiteZipCode'],
            'customerName' => $project['customerName'],
            'settings' => $settings['address'],
            'documentId' => $documentId
        ]);

        if (!empty($data['Mitarbeiter']) && is_string($data['Mitarbeiter'])) {
            $data['Mitarbeiter'] = $this->getEmployeeName($data['Mitarbeiter'], $curl);
        } else {
            $data['Mitarbeiter'] = [
                'firstName' => '',
                'lastName' => '',
                'birthDate' => ''
            ];
        }
        return $data;
    }

    /**
     * @return array<mixed>
     */
    private function getEmployeeName(string $empNo, PrintoutCurl $curl): array
    {
        $employee = PrintoutHelper::downloadEmployee($empNo, $curl);
        return [
            'firstName' => $employee['firstName'],
            'lastName' => $employee['lastName'],
            'birthDate' => $employee['birthDate'],
        ];
    }
}
