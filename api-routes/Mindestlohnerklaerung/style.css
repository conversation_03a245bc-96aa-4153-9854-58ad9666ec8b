body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    font-size: 14px;
    width: 200mm;
    padding: 5mm;
}

.headline {
    font-weight: bold;
    margin-bottom: 10px;
    text-align: center;
}

h3 {
    margin-top: 0;
    text-align: center;
}

.content-section {
    margin-top: 20px;
    margin-bottom: 20px;
}

.field {
    margin-bottom: 5px;
}

u {
    text-underline-offset: 2px;
}

ul {
    padding-left: 0;
}

.signature-section {
    margin-top: 30px;
}

.anlagen {
    margin-top: 10px;
}

.anlagen-flex {
    display: flex
}

.anlagen-empty {
    width: 100%;
}

.left-align {
    margin-left: auto;
}

.field-value {
    font-weight: bold;
}

.stand-date {
    bottom: 10px;
    right: 10px;
    font-size: 0.9em;
    text-align-last: end;
    color: #333;
}

.custom-list {
    list-style-type: "-  ";
    padding: 12px;
}

.custom-list-text {
    padding-left: 12px;
    display: flex;
}

.attachments {
    page-break-before: always
}

.image {
    /* subtract margins from image sizing */
    max-width: 200mm;
    max-height: 285mm;
    object-fit: contain;
    margin-top: 5mm;
    margin-bottom: 5mm;
}

.signature {
    max-height: 150px;
    max-width: 500px;
    object-fit: contain;
}

#label-signature {
    margin-right: 10px;
}

#signature-container {
    display: flex;
    align-items: center;
}