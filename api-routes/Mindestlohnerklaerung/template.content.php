<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Mindestlohnerklaerung())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<mixed> $data
 */
function getDataField(array $data, string $field, string $default = ''): string
{
    return $data[$field] ?? $default;
}

$emptyUnderlinedField = str_repeat("&nbsp;", 12);
?>

<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Mindestlohnerklarung</title>
</head>
<body>
<h3>ERKLÄRUNG</h3>
<p>
    Bestätigung über den Erhalt des Mindestentgelts nach dem Mindestlohngesetz und dem Arbeitnehmer-Entsendegesetz.
</p>
<div class="content-section">
    <div class="field">
        <span class="field-label">Projektadresse:</span>
        <span class="field-value">
                <?= getDataField($data, 'projectSiteAddress') ?>,
                <?= getDataField($data, 'projectSiteZipCode') ?>
            <?= getDataField($data, 'projectSiteCity') ?>
            </span>
    </div>
    <div class="field">
        <span class="field-label">Auftraggeber:</span> <span
                class="field-value"><?= getDataField($data, 'customerName') ?></span>
    </div>
    <div class="field">
        <span class="field-label">Arbeitgeber:</span> <span
                class="field-value"><?= ($data['settings']['address'] ?? '') ?></span>
    </div>
</div>
<div class="text">
    Mein Arbeitgeber hat mich über das Mindestlohngesetz und das Arbeitnehmer-Entsendegesetz informiert. Nach diesen
    Vorschriften haften Unternehmen (Auftraggeber), die ein anderes Unternehmen mit der Erbringung von Werk- oder
    Dienstleistungen beauftragen (Auftragnehmer), für die Verpflichtungen des Arbeitgebers (= Auftragnehmers) und
    weiterer Nachunternehmen oder Verleiher auf die Zahlung des Mindestentgelts.

    Zur Abdeckung des Haftungsrisikos des Auftraggebers ist nachzuweisen, dass der Arbeitgeber seinen
    Verpflichtungen zur Zahlung des Mindestentgelts nachgekommen ist.

    Vor diesem Hintergrund bestätige ich,
    <br>
</div>
<div class="content-section">
    <div class="field">
        <span class="field-label">Name, Vorname:</span>
        <span class="field-value">
                <?=
                !empty($data['Mitarbeiter']['lastName']) && !empty($data['Mitarbeiter']['firstName'])
                    ? $data['Mitarbeiter']['lastName'] . ', ' . $data['Mitarbeiter']['firstName']
                    : ''
                ?>
            </span>
    </div>
    <div class="field">
        <span class="field-label">Geburtsdatum:</span>
        <span class="field-value">
                <?=
                empty($data['Mitarbeiter']['birthDate'])
                    ? ''
                    : date('d.m.Y', strtotime($data['Mitarbeiter']['birthDate']))
                ?>
            </span>
    </div>
</div>
<div class="text">
    Anlagen in Kopie:
    <ul class="custom-list">
        <li>
            <i class="custom-list-text">
                für ausländische Arbeitnehmer außerhalb der EU, der EWR und der Schweiz und Arbeitnehmer aus den
                neuen
                Beitrittsländern (derzeit Kroatien): Aufenthaltstitel gemäß § 4 Absatz 1 Aufenthaltsgesetz, Pass /
                Passersatz oder Ausweisersatz.
            </i>
        </li>
        <li>
            <i class="custom-list-text">
                für ausländische Arbeitnehmer der EU-Staaten: Meldebescheinigung und Pass / Passersatz oder
                Ausweisersatz.
            </i>
        </li>
    </ul>
</div>
<div class="content-section">
    <p>
        Dass ich je tatsächlich geleisteter Arbeitsstunde für den Abrechnungszeitraum
        <span class="field-value">
                <u>
                    <?= getDataField($data, 'Abrechnungszeitraum') ?: $emptyUnderlinedField; ?>
                </u>
            </span>
        &nbsp;mindestens EURO
        <span class="field-value">
                <u>
                    <?php
                    $fieldValue = getDataField($data, 'Mindestlohn (€)');
                    echo is_numeric($fieldValue) ? number_format((float)$fieldValue, 2, ',', '') : $emptyUnderlinedField;
                    ?>
                </u>
            </span>
        &nbsp;brutto, d.h. vor Abzug von Steuern und Sozialabgaben, erhalten habe.
    </p>
</div>
<div class="text">
    Ich versichere ausdrücklich, dass mein Arbeitgeber neben den gesetzlichen Abzügen keine weiteren Abzüge von
    meinem Entgelt vorgenommen hat und alle meine tatsächlich geleisteten Arbeitsstunden für den Abrechnungszeitraum
    abgerechnet hat. Ich habe für den oben angegebenen Abrechnungszeitraum keine offenen Entgeltforderungen
    gegenüber meinem Arbeitgeber.

    Ich verpflichte mich ausdrücklich, den Auftraggeber unverzüglich schriftlich in Kenntnis zu setzen, falls das
    mir zustehende Nettogehalt (nach Abzug von Steuern und Sozialabgaben) nicht bis zum 15. des Folgemonats
    vollständig an mich ausbezahlt wird.

    Bei schuldhaftem Verstoß gegen diese Pflicht zur Inkenntnissetzung über die Unterschreitung des Mindestentgelts
    mache ich mich dem Auftraggeber gegenüber schadensersatzpflichtig.

    Ich bin damit einverstanden, dass diese Bescheinigung potenziellen Auftraggebern vorgelegt bzw. zur Verfügung
    gestellt wird.
</div>
<div class="signature-section">
    <div class="field">
        <span class="field-label">Datum:</span>
        <span class="field-value">
                <?= isset($data['Datum'])
                    ? date('d.m.Y', strtotime($data['Datum']))
                    : ''; ?>
            </span>
    </div>
    <div class="field" id="signature-container">
        <span class="field-label" id="label-signature">Unterschrift:</span>
        <?php if (!empty($data['Unterschrift'])) { ?>
            <img class="signature" src="<?= getDataField($data, 'Unterschrift') ?>" alt="signature"/>
        <?php } ?>
    </div>
</div>

<div class="anlagen">
    <?php
    if (!empty($data['Anhang'])) { ?>
        <div class="anlagen-flex">
            <div>ANLAGEN</div>
            <div class="stand-date left-align">Stand: Februar 2015</div>
        </div>
        <p class="attachments"></p>
        <?php foreach ($data['Anhang'] as $attachment) { ?>
            <?php if (isset($attachment['filePath'])) { ?>
                <div class="photos">
                    <img class="image" src="<?= $attachment['filePath'] ?>" alt="Image">
                </div>
            <?php } ?>
        <?php } ?>
    <?php } else { ?>
        <div class="anlagen-empty"></div>
        <div class="stand-date">Stand: Februar 2015</p></div>
    <?php } ?>
</div>
</body>
</html>
