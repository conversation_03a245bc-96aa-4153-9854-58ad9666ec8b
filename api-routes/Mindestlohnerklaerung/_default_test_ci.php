<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/SampleDocumentationCreator.php';

$creator = new SampleDocumentationCreator(__DIR__, forceDocumentationCreation: false);
$docs = $creator->createDocumentations([
    'schema.json',
]);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::Mindestlohnerklaerung,
    //fileType: printoutFileType::Html,
    params: [
        "schemaId" => $docs[0]['schemaId'],
        "documentId" => $docs[0]['documentId'],
		],
    cleanupCallback: function () use ($creator, $docs) {
        $creator->deleteSchemasByIds([$docs[0]['schemaId']]);
    }
);
