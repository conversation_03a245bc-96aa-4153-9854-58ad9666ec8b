<?php /** @noinspection PhpUnhandledExceptionInspection */
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_AcceptanceProtocol())->getData($_GET['personalNo'], $_GET['from'] ?? null, $_GET['to'] ?? null);
}

function getMonths(string $language): string
{
    $fromDate = new DateTime($_GET['from']);
    $toDate = new DateTime($_GET['to']);
    $interval = $fromDate->diff($toDate);
    $numMonths = $interval->m + 1;

    $output = "";
    for ($i = 0; $i < $numMonths; $i++) {
        $currentDate = $fromDate->format('Y-m');
        $output .= PrintoutHelper::getMonth($language, intval(date('n', strtotime($currentDate)))) . ",\n";
        $fromDate->modify('+1 month');
    }
    return rtrim($output, ",\n");
}

function getYears(): string
{
    $fromDate = new DateTime($_GET['from']);
    $toDate = new DateTime($_GET['to']);
    $startYear = (int)$fromDate->format('Y');
    $endYear = (int)$toDate->format('Y');

    $output = "";
    for ($year = $startYear; $year <= $endYear; $year++) {
        $output .= $year . ",\n";
    }

    return rtrim($output, ",\n");
}

?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Acceptance Protocol</title>
</head>
<body>
<div class="container">
    <header>
        <h2>КОНСТАТИВЕН ПРОТОКОЛ - Приложение 1</h2>
        <h3>Acceptance protocol - Appendix 1</h3>
    </header>

    <p class="mt-20">
        Работата е извършена и приета без възражения/ the services have been accepted without objections –
    </p>
    <p class="mt-7">
        ...............................................................................................................................................................</p>

    <p class="mt-20">за фирмата Възложител / for the company - Client.</p>

    <p class="mt-20">За месец / for the month(s)
        <span class="bold"><?= getMonths('bg'); ?></span>/
        <span class="bold"><?= getMonths('en'); ?></span>
        <span class="bold"><?= getYears() ?></span>
        са отработени общо/ have been elaborated:
        <span class="bold"><?= $data['totalHours'] ?></span>
        часа/hours, за следните дейности/ for the following activities: </p>

    <table class="main-table mt-35">
        <thead>
        <tr>
            <th>Описание на дейността /<br>Description of activity</th>
            <th>Общо нетно възнаграждение /<br>Total net remuneration</th>
        </tr>
        </thead>
        <tbody>
        <?php foreach ($data['rows'] as $k => $v): ?>
            <tr>
                <td><?= $v['projectName'] ?></td>
                <td class="text-align-right"><?= PrintoutHelper::formatTimeNumberWithThousands($v['wage']) ?></td>
            </tr>
        <?php endforeach; ?>
        <tr>
            <th>Обща сума/ Total:</th>
            <th class="text-align-right"><?= PrintoutHelper::formatTimeNumberWithThousands($data['totalWages']) ?></th>
        </tr>
        </tbody>
    </table>

    <table class="sub-table-1 mt-35">
        <tr>
            <td>...............година / year</td>
            <td>rp. (c.) / city (village)..............</td>
        </tr>
    </table>

    <table class="sub-table-2 mt-35">
        <tr>
            <td>Приема се / Accepted by: ...........</td>
            <td>Доставено от / Delivered by: ...........</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>тюлен / seal:</td>
            <td>/............../</td>
        </tr>
    </table>
</div>
</body>
</html>
