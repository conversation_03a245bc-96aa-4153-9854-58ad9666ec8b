<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_AcceptanceProtocol
{
    /** @return array<string, mixed> */
    public function getData(string $personalNo, string $from, string $to): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $contractId = PrintoutHelper::downloadEmployee($personalNo, $curl)['mostRecentContractId'];

        $downloadedData = $this->downloadData($from, $to, $personalNo, $contractId, $curl);
        $hourlyWage = $downloadedData['contract']['HourlyWage'];
        $hours = $downloadedData['hours'];

        list($data['rows'], $data['totalHours'], $data['totalWages']) = $this->getMappedData($hours, $hourlyWage);

        return $data;
    }

    /**
     * @param string $from
     * @param string $to
     * @param string $personalNo
     * @param string $contractId
     * @param PrintoutCurl $curl
     * @return array<string, array<mixed>>
     */
    private function downloadData($from, $to, $personalNo, $contractId, $curl): array
    {
        $contractUrl = "v1/employees/$personalNo/contracts/$contractId";
        $hoursUrl = "v1/hours?resnr=$personalNo&date=$from&to_date=$to&type=approved";
        $responses = $curl->_multi_call('get',
            [$contractUrl, $hoursUrl],
            [], PrintoutHelper::getHeadersForApiCalls());
        return [
            'contract' => $responses[0]['response'] ?? [],
            'hours' => $responses[1]['response'] ?? []
        ];
    }

    /**
     * @param array<mixed> $hours
     * @param float $hourlyWage
     * @return array{0: array<string, array<string, mixed>>, 1: float, 2: float}
     */
    public function getMappedData($hours, $hourlyWage): array
    {
        $rows = [];
        $totalWages = 0;
        $totalHours = 0;

        foreach ($hours as $hour) {
            if ($hour['kname'] == "") {
                $projectName = $hour['name'];
            } else {
                $projectName = $hour['name'] . " (" . $hour['kname'] . ")";
            }
            $wage = $hour['hours'] * $hourlyWage;

            if (array_key_exists($hour['ktr'], $rows))
                $rows[$hour['ktr']]['wage'] += $wage;
            else {
                $rows[$hour['ktr']]['projectName'] = $projectName;
                $rows[$hour['ktr']]['wage'] = $wage;
            }

            $totalWages += $wage;
            $totalHours += $hour['hours'];
        }

        return [$rows, $totalHours, $totalWages];
    }
}