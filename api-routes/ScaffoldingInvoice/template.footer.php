<!DOCTYPE HTML>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Scaffolding Invoice Footer</title>
    <!-- taken from https://wkhtmltopdf.org/usage/wkhtmltopdf.txt -->
    <style>
        * {
            font-family: sans-serif;
        }

        .not-bold {
            font-weight: normal;
        }

        .footer {
            width: 100%;
            font-size: small !important;
            border-collapse: collapse;
            mso-cellspacing: 0;
            color: black;
        }

        .seitefolgt, hr {
            max-width: 100px !important;
        }

        .rightAlign {
            text-align: right;
            font-size: medium;
        }

        .hidden {
            visibility: hidden;
        }
    </style>
</head>
<body onload="subst()">
<?php
# include "/Applications/XAMPP/xamppfiles/htdocs/invoice-scaffolding/scaffoldingInvoice.template.php";
$companyAdditionalInfo = "VERO Scaffolding EOOD Niederlassung Deutschland Merseburgerstr. 6-8 33106 Paderborn<br>
            Niederlassung der VERO Scaffolding EOOD Ul. Graf Ignatiev 10 BG 6000-Stara Zagora<br>
            HRB 11618 - EIK ********* - USt ID-Nr.: DE *********** - St.-Nr.: 339 / 5992 / 0737<br>
            Geschäftsführer: ";
$leadName = "A. Vermeulen";
$leadTitle = "Dipl.-Ing.";
?>
<table class="footer">
    <tr>
        <td colspan="3"></td>
        <td class="seitefolgt">
            <div class="fit">
                <section>
                    <hr>
                </section>
                <p id="page-follows" style="text-align: center;">
                    Seite <span class="page"></span> folgt
                </p>
            </div>
        </td>
        <td colspan="4"></td>
    </tr>
    <tr>
        <td colspan="4" id="vero-ood-info">
            <b>
                <?php echo $companyAdditionalInfo . " <section class='not-bold' style='display: inline;'>" .
                    $leadTitle . " " . $leadName . "</section> <br>"; ?>
            </b>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
</table>
<script type="text/javascript">
    var els = 0;
    var ems;

    function subst() {
        var vars = {};
        var query_strings_from_url = document.location.search.substring(1).split('&');
        for (var query_string in query_strings_from_url) {
            if (query_strings_from_url.hasOwnProperty(query_string)) {
                var temp_var = query_strings_from_url[query_string].split('=', 2);
                vars[temp_var[0]] = decodeURI(temp_var[1]);
            }
        }
        var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
        for (var css_class in css_selector_classes) {
            if (css_selector_classes.hasOwnProperty(css_class)) {
                var element = document.getElementsByClassName(css_selector_classes[css_class]);
                for (var j = 0; j < element.length; ++j) {
                    element[j].textContent = parseInt(vars[css_selector_classes[css_class]], 10) + 1;
                }
            }
        }
        var page = parseInt(vars['page']);
        var topage = parseInt(vars['topage']);
        var allvars = element.length;
        if (parseInt(vars['page']) == topage && parseInt(vars['page']) > 1) {
            document.getElementsByTagName('html')[0].style.display = "none";
        } else if (parseInt(vars['page']) == 1 && parseInt(vars['page']) == topage) {
            document.getElementsByClassName('seitefolgt')[0].style.display = "none";
        }
    }
</script>
</body>
</html>
