<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_ScaffoldingInvoice
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $year, string $invoiceNo): array
    {
        $curl = new PrintoutCurl();
        return $this->getInvoice($curl, $year, $invoiceNo);
    }

    /**
     * @return array<string, mixed>|null
     */
    public function getInvoice(PrintoutCurl $curl, string $year, string $invoiceNo): ?array
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get', "$base/receipts/invoice/$year/$invoiceNo",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($json, true);
    }
}
