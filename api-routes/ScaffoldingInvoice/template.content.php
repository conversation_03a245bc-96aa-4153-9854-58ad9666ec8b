<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ScaffoldingInvoice())->getData($_GET['year'], $_GET['invoiceNo']);
}

$iterCounter = 0;
$dueSum = 57646.66;
$phoneNum = "+49 (0) 52 51/874 100";
$email = "<EMAIL>";
$scaffDateFormat = "d.m.Y";
$constructionObject = "WEC III / Stedener Feld -Gerüstbauarbeiten-";
$institution = "Sparkasse Paderborn";
$senderCompany = "Merseburger Straße 6-8<br>
            33106 Paderborn<br><br>
            Tel.: $phoneNum <br>
            e-Mail: $email <br>
            www.vero.de<br>";
$companyBasedIn = "Niederlassung Deutschland";
$companyName1 = "VERO Scaffolding EOOD";
$measureNr = "10617-001";
$billingType = "1. Abschlagsrechnung";
$senderInfo = "VERO Scaffolding EOOD Ndl. Deutschland · Merseburger Str. 6-8 <br>33106 Paderborn";
$iban = "DE 25 4765 0130 0001 0768 76";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Scaffolding Invoice</title>
    <meta name="description" content="Scaffolding Invoice">
    <style>
        * {
            font-family: sans-serif;
        }

        .receiver-company-full-address {
            font-size: 16px;
        }

        .hidden {
            visibility: hidden;
        }

        .sender-info {
            font-size: 10px;
        }

        #company-name-main {
            font-size: 20px;
            width: 250px;
        }

        .construction-services-table td {
            padding: 6px;
        }

        .construction-services-table th:nth-child(1) {
            text-align: center;
            width: 20px !important;
        }

        .construction-services-table th:nth-child(2) {
            text-align: center;
            width: 150px !important;
        }

        .construction-services-table td:nth-child(2) {
            width: 150px !important;
            text-align: left;
        }

        .construction-services-table td:nth-child(3) {
            width: 500px !important;
        }

        .construction-services-table th:nth-child(3) {
            width: 500px !important;
        }

        .construction-services-table th:nth-child(4) {
            text-align: right;
            width: 120px;
        }

        .construction-services-table td:nth-child(4) {
            text-align: right;
            width: 120px;
        }

        /* apply padding for the first row in the table*/
        .table-data tr:nth-child(2) td {
            padding-top: 9px;
        }

        .table-data td {
            padding-bottom: 6px;
        }

        .construction-services-table td:nth-child(5) {
            text-align: right;
            width: 120px;
        }

        .construction-services-table th:nth-child(5) {
            text-align: right;
            width: 120px;
        }

        .rechnungs-details tr, .rechnungs-details td {
            font-weight: bold;
            padding-left: 0;
        }

        .instruction {
            padding: 0;
            font-size: 13px;
        }

        td, th {
            font-size: 16px;
            text-align: left;
        }

        .border-bottom {
            border-bottom: 2px solid black;
        }

        .header-table {
            padding: 0;
        }

        .header-table td:nth-child(1) {
            width: 150px;
        }

        .header-table th:nth-child(1) {
            width: 50px;
        }

        .header-table td:nth-child(2) {
            width: 50px;
        }

        .header-table td:nth-child(3) {
            width: 150px;
        }

        .header-table td:nth-child(4) {
            width: 150px;
            text-align: right;
        }

        .header-table td:nth-child(5) {
            width: 150px;
            text-align: right;
        }

        .align-right {
            text-align: right;
            border-spacing: 0;
            padding: 0 !important;
            margin: 0;
        }

        .align-left {
            text-align: left;
            border-spacing: 0;
            padding: 0 !important;
            margin: 0;
        }

        div {
            display: block;
        }

        body {
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0 !important;
            width: 100%;
        }

        .logos {
            margin: 0 !important;
            padding: 0 !important;
        }

        #leistungszeitraum {
            font-size: 0.8em;
        }

    </style>
</head>
<body onload="subst(); singlePageFit()">
<table class="header-table">
    <tr>
        <td colspan="2"><img
                    src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/pqms-logo.jpeg") ?>"
                    alt="logo" class="logos" style="width: 150px"></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td colspan="2"><img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/vero-logo.png") ?>"
                             alt="logo" class="logos" style="width: 210px"></td>
    </tr>
    <tr>
        <td colspan="2">
            <section class="sender-info"><u><b><?= $senderInfo ?></b></u></section>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td class="company-data" colspan="2">
            <section id="company-name-main"><b><?= $companyName1 ?></b></section>
            <?= $companyBasedIn ?>
        </td>
    </tr>
    <tr>
        <td colspan="8"></td>
    </tr>
    <tr>
        <td colspan="2" class="receiver-company-full-address">
            <?=
            $data["kanrede"] . "<br>" .
            $data["kname"] . "<br>" .
            $data["kaddr1"] . "<br>" .
            $data["kplz"] . "&nbsp" . $data["kort"] ?>
        </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td colspan="2">
            <br>
            <?= $senderCompany ?>
        </td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td class="hidden" style="height: 15px">
            -
        </td>
    </tr>
    <tr>
        <td style="width: 100%;"></td>
        <td style="width: 100%;"></td>
        <td style="width: 100%;"></td>
        <td style="width: 100%;"></td>
        <td style="width: 100%;"></td>
        <td style="width: 100%;"></td>
        <td colspan="2">&nbsp;&nbsp;&nbsp;
            <?= $data["kort"] . ', ' . date_format(date_create($data["datum"]), $scaffDateFormat) ?>
        </td>
    </tr>
    <td class="hidden" style="height: 20px">
        -
    </td>
    <tr>
        <td colspan="8"></td>
    </tr>
    <tr>
        <td colspan="8" class="instruction">bitte bei Zahlung unbedingt angeben:</td>
    </tr>
</table>
<table class="rechnungs-details">
    <tr>
        <td><b>Rechnung: </b></td>
        <td><?= $data["rechnr"] ?></td>
        <td colspan="2"><?= $billingType ?></td>
    </tr>
    <tr>
        <td><b>Projekt: </b></td>
        <td><?= $data["ktr"] ?></td>
        <td colspan="2">Aufmaß-Nr: <?= $measureNr ?></td>
    </tr>
    <tr>
        <td><b>Kunden-Nr:</b></td>
        <td><?= $data["knr"] ?></td>
        <td colspan="2">
            <p id="leistungszeitraum">
                Leistungszeitraum
                <?php
                echo date_format(date_create($data["begdatum"]), $scaffDateFormat);
                if ($data["aufmdatum"]) {
                    echo " bis " . date_format(date_create($data["aufmdatum"]), $scaffDateFormat);
                }
                ?>
            </p>
        </td>
    </tr>
    <tr>
        <td>
            <b>Objekt:</b>
        </td>
        <td colspan="2"><?= $constructionObject ?></td>
    </tr>
</table>
<table class="construction-services-table-message" style="margin-top: 27px; margin-bottom: 27px">
    <tr>
        <td colspan="8" rowspan="2">Für durchgeführte Gerüstbauleistungen berechnen wir Ihnen:</td>
    </tr>
</table>


<table class='table-data'>
    <tr>
        <th class="border-bottom">Position</th>
        <th class="border-bottom" colspan="2" style="text-align: center; width: 24%">Menge EH</th>
        <th class="border-bottom" colspan="3">Beschreibung</th>
        <th class="border-bottom" style="text-align: center">
            <section> Einzelpreis</section>
            <section> <?= $data["dsymbol"] ?></section>
        </th>
        <th class="border-bottom" style="text-align: center">
            <section> Gesamtpreis</section>
            <section><?= $data["dsymbol"] ?></section>
        </th>
    </tr>
    <?php
    $sumTotals = 0;
    foreach ($data["positions"] as $position) {
        echo "<tr>";
        $iterCounter += 1;
        $posNum = $position["posnum"];
        $amount = $position["menge"];
        $constPartDesc = $position["ktext"];
        $pricePerEl = $position["ep"];
        $totalPrice = $position["gp"];
        $measureUnit = $position["einheit"];

        if ($constPartDesc == "Bauteil A") {
            echo
                "<td>" . $posNum . "</td>
                <td colspan='2'>
                </td>
                <td colspan='3'><b>" . $constPartDesc . "</b></td>
                <td>&nbsp</td>
                <td>&nbsp</td>  </tr>";
        } else {
            $sumTotals += floatval($totalPrice);
            echo
                "<td>" . $posNum . "</td>
                <td>
                    <div style='text-align: right'>" .
                PrintoutHelper::formatTimeNumberWithThousands($amount) .
                "</div></td>
                <td>$measureUnit</td>
                <td colspan='3'>" . $constPartDesc . "</td>
                <td style='text-align: right; padding-right: 3%'>" . PrintoutHelper::formatTimeNumberWithThousands($pricePerEl) . "</td>
                <td style='text-align: right'>" . PrintoutHelper::formatTimeNumberWithThousands($totalPrice) . "</td>  </tr>";
        }
    }
    $iterCounter = 0;
    $securityDeposit = $sumTotals * 0.10;
    $netSumTotals = $sumTotals + $securityDeposit;
    $MwSt = $sumTotals * 0.19;
    $bruttoSumTotals = $netSumTotals + $MwSt;
    ?>
</table>
<table>
    <tr class="sums">
        <td></td>
        <td colspan="2"></td>
        <td colspan="3"></td>
        <td class="totalsum border-bottom"></td>
        <td class="totalsum border-bottom"></td>
    </tr>
    <tr class="netto">
        <th id="netto-empty-td" class="hidden" colspan="8">hidden</th>
    </tr>
    <tr class="leistungswert">
        <td class="leistungswert"></td>
        <td class="leistungswert" colspan="2"></td>
        <td class="leistungswert" colspan="3"></td>
        <td class="leistungswert"></td>
        <td class="leistungswert"></td>
    </tr>
    <tr>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td class="align-right" colspan="2" style="padding-top: 5px !important;"><b>Leistungswert netto</b></td>
        <td></td>
        <td></td>
        <td class="align-right" style="padding-top: 5px !important;">
            <b><?= PrintoutHelper::formatTimeNumberWithThousands($sumTotals) ?></b></td>
    </tr>
    <tr>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="border-bottom align-right" colspan="2" style="padding-bottom: 5px !important;">Sicherheitseinbehalt
            10,00%
        </td>
        <td class="border-bottom"></td>
        <td class="border-bottom"></td>
        <td class="align-right border-bottom" style="padding-bottom: 5px !important;">
            <b><?= PrintoutHelper::formatTimeNumberWithThousands($securityDeposit) ?></b></td>
    </tr>

    <tr>
        <th></th>
        <th colspan="2">
            <section class="hidden">hidden</section>
        </th>
        <th colspan="3"></th>
        <th></th>
        <th></th>
    </tr>
    <tr>
        <td></td>
        <td colspan="2"></td>
        <td colspan="3"></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td></td>
        <td colspan="2"></td>
        <td colspan="3"></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td class="align-right" colspan="2"><b>Gesamtleistung netto</b></td>
        <td></td>
        <td></td>
        <td class="align-right"><b><?= PrintoutHelper::formatTimeNumberWithThousands($netSumTotals) ?></b></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td class="align-right" colspan="2" style="padding-bottom: 5px !important;"><b>MwSt. 19%</b></td>
        <td></td>
        <td></td>
        <td class="align-right border-bottom" style="padding-bottom: 5px !important;">
            <b><?= PrintoutHelper::formatTimeNumberWithThousands($MwSt) ?></b></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td class="align-right" colspan="2" style="padding: 5px !important;"><b>Gesamtleistung brutto</b></td>
        <td></td>
        <td></td>
        <td class="align-right border-bottom" style="padding: 5px !important;">
            <b>
                <?= PrintoutHelper::formatTimeNumberWithThousands($bruttoSumTotals) ?>
            </b>
        </td>
    </tr>
    <tr>
        <td colspan="8"></td>
    </tr>
    <tr>
        <td colspan="8"></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td class="border-bottom"></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td colspan="2"></td>
    </tr>
</table>
<table style="margin-top: 15px">
    <tr>
        <td>zahlbar bis zum <?= date_format(date_create($data["belegdatum"]), $scaffDateFormat) ?> abzgl. 2.00%
            Skonto = <?= $dueSum ?> </td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td>bis zum <?= date_format(date_create($data["begdatum"]), $scaffDateFormat) ?> netto ohne Abzüge</td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <td><?= $institution ?> <b><?= $iban ?></b></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
    </tr>
</table>
</body>
</html>
