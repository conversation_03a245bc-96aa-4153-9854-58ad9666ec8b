{"name": "UnfallanzeigeBgBau", "status": "active", "createdBy": "364", "createdOn": "2025-01-14T00:00:00Z", "positions": [{"id": 1, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "EMPLOYEESELECTOR"}, {"id": 2, "title": "Mitarbeiter Geschlecht", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"id": 3, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "values": ["ja", "nein"]}, {"id": 4, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "values": ["ja", "nein"]}, {"id": 5, "title": "Ist der Versicherte", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON>", "mit dem Unternehmer verwandt", "Ehegatte des Unternehmers", "Gesellschafter/ Geschäftsführer"]}, {"id": 6, "title": "Anspruch auf Entgeltfortzahlung", "type": "int"}, {"id": 7, "title": "Krankenkasse des Versicherten", "type": "string"}, {"id": 8, "title": "Tödlicher Unfall", "type": "combobox", "values": ["ja", "nein"]}, {"id": 9, "title": "Unfallzeitpunkt Datum", "type": "date"}, {"id": 10, "title": "Unfallzeitpunkt Zeit", "type": "time"}, {"id": 11, "title": "Unfallort", "type": "string"}, {"id": 12, "title": "Ausführliche Schilderung des Unfallhergangs", "type": "string"}, {"id": 13, "displayInside": 12, "title": "Die Angaben beruhen auf der Schilderung", "type": "combobox", "values": ["des Versicherten", "<PERSON><PERSON>"]}, {"id": 14, "title": "Verletzte Körperteile", "type": "string"}, {"id": 15, "title": "Art der Verletzung", "type": "string"}, {"id": 16, "title": "Wer hat von dem Unfall zuerst Kenntnis genommen?", "type": "string"}, {"id": 17, "displayInside": 16, "title": "War diese Person Augenzeuge", "type": "combobox", "values": ["ja", "nein"]}, {"id": 18, "title": "Name und Anschrift des erstbehandelnden Arztes/Krankenhauses", "type": "string"}, {"id": 19, "title": "Beginn der Arbeitszeit des Versicherten", "type": "time"}, {"id": 20, "title": "Ende der Arbeitszeit des Versicherten", "type": "time"}, {"id": 21, "title": "Zum Unfallzeitpunkt beschäftigt/tätig als", "type": "string"}, {"id": 22, "title": "Seit wann bei dieser Tätigkeit", "type": "string"}, {"id": 23, "displayInside": 22, "title": "Datum - <PERSON>it wann bei dieser Tätigkeit", "type": "date"}, {"id": 24, "title": "In welchem Teil des Unternehmens ist der Versicherte ständig tätig?", "type": "string"}, {"id": 25, "title": "Hat der Versicherte die Arbeit eingestellt?", "type": "combobox", "values": ["nein", "sofort", "später am"]}, {"id": 26, "displayInside": 25, "title": "Datum", "type": "date"}, {"id": 27, "displayInside": 25, "title": "Uhrzeit", "type": "time"}, {"id": 28, "title": "Hat der Versicherte die Arbeit wieder aufgenommen?", "type": "combobox", "values": ["ja, am", "nein"]}, {"id": 29, "displayInside": 28, "title": "Datum - Hat der Versicherte die Arbeit wieder aufgenommen", "type": "date"}, {"id": 30, "title": "Datum", "type": "date"}, {"id": 31, "title": "Unternehmer/Bevollmächtigter", "type": "string"}, {"id": 32, "title": "Betriebsrat (Personalrat)", "type": "string"}, {"id": 33, "title": "Telefon-Nr. für Rückfragen (Ansprechpartner)", "type": "string"}]}