@page {
    size: 210mm 297mm;
}

* {
    font-family: Arial, sans-serif;
    font-size: 13px;
}

body {
    width: 230mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

.page {
    page-break-before: always;
    max-height: 297mm;
    min-height: 297mm;
    min-width: 210mm;
    max-width: 210mm;
    box-sizing: border-box;
}

.border-none {
    border: none !important;
}

.border-none-bottom {
    border-bottom: none !important;
}

.border-none-top {
    border-top: none !important;
}

.border-none-left {
    border-left: none !important;
}

.border-none-right {
    border-right: none !important;
}

.border-bottom-only {
    border-top: none !important;
    border-right: none !important;
    border-left: none !important;
    border-bottom: 1px solid black;
}

.border-right-only {
    border-top: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: 1px solid black;
    padding-right: 3px
}

.border-left-right-only {
    border-top: none !important;
    border-bottom: none !important;
    border-right: 1px solid black;
    border-left: 1px solid black;
    padding-left: 3px
}

.heading-title {
    padding-left: 0 !important;
    padding-bottom: 0 !important;
}

.left-space {
    padding-left: 20px;
}

.corners {
    position: relative;
    width: 150px;
    padding: 30px;
    margin-top: 2px;
}

.checkboxes-table {
    width: 100%;
}

.tag-table, .hours-table, .work-table {
    border-spacing: 0 !important;
    width: 100%;
}

.top, .bottom {
    position: absolute;
    width: 20px;
    height: 20px;
    pointer-events: none;
}

.top {
    top: 0;
    border-top: 2px solid;
}

.bottom {
    bottom: 0;
    border-bottom: 2px solid;
}

.left {
    left: 0;
    border-left: 2px solid;
}

.right {
    right: 0;
    border-right: 2px solid;
}

.heading-text {
    font-size: 24px;
    font-weight: 900;
}

.main-table {
    width: 100%;
    border-collapse: collapse;
}

.main-table td {
    border: 1px solid black;
    padding: 3px;
}

.main-table tr {
    page-break-inside: avoid;
    break-inside: avoid;
}

.header-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 5px;
}

.header-table td {
    vertical-align: top;
}

.number-table {
    border-collapse: collapse;
    margin: 10px 0;
}

.number-table td {
    border: 1px solid black;
    text-align: center;
    padding-right: 10px;
    padding-top: 20px;
    font-weight: bold;
}

.text-container {
    padding-top: 3px;
}

.main-table td {
    border: 1px solid black;
    vertical-align: top;
}

.logo {
    width: 200px;
    height: auto;
}

