<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_PaderTor
{
    /**
     * @return array<mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $filePath = __DIR__ . '/red-markers.json';
        if (!file_exists($filePath)) {
            /** @noinspection PhpUnhandledExceptionInspection */
            throw new Exception("File not found: $filePath");
        }
        $redMarkers = json_decode(file_get_contents($filePath), associative: true);

        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $schema = PrintoutHelper::downloadSchema($schemaId, $curl);
        $document = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl)['fullDocument'];

        $data = [];
        if ($document['documentRelType'] === 'equipment') {
            $data['equipment'] = $this->downloadEquipment($document['documentRelKey1'], $curl);
            $equipmentRelation = $data['equipment']['relations'][0];
            $relType = $equipmentRelation['rel_type'];
            if ($relType === "project" || $relType === "workingOrder") {
                $projectNoForCustomerDownload = explode('-', $equipmentRelation['rel_key'])[0];
            } else {
                die_with_response_code(Response::BAD_REQUEST,
                    "The equipment is only allowed to be related to a project or working order, but it is related to $relType");
            }
        } else {
            // hope for project or workingOrder
            $projectNoForCustomerDownload = $document['documentRelKey1'];
        }

        $project = $this->downloadProjectData($projectNoForCustomerDownload, $curl);
        $data['customerData'] = $this->downloadCustomer($project['customerNo'], $curl);

        // for testing
        /*$document['children'][1]['reportedValue'] = "Ja";
        $document['children'][1]['reportedValues'] = [$document['children'][1]['reportedValue']];
        $document['children'][48]['reportedValue'] = "n.i.o.";
        $document['children'][48]['reportedValues'] = [$document['children'][48]['reportedValue']];

        foreach ($document['children'] as &$child) {
            $type = strtoupper($child['type']);
            if ($type === SchemaTypes::COMBOBOX_TAP) {
//                                $child['reportedValue'] = "n.v.";
                $child['reportedValue'] = "n.i.o.";
                $child['reportedValues'] = [$child['reportedValue']];
            }
            // for Messung
            if ($type === SchemaTypes::COMBOBOX) {
                $child['reportedValue'] = "n.i.o.";
                $child['reportedValues'] = [$child['reportedValue']];
            }
        }*/

        $schemaPositions = $schema['children'] ?? [];
        $mappedChildren = PrintoutHelper::mapDocumentChildrenToValues($document, true, $schemaPositions);
        $data['footer'] = $this->getFooter($document, $mappedChildren);
        $data['mappedChildren'] = $mappedChildren;
        $organizedChildren = $this->getOrganizedChildren($document['children']);
        $data['safetyDeficiencies'] = array_merge(
            $this->getSafetyDeficienciesForRedMarkersJson($redMarkers, $organizedChildren, $mappedChildren, $schema['title']),
            $this->getSafetyDeficienciesForDisplayInsideComments($document['children'])
        );

        usort($data['safetyDeficiencies'], [$this, 'compareHeadingsForSorting']);

        $withMessungen = $this->reorderBetriebskraeftemessungMessungen($organizedChildren);
        $filteredChildren = $this->filterOutPositionsBasedOnConditions($schema['title'], $withMessungen, $mappedChildren);
        $splitChildren = $this->splitMappedChildren($filteredChildren);
        $data['leftChildren'] = $splitChildren[0];
        $data['rightChildren'] = $splitChildren[1];
        $data['schemaTitle'] = $schema['title'];
        $data['lastwechselTitle'] = $this->getLastWechsel($schema['title']);
        $data['photos'] = $this->getPhotos($document, $schemaPositions, $mappedChildren);
        return $data;
    }

    /**
     * Sort because the headings contain prefixes like 1.3 or 5.1.1
     * Otherwise, they are unsorted because the safety deficiencies are aggregated from red-markers.json and from displayInside values.
     */
    private function compareHeadingsForSorting(string $a, string $b): int
    {
        // Extract the heading parts
        preg_match('/^([\d.]+)\s/', $a, $matchesA);
        preg_match('/^([\d.]+)\s/', $b, $matchesB);

        $partsA = isset($matchesA[1]) ? array_map('intval', explode('.', $matchesA[1])) : [0];
        $partsB = isset($matchesB[1]) ? array_map('intval', explode('.', $matchesB[1])) : [0];

        // Compare each corresponding part
        $len = max(count($partsA), count($partsB));
        for ($i = 0; $i < $len; $i++) {
            $partA = $partsA[$i] ?? 0;
            $partB = $partsB[$i] ?? 0;
            if ($partA != $partB) {
                return $partA - $partB;
            }
        }
        return 0;
    }

    /**
     * Reorders the 'Betriebskräftemessung' measurements in the given array of organized children.
     *
     * This function looks for a key in the array that contains the term "Betriebskräftemessung"
     * (e.g., "5. Betriebskräftemessung"). It then identifies all keys that share the same prefix
     * (e.g., "5.1.1. Messung") and moves them under the "Betriebskräftemessung" key. All other keys
     * remain unchanged.
     */
    /**
     * @param array<mixed> $organizedChildren
     * @return array<mixed>
     */
    private function reorderBetriebskraeftemessungMessungen(array $organizedChildren): array
    {
        // will be something like "5. Betriebskräftemessung"
        $foundBetriebskraeftemessungKeyAt = null;
        foreach ($organizedChildren as $key => $value) {
            $parts = explode(' ', $key);
            $lastPart = end($parts);
            if ($lastPart === "Betriebskräftemessung") {
                $foundBetriebskraeftemessungKeyAt = $key;
                break;
            }
        }
        if ($foundBetriebskraeftemessungKeyAt === null) {
            return $organizedChildren;
        }
        $prefix = explode(' ', $foundBetriebskraeftemessungKeyAt)[0];
        $result = [];
        foreach ($organizedChildren as $key => $value) {
            // handle special case with nested "1 Grenzwert" headline under "X. Betriebskräftemessung"
            if ($key === "1 Grenzwert" || (str_starts_with($key, $prefix) && $key !== $foundBetriebskraeftemessungKeyAt)) {
                $result[$foundBetriebskraeftemessungKeyAt][$key] = $value;
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    /**
     * @param array<mixed> $organizedChildren
     * @param array<mixed> $mappedChildren
     * @return array<mixed>
     */
    private function filterOutPositionsBasedOnConditions(string $schemaTitle, array $organizedChildren, array $mappedChildren): array
    {
        $filteredChildren = $organizedChildren;
        if ($schemaTitle === "Brandschutztüren") {
            if (($organizedChildren['1. Torflügel']['1.5 Kennzeichnung als Rettungsweg'] ?? null) === "n.v.") {
                unset($filteredChildren['1. Torflügel']['1.7 Panikschloss (bei Fluchtweg)']);
                unset($filteredChildren['1. Torflügel']['1.8 Funktion E (Griff/Knauf)']);
                unset($filteredChildren['1. Torflügel']['1.9 Funktion B (Griff/Griff)']);
            }
        } else if ($schemaTitle === "Brandschutz Schiebetore") {
            if (($organizedChildren['1. Torflügel']['1.8 Schlupftür'] ?? null) === "n.v.") {
                unset($filteredChildren['1. Torflügel']['1.9 Obentürschließer']);
            }
        } else if ($schemaTitle === "Sektionaltore") {
            $antrieb = $mappedChildren["Antrieb vorhanden?"] ?? "";
            if ($antrieb === "Ja") {
                if (($mappedChildren['2.6 voreilende Lichtschranke, Lichtgitter'] ?? null) === "n.v.") {
                    unset($filteredChildren['3. Betriebskräftemessung']);
                    unset($filteredChildren['6. Antrieb']);
                }
            } else if ($antrieb === "Nein") {
                unset($filteredChildren['2. Sicherheitseinrichtung']['2.6 voreilende Lichtschranke, Lichtgitter']);
                unset($filteredChildren['3. Betriebskräftemessung']);
                unset($filteredChildren['6. Antrieb']);
            }
        }
        return $filteredChildren;
    }

    /**
     * Returns an array of headline titles with their direct children.
     */
    /**
     * @param array<mixed> $children
     * @return array<mixed>
     */
    private function getOrganizedChildren(array $children): array
    {
        $result = [];
        $foundFirstHeadlineForSchemaName = false;
        foreach ($children as $child) {
            $isHeadline = strtoupper($child['type']) === SchemaTypes::HEADLINE;
            if (!$isHeadline) {
                continue;
            }
            if ($foundFirstHeadlineForSchemaName) {
                $result[$child['title']] = $this->getChildren($child['id'], $children);
            } else {
                $foundFirstHeadlineForSchemaName = true;
            }
        }
        // exclude footer headlines
        unset($result['Reparaturleistungen']);
        unset($result['Ausgeführte Arbeiten']);
        return $result;
    }

    /**
     * @param array<mixed> $children
     * @return array<mixed>
     */
    private function getChildren(string|null $parentId, array $children): array
    {
        $data = [];
        foreach ($children as $child) {
            if (!isset($child['parentId'])) {
                continue;
            }
            $type = strtoupper($child['type']);
            if ($parentId == $child['parentId'] && $type != SchemaTypes::HEADLINE) {
                if ($type === SchemaTypes::DATE) {
                    $data[$child['title']] = [
                        "type" => $type,
                        "value" => PrintoutHelper::dateConverter($child['reportedValue'], 'd.m.Y')
                    ];
                } else {
                    $data[$child['title']] = [
                        "type" => $type,
                        "value" => $child['reportedValue']
                    ];
                }
            }
        }
        return $data;
    }

    /**
     * Splits for display logic in template.
     *
     * @return array[] an array with 2 keys: left and right. Each key contains an array of the mapped children.
     */
    /**
     * @param array<mixed> $mappedChildren
     * @return array<mixed>
     */
    private function splitMappedChildren(array $mappedChildren): array
    {
        $left = [];
        $right = [];
        $length = count($mappedChildren);
        $halfLength = (int)ceil($length / 2);
        $counter = 0;
        foreach ($mappedChildren as $key => $value) {
            if ($counter < $halfLength) {
                $left[$key] = $value;
            } else {
                $right[$key] = $value;
            }
            $counter++;
        }
        return [$left, $right];
    }

    /**
     * @return array<mixed>
     */
    private function downloadProjectData(string $projectNo, PrintoutCurl $curl): array
    {
        $partial = 'customerNo';
        return PrintoutHelper::downloadProject($projectNo, $partial, $curl);
    }

    /**
     * @return array<mixed>
     */
    private function downloadCustomer(string $knr, PrintoutCurl $curl): array
    {
        if ($knr == "") {
            return [];
        }
        $knr = intval($knr);
        return PrintoutHelper::downloadCustomer($knr, $curl);
    }

    /**
     * @param array<mixed> $document
     * @param array<mixed> $mappedChildren
     * @return array<mixed>
     */
    private function getFooter(array $document, array $mappedChildren): array
    {
        $data = [];
        $data['others'] = $mappedChildren['Sonstiges'] ?? '';
        $data['ort'] = $mappedChildren['Ort'] ?? '';

        $date = DateTime::createFromFormat('Y-m-d\TH:i:s+', $document['documentCreatedOn'])
            ->setTimezone(new DateTimeZone('UTC'))
            ->setTimezone(new DateTimeZone('Europe/Berlin'));
        $data['date'] = $date->format('d.m.Y');

        if (isset($mappedChildren['Unterschrift des Sachkundigen'])) {
            $data['leftSignature'] = '<img src="' . $mappedChildren['Unterschrift des Sachkundigen'] . '">';
        } else {
            $data['leftSignature'] = '';
        }
        if (isset($mappedChildren['Unterschrift des Betreibers'])) {
            $data['rightSignature'] = '<img src="' . $mappedChildren['Unterschrift des Sachkundigen'] . '">';
        } else {
            $data['rightSignature'] = '';
        }
        return $data;
    }

    /**
     * @param array<mixed> $redMarkers
     * @param array<mixed> $children
     * @param array<mixed> $mappedChildren
     * @return array<mixed> of strings of all children titles which are a safety deficiency depending on red-markers.json
     */
    private function getSafetyDeficienciesForRedMarkersJson(
        array $redMarkers, array $children, array $mappedChildren, string $schemaTitle): array
    {
        $result = [];
        // handle dynamic cases for the red markers
        if ($schemaTitle === "Rolltore") {
            $antrieb = $mappedChildren["Antrieb vorhanden?"] ?? "";
            if ($antrieb === "Nein" && ($mappedChildren['4.7 Fangvorr., auf die Welle, wirkend, separat angeordnet'] ?? null) === "n.i.o.") {
                $result[] = "4.7 Fangvorr., auf die Welle, wirkend, separat angeordnet";
            }
        } else if ($schemaTitle === "Sektionaltore") {
            $schnellEntriegelung = $mappedChildren['6.4 Schnellentriegelung'] ?? null;
            if (($schnellEntriegelung === "i.o." || $schnellEntriegelung === "n.i.o.") &&
                ($mappedChildren['5.5 Gewichtsausgleich, Federspannung'] ?? null) === "n.i.o.") {

                $result[] = "5.5 Gewichtsausgleich, Federspannung";
            }
        }
        foreach ($children as $heading => $child) {
            foreach ($redMarkers as $redMarker) {
                if ($redMarker['schema'] != $schemaTitle) {
                    continue;
                }
                foreach ($redMarker['children'] as $redMarkerChild) {
                    if (empty($child[$redMarkerChild['title']])) {
                        continue;
                    }
                    $value = $child[$redMarkerChild['title']]['value'];
                    if (!in_array($value, $redMarkerChild['columns'])) {
                        continue;
                    }
                    if ($redMarkerChild['isMessung'] ?? false) {
                        // remove dot for parity to other position titles in schema
                        $result[] = str_replace(". Messung", " Messung", $heading);
                    } else {
                        $result[] = $redMarkerChild['title'];
                    }
                }
            }
        }
        return $result;
    }

    /**
     * @param array<mixed> $documentChildren
     * @return array<mixed> of all set displayInside comments to combobox-tap when it has value: n.i.o.
     */
    private function getSafetyDeficienciesForDisplayInsideComments(array $documentChildren): array
    {
        $result = [];
        foreach ($documentChildren as $child) {
            if (strtoupper($child['type']) !== SchemaTypes::STRING || $child['title'] !== "Kommentar" || empty($child['displayInside'])) {
                continue;
            }
            $displayInside = $child['displayInside'];
            $displayedTo = null;
            foreach ($documentChildren as $displayInsideChild) {
                if ($displayInsideChild['id'] === $child['displayInside']) {
                    $displayedTo = $displayInsideChild;
                    break;
                }
            }
            if ($displayedTo === null) {
                die_with_response_code(Response::SERVER_ERROR, "Did not find displayInside child with id $displayInside");
            }
            $reportedValue = $displayedTo['reportedValue'];
            if ($reportedValue === "n.i.o.") {
                $result[] = $displayedTo['title'] . ": " . $child['reportedValue'];
            }
        }
        return $result;
    }

    /**
     * Extract photos from document children and format them for Playwright display
     *
     * Filters document children to find photo elements that should be displayed inside the document
     * (those with 'displayInside' set), then matches them with actual file data from mappedChildren
     * to create photo objects with file paths and titles.
     *
     * @param array<mixed> $document The document data containing children elements
     * @param array<mixed> $schemaPositions The schema positions used to resolve photo titles
     * @param array<mixed> $mappedChildren The mapped children data containing file URLs and paths from API calls
     * @return array<mixed> Array of photo objects with 'filePath' and 'title' keys
     */
    private function getPhotos(array $document, array $schemaPositions, array $mappedChildren): array
    {
        $photoChildren = [];
        $photoKeys = [];
        $photos = [];
        foreach ($document['children'] as $child) {
            $isPhoto = strtoupper($child['type']) === SchemaTypes::PHOTO;
            $isVisible = !empty($child['displayInside']);
            if ($isPhoto && $isVisible) {
                $photoChildren[] = $child;
            }
        }
        // this is a super smart way to detect type=photo entries
        foreach ($mappedChildren as $key => $value) {
            if (isset($value[0]['filePath'])) {
                $photoKeys[] = $key;
            }
        }
        foreach ($photoChildren as $index => $child) {
            if (!isset($photoKeys[$index])) {
                continue;
            }
            $photoKey = $photoKeys[$index];
            $title = $this->getPhotoTitle($schemaPositions, $child['displayInside']) ?? $photoKey;
            // Handle multiple files per photo
            foreach ($mappedChildren[$photoKey] as $file) {
                $photos[] = [
                    'filePath' => $file['filePath'] ?? $file['thumbPath'],
                    'title' => $title,
                ];
            }
        }
        return $photos;
    }

    /**
     * @param array<mixed> $schemaPositions
     */
    private function getPhotoTitle(array $schemaPositions, string $parentId): ?string
    {
        foreach ($schemaPositions as $pos) {
            if (($pos['id'] ?? null) === $parentId) {
                return $pos['title'] ?? null;
            }
        }
        die_with_response_code(Response::SERVER_ERROR, "Did not find parent element with id $parentId in schema positions");
    }

    private function getLastwechsel(string $schemaTitle): string
    {
        $common = [
            'Sektionaltore', 'Drehtore', 'Rolltore', 'Falttore', 'Sammelgaragentore ET500', 'Schiebetore',
            'Vertikal Schnellauftor', 'Brandschutztüren', 'Brandschutz Schiebetore', 'Förderanlagenabschlüsse'
        ];
        if (in_array($schemaTitle, $common)) {
            return 'Tor hat erhebliche Sicherheitsmängel';
        }
        if ($schemaTitle === 'Verladerampen') {
            return 'Verladerampe hat erhebliche Sicherheitsmängel';
        }
        if ($schemaTitle === 'Schranken') {
            return 'Schranke hat erhebliche Sicherheitsmängel';
        }
        return '';
    }

    /**
     * @return array<mixed> always has one element. If the API fails to return any equipment, it will crash
     */
    public function downloadEquipment(string $documentRelKey1, PrintoutCurl $curl): array
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get', "$base/v1/equipments?filter[id]=" . $documentRelKey1,
            [], PrintoutHelper::getHeadersForApiCalls());
        $equipments = json_decode($json, associative: true);
        if (empty($equipments)) {
            die_with_response_code(Response::BAD_REQUEST, "No equipment found for ID " . $documentRelKey1);
        }
        return $equipments[0];
    }
}