* {
    font-family: Arial, sans-serif;
    font-size: 11px;
}

html, body {
    width: 210mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

table {
    width: 100%;
    border-collapse: collapse;
}

.bold {
    font-weight: bold;
}

.container {
    width: 100%;
    margin: auto;
}

.header {
    width: 100%;
    display: flex;
    gap: 30mm;
}

.header img {
    width: 100%;
}

.header-right {
    text-align: right;
}

.sub-header,
.content {
    margin-top: 5px;
}

.sub-header-table {
    font-size: 12px;
}

.sub-header-table td:first-child {
    font-weight: bold;
    width: 60%;
}

.sub-header-table td:last-child {
    width: 40%;
}

.content {
    width: 100%;
    display: flex;
}

.content-left,
.content-right {
    width: 100%;
    padding: 0 10px;
}

.content-left-header td,
.content-right-header td {
    width: 100%;
    border-bottom: 1px solid black;
}

.content-table {
    margin: 14px 0;
    border: 1px solid black;
}

.content-table td {
    padding: 1px 2px;
    border: 1px solid black;
}

.content-table tr:first-child {
    font-weight: bold;
}

.footer {
    width: 100%;
    overflow: auto;
    font-weight: bold;
    display: flex;
}

.footer-left,
.footer-right {
    width: 100%;
    text-align: left;
    padding: 10px;
}

.footer-left table {
    border-collapse: collapse;
    width: auto;
}

.footer-left td {
    padding: 3px;
}

.footer-right-table {
    margin-top: 5px;
    border: 1px solid black;
    width: 100%;
}

.footer-right-table td {
    padding: 1px 2px;
    border: 1px solid black;
}

.footer-right-table td:last-child,
.footer-right-table td:nth-child(2) {
    width: 10%;
}

.footer-right-header {
    width: 100%;
}

.footer-right-header td {
    border-bottom: 1px solid black;
}

.sub-footer-table {
    margin-top: 20px;
    font-weight: normal;
}

.sub-footer-table td:first-child,
.sub-footer-table td:last-child {
    width: 45%;
}

.sub-footer-table tr:last-child td:first-child,
.sub-footer-table tr:last-child td:last-child {
    border-top: 1px solid black;
}

.sub-footer-table img {
    width: 100px;
    height: 80px;
}

.sub-footer p {
    text-align: left;
}

.photo-page {
    height: 270mm;
    display: flex;
    flex-direction: column;
    border: 1px solid lightgray;
    page-break-inside: avoid;
    page-break-before: always;
}

.photo-header {
    height: 20mm;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid lightgray;
}

.photo-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10mm;
}

.photo-title {
    margin-bottom: 5mm;
    text-align: left;
}

.photo-title h3 {
    margin: 0;
    font-size: 12px;
}

.photo-image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: left;
}

.photo-image {
    max-width: 100%;
    max-height: 230mm;
    object-fit: contain;
}

.photo-header-title {
    margin: 0;
    font-size: 13px;
}