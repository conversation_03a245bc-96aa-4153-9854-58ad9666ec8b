{"name": "Brandschutz Schiebetore", "description": "", "status": "active", "createdBy": "365", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Beschädigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Labyrinthschiene", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Dämpfer", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 Seilbefestigung, Seil", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "parentId": 1, "title": "1.5 Riemenbefestigung, Zahnriemen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 19, "displayInside": 18, "title": "Kommentar", "type": "string"}, {"id": 20, "displayInside": 18, "title": "Fotos", "type": "photo"}, {"id": 21, "displayInside": 18, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 22, "parentId": 1, "title": "1.6 Torlauf/ Geschwindigkeit", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 23, "displayInside": 22, "title": "Kommentar", "type": "string"}, {"id": 24, "displayInside": 22, "title": "Fotos", "type": "photo"}, {"id": 25, "displayInside": 22, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 26, "parentId": 1, "title": "1.7 Ankerplatte (Magnet)", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 27, "displayInside": 26, "title": "Kommentar", "type": "string"}, {"id": 28, "displayInside": 26, "title": "Fotos", "type": "photo"}, {"id": 29, "displayInside": 26, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 30, "parentId": 1, "title": "1.8 Schlupftür", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 31, "displayInside": 30, "title": "Kommentar", "type": "string"}, {"id": 32, "displayInside": 30, "title": "Fotos", "type": "photo"}, {"id": 33, "displayInside": 30, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 34, "parentId": 1, "title": "1.9 Obentürschließer", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 35, "displayInside": 34, "title": "Kommentar", "type": "string"}, {"id": 36, "displayInside": 34, "title": "Fotos", "type": "photo"}, {"id": 37, "displayInside": 34, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 38, "parentId": 1, "title": "1.10 Kennzeichnung Schließbereich", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 39, "displayInside": 38, "title": "Kommentar", "type": "string"}, {"id": 40, "displayInside": 38, "title": "Fotos", "type": "photo"}, {"id": 41, "displayInside": 38, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 42, "title": "2. Torkonstruk<PERSON>", "type": "headline"}, {"id": 43, "parentId": 42, "title": "2.1 Laufschiene", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 44, "displayInside": 43, "title": "Kommentar", "type": "string"}, {"id": 45, "displayInside": 43, "title": "Fotos", "type": "photo"}, {"id": 46, "displayInside": 43, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 47, "parentId": 42, "title": "2.2 Einlauftrichter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 48, "displayInside": 47, "title": "Kommentar", "type": "string"}, {"id": 49, "displayInside": 47, "title": "Fotos", "type": "photo"}, {"id": 50, "displayInside": 47, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 51, "parentId": 42, "title": "2.3 Führungsrollen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 52, "displayInside": 51, "title": "Kommentar", "type": "string"}, {"id": 53, "displayInside": 51, "title": "Fotos", "type": "photo"}, {"id": 54, "displayInside": 51, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 55, "parentId": 42, "title": "2.4 Brandschutzdichtstreifen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 56, "displayInside": 55, "title": "Kommentar", "type": "string"}, {"id": 57, "displayInside": 55, "title": "Fotos", "type": "photo"}, {"id": 58, "displayInside": 55, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 59, "parentId": 42, "title": "2.5 Gewichtskasten", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 60, "displayInside": 59, "title": "Kommentar", "type": "string"}, {"id": 61, "displayInside": 59, "title": "Fotos", "type": "photo"}, {"id": 62, "displayInside": 59, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 63, "parentId": 42, "title": "2.6 Bremsblock/ Umlenkrolle", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 64, "displayInside": 63, "title": "Kommentar", "type": "string"}, {"id": 65, "displayInside": 63, "title": "Fotos", "type": "photo"}, {"id": 66, "displayInside": 63, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 67, "title": "3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 68, "parentId": 67, "title": "3.1 Magnet", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 69, "displayInside": 68, "title": "Kommentar", "type": "string"}, {"id": 70, "displayInside": 68, "title": "Fotos", "type": "photo"}, {"id": 71, "displayInside": 68, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 72, "parentId": 67, "title": "3.2 Handauslösung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 73, "displayInside": 72, "title": "Kommentar", "type": "string"}, {"id": 74, "displayInside": 72, "title": "Fotos", "type": "photo"}, {"id": 75, "displayInside": 72, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 76, "parentId": 67, "title": "3.3 Rauchschalter/Betriebszeit", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 77, "displayInside": 76, "title": "Kommentar", "type": "string"}, {"id": 78, "displayInside": 76, "title": "Fotos", "type": "photo"}, {"id": 79, "displayInside": 76, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 80, "parentId": 67, "title": "3.4 Datum der Rauchschalter", "type": "date", "required": "false"}, {"id": 81, "parentId": 67, "title": "3.5 Anzahl der Rauchschalter", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 82, "displayInside": 81, "title": "Kommentar", "type": "string"}, {"id": 83, "displayInside": 81, "title": "Fotos", "type": "photo"}, {"id": 84, "displayInside": 81, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 85, "parentId": 67, "title": "3.6 Anordnung der Rauchschalter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 86, "displayInside": 85, "title": "Kommentar", "type": "string"}, {"id": 87, "displayInside": 85, "title": "Fotos", "type": "photo"}, {"id": 88, "displayInside": 85, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 89, "parentId": 67, "title": "3.7 Blitzleuchte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 90, "displayInside": 89, "title": "Kommentar", "type": "string"}, {"id": 91, "displayInside": 89, "title": "Fotos", "type": "photo"}, {"id": 92, "displayInside": 89, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 93, "parentId": 67, "title": "3.8 Signalhupe", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 94, "displayInside": 93, "title": "Kommentar", "type": "string"}, {"id": 95, "displayInside": 93, "title": "Fotos", "type": "photo"}, {"id": 96, "displayInside": 93, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 97, "parentId": 67, "title": "3.9 Zuleitung/ Hauptschalter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 98, "displayInside": 97, "title": "Kommentar", "type": "string"}, {"id": 99, "displayInside": 97, "title": "Fotos", "type": "photo"}, {"id": 100, "displayInside": 97, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 101, "title": "4. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 102, "parentId": 101, "title": "4.1 Antrieb", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 103, "displayInside": 102, "title": "Kommentar", "type": "string"}, {"id": 104, "displayInside": 102, "title": "Fotos", "type": "photo"}, {"id": 105, "displayInside": 102, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 106, "parentId": 101, "title": "4.2 Befestigung, Aufhängung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 107, "displayInside": 106, "title": "Kommentar", "type": "string"}, {"id": 108, "displayInside": 106, "title": "Fotos", "type": "photo"}, {"id": 109, "displayInside": 106, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 110, "parentId": 101, "title": "4.3 Antriebsschiene", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 111, "displayInside": 110, "title": "Kommentar", "type": "string"}, {"id": 112, "displayInside": 110, "title": "Fotos", "type": "photo"}, {"id": 113, "displayInside": 110, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 114, "parentId": 101, "title": "4.4 <PERSON><PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 115, "displayInside": 114, "title": "Kommentar", "type": "string"}, {"id": 116, "displayInside": 114, "title": "Fotos", "type": "photo"}, {"id": 117, "displayInside": 114, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 118, "parentId": 101, "title": "4.5 Entriegelung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 119, "displayInside": 118, "title": "Kommentar", "type": "string"}, {"id": 120, "displayInside": 118, "title": "Fotos", "type": "photo"}, {"id": 121, "displayInside": 118, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 122, "parentId": 101, "title": "4.6 <PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 123, "displayInside": 122, "title": "Kommentar", "type": "string"}, {"id": 124, "displayInside": 122, "title": "Fotos", "type": "photo"}, {"id": 125, "displayInside": 122, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 126, "title": "Reparaturleistungen", "type": "headline"}, {"id": 127, "parentId": 126, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 128, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 129}, {"id": 129, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 128, "nextSiblingId": 130}, {"id": 130, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 129, "nextSiblingId": 131}, {"id": 131, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 130, "nextSiblingId": 132}, {"id": 132, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 131, "nextSiblingId": 133}, {"id": 133, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 132, "nextSiblingId": 134}, {"id": 134, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 133, "nextSiblingId": 135}, {"id": 135, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 134, "nextSiblingId": 136}, {"id": 136, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 135, "nextSiblingId": 137}, {"id": 137, "parentId": 127, "title": "Arbeit", "type": "string", "prevSiblingId": 136, "nextSiblingId": null}, {"id": 138, "parentId": 127, "title": "von", "type": "time"}, {"id": 139, "parentId": 127, "title": "bis", "type": "time"}, {"id": 140, "parentId": 127, "title": "Ersatzteile", "type": "measurement"}, {"id": 141, "parentId": 127, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 142, "parentId": 127, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 143, "parentId": 126, "title": "Sonstiges", "type": "string"}, {"id": 144, "parentId": 126, "title": "Ort", "type": "string"}, {"id": 145, "parentId": 126, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 146, "parentId": 126, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}