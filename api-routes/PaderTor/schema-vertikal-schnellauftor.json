{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Befestigung Schaltschrank", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Endlagen richtig eingestellt", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 <PERSON><PERSON><PERSON><PERSON><PERSON>, Steckverbindung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 externe Bedienelemente", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "parentId": 1, "title": "1.5 <PERSON><PERSON><PERSON>", "type": "int"}, {"id": 19, "displayInside": 18, "title": "Kommentar", "type": "string"}, {"id": 20, "displayInside": 18, "title": "Fotos", "type": "photo"}, {"id": 21, "displayInside": 18, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 22, "title": "2. Sicherheitseinrichtung", "type": "headline"}, {"id": 23, "parentId": 22, "title": "2.1 <PERSON>chtschranke, Lichtgitter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 24, "displayInside": 23, "title": "Kommentar", "type": "string"}, {"id": 25, "displayInside": 23, "title": "Fotos", "type": "photo"}, {"id": 26, "displayInside": 23, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 27, "parentId": 22, "title": "2.2 Wellen - Antriebsverkl. bei lichter Höhe bis 2,5 m", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 28, "displayInside": 27, "title": "Kommentar", "type": "string"}, {"id": 29, "displayInside": 27, "title": "Fotos", "type": "photo"}, {"id": 30, "displayInside": 27, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 31, "parentId": 22, "title": "2.3 Schließkantensicherung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 32, "displayInside": 31, "title": "Kommentar", "type": "string"}, {"id": 33, "displayInside": 31, "title": "Fotos", "type": "photo"}, {"id": 34, "displayInside": 31, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 35, "parentId": 22, "title": "2.4 Notabschalteinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 36, "displayInside": 35, "title": "Kommentar", "type": "string"}, {"id": 37, "displayInside": 35, "title": "Fotos", "type": "photo"}, {"id": 38, "displayInside": 35, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 39, "parentId": 22, "title": "2.5 Crash-<PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 40, "displayInside": 39, "title": "Kommentar", "type": "string"}, {"id": 41, "displayInside": 39, "title": "Fotos", "type": "photo"}, {"id": 42, "displayInside": 39, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 43, "parentId": 22, "title": "2.6 Kennzeichnung Rettungsweg", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 44, "displayInside": 43, "title": "Kommentar", "type": "string"}, {"id": 45, "displayInside": 43, "title": "Fotos", "type": "photo"}, {"id": 46, "displayInside": 43, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 47, "parentId": 22, "title": "2.7 USV, Akku, Datum", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 48, "displayInside": 47, "title": "Kommentar", "type": "string"}, {"id": 49, "displayInside": 47, "title": "Fotos", "type": "photo"}, {"id": 50, "displayInside": 47, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 51, "parentId": 22, "title": "2.8 Maße Batterieanschluss in mm", "type": "float"}, {"id": 52, "displayInside": 51, "title": "Kommentar", "type": "string"}, {"id": 53, "displayInside": 51, "title": "Fotos", "type": "photo"}, {"id": 54, "displayInside": 51, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 55, "title": "3. Betriebskräftemessung", "type": "headline"}, {"id": 56, "parentId": 55, "title": "1 Grenzwert", "type": "headline"}, {"id": 57, "parentId": 56, "title": "F_dyn", "type": "int"}, {"id": 58, "parentId": 56, "title": "t_dyn", "type": "int"}, {"id": 59, "parentId": 56, "title": "F_End", "type": "int"}, {"id": 60, "parentId": 56, "title": "0. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 61, "parentId": 55, "title": "3.1.1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 62, "parentId": 61, "title": "F_dyn", "type": "int"}, {"id": 63, "parentId": 61, "title": "t_dyn", "type": "int"}, {"id": 64, "parentId": 61, "title": "F_End", "type": "int"}, {"id": 65, "parentId": 61, "title": "1. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 66, "parentId": 55, "title": "3.2.2. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 67, "parentId": 66, "title": "F_dyn", "type": "int"}, {"id": 68, "parentId": 66, "title": "t_dyn", "type": "int"}, {"id": 69, "parentId": 66, "title": "F_End", "type": "int"}, {"id": 70, "parentId": 66, "title": "2. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 71, "parentId": 55, "title": "3.3.3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 72, "parentId": 71, "title": "F_dyn", "type": "int"}, {"id": 73, "parentId": 71, "title": "t_dyn", "type": "int"}, {"id": 74, "parentId": 71, "title": "F_End", "type": "int"}, {"id": 75, "parentId": 71, "title": "3. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 76, "title": "4. <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 77, "parentId": 76, "title": "4.1 Behang, Beschädigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 78, "displayInside": 77, "title": "Kommentar", "type": "string"}, {"id": 79, "displayInside": 77, "title": "Fotos", "type": "photo"}, {"id": 80, "displayInside": 77, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 81, "parentId": 76, "title": "4.2 Kederbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 82, "displayInside": 81, "title": "Kommentar", "type": "string"}, {"id": 83, "displayInside": 81, "title": "Fotos", "type": "photo"}, {"id": 84, "displayInside": 81, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 85, "parentId": 76, "title": "4.3 Unterteil, Beschädigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 86, "displayInside": 85, "title": "Kommentar", "type": "string"}, {"id": 87, "displayInside": 85, "title": "Fotos", "type": "photo"}, {"id": 88, "displayInside": 85, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 89, "title": "5. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 90, "parentId": 89, "title": "5.1 Befestigung, Zustand", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 91, "displayInside": 90, "title": "Kommentar", "type": "string"}, {"id": 92, "displayInside": 90, "title": "Fotos", "type": "photo"}, {"id": 93, "displayInside": 90, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 94, "parentId": 89, "title": "5.2 <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 95, "displayInside": 94, "title": "Kommentar", "type": "string"}, {"id": 96, "displayInside": 94, "title": "Fotos", "type": "photo"}, {"id": 97, "displayInside": 94, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 98, "parentId": 89, "title": "5.3 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 99, "displayInside": 98, "title": "Kommentar", "type": "string"}, {"id": 100, "displayInside": 98, "title": "Fotos", "type": "photo"}, {"id": 101, "displayInside": 98, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 102, "parentId": 89, "title": "5.4 <PERSON>ug<PERSON><PERSON>, Umlenkrolle", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 103, "displayInside": 102, "title": "Kommentar", "type": "string"}, {"id": 104, "displayInside": 102, "title": "Fotos", "type": "photo"}, {"id": 105, "displayInside": 102, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 106, "parentId": 89, "title": "5.5 Energieket<PERSON> <PERSON> Kabel, Wendell<PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 107, "displayInside": 106, "title": "Kommentar", "type": "string"}, {"id": 108, "displayInside": 106, "title": "Fotos", "type": "photo"}, {"id": 109, "displayInside": 106, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 110, "title": "6. <PERSON><PERSON>", "type": "headline"}, {"id": 111, "parentId": 110, "title": "6.1 Schweißnähte des Wellenzapfens, Gurtscheibe", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 112, "displayInside": 111, "title": "Kommentar", "type": "string"}, {"id": 113, "displayInside": 111, "title": "Fotos", "type": "photo"}, {"id": 114, "displayInside": 111, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 115, "parentId": 110, "title": "6.2 Befestigung der Konsolen, Zustand", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 116, "displayInside": 115, "title": "Kommentar", "type": "string"}, {"id": 117, "displayInside": 115, "title": "Fotos", "type": "photo"}, {"id": 118, "displayInside": 115, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 119, "parentId": 110, "title": "6.3 Sicherung gegen Achsialverschiebung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 120, "displayInside": 119, "title": "Kommentar", "type": "string"}, {"id": 121, "displayInside": 119, "title": "Fotos", "type": "photo"}, {"id": 122, "displayInside": 119, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 123, "parentId": 110, "title": "6.4 Gewichtsausgleich", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 124, "displayInside": 123, "title": "Kommentar", "type": "string"}, {"id": 125, "displayInside": 123, "title": "Fotos", "type": "photo"}, {"id": 126, "displayInside": 123, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 127, "parentId": 110, "title": "6.5 Behangbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 128, "displayInside": 127, "title": "Kommentar", "type": "string"}, {"id": 129, "displayInside": 127, "title": "Fotos", "type": "photo"}, {"id": 130, "displayInside": 127, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 131, "title": "7. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 132, "parentId": 131, "title": "7.1 Antriebs - und Konsolenbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 133, "displayInside": 132, "title": "Kommentar", "type": "string"}, {"id": 134, "displayInside": 132, "title": "Fotos", "type": "photo"}, {"id": 135, "displayInside": 132, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 136, "parentId": 131, "title": "7.2 Geräusche und Dichtheit der Anlage", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 137, "displayInside": 136, "title": "Kommentar", "type": "string"}, {"id": 138, "displayInside": 136, "title": "Fotos", "type": "photo"}, {"id": 139, "displayInside": 136, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 140, "parentId": 131, "title": "7.3 Nothandbetätigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 141, "displayInside": 140, "title": "Kommentar", "type": "string"}, {"id": 142, "displayInside": 140, "title": "Fotos", "type": "photo"}, {"id": 143, "displayInside": 140, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 144, "parentId": 131, "title": "7.4 Bremswirkung und Nachlauf", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 145, "displayInside": 144, "title": "Kommentar", "type": "string"}, {"id": 146, "displayInside": 144, "title": "Fotos", "type": "photo"}, {"id": 147, "displayInside": 144, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 148, "title": "Reparaturleistungen", "type": "headline"}, {"id": 149, "parentId": 148, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 150, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 151}, {"id": 151, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 150, "nextSiblingId": 152}, {"id": 152, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 151, "nextSiblingId": 153}, {"id": 153, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 152, "nextSiblingId": 154}, {"id": 154, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 153, "nextSiblingId": 155}, {"id": 155, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 154, "nextSiblingId": 156}, {"id": 156, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 155, "nextSiblingId": 157}, {"id": 157, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 156, "nextSiblingId": 158}, {"id": 158, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 157, "nextSiblingId": 159}, {"id": 159, "parentId": 149, "title": "Arbeit", "type": "string", "prevSiblingId": 158, "nextSiblingId": null}, {"id": 160, "parentId": 149, "title": "von", "type": "time"}, {"id": 161, "parentId": 149, "title": "bis", "type": "time"}, {"id": 162, "parentId": 149, "title": "Ersatzteile", "type": "measurement"}, {"id": 163, "parentId": 149, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 164, "parentId": 149, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 165, "parentId": 148, "title": "Sonstiges", "type": "string"}, {"id": 166, "parentId": 148, "title": "Ort", "type": "string"}, {"id": 167, "parentId": 148, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 168, "parentId": 148, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}