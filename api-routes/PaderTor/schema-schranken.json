{"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Antriebs - und Konsolenbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Geräusche und Dichtheit der Anlage", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Nothandbetätigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 Bremswirkung und Nachlauf", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 19, "parentId": 18, "title": "2.1 Befestigung Schaltschrank", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 20, "displayInside": 19, "title": "Kommentar", "type": "string"}, {"id": 21, "displayInside": 19, "title": "Fotos", "type": "photo"}, {"id": 22, "displayInside": 19, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 23, "parentId": 18, "title": "2.2 Schaltplan Funktionsbeschreibung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 24, "displayInside": 23, "title": "Kommentar", "type": "string"}, {"id": 25, "displayInside": 23, "title": "Fotos", "type": "photo"}, {"id": 26, "displayInside": 23, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 27, "parentId": 18, "title": "2.3 Endlagen richtig eingestellt", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 28, "displayInside": 27, "title": "Kommentar", "type": "string"}, {"id": 29, "displayInside": 27, "title": "Fotos", "type": "photo"}, {"id": 30, "displayInside": 27, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 31, "parentId": 18, "title": "2.4 Notabschalteinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 32, "displayInside": 31, "title": "Kommentar", "type": "string"}, {"id": 33, "displayInside": 31, "title": "Fotos", "type": "photo"}, {"id": 34, "displayInside": 31, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 35, "parentId": 18, "title": "2.5 <PERSON><PERSON><PERSON>", "type": "int"}, {"id": 36, "displayInside": 35, "title": "Kommentar", "type": "string"}, {"id": 37, "displayInside": 35, "title": "Fotos", "type": "photo"}, {"id": 38, "displayInside": 35, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 39, "title": "3. Sicherheitseinrichtung", "type": "headline"}, {"id": 40, "parentId": 39, "title": "3.1 Signalgeber, Zustand, Funktion", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 41, "displayInside": 40, "title": "Kommentar", "type": "string"}, {"id": 42, "displayInside": 40, "title": "Fotos", "type": "photo"}, {"id": 43, "displayInside": 40, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 44, "parentId": 39, "title": "3.2 Signalübertragung, <PERSON>ust<PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 45, "displayInside": 44, "title": "Kommentar", "type": "string"}, {"id": 46, "displayInside": 44, "title": "Fotos", "type": "photo"}, {"id": 47, "displayInside": 44, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 48, "parentId": 39, "title": "3.3 <PERSON><PERSON>wer<PERSON>g, Funk<PERSON>, Einstellung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 49, "displayInside": 48, "title": "Kommentar", "type": "string"}, {"id": 50, "displayInside": 48, "title": "Fotos", "type": "photo"}, {"id": 51, "displayInside": 48, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 52, "parentId": 39, "title": "3.4 <PERSON><PERSON><PERSON><PERSON><PERSON>, Steckverbindung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 53, "displayInside": 52, "title": "Kommentar", "type": "string"}, {"id": 54, "displayInside": 52, "title": "Fotos", "type": "photo"}, {"id": 55, "displayInside": 52, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 56, "parentId": 39, "title": "3.5 Signalleuchte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 57, "displayInside": 56, "title": "Kommentar", "type": "string"}, {"id": 58, "displayInside": 56, "title": "Fotos", "type": "photo"}, {"id": 59, "displayInside": 56, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 60, "title": "4. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 61, "parentId": 60, "title": "4.1 <PERSON><PERSON><PERSON>, Verschleiß", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 62, "displayInside": 61, "title": "Kommentar", "type": "string"}, {"id": 63, "displayInside": 61, "title": "Fotos", "type": "photo"}, {"id": 64, "displayInside": 61, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 65, "parentId": 60, "title": "4.2 Aufhängung, Befestigungsschrauben", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 66, "displayInside": 65, "title": "Kommentar", "type": "string"}, {"id": 67, "displayInside": 65, "title": "Fotos", "type": "photo"}, {"id": 68, "displayInside": 65, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 69, "parentId": 60, "title": "4.3 <PERSON><PERSON>igung, <PERSON><PERSON><PERSON> (Deformation)", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 70, "displayInside": 69, "title": "Kommentar", "type": "string"}, {"id": 71, "displayInside": 69, "title": "Fotos", "type": "photo"}, {"id": 72, "displayInside": 69, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 73, "parentId": 60, "title": "4.4 Einlauftrichter/Stützarm", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 74, "displayInside": 73, "title": "Kommentar", "type": "string"}, {"id": 75, "displayInside": 73, "title": "Fotos", "type": "photo"}, {"id": 76, "displayInside": 73, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 77, "title": "Reparaturleistungen", "type": "headline"}, {"id": 78, "parentId": 77, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 79, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 80}, {"id": 80, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 79, "nextSiblingId": 81}, {"id": 81, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 80, "nextSiblingId": 82}, {"id": 82, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 81, "nextSiblingId": 83}, {"id": 83, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 82, "nextSiblingId": 84}, {"id": 84, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 83, "nextSiblingId": 85}, {"id": 85, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 84, "nextSiblingId": 86}, {"id": 86, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 85, "nextSiblingId": 87}, {"id": 87, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 86, "nextSiblingId": 88}, {"id": 88, "parentId": 78, "title": "Arbeit", "type": "string", "prevSiblingId": 87, "nextSiblingId": null}, {"id": 89, "parentId": 78, "title": "von", "type": "time"}, {"id": 90, "parentId": 78, "title": "bis", "type": "time"}, {"id": 91, "parentId": 78, "title": "Ersatzteile", "type": "measurement"}, {"id": 92, "parentId": 78, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 93, "parentId": 78, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 94, "parentId": 77, "title": "Sonstiges", "type": "string"}, {"id": 95, "parentId": 77, "title": "Ort", "type": "string"}, {"id": 96, "parentId": 77, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 97, "parentId": 77, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}