{"name": "Sc<PERSON>eb<PERSON><PERSON>", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Antriebs - und Konsolenbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Geräusche und Dichtheit der Anlage", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Nothandbetätigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 Bremswirkung und Nachlauf", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 19, "parentId": 18, "title": "2.1 Befestigung Schaltschrank", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 20, "displayInside": 19, "title": "Kommentar", "type": "string"}, {"id": 21, "displayInside": 19, "title": "Fotos", "type": "photo"}, {"id": 22, "displayInside": 19, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 23, "parentId": 18, "title": "2.2 Endlagen richtig eingestellt", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 24, "displayInside": 23, "title": "Kommentar", "type": "string"}, {"id": 25, "displayInside": 23, "title": "Fotos", "type": "photo"}, {"id": 26, "displayInside": 23, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 27, "parentId": 18, "title": "2.3 <PERSON><PERSON><PERSON><PERSON><PERSON>, Steckverbindung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 28, "displayInside": 27, "title": "Kommentar", "type": "string"}, {"id": 29, "displayInside": 27, "title": "Fotos", "type": "photo"}, {"id": 30, "displayInside": 27, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 31, "parentId": 18, "title": "2.4 <PERSON><PERSON><PERSON>", "type": "int"}, {"id": 32, "displayInside": 31, "title": "Kommentar", "type": "string"}, {"id": 33, "displayInside": 31, "title": "Fotos", "type": "photo"}, {"id": 34, "displayInside": 31, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 35, "title": "3. Sicherheitseinrichtung", "type": "headline"}, {"id": 36, "parentId": 35, "title": "3.1 Signalgeber, Zustand, Funktion", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 37, "displayInside": 36, "title": "Kommentar", "type": "string"}, {"id": 38, "displayInside": 36, "title": "Fotos", "type": "photo"}, {"id": 39, "displayInside": 36, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 40, "parentId": 35, "title": "3.2 Lichtschranken (Sicherheit, Sachschutz)", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 41, "displayInside": 40, "title": "Kommentar", "type": "string"}, {"id": 42, "displayInside": 40, "title": "Fotos", "type": "photo"}, {"id": 43, "displayInside": 40, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 44, "parentId": 35, "title": "3.3 Notabschalteinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 45, "displayInside": 44, "title": "Kommentar", "type": "string"}, {"id": 46, "displayInside": 44, "title": "Fotos", "type": "photo"}, {"id": 47, "displayInside": 44, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 48, "parentId": 35, "title": "3.4 Anordnung der Schaltleisten", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 49, "displayInside": 48, "title": "Kommentar", "type": "string"}, {"id": 50, "displayInside": 48, "title": "Fotos", "type": "photo"}, {"id": 51, "displayInside": 48, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 52, "parentId": 35, "title": "3.5 Zaundurchgreifschutz", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 53, "displayInside": 52, "title": "Kommentar", "type": "string"}, {"id": 54, "displayInside": 52, "title": "Fotos", "type": "photo"}, {"id": 55, "displayInside": 52, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 56, "title": "4. <PERSON><PERSON><PERSON><PERSON><PERSON> / Führungsschienen / Ra<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 57, "parentId": 56, "title": "4.1 <PERSON><PERSON><PERSON>, Verschleiß", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 58, "displayInside": 57, "title": "Kommentar", "type": "string"}, {"id": 59, "displayInside": 57, "title": "Fotos", "type": "photo"}, {"id": 60, "displayInside": 57, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 61, "parentId": 56, "title": "4.2 Seitenarretierung, Obere Führungsrollen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 62, "displayInside": 61, "title": "Kommentar", "type": "string"}, {"id": 63, "displayInside": 61, "title": "Fotos", "type": "photo"}, {"id": 64, "displayInside": 61, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 65, "parentId": 56, "title": "4.3 Au<PERSON>hängung, Befestigungsschrauben", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 66, "displayInside": 65, "title": "Kommentar", "type": "string"}, {"id": 67, "displayInside": 65, "title": "Fotos", "type": "photo"}, {"id": 68, "displayInside": 65, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 69, "parentId": 56, "title": "4.4 <PERSON><PERSON>igung, <PERSON><PERSON><PERSON> (Deformation)", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 70, "displayInside": 69, "title": "Kommentar", "type": "string"}, {"id": 71, "displayInside": 69, "title": "Fotos", "type": "photo"}, {"id": 72, "displayInside": 69, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 73, "parentId": 56, "title": "4.5 Einlauftrichter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 74, "displayInside": 73, "title": "Kommentar", "type": "string"}, {"id": 75, "displayInside": 73, "title": "Fotos", "type": "photo"}, {"id": 76, "displayInside": 73, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 77, "parentId": 56, "title": "4.6 Rundumleuchte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 78, "displayInside": 77, "title": "Kommentar", "type": "string"}, {"id": 79, "displayInside": 77, "title": "Fotos", "type": "photo"}, {"id": 80, "displayInside": 77, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 81, "title": "5. Betriebskräftemessung", "type": "headline"}, {"id": 82, "parentId": 81, "title": "1 Grenzwert", "type": "headline"}, {"id": 83, "parentId": 82, "title": "F_dyn", "type": "int"}, {"id": 84, "parentId": 82, "title": "t_dyn", "type": "int"}, {"id": 85, "parentId": 82, "title": "F_End", "type": "int"}, {"id": 86, "parentId": 82, "title": "0. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 87, "parentId": 81, "title": "5.1.1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 88, "parentId": 87, "title": "F_dyn", "type": "int"}, {"id": 89, "parentId": 87, "title": "t_dyn", "type": "int"}, {"id": 90, "parentId": 87, "title": "F_End", "type": "int"}, {"id": 91, "parentId": 87, "title": "1. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 92, "parentId": 81, "title": "5.2.2. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 93, "parentId": 92, "title": "F_dyn", "type": "int"}, {"id": 94, "parentId": 92, "title": "t_dyn", "type": "int"}, {"id": 95, "parentId": 92, "title": "F_End", "type": "int"}, {"id": 96, "parentId": 92, "title": "2. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 97, "parentId": 81, "title": "5.3.3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 98, "parentId": 97, "title": "F_dyn", "type": "int"}, {"id": 99, "parentId": 97, "title": "t_dyn", "type": "int"}, {"id": 100, "parentId": 97, "title": "F_End", "type": "int"}, {"id": 101, "parentId": 97, "title": "3. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 102, "title": "Reparaturleistungen", "type": "headline"}, {"id": 103, "parentId": 102, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 104, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 105}, {"id": 105, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 104, "nextSiblingId": 106}, {"id": 106, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 105, "nextSiblingId": 107}, {"id": 107, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 106, "nextSiblingId": 108}, {"id": 108, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 107, "nextSiblingId": 109}, {"id": 109, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 108, "nextSiblingId": 110}, {"id": 110, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 109, "nextSiblingId": 111}, {"id": 111, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 110, "nextSiblingId": 112}, {"id": 112, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 111, "nextSiblingId": 113}, {"id": 113, "parentId": 103, "title": "Arbeit", "type": "string", "prevSiblingId": 112, "nextSiblingId": null}, {"id": 114, "parentId": 103, "title": "von", "type": "time"}, {"id": 115, "parentId": 103, "title": "bis", "type": "time"}, {"id": 116, "parentId": 103, "title": "Ersatzteile", "type": "measurement"}, {"id": 117, "parentId": 103, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 118, "parentId": 103, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 119, "parentId": 102, "title": "Sonstiges", "type": "string"}, {"id": 120, "parentId": 102, "title": "Ort", "type": "string"}, {"id": 121, "parentId": 102, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 122, "parentId": 102, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}