{"name": "Falttore", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Antriebs - und Konsolenbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Geräusche und Dichtheit der Anlage", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Nothandbetätigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 15, "parentId": 14, "title": "2.1 Befestigung Schaltschrank", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 16, "displayInside": 15, "title": "Kommentar", "type": "string"}, {"id": 17, "displayInside": 15, "title": "Fotos", "type": "photo"}, {"id": 18, "displayInside": 15, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 19, "parentId": 14, "title": "2.2 Endlagen richtig eingestellt", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 20, "displayInside": 19, "title": "Kommentar", "type": "string"}, {"id": 21, "displayInside": 19, "title": "Fotos", "type": "photo"}, {"id": 22, "displayInside": 19, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 23, "parentId": 14, "title": "2.3 <PERSON><PERSON><PERSON><PERSON><PERSON>, Steckverbindung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 24, "displayInside": 23, "title": "Kommentar", "type": "string"}, {"id": 25, "displayInside": 23, "title": "Fotos", "type": "photo"}, {"id": 26, "displayInside": 23, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 27, "parentId": 14, "title": "2.4 <PERSON><PERSON><PERSON>", "type": "int"}, {"id": 28, "displayInside": 27, "title": "Kommentar", "type": "string"}, {"id": 29, "displayInside": 27, "title": "Fotos", "type": "photo"}, {"id": 30, "displayInside": 27, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 31, "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 32, "parentId": 31, "title": "3.1 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 33, "displayInside": 32, "title": "Kommentar", "type": "string"}, {"id": 34, "displayInside": 32, "title": "Fotos", "type": "photo"}, {"id": 35, "displayInside": 32, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 36, "parentId": 31, "title": "3.2 Aufhängung, Befestigungsmittel", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 37, "displayInside": 36, "title": "Kommentar", "type": "string"}, {"id": 38, "displayInside": 36, "title": "Fotos", "type": "photo"}, {"id": 39, "displayInside": 36, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 40, "parentId": 31, "title": "3.3 Torflügelverschluss", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 41, "displayInside": 40, "title": "Kommentar", "type": "string"}, {"id": 42, "displayInside": 40, "title": "Fotos", "type": "photo"}, {"id": 43, "displayInside": 40, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 44, "parentId": 31, "title": "3.4 Zargendichtung / Rundum", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 45, "displayInside": 44, "title": "Kommentar", "type": "string"}, {"id": 46, "displayInside": 44, "title": "Fotos", "type": "photo"}, {"id": 47, "displayInside": 44, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 48, "parentId": 31, "title": "3.5 Schmierung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 49, "displayInside": 48, "title": "Kommentar", "type": "string"}, {"id": 50, "displayInside": 48, "title": "Fotos", "type": "photo"}, {"id": 51, "displayInside": 48, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 52, "title": "4. Sicherheitseinrichtung", "type": "headline"}, {"id": 53, "parentId": 52, "title": "4.1 Zustand der Sensoren und Kabel", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 54, "displayInside": 53, "title": "Kommentar", "type": "string"}, {"id": 55, "displayInside": 53, "title": "Fotos", "type": "photo"}, {"id": 56, "displayInside": 53, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 57, "parentId": 52, "title": "4.2 Funktion und Einstellung der Sicherheitseinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 58, "displayInside": 57, "title": "Kommentar", "type": "string"}, {"id": 59, "displayInside": 57, "title": "Fotos", "type": "photo"}, {"id": 60, "displayInside": 57, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 61, "parentId": 52, "title": "4.3 Zustand SK<PERSON>, Wendelleitung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 62, "displayInside": 61, "title": "Kommentar", "type": "string"}, {"id": 63, "displayInside": 61, "title": "Fotos", "type": "photo"}, {"id": 64, "displayInside": 61, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 65, "parentId": 52, "title": "4.4 Notabschalteinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 66, "displayInside": 65, "title": "Kommentar", "type": "string"}, {"id": 67, "displayInside": 65, "title": "Fotos", "type": "photo"}, {"id": 68, "displayInside": 65, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 69, "parentId": 52, "title": "4.5 Signalleuchte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 70, "displayInside": 69, "title": "Kommentar", "type": "string"}, {"id": 71, "displayInside": 69, "title": "Fotos", "type": "photo"}, {"id": 72, "displayInside": 69, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 73, "title": "5. Betriebskräftemessung", "type": "headline"}, {"id": 74, "parentId": 73, "title": "1 Grenzwert", "type": "headline"}, {"id": 75, "parentId": 74, "title": "F_dyn", "type": "int"}, {"id": 76, "parentId": 74, "title": "t_dyn", "type": "int"}, {"id": 77, "parentId": 74, "title": "F_End", "type": "int"}, {"id": 78, "parentId": 74, "title": "0. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 79, "parentId": 73, "title": "5.1.1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 80, "parentId": 79, "title": "F_dyn", "type": "int"}, {"id": 81, "parentId": 79, "title": "t_dyn", "type": "int"}, {"id": 82, "parentId": 79, "title": "F_End", "type": "int"}, {"id": 83, "parentId": 79, "title": "1. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 84, "parentId": 73, "title": "5.2.2. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 85, "parentId": 84, "title": "F_dyn", "type": "int"}, {"id": 86, "parentId": 84, "title": "t_dyn", "type": "int"}, {"id": 87, "parentId": 84, "title": "F_End", "type": "int"}, {"id": 88, "parentId": 84, "title": "2. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 89, "parentId": 73, "title": "5.3.3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 90, "parentId": 89, "title": "F_dyn", "type": "int"}, {"id": 91, "parentId": 89, "title": "t_dyn", "type": "int"}, {"id": 92, "parentId": 89, "title": "F_End", "type": "int"}, {"id": 93, "parentId": 89, "title": "3. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 94, "title": "Reparaturleistungen", "type": "headline"}, {"id": 95, "parentId": 94, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 96, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 97}, {"id": 97, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 96, "nextSiblingId": 98}, {"id": 98, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 97, "nextSiblingId": 99}, {"id": 99, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 98, "nextSiblingId": 100}, {"id": 100, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 99, "nextSiblingId": 101}, {"id": 101, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 100, "nextSiblingId": 102}, {"id": 102, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 101, "nextSiblingId": 103}, {"id": 103, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 102, "nextSiblingId": 104}, {"id": 104, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 103, "nextSiblingId": 105}, {"id": 105, "parentId": 95, "title": "Arbeit", "type": "string", "prevSiblingId": 104, "nextSiblingId": null}, {"id": 106, "parentId": 95, "title": "von", "type": "time"}, {"id": 107, "parentId": 95, "title": "bis", "type": "time"}, {"id": 108, "parentId": 95, "title": "Ersatzteile", "type": "measurement"}, {"id": 109, "parentId": 95, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 110, "parentId": 95, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 111, "parentId": 94, "title": "Sonstiges", "type": "string"}, {"id": 112, "parentId": 94, "title": "Ort", "type": "string"}, {"id": 113, "parentId": 94, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 114, "parentId": 94, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}