{"name": "<PERSON><PERSON><PERSON>", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Antriebs - und Konsolenbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Geräusche und Dichtheit der Anlage", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Nothandbetätigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 Bremswirkung und Nachlauf", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 19, "parentId": 18, "title": "2.1 Befestigung Schaltschrank", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 20, "displayInside": 19, "title": "Kommentar", "type": "string"}, {"id": 21, "displayInside": 19, "title": "Fotos", "type": "photo"}, {"id": 22, "displayInside": 19, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 23, "parentId": 18, "title": "2.2 Endlagen richtig eingestellt", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 24, "displayInside": 23, "title": "Kommentar", "type": "string"}, {"id": 25, "displayInside": 23, "title": "Fotos", "type": "photo"}, {"id": 26, "displayInside": 23, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 27, "parentId": 18, "title": "2.3 <PERSON><PERSON><PERSON><PERSON><PERSON>, Steckverbindung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 28, "displayInside": 27, "title": "Kommentar", "type": "string"}, {"id": 29, "displayInside": 27, "title": "Fotos", "type": "photo"}, {"id": 30, "displayInside": 27, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 31, "parentId": 18, "title": "2.4 <PERSON><PERSON><PERSON>", "type": "int"}, {"id": 32, "displayInside": 31, "title": "Kommentar", "type": "string"}, {"id": 33, "displayInside": 31, "title": "Fotos", "type": "photo"}, {"id": 34, "displayInside": 31, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 35, "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 36, "parentId": 35, "title": "3.1 <PERSON><PERSON><PERSON>, Verschleiss", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 37, "displayInside": 36, "title": "Kommentar", "type": "string"}, {"id": 38, "displayInside": 36, "title": "Fotos", "type": "photo"}, {"id": 39, "displayInside": 36, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 40, "parentId": 35, "title": "3.2 Aufhängung, Befestigungsmittel", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 41, "displayInside": 40, "title": "Kommentar", "type": "string"}, {"id": 42, "displayInside": 40, "title": "Fotos", "type": "photo"}, {"id": 43, "displayInside": 40, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 44, "parentId": 35, "title": "3.3 Torflügelverschluss", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 45, "displayInside": 44, "title": "Kommentar", "type": "string"}, {"id": 46, "displayInside": 44, "title": "Fotos", "type": "photo"}, {"id": 47, "displayInside": 44, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 48, "title": "4. Sicherheitseinrichtung", "type": "headline"}, {"id": 49, "parentId": 48, "title": "4.1 Signalgeber, Zustand, Funktion", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 50, "displayInside": 49, "title": "Kommentar", "type": "string"}, {"id": 51, "displayInside": 49, "title": "Fotos", "type": "photo"}, {"id": 52, "displayInside": 49, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 53, "parentId": 48, "title": "4.2 Lichtschranken (Sicherheit, Sachschutz)", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 54, "displayInside": 53, "title": "Kommentar", "type": "string"}, {"id": 55, "displayInside": 53, "title": "Fotos", "type": "photo"}, {"id": 56, "displayInside": 53, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 57, "parentId": 48, "title": "4.3 Notabschalteinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 58, "displayInside": 57, "title": "Kommentar", "type": "string"}, {"id": 59, "displayInside": 57, "title": "Fotos", "type": "photo"}, {"id": 60, "displayInside": 57, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 61, "parentId": 48, "title": "4.4 Anordnung der Schaltleisten", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 62, "displayInside": 61, "title": "Kommentar", "type": "string"}, {"id": 63, "displayInside": 61, "title": "Fotos", "type": "photo"}, {"id": 64, "displayInside": 61, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 65, "parentId": 48, "title": "4.5 Signalleuchte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 66, "displayInside": 65, "title": "Kommentar", "type": "string"}, {"id": 67, "displayInside": 65, "title": "Fotos", "type": "photo"}, {"id": 68, "displayInside": 65, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 69, "title": "5. Betriebskräftemessung", "type": "headline"}, {"id": 70, "parentId": 69, "title": "1 Grenzwert", "type": "headline"}, {"id": 71, "parentId": 70, "title": "F_dyn", "type": "int"}, {"id": 72, "parentId": 70, "title": "t_dyn", "type": "int"}, {"id": 73, "parentId": 70, "title": "F_End", "type": "int"}, {"id": 74, "parentId": 70, "title": "0. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 75, "parentId": 69, "title": "5.1.1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 76, "parentId": 75, "title": "F_dyn", "type": "int"}, {"id": 77, "parentId": 75, "title": "t_dyn", "type": "int"}, {"id": 78, "parentId": 75, "title": "F_End", "type": "int"}, {"id": 79, "parentId": 75, "title": "1. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 80, "parentId": 69, "title": "5.2.2. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 81, "parentId": 80, "title": "F_dyn", "type": "int"}, {"id": 82, "parentId": 80, "title": "t_dyn", "type": "int"}, {"id": 83, "parentId": 80, "title": "F_End", "type": "int"}, {"id": 84, "parentId": 80, "title": "2. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 85, "parentId": 69, "title": "5.3.3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 86, "parentId": 85, "title": "F_dyn", "type": "int"}, {"id": 87, "parentId": 85, "title": "t_dyn", "type": "int"}, {"id": 88, "parentId": 85, "title": "F_End", "type": "int"}, {"id": 89, "parentId": 85, "title": "3. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 90, "title": "Reparaturleistungen", "type": "headline"}, {"id": 91, "parentId": 90, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 92, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 93}, {"id": 93, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 92, "nextSiblingId": 94}, {"id": 94, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 93, "nextSiblingId": 95}, {"id": 95, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 94, "nextSiblingId": 96}, {"id": 96, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 95, "nextSiblingId": 97}, {"id": 97, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 96, "nextSiblingId": 98}, {"id": 98, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 97, "nextSiblingId": 99}, {"id": 99, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 98, "nextSiblingId": 100}, {"id": 100, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 99, "nextSiblingId": 101}, {"id": 101, "parentId": 91, "title": "Arbeit", "type": "string", "prevSiblingId": 100, "nextSiblingId": null}, {"id": 102, "parentId": 91, "title": "von", "type": "time"}, {"id": 103, "parentId": 91, "title": "bis", "type": "time"}, {"id": 104, "parentId": 91, "title": "Ersatzteile", "type": "measurement"}, {"id": 105, "parentId": 91, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 106, "parentId": 91, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 107, "parentId": 90, "title": "Sonstiges", "type": "string"}, {"id": 108, "parentId": 90, "title": "Ort", "type": "string"}, {"id": 109, "parentId": 90, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 110, "parentId": 90, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}