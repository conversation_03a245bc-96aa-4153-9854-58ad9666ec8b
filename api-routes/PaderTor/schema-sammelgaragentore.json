{"name": "Sammelgaragentore ET500", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Antriebs - und Konsolenbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Nothandbetätigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Befestigung und Zustand Antriebsschiene", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 15, "parentId": 14, "title": "2.1 Befestigung Schaltschrank", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 16, "displayInside": 15, "title": "Kommentar", "type": "string"}, {"id": 17, "displayInside": 15, "title": "Fotos", "type": "photo"}, {"id": 18, "displayInside": 15, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 19, "parentId": 14, "title": "2.2 Endlagen richtig eingestellt", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 20, "displayInside": 19, "title": "Kommentar", "type": "string"}, {"id": 21, "displayInside": 19, "title": "Fotos", "type": "photo"}, {"id": 22, "displayInside": 19, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 23, "parentId": 14, "title": "2.3 <PERSON><PERSON><PERSON><PERSON><PERSON>, Steckverbindung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 24, "displayInside": 23, "title": "Kommentar", "type": "string"}, {"id": 25, "displayInside": 23, "title": "Fotos", "type": "photo"}, {"id": 26, "displayInside": 23, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 27, "parentId": 14, "title": "2.4 <PERSON><PERSON><PERSON>", "type": "int"}, {"id": 28, "displayInside": 27, "title": "Kommentar", "type": "string"}, {"id": 29, "displayInside": 27, "title": "Fotos", "type": "photo"}, {"id": 30, "displayInside": 27, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 31, "title": "3. <PERSON><PERSON><PERSON>/<PERSON>n", "type": "headline"}, {"id": 32, "parentId": 31, "title": "3.1 Befestigung und Zustand", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 33, "displayInside": 32, "title": "Kommentar", "type": "string"}, {"id": 34, "displayInside": 32, "title": "Fotos", "type": "photo"}, {"id": 35, "displayInside": 32, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 36, "parentId": 31, "title": "3.2 <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ers<PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 37, "displayInside": 36, "title": "Kommentar", "type": "string"}, {"id": 38, "displayInside": 36, "title": "Fotos", "type": "photo"}, {"id": 39, "displayInside": 36, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 40, "parentId": 31, "title": "3.3 Seile", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 41, "displayInside": 40, "title": "Kommentar", "type": "string"}, {"id": 42, "displayInside": 40, "title": "Fotos", "type": "photo"}, {"id": 43, "displayInside": 40, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 44, "parentId": 31, "title": "3.4 Federpuffer", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 45, "displayInside": 44, "title": "Kommentar", "type": "string"}, {"id": 46, "displayInside": 44, "title": "Fotos", "type": "photo"}, {"id": 47, "displayInside": 44, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 48, "title": "4. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 49, "parentId": 48, "title": "4.1 <PERSON><PERSON><PERSON>, Verschleiss", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 50, "displayInside": 49, "title": "Kommentar", "type": "string"}, {"id": 51, "displayInside": 49, "title": "Fotos", "type": "photo"}, {"id": 52, "displayInside": 49, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 53, "parentId": 48, "title": "4.2 Aufhängung, Befestigungsmittel", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 54, "displayInside": 53, "title": "Kommentar", "type": "string"}, {"id": 55, "displayInside": 53, "title": "Fotos", "type": "photo"}, {"id": 56, "displayInside": 53, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 57, "parentId": 48, "title": "4.3 Laufrollen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 58, "displayInside": 57, "title": "Kommentar", "type": "string"}, {"id": 59, "displayInside": 57, "title": "Fotos", "type": "photo"}, {"id": 60, "displayInside": 57, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 61, "parentId": 48, "title": "4.4 Schlupftür", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 62, "displayInside": 61, "title": "Kommentar", "type": "string"}, {"id": 63, "displayInside": 61, "title": "Fotos", "type": "photo"}, {"id": 64, "displayInside": 61, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 65, "parentId": 48, "title": "4.5 Spiralkabel", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 66, "displayInside": 65, "title": "Kommentar", "type": "string"}, {"id": 67, "displayInside": 65, "title": "Fotos", "type": "photo"}, {"id": 68, "displayInside": 65, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 69, "title": "5. Si<PERSON>heitseinrichtung", "type": "headline"}, {"id": 70, "parentId": 69, "title": "5.1 Signalgeber, Zustand, Funktion", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 71, "displayInside": 70, "title": "Kommentar", "type": "string"}, {"id": 72, "displayInside": 70, "title": "Fotos", "type": "photo"}, {"id": 73, "displayInside": 70, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 74, "parentId": 69, "title": "5.2 Zustand SKS Dose", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 75, "displayInside": 74, "title": "Kommentar", "type": "string"}, {"id": 76, "displayInside": 74, "title": "Fotos", "type": "photo"}, {"id": 77, "displayInside": 74, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 78, "parentId": 69, "title": "5.3 Notabschalteinrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 79, "displayInside": 78, "title": "Kommentar", "type": "string"}, {"id": 80, "displayInside": 78, "title": "Fotos", "type": "photo"}, {"id": 81, "displayInside": 78, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 82, "parentId": 69, "title": "5.4 Signalleuchte/Ampel", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 83, "displayInside": 82, "title": "Kommentar", "type": "string"}, {"id": 84, "displayInside": 82, "title": "Fotos", "type": "photo"}, {"id": 85, "displayInside": 82, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 86, "title": "6. Betriebskräftemessung", "type": "headline"}, {"id": 87, "parentId": 86, "title": "1 Grenzwert", "type": "headline"}, {"id": 88, "parentId": 87, "title": "F_dyn", "type": "int"}, {"id": 89, "parentId": 87, "title": "t_dyn", "type": "int"}, {"id": 90, "parentId": 87, "title": "F_End", "type": "int"}, {"id": 91, "parentId": 87, "title": "0. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 92, "parentId": 86, "title": "6.1.1. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 93, "parentId": 92, "title": "F_dyn", "type": "int"}, {"id": 94, "parentId": 92, "title": "t_dyn", "type": "int"}, {"id": 95, "parentId": 92, "title": "F_End", "type": "int"}, {"id": 96, "parentId": 92, "title": "1. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 97, "parentId": 86, "title": "6.2.2. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 98, "parentId": 97, "title": "F_dyn", "type": "int"}, {"id": 99, "parentId": 97, "title": "t_dyn", "type": "int"}, {"id": 100, "parentId": 97, "title": "F_End", "type": "int"}, {"id": 101, "parentId": 97, "title": "2. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 102, "parentId": 86, "title": "6.3.3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 103, "parentId": 102, "title": "F_dyn", "type": "int"}, {"id": 104, "parentId": 102, "title": "t_dyn", "type": "int"}, {"id": 105, "parentId": 102, "title": "F_End", "type": "int"}, {"id": 106, "parentId": 102, "title": "3. <PERSON><PERSON><PERSON>", "type": "combobox", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}]}, {"id": 107, "title": "Reparaturleistungen", "type": "headline"}, {"id": 108, "parentId": 107, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 109, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 110}, {"id": 110, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 109, "nextSiblingId": 111}, {"id": 111, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 110, "nextSiblingId": 112}, {"id": 112, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 111, "nextSiblingId": 113}, {"id": 113, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 112, "nextSiblingId": 114}, {"id": 114, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 113, "nextSiblingId": 115}, {"id": 115, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 114, "nextSiblingId": 116}, {"id": 116, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 115, "nextSiblingId": 117}, {"id": 117, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 116, "nextSiblingId": 118}, {"id": 118, "parentId": 108, "title": "Arbeit", "type": "string", "prevSiblingId": 117, "nextSiblingId": null}, {"id": 119, "parentId": 108, "title": "von", "type": "time"}, {"id": 120, "parentId": 108, "title": "bis", "type": "time"}, {"id": 121, "parentId": 108, "title": "Ersatzteile", "type": "measurement"}, {"id": 122, "parentId": 108, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 123, "parentId": 108, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 124, "parentId": 107, "title": "Sonstiges", "type": "string"}, {"id": 125, "parentId": 107, "title": "Ort", "type": "string"}, {"id": 126, "parentId": 107, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 127, "parentId": 107, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}