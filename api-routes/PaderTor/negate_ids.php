<?php

$schema = 'schema-vertikal-schnellauftor.json';
$filePath = __DIR__ . '/' . $schema;
$jsonContent = file_get_contents($filePath);
$data = json_decode($jsonContent, true);

if (isset($data['positions']) && is_array($data['positions'])) {
    foreach ($data['positions'] as &$position) {
        if (isset($position['id']) && is_numeric($position['id']) && $position['id'] > 0) {
            $position['id'] = -$position['id'];
        }
        if (isset($position['parentId']) && is_numeric($position['parentId']) && $position['parentId'] > 0) {
            $position['parentId'] = -$position['parentId'];
        }
        if (isset($position['displayInside']) && is_numeric($position['displayInside']) && $position['displayInside'] > 0) {
            $position['displayInside'] = -$position['displayInside'];
        }
    }
}

$newJsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
file_put_contents($filePath, $newJsonContent);

echo "Positive IDs, parentIds, and displayInside values in $schema have been negated.\n";