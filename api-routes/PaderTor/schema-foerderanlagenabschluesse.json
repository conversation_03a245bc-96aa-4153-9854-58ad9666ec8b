{"name": "Förderanlagenabschlüsse", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Beschädigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Labyrinthschiene", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Bremse", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 Seilbefestigung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "parentId": 1, "title": "1.5 Seil, Zahnriemen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 19, "displayInside": 18, "title": "Kommentar", "type": "string"}, {"id": 20, "displayInside": 18, "title": "Fotos", "type": "photo"}, {"id": 21, "displayInside": 18, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 22, "parentId": 1, "title": "1.6 <PERSON><PERSON><PERSON>", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 23, "displayInside": 22, "title": "Kommentar", "type": "string"}, {"id": 24, "displayInside": 22, "title": "Fotos", "type": "photo"}, {"id": 25, "displayInside": 22, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 26, "parentId": 1, "title": "1.7 Ankerplatte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 27, "displayInside": 26, "title": "Kommentar", "type": "string"}, {"id": 28, "displayInside": 26, "title": "Fotos", "type": "photo"}, {"id": 29, "displayInside": 26, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 30, "parentId": 1, "title": "1.8 Kennzeichnung Schließbereich", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 31, "displayInside": 30, "title": "Kommentar", "type": "string"}, {"id": 32, "displayInside": 30, "title": "Fotos", "type": "photo"}, {"id": 33, "displayInside": 30, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 34, "title": "2. Torkonstruk<PERSON>", "type": "headline"}, {"id": 35, "parentId": 34, "title": "2.1 Laufschiene", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 36, "displayInside": 35, "title": "Kommentar", "type": "string"}, {"id": 37, "displayInside": 35, "title": "Fotos", "type": "photo"}, {"id": 38, "displayInside": 35, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 39, "parentId": 34, "title": "2.2 Einlauftrichter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 40, "displayInside": 39, "title": "Kommentar", "type": "string"}, {"id": 41, "displayInside": 39, "title": "Fotos", "type": "photo"}, {"id": 42, "displayInside": 39, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 43, "parentId": 34, "title": "2.3 Führungsrollen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 44, "displayInside": 43, "title": "Kommentar", "type": "string"}, {"id": 45, "displayInside": 43, "title": "Fotos", "type": "photo"}, {"id": 46, "displayInside": 43, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 47, "parentId": 34, "title": "2.4 Brandschutzdichtstreifen", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 48, "displayInside": 47, "title": "Kommentar", "type": "string"}, {"id": 49, "displayInside": 47, "title": "Fotos", "type": "photo"}, {"id": 50, "displayInside": 47, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 51, "parentId": 34, "title": "2.5 Gewichtskasten", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 52, "displayInside": 51, "title": "Kommentar", "type": "string"}, {"id": 53, "displayInside": 51, "title": "Fotos", "type": "photo"}, {"id": 54, "displayInside": 51, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 55, "parentId": 34, "title": "2.6 Umlenkrollen/ Gummibeschichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 56, "displayInside": 55, "title": "Kommentar", "type": "string"}, {"id": 57, "displayInside": 55, "title": "Fotos", "type": "photo"}, {"id": 58, "displayInside": 55, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 59, "parentId": 34, "title": "2.7 Klappstück/ Abräumvorrichtung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 60, "displayInside": 59, "title": "Kommentar", "type": "string"}, {"id": 61, "displayInside": 59, "title": "Fotos", "type": "photo"}, {"id": 62, "displayInside": 59, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 63, "title": "3. Freifahrtfunktion", "type": "headline"}, {"id": 64, "parentId": 63, "title": "3.1 Freifahrt bei Rauchmeldung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 65, "displayInside": 64, "title": "Kommentar", "type": "string"}, {"id": 66, "displayInside": 64, "title": "Fotos", "type": "photo"}, {"id": 67, "displayInside": 64, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 68, "parentId": 63, "title": "3.2 Freifahrt bei Spannungsausfall", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 69, "displayInside": 68, "title": "Kommentar", "type": "string"}, {"id": 70, "displayInside": 68, "title": "Fotos", "type": "photo"}, {"id": 71, "displayInside": 68, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 72, "title": "4. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 73, "parentId": 72, "title": "4.1 Magnet", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 74, "displayInside": 73, "title": "Kommentar", "type": "string"}, {"id": 75, "displayInside": 73, "title": "Fotos", "type": "photo"}, {"id": 76, "displayInside": 73, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 77, "parentId": 72, "title": "4.2 SB-Sensor", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 78, "displayInside": 77, "title": "Kommentar", "type": "string"}, {"id": 79, "displayInside": 77, "title": "Fotos", "type": "photo"}, {"id": 80, "displayInside": 77, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 81, "parentId": 72, "title": "4.3 Handauslösung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 82, "displayInside": 81, "title": "Kommentar", "type": "string"}, {"id": 83, "displayInside": 81, "title": "Fotos", "type": "photo"}, {"id": 84, "displayInside": 81, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 85, "parentId": 72, "title": "4.4 Anzahl der Rauchschalter", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 86, "displayInside": 85, "title": "Kommentar", "type": "string"}, {"id": 87, "displayInside": 85, "title": "Fotos", "type": "photo"}, {"id": 88, "displayInside": 85, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 89, "parentId": 72, "title": "4.5 Anordnung der Rauchschalter", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 90, "displayInside": 89, "title": "Kommentar", "type": "string"}, {"id": 91, "displayInside": 89, "title": "Fotos", "type": "photo"}, {"id": 92, "displayInside": 89, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 93, "parentId": 72, "title": "4.6 Blitzleuchte", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 94, "displayInside": 93, "title": "Kommentar", "type": "string"}, {"id": 95, "displayInside": 93, "title": "Fotos", "type": "photo"}, {"id": 96, "displayInside": 93, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 97, "parentId": 72, "title": "4.7 Signalhupe", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 98, "displayInside": 97, "title": "Kommentar", "type": "string"}, {"id": 99, "displayInside": 97, "title": "Fotos", "type": "photo"}, {"id": 100, "displayInside": 97, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 101, "title": "5. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 102, "parentId": 101, "title": "5.1 Batterie-Check/ Betriebszeit NSV", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 103, "displayInside": 102, "title": "Kommentar", "type": "string"}, {"id": 104, "displayInside": 102, "title": "Fotos", "type": "photo"}, {"id": 105, "displayInside": 102, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 106, "parentId": 101, "title": "5.2 Notstromversorgung", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 107, "displayInside": 106, "title": "Kommentar", "type": "string"}, {"id": 108, "displayInside": 106, "title": "Fotos", "type": "photo"}, {"id": 109, "displayInside": 106, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 110, "parentId": 101, "title": "5.3 Bat<PERSON>ie-Check/ Betriebszeit USV", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 111, "displayInside": 110, "title": "Kommentar", "type": "string"}, {"id": 112, "displayInside": 110, "title": "Fotos", "type": "photo"}, {"id": 113, "displayInside": 110, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 114, "title": "6. Betriebszeiten", "type": "headline"}, {"id": 115, "parentId": 114, "title": "6.1 <PERSON><PERSON> Tor", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 116, "displayInside": 115, "title": "Kommentar", "type": "string"}, {"id": 117, "displayInside": 115, "title": "Fotos", "type": "photo"}, {"id": 118, "displayInside": 115, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 119, "parentId": 114, "title": "6.2 Datum Rauchmelder NSV", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 120, "displayInside": 119, "title": "Kommentar", "type": "string"}, {"id": 121, "displayInside": 119, "title": "Fotos", "type": "photo"}, {"id": 122, "displayInside": 119, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 123, "parentId": 114, "title": "6.3 Datum Batterie NSV", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 124, "displayInside": 123, "title": "Kommentar", "type": "string"}, {"id": 125, "displayInside": 123, "title": "Fotos", "type": "photo"}, {"id": 126, "displayInside": 123, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 127, "parentId": 114, "title": "6.4 Datum Batterie USV", "type": "combobox-tap", "required": "false", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 128, "displayInside": 127, "title": "Kommentar", "type": "string"}, {"id": 129, "displayInside": 127, "title": "Fotos", "type": "photo"}, {"id": 130, "displayInside": 127, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 131, "title": "Reparaturleistungen", "type": "headline"}, {"id": 132, "parentId": 131, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 133, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 134}, {"id": 134, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 133, "nextSiblingId": 135}, {"id": 135, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 134, "nextSiblingId": 136}, {"id": 136, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 135, "nextSiblingId": 137}, {"id": 137, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 136, "nextSiblingId": 138}, {"id": 138, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 137, "nextSiblingId": 139}, {"id": 139, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 138, "nextSiblingId": 140}, {"id": 140, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 139, "nextSiblingId": 141}, {"id": 141, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 140, "nextSiblingId": 142}, {"id": 142, "parentId": 132, "title": "Arbeit", "type": "string", "prevSiblingId": 141, "nextSiblingId": null}, {"id": 143, "parentId": 132, "title": "von", "type": "time"}, {"id": 144, "parentId": 132, "title": "bis", "type": "time"}, {"id": 145, "parentId": 132, "title": "Ersatzteile", "type": "measurement"}, {"id": 146, "parentId": 132, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 147, "parentId": 132, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 148, "parentId": 131, "title": "Sonstiges", "type": "string"}, {"id": 149, "parentId": 131, "title": "Ort", "type": "string"}, {"id": 150, "parentId": 131, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 151, "parentId": 131, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}