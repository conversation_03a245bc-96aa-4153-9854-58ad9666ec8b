{"name": "Brandschutztüren", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Beschädigung", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 3, "displayInside": 2, "title": "Kommentar", "type": "string"}, {"id": 4, "displayInside": 2, "title": "Fotos", "type": "photo"}, {"id": 5, "displayInside": 2, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 6, "parentId": 1, "title": "1.2 Leichtgängigkeit", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 7, "displayInside": 6, "title": "Kommentar", "type": "string"}, {"id": 8, "displayInside": 6, "title": "Fotos", "type": "photo"}, {"id": 9, "displayInside": 6, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 10, "parentId": 1, "title": "1.3 Zargenbefestigung", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 11, "displayInside": 10, "title": "Kommentar", "type": "string"}, {"id": 12, "displayInside": 10, "title": "Fotos", "type": "photo"}, {"id": 13, "displayInside": 10, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 14, "parentId": 1, "title": "1.4 Brandschutzdichtstreifen", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 15, "displayInside": 14, "title": "Kommentar", "type": "string"}, {"id": 16, "displayInside": 14, "title": "Fotos", "type": "photo"}, {"id": 17, "displayInside": 14, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 18, "parentId": 1, "title": "1.5 Kennzeichnung als Rettungsweg", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 19, "displayInside": 18, "title": "Kommentar", "type": "string"}, {"id": 20, "displayInside": 18, "title": "Fotos", "type": "photo"}, {"id": 21, "displayInside": 18, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 22, "parentId": 1, "title": "1.6 Profilzylinder", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 23, "displayInside": 22, "title": "Kommentar", "type": "string"}, {"id": 24, "displayInside": 22, "title": "Fotos", "type": "photo"}, {"id": 25, "displayInside": 22, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 26, "parentId": 1, "title": "1.7 Blindzylinder", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 27, "displayInside": 26, "title": "Kommentar", "type": "string"}, {"id": 28, "displayInside": 26, "title": "Fotos", "type": "photo"}, {"id": 29, "displayInside": 26, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 30, "parentId": 1, "title": "1.7 Panikschloss (bei Fluchtweg)", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 31, "displayInside": 30, "title": "Kommentar", "type": "string"}, {"id": 32, "displayInside": 30, "title": "Fotos", "type": "photo"}, {"id": 33, "displayInside": 30, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 34, "parentId": 1, "title": "1.8 Funktion E (Griff/Knauf)", "type": "combobox", "value": ["Griff", "<PERSON><PERSON><PERSON>"]}, {"id": 35, "parentId": 1, "title": "1.9 Funktion B (Griff/Griff)", "type": "combobox", "value": ["Griff", "<PERSON><PERSON><PERSON>"]}, {"id": 36, "parentId": 1, "title": "1.10 Obentürschließer/ Bandschließer", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 37, "displayInside": 36, "title": "Kommentar", "type": "string"}, {"id": 38, "displayInside": 36, "title": "Fotos", "type": "photo"}, {"id": 39, "displayInside": 36, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 40, "parentId": 1, "title": "1.11 absenkbare Bodendichtung", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 41, "displayInside": 40, "title": "Kommentar", "type": "string"}, {"id": 42, "displayInside": 40, "title": "Fotos", "type": "photo"}, {"id": 43, "displayInside": 40, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 44, "parentId": 1, "title": "1.12 doppel Flügel: Schließfolgeregelung", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 45, "displayInside": 44, "title": "Kommentar", "type": "string"}, {"id": 46, "displayInside": 44, "title": "Fotos", "type": "photo"}, {"id": 47, "displayInside": 44, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 48, "parentId": 1, "title": "1.13 doppel Flügel: Verriegelung Standflügel", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 49, "displayInside": 48, "title": "Kommentar", "type": "string"}, {"id": 50, "displayInside": 48, "title": "Fotos", "type": "photo"}, {"id": 51, "displayInside": 48, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 52, "parentId": 1, "title": "1.14 Kennzeichnung Schließbereich", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 53, "displayInside": 52, "title": "Kommentar", "type": "string"}, {"id": 54, "displayInside": 52, "title": "Fotos", "type": "photo"}, {"id": 55, "displayInside": 52, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 56, "parentId": 1, "title": "1.15  <PERSON><PERSON> greift", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 57, "displayInside": 56, "title": "Kommentar", "type": "string"}, {"id": 58, "displayInside": 56, "title": "Fotos", "type": "photo"}, {"id": 59, "displayInside": 56, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 60, "parentId": 1, "title": "1.16 Zargendichtung", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 61, "displayInside": 60, "title": "Kommentar", "type": "string"}, {"id": 62, "displayInside": 60, "title": "Fotos", "type": "photo"}, {"id": 63, "displayInside": 60, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 64, "parentId": 1, "title": "1.17 Spaltmaße der Tür", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 65, "displayInside": 64, "title": "Kommentar", "type": "string"}, {"id": 66, "displayInside": 64, "title": "Fotos", "type": "photo"}, {"id": 67, "displayInside": 64, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 68, "title": "2. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 69, "parentId": 68, "title": "2.1 Magnet", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 70, "displayInside": 69, "title": "Kommentar", "type": "string"}, {"id": 71, "displayInside": 69, "title": "Fotos", "type": "photo"}, {"id": 72, "displayInside": 69, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 73, "parentId": 68, "title": "2.2 Handauslösung", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 74, "displayInside": 73, "title": "Kommentar", "type": "string"}, {"id": 75, "displayInside": 73, "title": "Fotos", "type": "photo"}, {"id": 76, "displayInside": 73, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 77, "parentId": 68, "title": "2.3 Rauchschalter/Betriebszeit", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 78, "displayInside": 77, "title": "Kommentar", "type": "string"}, {"id": 79, "displayInside": 77, "title": "Fotos", "type": "photo"}, {"id": 80, "displayInside": 77, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 81, "parentId": 68, "title": "2.4 Datum der Rauchschalter", "type": "date"}, {"id": 82, "displayInside": 81, "title": "Kommentar", "type": "string"}, {"id": 83, "displayInside": 81, "title": "Fotos", "type": "photo"}, {"id": 84, "displayInside": 81, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 85, "parentId": 68, "title": "2.5 Anzahl der Rauchschalter", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 86, "displayInside": 85, "title": "Kommentar", "type": "string"}, {"id": 87, "displayInside": 85, "title": "Fotos", "type": "photo"}, {"id": 88, "displayInside": 85, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 89, "parentId": 68, "title": "2.6 Anordnung der Rauchschalter", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 90, "displayInside": 89, "title": "Kommentar", "type": "string"}, {"id": 91, "displayInside": 89, "title": "Fotos", "type": "photo"}, {"id": 92, "displayInside": 89, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 93, "parentId": 68, "title": "2.7 Blitzleuchte", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 94, "displayInside": 93, "title": "Kommentar", "type": "string"}, {"id": 95, "displayInside": 93, "title": "Fotos", "type": "photo"}, {"id": 96, "displayInside": 93, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 97, "parentId": 68, "title": "2.8 Signalhupe", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 98, "displayInside": 97, "title": "Kommentar", "type": "string"}, {"id": 99, "displayInside": 97, "title": "Fotos", "type": "photo"}, {"id": 100, "displayInside": 97, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 101, "parentId": 68, "title": "2.9 Zuleitung/ Hauptschalter", "type": "combobox-tap", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}, {"value": "n.m."}]}, {"id": 102, "displayInside": 101, "title": "Kommentar", "type": "string"}, {"id": 103, "displayInside": 101, "title": "Fotos", "type": "photo"}, {"id": 104, "displayInside": 101, "title": "Foto mit Markierungen", "type": "graphicalMeasurement"}, {"id": 105, "title": "Reparaturleistungen", "type": "headline"}, {"id": 106, "parentId": 105, "title": "Ausgeführte Arbeiten", "type": "headline"}, {"id": 107, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": null, "nextSiblingId": 108}, {"id": 108, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 107, "nextSiblingId": 109}, {"id": 109, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 108, "nextSiblingId": 110}, {"id": 110, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 109, "nextSiblingId": 111}, {"id": 111, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 110, "nextSiblingId": 112}, {"id": 112, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 111, "nextSiblingId": 113}, {"id": 113, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 112, "nextSiblingId": 114}, {"id": 114, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 113, "nextSiblingId": 115}, {"id": 115, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 114, "nextSiblingId": 116}, {"id": 116, "parentId": 106, "title": "Arbeit", "type": "string", "prevSiblingId": 115, "nextSiblingId": null}, {"id": 117, "parentId": 106, "title": "von", "type": "time"}, {"id": 118, "parentId": 106, "title": "bis", "type": "time"}, {"id": 119, "parentId": 106, "title": "Ersatzteile", "type": "measurement"}, {"id": 120, "parentId": 106, "title": "Ersatzteile Sonstiges", "type": "string"}, {"id": 121, "parentId": 106, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 122, "parentId": 105, "title": "Sonstiges", "type": "string"}, {"id": 123, "parentId": 105, "title": "Ort", "type": "string"}, {"id": 124, "parentId": 105, "title": "Unterschrift des Sachkundigen", "type": "signatureField"}, {"id": 125, "parentId": 105, "title": "Unterschrift des Betreibers", "type": "signatureField"}]}