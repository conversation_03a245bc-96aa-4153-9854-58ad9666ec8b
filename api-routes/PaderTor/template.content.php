<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    /** @noinspection PhpUnhandledExceptionInspection */
    $data = (new C_PaderTor())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<mixed> $headlineWithChildren
 */
function mapDocumentationValuesToTables(array $headlineWithChildren): string
{
    $comboboxTapValues = ['i.O.', 'n.i.O.', 'n.v.', 'n.m.'];
    $table = '';
    foreach ($headlineWithChildren as $headlineName => $headlineChildren) {
        if (empty($headlineChildren)) {
            continue;
        }
        list(, $parentTitle) = getNumberTitle($headlineName);
        if ($parentTitle === 'Betriebskräftemessung') {
            $table .= getBetriebskraeftemessungTable($headlineChildren, $headlineName);
        } else {
            $table .= '<table class="content-table">';
            $table .= getTableHeaderWithNumbering($headlineName, $comboboxTapValues);
            foreach ($headlineChildren as $rowTitle => $rowValue) {
                list($number, $title) = getNumberTitle($rowTitle);
                $table .= '<tr><td>' . $number . '</td>';
                $table .= '<td>' . $title . '</td>';
                $table .= getValueTableCells($rowValue, $comboboxTapValues, $title);
                $table .= '</tr>';
            }
        }
        $table .= '</table>';
    }
    return $table;
}

function getMessungTableCells(string $value): string
{
    if ($value === 'i.o.') {
        return '<td>&#9745;</td><td>&#9744;</td>';
    }
    if ($value === 'n.i.o.') {
        return '<td>&#9744;</td><td>&#9745;</td>';
    }
    return '<td>&#9744;</td><td>&#9744;</td>';
}

/**
 * @param array<mixed> $messungHeadlinesWithValues
 */
function getBetriebskraeftemessungTable(array $messungHeadlinesWithValues, string $subHeadlineTitle): string
{
    if (empty($messungHeadlinesWithValues)) {
        return '';
    }
    $columns = ['F_dyn', 't_dyn', 'F_End', 'i.O.', 'n.i.O.'];
    $table = '<table class="content-table">';
    $table .= getTableHeaderWithNumbering($subHeadlineTitle, $columns);
    foreach ($messungHeadlinesWithValues as $subHeadlineTitle => $children) {
        if (empty($children)) {
            continue;
        }
        list($number, $title) = getNumberTitle($subHeadlineTitle);
        if ($title === 'Grenzwert') {
            $number = '';
            // append e at end like in reference Excel
            $title = 'Grenzwerte';
        } else {
            // those are the Messung headlines, in this format: 5.1.1 Messung
            // we need to take the last number, cut it off from the start and prepend to Messung
            $titleDots = array_filter(explode(".", $number));
            $firstTwoParts = array_slice($titleDots, 0, 2);
            $number = implode(".", $firstTwoParts);
            $title = end($titleDots) . ". " . $title;
        }
        $f_dyn = $children['F_dyn']['value'] ?? '';
        $t_dyn = $children['t_dyn']['value'] ?? '';
        $f_end = $children['F_End']['value'] ?? '';
        $table .= '<tr><td>' . $number . '</td><td>' . $title . '</td>';
        $table .= '<td>' . $f_dyn . '</td>' . '<td>' . $t_dyn . '</td>' . '<td>' . $f_end . '</td>';
        $insertedMessung = false;
        // the title is dynamic, it can be "0. Messung", "1. Messung", etc.
        foreach ($children as $childKey => $childValue) {
            if (str_ends_with($childKey, "Messung")) {
                $table .= getMessungTableCells($childValue['value'] ?? '');
                $insertedMessung = true;
                break;
            }
        }
        if (!$insertedMessung) {
            $table .= getMessungTableCells('');
        }
        $table .= '</tr>';
    }
    return $table . '</table>';
}

function getAntriebVorhandenCells(bool|int|string $value): string
{
    if (PrintoutHelper::isCheckboxTickedByReportedValue($value)) {
        return 'Ja';
    }
    return 'Nein';
}

function getCheckboxTableCells(bool|int|string $value): string
{
    if (PrintoutHelper::isCheckboxTickedByReportedValue($value)) {
        return '<td>&#9745;</td><td>&#9744;</td>';
    }
    return '<td>&#9744;</td><td>&#9745;</td>';
}

/**
 * @param array<mixed> $columnNames
 */
function getTableHeaderWithNumbering(string $headline, array $columnNames): string
{
    list($number, $title) = getNumberTitle($headline);
    $tr = '<tr><td>' . $number . ' </td>';
    $tr .= '<td>' . $title . ' </td>';
    foreach ($columnNames as $name) {
        $tr .= '<td>' . $name . ' </td>';
    }
    return $tr . '</tr>';
}

/**
 * @return array<mixed>
 */
function getNumberTitle(string $row): array
{
    $parts = explode(' ', $row, 2);
    $number = $parts[0];
    $title = $parts[1] ?? '';
    return [$number, $title];
}

/**
 * @param array<mixed> $row
 * @param array<mixed> $comboboxTapValues
 */
function getValueTableCells(array $row, array $comboboxTapValues, string $title): string
{
    $value = strtolower($row['value']);
    $titlesForSpanLayout = ['Funktion E (Griff/Knauf)', 'Funktion B (Griff/Griff)'];
    if (in_array($title, $titlesForSpanLayout)) {
        return '<td colspan="2">auswählen: </td><td colspan="2">' . $value . '</td>';
    }
    if ($row['type'] === SchemaTypes::COMBOBOX_TAP) {
        if ($value === strtolower($comboboxTapValues[0])) {
            return '<td>&#9745;</td><td>&#9744;</td><td>&#9744;</td><td>&#9744;</td>';
        }
        if ($value === strtolower($comboboxTapValues[1])) {
            return '<td>&#9744;</td><td>&#9745;</td><td>&#9744;</td><td>&#9744;</td>';
        }
        if ($value === strtolower($comboboxTapValues[2])) {
            return '<td>&#9744;</td><td>&#9744;</td><td>&#9745;</td><td>&#9744;</td>';
        }
        if ($value === strtolower($comboboxTapValues[3])) {
            return '<td>&#9744;</td><td>&#9744;</td><td>&#9744;</td><td>&#9745;</td>';
        }
        return '<td>&#9744;</td><td>&#9744;</td><td>&#9744;</td><td>&#9744;</td>';
    }
    return '<td colspan="4">' . $value . '</td>';
}

function getSubTitle(string $title): string
{
    $exceptions = ['Brandschutztüren', 'Brandschutz Schiebetore', 'Schranken', 'Verladerampen'];
    if (in_array($title, $exceptions)) {
        return '';
    }
    return ' nach ASR 1.7';
}

?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>PaderTor</title>
</head>
<body>
<div class="header">
    <div class="header-left"><img
                src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/houses.png") ?>"
                alt=""></div>
    <div class="header-right"><img
                src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/logo.png") ?>"
                alt=""></div>
</div>
<div class="container">
    <div class="sub-header">
        <table class="sub-header-table">
            <tr>
                <td>UVV für <?= $data['schemaTitle'] . getSubTitle($data['schemaTitle']) ?></td>
                <td>PaderTor GmbH | Tel.: 0 52 51/668 11-0</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>Navarrastraße 12 | Fax: 0 52 51/668 11-99</td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td>33106 Paderborn | Mail: <EMAIL></td>
            </tr>
        </table>
    </div>

    <div class="content">
        <div class="content-left">
            <table class="content-left-header">
                <tr>
                    <td><b>Kunde:</b> <?= $data['customerData']['name'] ?></td>
                </tr>
                <tr>
                    <td><b>Straße / Hausnr:</b> <?= $data['customerData']['address'] ?? '' ?></td>
                </tr>
                <tr>
                    <td>
                        <b>PLZ / Ort:</b>
                        <?php
                        $parts = [];
                        if (!empty($data['customerData']['postcode'])) {
                            $parts[] = $data['customerData']['postcode'];
                        }
                        if (!empty($data['customerData']['city'])) {
                            $parts[] = $data['customerData']['city'];
                        }
                        echo implode(' ', $parts);
                        ?>
                    </td>
                </tr>
                <tr>
                    <td><b>Telefon:</b> <?= $data['customerData']['phoneNumber'] ?? '' ?></td>
                </tr>
                <tr>
                    <td><b>Anlagenstandort:</b></td>
                </tr>
            </table>

            <?php if (isset($data['mappedChildren']['Antrieb vorhanden?'])) { ?>
                <table class="content-table">
                    <tr>
                        <td style="width: 21px">0.</td>
                        <td style="font-weight: normal">Antrieb vorhanden?</td>
                        <td style="font-weight: normal; text-align: center">
                            <?= getAntriebVorhandenCells($data['mappedChildren']['Antrieb vorhanden?']) ?>
                        </td>
                    </tr>
                </table>
            <?php } ?>

            <?= mapDocumentationValuesToTables($data['leftChildren']) ?>
        </div>
        <div class="content-right">
            <table class="content-right-header">
                <tr>
                    <td>
                        <b>Tortyp: </b>
                        <?= $data['equipment']['name'] ?? '' ?>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Seriennr. / Tor-Nr:</b>
                        <?= $data['equipment']['serialNumber'] ?? '' ?>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Auftrags- / Zulassungs-Nr.:</b>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Hersteller und Baujahr:</b>
                        <?= $data['equipment']['modelNumber'] ?? '' ?>
                    </td>
                </tr>
                <tr>
                    <td>
                        <b>Anlagennr:</b>
                        <?= $data['equipment']['id'] ?? '' ?>
                    </td>
                </tr>
            </table>

            <?= mapDocumentationValuesToTables($data['rightChildren']) ?>
        </div>
    </div>

    <div class="footer">
        <div class="footer-left">
            <table>
                <tr>
                    <td>i.O.</td>
                    <td>=&nbsp;&nbsp;in Ordnung</td>
                </tr>
                <tr>
                    <td>n.i.O.</td>
                    <td>=&nbsp;&nbsp;nicht in Ordnung</td>
                </tr>
                <tr>
                    <td>n.v.</td>
                    <td>=&nbsp;&nbsp;nicht vorhanden</td>
                </tr>
                <tr>
                    <td>n.m.</td>
                    <td>=&nbsp;&nbsp;nicht möglich</td>
                </tr>
            </table>
        </div>

        <div class="footer-right">
            <table class="footer-right-header">
                <tr>
                    <td>Lastwechsel:</td>
                </tr>
            </table>

            <table class="footer-right-table">
                <tr>
                    <td rowspan="2"><?= $data['lastwechselTitle'] ?></td>
                    <td>Ja</td>
                    <td>Nein</td>
                </tr>
                <tr>
                    <?= getCheckboxTableCells(count($data['safetyDeficiencies']) >= 1) ?>
                </tr>
            </table>
        </div>
    </div>
    <div class="sub-footer">
        <p><span class="bold">Sonstiges: </span> <?= $data['footer']['others'] ?></p>
        <p>
            <span class="bold">Festgestellte Sicherheitsmängel: </span>
            <?php
            if (count($data['safetyDeficiencies']) >= 1) {
                echo implode(', ', $data['safetyDeficiencies']);
            }
            ?>
        </p>
        <p class="bold">
            Die Prüfung wurde nach bestem Wissen und Gewissen durchgeführt. Für versteckte Mängel,
            die bei der Anwendung der erforderlichen Sorgfalt nicht zu erkennen sind, wird eine Haftung ausgeschlossen.
        </p>
        <table class="sub-footer-table bold">
            <tr>
                <td><?= $data['footer']['ort'] . ' ' . $data['footer']['date'] ?></td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td><?= $data['footer']['leftSignature'] ?></td>
                <td></td>
                <td><?= $data['footer']['rightSignature'] ?></td>
            </tr>
            <tr>
                <td>Ort, Datum und Unterschrift des Sachkundigen</td>
                <td></td>
                <td>Unterschrift des Betreibers (Druckbuchstaben)</td>
            </tr>
        </table>
        <?php foreach ($data['photos'] as $i => $photo) { ?>
            <div class="photo-page">
                <?php if ($i === 0) { ?>
                    <div class="photo-header">
                        <h2 class="photo-header-title">Fotos</h2>
                    </div>
                <?php } ?>
                <div class="photo-content">
                    <?php if (!empty($photo['title'])) { ?>
                        <div class="photo-title">
                            <h3><?= $photo['title'] ?></h3>
                        </div>
                    <?php } ?>
                    <div class="photo-image-container">
                        <img src="<?= $photo['filePath'] ?>" class="photo-image" alt=""/>
                    </div>
                </div>
            </div>
        <?php } ?>
    </div>
</div>
</body>
</html>
