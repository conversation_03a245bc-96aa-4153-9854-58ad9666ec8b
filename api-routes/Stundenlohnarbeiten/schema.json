{"name": "Stundenlohnarbeiten", "description": "", "status": "active", "createdBy": "289", "printText": null, "createdOn": "2022-05-19T10:00:00Z", "editedBy": null, "editedOn": "2022-05-19T13:59:32Z", "applicableEntities": [], "printOptions": ["8"], "positions": [{"id": -1, "title": "Datum", "type": "date", "isRequiredSignature": "0"}, {"id": -2, "title": "Baustelle/Bereich", "type": "string", "isRequiredSignature": "0"}, {"id": -3, "title": "Tagesblattnummer", "type": "int", "isRequiredSignature": "0"}, {"id": -4, "title": "Mitarbeiter 1", "type": "headline", "isRequiredSignature": "0"}, {"id": -5, "parentId": -4, "title": "<PERSON><PERSON><PERSON>", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}}, {"id": -6, "parentId": -4, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -7, "parentId": -4, "title": "von", "type": "string", "isRequiredSignature": "0"}, {"id": -8, "parentId": -4, "title": "bis", "type": "string", "isRequiredSignature": "0"}, {"id": -9, "parentId": -4, "title": "Gesamtstunden", "type": "int", "isRequiredSignature": "0"}, {"id": -10, "title": "Mitarbeiter 2", "type": "headline", "isRequiredSignature": "0"}, {"id": -11, "parentId": -10, "title": "<PERSON><PERSON><PERSON>", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}}, {"id": -12, "parentId": -10, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -13, "parentId": -10, "title": "von", "type": "string", "isRequiredSignature": "0"}, {"id": -14, "parentId": -10, "title": "bis", "type": "string", "isRequiredSignature": "0"}, {"id": -15, "parentId": -10, "title": "Gesamtstunden", "type": "int", "isRequiredSignature": "0"}, {"id": -16, "title": "Mitarbeiter 3", "type": "headline", "isRequiredSignature": "0"}, {"id": -17, "parentId": -16, "title": "<PERSON><PERSON><PERSON>", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}}, {"id": -18, "parentId": -16, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -19, "parentId": -16, "title": "von", "type": "string", "isRequiredSignature": "0"}, {"id": -20, "parentId": -16, "title": "bis", "type": "string", "isRequiredSignature": "0"}, {"id": -21, "parentId": -16, "title": "Gesamtstunden", "type": "int", "isRequiredSignature": "0"}, {"id": -22, "title": "Durchgeführte Arbeiten", "type": "string", "isRequiredSignature": "0"}, {"id": -23, "title": "Eingesetztes Material", "type": "string", "isRequiredSignature": "0"}, {"id": -24, "title": "Eingesetztes Werkzeug / Bauschutt/ Sonstiges", "type": "headline", "isRequiredSignature": "0"}, {"id": -25, "parentId": -24, "title": "Kompressor/ Presslufthammer in Stunden", "type": "string", "isRequiredSignature": "0"}, {"id": -26, "parentId": -24, "title": "Elektrohammer in Stunden", "type": "string", "isRequiredSignature": "0"}, {"id": -27, "parentId": -24, "title": "Elektrohammer in Stunden", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}}, {"id": -28, "parentId": -24, "title": "Schnittfläche Wandsäge/Beton/Mauerwerk in m²", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}}, {"id": -29, "parentId": -24, "title": "LKW in Stunden", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}}, {"id": -30, "parentId": -24, "title": "Stk. Kernbohrung (__L<PERSON>nge_____/Beton_MW)", "type": "string", "isRequiredSignature": "0"}, {"id": -31, "parentId": -24, "title": "Teleskoplader in Stunden", "type": "string", "isRequiredSignature": "0"}, {"id": -32, "parentId": -24, "title": "<PERSON><PERSON>/ Radlader in Stunden", "type": "string", "isRequiredSignature": "0"}, {"id": -33, "parentId": -24, "title": "Bauschutt/Restmüll in m³", "type": "string", "isRequiredSignature": "0"}, {"id": -34, "parentId": -24, "title": "Sonstiges", "type": "string", "isRequiredSignature": "0"}, {"id": -35, "title": "Unterschriftsbereich", "type": "headline", "isRequiredSignature": "0"}, {"id": -36, "parentId": -35, "title": "Ort", "type": "string", "isRequiredSignature": "0"}, {"id": -37, "parentId": -35, "title": "Datum", "type": "date", "isRequiredSignature": "0"}, {"id": -38, "parentId": -35, "title": "Name Auftraggeber", "type": "string", "isRequiredSignature": "0"}, {"id": -39, "parentId": -35, "title": "Unterschrift Auftragnehmer", "type": "signatureField", "isRequiredSignature": "0"}], "group_ids": [1, 3]}