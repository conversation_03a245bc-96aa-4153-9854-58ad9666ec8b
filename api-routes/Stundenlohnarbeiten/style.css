#table-werkzeug td {
    vertical-align: text-top;
}

body {
    width: 100%;
    margin: 30px auto;
    font-family: "Arial", sans-serif;
    font-size: 14px;
    font-weight: 400;
}

.flex-container {
    display: -webkit-box;
}

header {
    position: relative;
}

.header-right, .header-left {
    width: 50%;
}

.header-right {
    position: absolute;
    font-style: italic;
    right: 0;
    top: 0;
}

p {
    line-height: 0.2em;
}

.header-table {
    margin: 20px 0;
    width: 100%;
    border: none;
}

.header-table td:nth-child(2) {
    width: 50%;
}

hr {
    width: 100%;
    border: 1px dashed rgb(128, 128, 128);
}

main {
    margin-top: 20px;
}

.main-table {
    width: 100%;
    border: 1px solid rgb(128, 128, 128);
    border-collapse: collapse;
}

.main-table tr:nth-child(even) {
    background: #d2d6d9;
}

.main-table th, .main-table td {
    border: 1px solid rgb(128, 128, 128);
    padding: 5px 10px;
}

.main-table th {
    background: rgb(128, 128, 128);
    font-size: 18px;
    color: #ffffff;
    text-transform: capitalize;
}

.sub-table__left, .sub-table__right {
    width: 48%;
}

.sub-table__left td, .sub-table__right td {
    padding: 10px 0;
}

footer table {
    width: 100%;
}

.footer-table-1 td {
    width: 33%;
}

.mt-20 {
    margin-top: 20px;
}

.mt-50 {
    margin-top: 50px;
}
