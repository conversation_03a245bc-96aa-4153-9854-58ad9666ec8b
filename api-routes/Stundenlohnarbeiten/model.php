<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Stundenlohnarbeiten
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        $documentData = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $main = [];
        $main['id'] = $documentData['fullDocument']['id'];
        $main['title'] = $documentData['fullDocument']['title'];
        $main['type'] = $documentData['fullDocument']['type'];
        $main['required'] = $documentData['fullDocument']['required'];
        $main['reportedValues'] = [];
        // place it the first element in the children array
        array_unshift($documentData['fullDocument']['children'], $main);

        $documentDataGrouped = PrintoutHelper::groupChildrenUnderParentRecursive($documentData['fullDocument']['children'],
            'id', 'parentId');
        $document = PrintoutHelper::getDeepValueFromField('title', 'reportedValues', $documentDataGrouped[0], 'children');

        $templateData['companyLogoUrl'] = PrintoutHelper::downloadSettings($curl)['logo'];
        $templateData['companyDetails'] = $this->getSettingsInfo($curl)['address'];
        $templateData['documentData'] = $document['Stundenlohnarbeiten'];

        return $templateData;
    }

    /**
     * @return array<string, mixed>
     */
    public function getSettingsInfo(PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get', "v1/settings/info", [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}
