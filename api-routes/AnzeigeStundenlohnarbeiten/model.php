<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../utils/BundesInnungGeruestbauHelper.php';

class C_AnzeigeStundenlohnarbeiten
{
    /** @return array<string, string|array<string, string>> */
    public function getData(int $schemaId, int $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument((int)$schemaId, (int)$documentId, $curl);

        $schemaChildren = PrintoutHelper::downloadSchema($schemaId, $curl)['children'];

        $data['documentDate'] = $doc['fullDocument']['documentCreatedOn'];

        $mappedDocument = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], false, $schemaChildren);
        $ausfuehrungsdatum = "Ausführung der Stundenlohnarbeiten am";
        $data['Ausführungsdatum'] = $this->getReportedValue($doc['fullDocument'], $ausfuehrungsdatum);
        $leistungen = "Folgende Leistungen, werden wie in vorbezeichneter Angelegenheit vertraglich vereinbart, als Stundenlohnarbeiten ausgeführt";
        $data['Leistungen'] = $this->getReportedValue($doc['fullDocument'], $leistungen);
        $data['Unterschrift Auftragnehmer'] = $mappedDocument['Unterschrift Auftragnehmer'] ?? '';
        $data['Ort'] = $mappedDocument['Ort'] ?? '';
        $data['Datum'] = $mappedDocument['Datum'] ?? '';

        $projectNo = $doc['fullDocument']['documentRelKey1'];
        $data = array_merge(BundesInnungGeruestbauHelper::downloadCommonDataForHeader($curl, $projectNo), $data);

        $partial = "projectName,customerName";
        $project = PrintoutHelper::downloadProject((int)$doc['documentRelKey1'], $partial, $curl);
        $data['projectName'] = $project['projectName'];
        $data['customerName'] = $project['customerName'];

        return $data;
    }

    /**
     * @param array{children: array<mixed>} $fullDocument
     * @param string $title
     * @return string
     */
    private function getReportedValue(array $fullDocument, string $title): string
    {
        foreach ($fullDocument['children'] as $child) {
            if ($child['title'] == $title) {
                return $child['reportedValue'];
            }
        }
        return '';
    }
}

