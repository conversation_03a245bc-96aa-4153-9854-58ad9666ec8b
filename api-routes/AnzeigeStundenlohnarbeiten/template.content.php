<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_AnzeigeStundenlohnarbeiten())->getData($_GET['schemaId'], $_GET['documentId']);
}

$format = 'd.m.Y';
?>

<!doctype html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Anzeige Stundenlohnarbeiten</title>
</head>
<body>
<div class="header">
    <img src="<?= $data['logo'] ?>" alt="">
    <div class="contact">
        <p><?= $data['addressWithPhone'] ?></p>
    </div>
</div>
<table class="info-table sender-info-table">
    <tr>
        <td>Absender</td>
    </tr>
    <tr>
        <td><?= $data['companyTitle'] ?> <?= $data['companyName'] ?></td>
    </tr>
    <tr>
        <td><?= $data['companyAddress'] ?></td>
    </tr>
    <tr>
        <td><?= $data['companyPostCode'] ?> <?= $data['companyCity'] ?></td>
    </tr>
</table>
<table class="info-table client-info-table">
    <tr>
        <td>Auftraggeber</td>
    </tr>
    <tr>
        <td><?= $data['principalDisplayName'] ?></td>
    </tr>
    <tr>
        <td><?= $data['principalRealAddress'] ?></td>
    </tr>
    <tr>
        <td>
            <?= $data['principalAddressCountryCode'] ?>
            <?= $data['principalPostcode'] ?>
            <?= $data['principalAddressCity'] ?>
        </td>
    </tr>
</table>
<table class="info-table project-info-table">
    <tr>
        <td>Bauvorhaben</td>
    </tr>
    <tr>
        <td><?= $data['projectName'] ?></td>
    </tr>
    <tr>
        <?= $data['projectSiteAddress'] ?>
        <?= $data['projectSiteCity'] ?>
        <?= $data['projectSiteZipCode'] ?>
    </tr>
    <tr>
        <td>
            Auftrag vom <b><?= PrintoutHelper::dateConverter($data['projectValidStartDate'], $format) ?></b>
        </td>
    </tr>
</table>
<table class="w-full">
    <tr>
        <td>
            <h3 class="document-title">Anzeige von Stundenlohnarbeiten gem. § 15 Abs. 3 VOB/B</h3>
            <p>Sehr geehrte Damen und Herren,</p>
        </td>
    </tr>
    <tr>
        <td></td>
    </tr>
    <tr>
        <td>
            <p>in vorbezeichneter Angelegenheit haben wir vertraglich vereinbart, dass folgende Leistungen</p>
        </td>
    </tr>
    <tr>
        <td>
            <p class="service">
                <b> <?= $data['Leistungen'] ?> </b>
            </p>
        </td>
    </tr>
    <tr>
        <td>
            <p>
                als Stundenlohnarbeiten ausgeführt werden sollen.
                <br><br>
                Demgemäß erlauben wir uns, Ihnen hiermit gem. § 15 Abs. 3 VOB/B mitzuteilen, dass mit der Ausführung der
                Stundenlohnarbeiten am
            </p>
            <p class="date">
                <b>
                    <?= empty($data['Ausführungsdatum']) ? '' : PrintoutHelper::dateConverter($data['Ausführungsdatum'], $format) ?>
                </b>
            </p>
        </td>
    </tr>
    <tr>
        <td>
            <p>
                begonnen wird.
            </p>
        </td>
    </tr>
</table>
<table class="w-full">
    <tr>
        <td>
            <div class="location">
                <?php
                    if ($data['Ort'] != "" && $data['Datum'] != "") {
                        echo $data['Ort'] . ", " . PrintoutHelper::dateConverter($data['Datum'], $format);
                    } elseif ($data['Ort'] != "") {
                        echo $data['Ort'];
                    } elseif ($data['Datum'] != "") {
                        echo PrintoutHelper::dateConverter($data['Datum'], $format);
                    }
                ?>
                <p>Ort, Datum</p>
            </div>
        </td>
        <td>
            <?php if (!empty($data['Unterschrift Auftragnehmer'])) { ?>
                <img src="<?= $data['Unterschrift Auftragnehmer'] ?>" class="signature" alt="signature">
            <?php } ?>
        </td>
    </tr>
    <tr>
        <td></td>
        <td>Auftragnehmer</td>
    </tr>
</table>
</body>
</html>