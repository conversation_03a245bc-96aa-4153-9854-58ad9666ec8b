html, body {
    margin: 0;
    padding: 0;
}

* {
    font-family: Arial, sans-serif;
    font-size: 12px;
}

.w-full {
    width: 100%;
}

.header {
    width: 100%;
    display: -webkit-box;
    -webkit-box-pack: center;
    padding: 10px 0;
}

.header img {
    width: 150px;
    display: inline-block;
    clip-path: inset(0 10px 0 0);
}

.header .contact {
    display: -webkit-box;
    -webkit-box-pack: center;
    -webkit-box-orient: block-axis;
    margin-left: 15px;
}

.header .contact p {
    margin: 0;
    font-weight: lighter;
    display: inline-block;
    font-size: 0.9em;
    color: rgb(102, 102, 102);
}

.info-table {
    width: 48%;
    border-collapse: collapse;
    border: 2px solid black;
}

.info-table tr:first-child {
    background-color: #f5f5f5;
}

.info-table tr td {
    padding: 8px;
    border: 1px solid black;
}

.client-info-table {
    float: left;
    margin: 15px 0 40px;
}

.project-info-table {
    float: right;
    margin: 15px 0 40px;
}

.document-title {
    margin-bottom: 25px;
}

.service {
    padding: 20px 0;
}

.date {
    width: 100px;
    padding: 20px 0;
    margin-left: auto;
    margin-right: auto;
}

.location {
    padding-right: 30px;
}

.location p {
    width: 500px;
    margin: 0;
    border-top: 1px solid black;
}

.signature {
    width: 100px;
    height: auto;
}