<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "schemaId", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentId", required: true);
$config->wkhtmltopdfArguments = [
    "--disable-smart-shrinking",
    "--page-size", "a4",
    "--margin-bottom", "0",
    "--margin-top", "0",
    "--margin-left", "0",
    "--margin-right", "0",
    "--dpi", "300",
    "--no-pdf-compression"
];
return $config;