{"name": "Last Minute Risk Analysis", "description": "", "status": "active", "createdBy": "4", "createdOn": "2023-09-25T08:47:19Z", "editedOn": "2023-09-25T10:07:02Z", "positions": [{"id": 1, "title": "Allgemeine Arbeitsbedingungen", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Sind alle notwendigen Genehmigungen vorhanden?", "type": "checkbox"}, {"id": 3, "displayInside": 2, "title": "Beschreibung", "type": "string"}, {"id": 4, "parentId": 1, "title": "Liegt das Sicherheitsdatenblatt und der Bauplan aus?", "type": "checkbox"}, {"id": 5, "displayInside": 4, "title": "Beschreibung", "type": "string"}, {"id": 6, "title": "Arbeitsbereich", "type": "headline"}, {"id": 7, "parentId": 6, "title": "Ist der Arbeitsbereich abgesperrt und gut beleuchtet?", "type": "checkbox"}, {"id": 8, "displayInside": 7, "title": "Beschreibung", "type": "string"}, {"id": 9, "parentId": 6, "title": "Gibt es Hindernisse, die entfernt werden müssen?", "type": "checkbox"}, {"id": 10, "displayInside": 9, "title": "Beschreibung", "type": "string"}, {"id": 11, "title": "Personal", "type": "headline"}, {"id": 12, "parentId": 11, "title": "Sind alle Mitarbeiter geschult und unterwiesen?", "type": "checkbox"}, {"id": 13, "displayInside": 12, "title": "Beschreibung", "type": "string"}, {"id": 14, "parentId": 11, "title": "Ist die persönliche Schutzausrüstung (PSA) vollständig und intakt?", "type": "checkbox"}, {"id": 15, "displayInside": 14, "title": "Beschreibung", "type": "string"}, {"id": 16, "title": "Material und Werkzeug", "type": "headline"}, {"id": 17, "parentId": 16, "title": "Sind alle Materialien und Werkzeuge in gutem Zustand?", "type": "checkbox"}, {"id": 18, "displayInside": 17, "title": "Beschreibung", "type": "string"}, {"id": 19, "parentId": 16, "title": "Ist das Gerüstmaterial auf Vollständigkeit und Integrität überprüft?", "type": "checkbox"}, {"id": 20, "displayInside": 19, "title": "Beschreibung", "type": "string"}, {"id": 21, "title": "Aufbau", "type": "headline"}, {"id": 22, "parentId": 21, "title": "Werden alle Anweisungen des Bauplans genau befolgt?", "type": "checkbox"}, {"id": 23, "displayInside": 22, "title": "Beschreibung", "type": "string"}, {"id": 24, "parentId": 21, "title": "Wurde die Standsicherheit des Gerüsts überprüft?", "type": "checkbox"}, {"id": 25, "displayInside": 24, "title": "Beschreibung", "type": "string"}, {"id": 26, "title": "H<PERSON>henarbeit", "type": "headline"}, {"id": 27, "parentId": 26, "title": "Sind geeignete Auffangsysteme vorhanden?", "type": "checkbox"}, {"id": 28, "displayInside": 27, "title": "Beschreibung", "type": "string"}, {"id": 29, "parentId": 26, "title": "Wurden Maßnahmen gegen Absturz ergriffen?", "type": "checkbox"}, {"id": 30, "displayInside": 29, "title": "Beschreibung", "type": "string"}, {"id": 31, "title": "Elektrische Sicherheit", "type": "headline"}, {"id": 32, "parentId": 31, "title": "Sind alle elektrischen Leitungen und Geräte in sicherem Zustand?", "type": "checkbox"}, {"id": 33, "displayInside": 32, "title": "Beschreibung", "type": "string"}, {"id": 34, "parentId": 31, "title": "Liegt ein Prüfprotokoll für elektrische Geräte vor?", "type": "checkbox"}, {"id": 35, "displayInside": 34, "title": "Beschreibung", "type": "string"}, {"id": 36, "title": "Wetterbedingungen", "type": "headline"}, {"id": 37, "parentId": 36, "title": "Ist das Wetter stabil genug für die Arbeit?", "type": "checkbox"}, {"id": 38, "displayInside": 37, "title": "Beschreibung", "type": "string"}, {"id": 39, "parentId": 36, "title": "Gibt es Pläne für den Umgang mit schlechten Wetterbedingungen?", "type": "checkbox"}, {"id": 40, "displayInside": 39, "title": "Beschreibung", "type": "string"}, {"id": 41, "title": "Notfallmaßnahmen", "type": "headline"}, {"id": 42, "parentId": 41, "title": "Sind alle Notfallpläne und -ausrüstungen vorhanden und zugänglich?", "type": "checkbox"}, {"id": 43, "displayInside": 42, "title": "Beschreibung", "type": "string"}, {"id": 44, "parentId": 41, "title": "Sind Rettungswege und Evakuierungspläne bekannt?", "type": "checkbox"}, {"id": 45, "displayInside": 44, "title": "Beschreibung", "type": "string"}, {"id": 46, "title": "Kommunikation", "type": "headline"}, {"id": 47, "parentId": 46, "title": "Ist eine zuverlässige Kommunikation zwischen den Teammitgliedern sichergestellt?", "type": "checkbox"}, {"id": 48, "displayInside": 47, "title": "Beschreibung", "type": "string"}, {"id": 49, "parentId": 46, "title": "Gibt es eine Person, die für die Koordination der Arbeiten verantwortlich ist?", "type": "checkbox"}, {"id": 50, "displayInside": 49, "title": "Beschreibung", "type": "string"}, {"id": 51, "title": "Name Gruppenleiter", "type": "EMPLOYEESELECTOR"}]}