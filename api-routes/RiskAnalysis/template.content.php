<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_RiskAnalysis())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<string, mixed> $data
 */
function displayCheck(array $data, string $title): void
{
    if (!isset($data[$title])) return;
    PrintoutHelper::echoCheckboxByReportedValue($data[$title]);
}

?>
<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Risk Analysis</title>
</head>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Risk Analysis</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body style="font-size: 80%">
<div class="page">
    <table style="width: 100%; border: none">
        <tr>
            <td style="width: 50%; text-align: center">
                <div class="top-title">Verantwortlicher vor Ort (AvO)</div>
                <div>
                    <?= $data['Name Gruppenleiter'] ?? '' ?>
                </div>
                <br>
                Vor- und Nachname in Druckbuchstaben
            </td>
            <td style="width: 50%; text-align: center">
                <?php if (isset($data['logo']) && $data['logo']) { ?>
                    <img class="companyLogo" src="<?= $data['logo'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
    </table>
    <table class="checkboxes" style="border-collapse: collapse; font-size: 78%">
        <tr>
            <td class="td" style="width: 48%"></td>
            <td style="width: 4%; padding: 3px"><b>Status</b></td>
            <td class="td" style="width: 48%; text-align: center"><b>Beschreibung</b></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>1. Allgemeine
                    Arbeitsbedingungen</b></td>
        </tr>
        <tr>
            <td class="td">Sind alle notwendigen Genehmigungen vorhanden?</td>
            <td class="td checkboxSize"
                style="text-align: center"><?php displayCheck($data, "Sind alle notwendigen Genehmigungen vorhanden?") ?></td>
            <td class="td"><?= $data['Beschreibung'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Liegt das Sicherheitsdatenblatt und der Bauplan aus?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Liegt das Sicherheitsdatenblatt und der Bauplan aus?") ?> </td>
            <td class="td"><?= $data['Beschreibung2'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding">
                <b>2. Arbeitsbereich</b>
            </td>
        </tr>
        <tr>
            <td class="td">Ist der Arbeitsbereich abgesperrt und gut beleuchtet?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Ist der Arbeitsbereich abgesperrt und gut beleuchtet?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung3'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Gibt es Hindernisse, die entfernt werden müssen?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Gibt es Hindernisse, die entfernt werden müssen?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung4'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>3. Personal</b></td>
        </tr>
        <tr>
            <td class="td">Sind alle Mitarbeiter geschult und unterwiesen?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Sind alle Mitarbeiter geschult und unterwiesen?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung5'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Ist die persönliche Schutzausrüstung (PSA) vollständig
                und intakt?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Ist die persönliche Schutzausrüstung (PSA) vollständig und intakt?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung6'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>4. Material und
                    Werkzeug</b></td>


        </tr>
        <tr>
            <td class="td">Sind alle Materialien und Werkzeuge in gutem Zustand?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Sind alle Materialien und Werkzeuge in gutem Zustand?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung7'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Ist das Gerüstmaterial auf Vollständigkeit und Integrität
                überprüft?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Ist das Gerüstmaterial auf Vollständigkeit und Integrität überprüft?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung8'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>5. Aufbau</b></td>
        </tr>
        <tr>
            <td class="td">Werden alle Anweisungen des Bauplans genau befolgt?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Werden alle Anweisungen des Bauplans genau befolgt?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung9'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Wurde die Standsicherheit des Gerüsts überprüft?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Wurde die Standsicherheit des Gerüsts überprüft?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung10'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>6. Höhenarbeit</b>
            </td>
        </tr>
        <tr>
            <td class="td">Sind geeignete Auffangsysteme vorhanden?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Sind geeignete Auffangsysteme vorhanden?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung11'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Wurden Maßnahmen gegen Absturz ergriffen?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Wurden Maßnahmen gegen Absturz ergriffen?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung12'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>7. Elektrische Sicherheit</b></td>
        </tr>
        <tr>
            <td class="td">Sind alle elektrischen Leitungen und Geräte in sicherem
                Zustand?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Sind alle elektrischen Leitungen und Geräte in sicherem Zustand?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung13'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Liegt ein Prüfprotokoll für elektrische Geräte vor?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Liegt ein Prüfprotokoll für elektrische Geräte vor?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung14'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>8. Wetterbedingungen</b>
            </td>
        </tr>
        <tr>
            <td class="td">Ist das Wetter stabil genug für die Arbeit?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Ist das Wetter stabil genug für die Arbeit?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung15'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Gibt es Pläne für den Umgang mit schlechten Wetterbedingungen?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Gibt es Pläne für den Umgang mit schlechten Wetterbedingungen?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung16'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>9. Notfallmaßnahmen</b></td>
        </tr>
        <tr>
            <td class="td">Sind alle Notfallpläne und -ausrüstungen vorhanden und zugänglich?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Sind alle Notfallpläne und -ausrüstungen vorhanden und zugänglich?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung17'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Sind Rettungswege und Evakuierungspläne bekannt?</td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Sind Rettungswege und Evakuierungspläne bekannt?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung18'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="3" class="headlinePadding"><b>10. Kommunikation</b>
            </td>
        </tr>
        <tr>
            <td class="td">Ist eine zuverlässige Kommunikation zwischen den Teammitgliedern sichergestellt?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Ist eine zuverlässige Kommunikation zwischen den Teammitgliedern sichergestellt?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung19'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="td">Gibt es eine Person, die für die Koordination der Arbeiten verantwortlich ist?
            </td>
            <td class="td checkboxSize"
                style="text-align: center">
                <?php displayCheck($data, "Gibt es eine Person, die für die Koordination der Arbeiten verantwortlich ist?") ?>
            </td>
            <td class="td"><?= $data['Beschreibung20'] ?? '' ?></td>
        </tr>
</div>
</body>
</html>
