<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ScaffoldListItem())->getData($_GET['ktr'], $_GET['intnr']);
}

/**
 * @param array<string, mixed> $measurement
 */
function displayMeasurement(array $measurement): void
{
    $values = array_filter(
        [
            $measurement['quantityCount'],
            $measurement['quantityLength'],
            $measurement['quantityDepth'],
            $measurement['quantityHeight']
        ],
        function ($value) {
            return $value > 0;
        }
    );
    $formattedValues = array_map(fn($value) => number_format($value, 2, ',', ''), $values);
    $measurementDetails = implode(' x ', $formattedValues);
    $quantity = str_replace(".", ",", $measurement['quantity']);
    $measurementRow =
        "<tr>" .
        "<td>&nbsp;</td>" .
        "<td>$measurementDetails</td>" .
        "<td>" . $quantity . $measurement['measuringUnitKey'] . "</td>" .
        "</tr>";

    echo $measurementRow;
}

function printEmptyRows(int $numRows): void
{
    for ($i = 0; $i < $numRows; $i++) {
        echo "<tr>
                <td colspan='1'>&nbsp;</td>
                <td colspan='1'>&nbsp;</td>
                <td colspan='1'>&nbsp;</td>
              </tr>";
    }
}

?>

<!doctype html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Document</title>
</head>
<body>
<h3 class="text-align-center">СКЕЛЕТА ПРОИЗВОДСТВО НА СЯРНА КИСЕЛИНА</h3>
<?php require_once __DIR__ . "/header_table.php"; ?>
<table class="main-table">
    <tr>
        <td style="border-bottom: 0; border-right: 0"></td>
        <td class="text-align-center" style="border-left: 0">Date/ДATA</td>
        <td>
            <?= !empty($data['scaffoldListItem']['vhbeg'])
                ? date("d.m.Y", strtotime($data['scaffoldListItem']['vhbeg'])) . 'г.'
                : '' ?>
        </td>
    </tr>
    <tr>
        <td style="border-top: 0; border-right: 0"></td>
        <td class="text-align-center" style="border-left: 0">Name/Име</td>
        <td>&nbsp;</td>
    </tr>

    <?php
    $counter = 1;
    foreach ($data['scaffoldListMeasurement'] as $measurement) {
        echo
            "<tr>" .
            "<td class='text-align-right'>" . $counter . "</td>" .
            "<td class='font-weight-bold'>" . $measurement['shortDescription'] . "</td>" .
            "<td>&nbsp;</td>" .
            "</tr>";
        displayMeasurement($measurement);
        $counter++;
    }
    printEmptyRows(2);
    ?>
</table>

<table class="footer-table">
    <tr>
        <td style="padding: 4px">DATE / ДATA</td>
        <td style="padding: 4px">Name + signature Company</td>
        <td style="padding: 4px">Name + signature Aurubis</td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td style="padding-left: 4px">Име + подпис за фирмата</td>
        <td style="padding-left: 4px">Име + подпис за Аурубис</td>
    </tr>
    <tr>
        <td style="padding: 4px"><?=
            !empty($data['scaffoldListItem']['datum']) ?
                date("d.m.Y", strtotime($data['scaffoldListItem']['datum'])) . 'г.' : ''
            ?>
        </td>
        <td style="padding: 4px"><?= $data['employee']['displayName'] ?? '' ?></td>
        <td style="padding: 4px"><?= $data['partnerName'] ?? '' ?></td>
    </tr>
</table>
</body>
</html>