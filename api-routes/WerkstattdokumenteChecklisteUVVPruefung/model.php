<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_WerkstattdokumenteChecklisteUVVPruefung
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string|null $schemaId, string|null $documentId): array
    {
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument((int)$schemaId, (int)$documentId, $curl)['fullDocument'];
        $data['schema'] = PrintoutHelper::mapDocumentChildrenToValues($doc, injectParentTitle: true);
        $relType = $doc['documentRelType'] ?? '';
        if ($relType == 'resource') {
            $rnr = $doc['documentRelKey1'] ?? null;
            if ($rnr) {
                $data['vehicleResource'] = PrintoutHelper::downloadResource($rnr, $curl);
            }
        }
        return $data;
    }
}