<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection HtmlDeprecatedAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_WerkstattdokumenteChecklisteUVVPruefung())->getData($_GET['schemaId'], $_GET['documentId']);
}
function formatDate(string $raw): string
{
    if (!$raw) {
        return '';
    }
    $ts = strtotime($raw);
    return $ts === false ? '' : date('d.m.Y', $ts);
}

$rows = [
    'Lichttechnische Einrichtungen',
    'Scheibenwischer/Waschanlage',
    'Verglasung/Beschädigungen/freie <PERSON>cht',
    'Spiegel innen/außen',
    'Hupe/Signalhorn',
    'Lenkung inkl. Der Hydraulikanlage',
    'Bremse/n (auch Handbremse usw.)',
    'Reifenluftdruck/Ventilkappen',
    'Profiltiefe/Zustand Bereifung',
    'Radmuttern/Bolzen',
    'Befestigung der Sitze',
    'Fahrzeugheizung/Lüftungseinrichtung/Kühlgeräte',
    'Sicherheitsgurte Zustand/Funktion',
    'Sichere Befestigung zulässiger Aufbauten',
    'Befestigung sonstiger Einbauten wie Navigeräte',
    'Abgasanlage und Fahrwerksdämpfer',
    'Warnweste Zustand/leicht zugänglich',
    'Warndreieck vorhanden/leicht zugänglich',
    'Verbandskasten vorhanden (Verfallsdatum)',
    'Einrichtungen zur Ladungssicherung',
    'Anhängervorrichtung (falls vorhanden)',
    'Sicherung gegen unbefugte Nutzung',
    'Betriebsanleitungen vom Hersteller vorhanden',
    'Feuerlöscher (Prüfungsdatum)',
];

$questions = [
    'Checkliste zur UVV-Prüfung Stehen einem Weiterbetrieb Bedenken – nach Abstellen der Mängel – entgegen' => 'Stehen einem Weiterbetrieb Bedenken – nach Abstellen der Mängel – entgegen?',
    'Checkliste zur UVV-Prüfung Ist eine Nachprüfung erforderlich' => 'Ist eine Nachprüfung erforderlich?',
    'Checkliste zur UVV-Prüfung Wurde eine Prüfplakette geklebt' => 'Wurde eine Prüfplakette geklebt?',
];
$nextDueKey = 'Checkliste zur UVV-Prüfung Nächste Prüfung fällig am';
?>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Werkstattdokumente: Checkliste zur UVV Prüfung</title>
</head>
<body>
<div class="page">
    <table class="first-header-table">
        <tr>
            <td>
                <u>
                    <strong class="header-title">
                        Checkliste zur UVV-Prüfung von Fahrzeugen
                    </strong><br>
                    <span class="header-subtitle">
                        Durch eine Befähigte Person gem. der DGUV Vorschrift 70, dem DGUV Grundsatz 314-003, der DGUV 109.008 und 109-009 sowie die DGUV Informationen 209-007
                    </span>
                </u>
            </td>
        </tr>
    </table>

    <table class="header-info-table">
        <tr>
            <td class="pr-5">Hersteller/Modell:</td>
            <td><u><?= $data['vehicleResource']['lteartikel'] ?? '' ?></u></td>
            <td class="pr-5">Fahrzeughalter:</td>
            <td><u><?= $data['schema']['Checkliste zur UVV-Prüfung Fahrzeughalter'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td class="pr-5">Fahrgestellnummer:</td>
            <td><u><?= $data['vehicleResource']['vin'] ?? '' ?></u></td>
            <td class="pr-5">Anschrift:</td>
            <td><u><?= $data['schema']['Checkliste zur UVV-Prüfung Anschrift'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td class="pr-5">Amt. Kennzeichen:</td>
            <td><u><?= $data['vehicleResource']['kurzname'] ?? '' ?></u></td>
            <td colspan="2"></td>
        </tr>
        <tr>
            <td class="pr-5">Aktueller km-Stand:</td>
            <td><u><?= $data['schema']['Checkliste zur UVV-Prüfung Aktueller km-Stand'] ?? '' ?></u></td>
            <td colspan="2"></td>
        </tr>
    </table>

    <table class="main-table">
        <tr class="header-row">
            <td><strong>Prüfpunkte</strong></td>
            <td><strong>i.O.</strong></td>
            <td><strong>n.i.O.</strong></td>
            <td><strong>Mängelbeschreibung/Behebung</strong></td>
            <td><strong>Datum</strong></td>
        </tr>
        <?php foreach ($rows as $i => $label) {
            $suffix = $i === 0 ? '' : $i + 1;
            $statusKey = "$label $label";
            $mangelKey = "Werkstattdokumente: Checkliste zur UVV Prüfung Mängelbeschreibung/Behebung$suffix";
            $dateKey = "Werkstattdokumente: Checkliste zur UVV Prüfung Datum$suffix";

            $rawStatus = $data['schema'][$statusKey] ?? '';
            $mangelText = $data['schema'][$mangelKey] ?? '';
            $rawDate = $data['schema'][$dateKey] ?? '';

            $low = strtolower($rawStatus);
            $isOk = stripos($low, 'i.o.') !== false && stripos($low, 'n.i.o.') === false;
            $notOk = stripos($low, 'n.i.o.') !== false;
            ?>
            <tr>
                <td><?= $label ?></td>
                <td class="text-center"><?= $isOk ? '&#10003;' : '' ?></td>
                <td class="text-center"><?= $notOk ? '&#10003;' : '' ?></td>
                <td><?= $mangelText ?></td>
                <td><?= formatDate($rawDate) ?></td>
            </tr>
        <?php } ?>
    </table>

    <table class="last-table">
        <?php foreach ($questions as $key => $label) {
            $value = $data['schema'][$key] ?? '';
            ?>
            <tr>
                <td colspan="2"><strong><?= $label ?></strong></td>
                <td>
                    <span class="checkbox-large"><?= $value === 'Ja' ? '&#9745;' : '&#9744;' ?></span>Ja
                </td>
                <td>
                    <span class="checkbox-large pl-20"><?= $value === 'Nein' ? '&#9745;' : '&#9744;' ?></span>Nein
                </td>
            </tr>
        <?php } ?>
        <tr>
            <td colspan="2" class="pt-10">
                <strong>Nächste Prüfung fällig am:
                    <u><?= formatDate($data['schema'][$nextDueKey] ?? '') ?></u>
                </strong>
            </td>
            <td colspan="2"></td>
        </tr>
    </table>
    <table class="page-text">
        <tr class="date-signature">
            <td class="date-field">
                <div>
                    <?php
                    $ort = $data['schema']['Checkliste zur UVV-Prüfung Ort (Sachkundiger)'] ?? '';
                    $rawDate = $data['schema']['Checkliste zur UVV-Prüfung Datum (Sachkundiger)'] ?? '';
                    $datum = $rawDate ? formatDate($rawDate) : '';
                    echo implode(', ', array_filter([$ort, $datum]));
                    ?>
                </div>
            </td>
            <td class="w-20"></td>
            <td class="date-field">
                <div class="img-date-container">

                    <?php if (!empty($data['schema']['Checkliste zur UVV-Prüfung Unterschrift (Sachkundiger)'])) { ?>
                        <img src="<?= $data['schema']['Checkliste zur UVV-Prüfung Unterschrift (Sachkundiger)'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </div>
            </td>
        </tr>
        <tr class="date-signature">
            <td class="date-field border-top va-top">
                Ort, Datum
            </td>
            <td class="w-20"></td>
            <td class="date-field border-top">
                Unterschrift/Stempel<br>
                <strong>(Sachkundiger)</strong>
            </td>
        </tr>

        <tr class="date-signature">
            <td class="date-field">
                <div>
                    <?php
                    $ort = $data['schema']['Checkliste zur UVV-Prüfung Ort (Betriebsverantwortlicher)'] ?? '';
                    $rawDate = $data['schema']['Checkliste zur UVV-Prüfung Datum (Betriebsverantwortlicher)'] ?? '';
                    $datum = $rawDate ? formatDate($rawDate) : '';
                    echo implode(', ', array_filter([$ort, $datum]));
                    ?>
                </div>
            </td>
            <td class="w-20"></td>
            <td class="date-field">
                <div class="img-date-container">

                    <?php if (isset($data['schema']['Checkliste zur UVV-Prüfung Unterschrift (Betriebsverantwortlicher)'])) { ?>
                        <img src="<?= $data['schema']['Checkliste zur UVV-Prüfung Unterschrift (Betriebsverantwortlicher)'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </div>
            </td>
        </tr>
        <tr class="date-signature">
            <td class="date-field border-top va-top">
                Ort, Datum
            </td>
            <td class="w-20"></td>
            <td class="date-field border-top">
                Unterschrift/Stempel<br>
                <strong>(Betriebsverantwortlicher)</strong>
            </td>
        </tr>
    </table>
    <div class="page-text-footer">
        <strong>Hinweis:</strong>
        <p>
            Gewerblich genutzte Fahrzeuge müssen nach §57 Abs. der Unfallverhütungsvorschriften (UVV) „Fahrzeuge“ einmal
            jährlich durch
            einen sachkundigen Prüfer auf einen betriebssicheren Zustand geprüft werden. Wer dieser Verpflichtung nicht
            nachkommt, riskiert
            ein Bußgeld. Die Prüfung ist schriftlich zu dokumentieren und gemäß §57 Abs. 2 der UVV und des
            DGUV-Grundsatzes „Fahrzeuge“
            bis zur nächsten Prüfung im Fahrzeug mitzuführen. Die Prüfung ersetzt nicht die amtliche Hauptuntersuchung
            (HU=. Siehe auch
            TRBS 1201 und die VDI 4068 usw. dazu was man alles Prüfen muss und wer eine Befähigte Person ist.
        </p>
    </div>
</div>
</body>
</html>