@page {
    size: 210mm 297mm;
}

* {
    font-family: Arial, sans-serif;
    font-size: 15px;
}

body {
    width: 230mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

.page {
    page-break-before: always;
    max-height: 297mm;
    min-height: 297mm;
    min-width: 210mm;
    max-width: 210mm;
    box-sizing: border-box;
}

.main-table-text {
    width: 100%;
    text-align: left
}

.first-header-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
}

.first-header-table td {
    padding: 20px !important;
}

.first-header-table td,
.main-table td {
    padding: 5px;
    border-right: 1px solid black;
    page-break-inside: avoid;
}

.header-info-table {
    width: 100%;
    margin: 40px 0;
}

.main-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    margin-top: 5px;
}

.main-table td {
    border-bottom: 1px solid black;
}

.main-table td:last-child {
    height: 40px;
}

.last-table {
    width: 100%;
    page-break-inside: avoid;
    page-break-before: always;
}

u {
    text-underline-offset: 2px;
}

.header-info-table td u {
    text-decoration: none;
    display: inline-block;
}

.header-info-table td u:not(:empty) {
    text-decoration: underline;
}

.header-info-table td u:empty {
    width: 120px;
    height: 1em;
    border-bottom: 1px solid;
}

.checkbox-size {
    font-size: 24px;
    background-color: #3965af;
    color: white;
    margin-right: .5rem;
    display: inline-block;
    width: 40px;
    height: 30px;
    text-align: center;
}

.checkbox-size.unchecked {
    color: #3965af;
}

.last-section {
    vertical-align: bottom;
}

.signature {
    max-height: 200px;
    object-fit: contain;
    max-width: 50%;
}

.page-text {
    padding-top: 50px;
    width: 100%;
    text-align: left;
    page-break-inside: avoid;
}

.date-signature {
    display: table;
    width: 100%;
    page-break-inside: avoid;
}

.date-field {
    display: table-cell;
    width: 40%;
    text-align: center;
    vertical-align: bottom;
}

.img-date-container {
    display: table-cell;
    width: 40%;
    text-align: center;
}

.border-top {
    border-top: 1px solid black;
    padding-top: 10px;
    margin: 0 50px;
}

.text-center {
    text-align: center;
    vertical-align: middle;
}

.header-title {
    font-size: 18px;
}

.header-subtitle {
    font-size: 16px;
    display: block;
    margin-top: 0.25em;
}


.main-table .header-row {
    background-color: #f2f2f2;
}

.last-table {
    margin-top: 30px;
    page-break-inside: avoid;
}

.page-text-footer {
    padding-top: 50px;
    page-break-inside: avoid;
    text-align: left;
}

.page-text-footer strong {
    display: block;
    font-size: 16px;
}

.checkbox-large {
    font-size: 30px;
    vertical-align: middle;
}

.pr-5 {
    padding-right: 5px;
}

.w-20 {
    width: 20%;
}

.pl-20 {
    padding-left: 20px;
}

.pt-10 {
    padding-top: 10px;
}

.va-top {
    vertical-align: top;
}
