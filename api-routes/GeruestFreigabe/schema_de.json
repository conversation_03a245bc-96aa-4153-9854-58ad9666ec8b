{"name": "Gerüstfreigabe v2", "description": "Protokoll für Gerüstfreigabe", "status": "active", "createdBy": "4", "createdOn": "2019-03-29T07:02:54Z", "positions": [{"id": 1, "title": "Arbeitsbeschreibung", "type": "string", "default": "${WORKING_ORDER_TITLE} ${WORKING_ORDER_DESCRIPTION}"}, {"id": 2, "title": "Örtlichkeit", "type": "string"}, {"id": 3, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 4, "title": "Koordinaten", "type": "string"}, {"id": 5, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 6, "title": "Ausführungsteam", "type": "headline"}, {"id": 7, "parentId": 6, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "EMPLOYEESELECTOR"}, {"id": 8, "parentId": 6, "title": "Unterschrift Job-Verantwortlicher", "type": "signatureField"}, {"id": 9, "parentId": 6, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "EMPLOYEESELECTOR"}, {"id": 10, "parentId": 6, "title": "Unterschrift Job-Ausführender", "type": "signatureField"}, {"id": 11, "title": "Gerüstart", "type": "headline"}, {"id": 12, "parentId": 11, "title": "Gerüstart", "type": "combobox-multi", "required": "true", "value": ["Arbeitsgerüst", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Sonstiges"]}, {"id": 13, "parentId": 11, "title": "Gerüstart Sonstiges", "type": "string"}, {"id": 14, "parentId": 11, "title": "Lastklasse", "type": "combobox", "required": "true", "value": ["2 (1,5 KN/m²)", "3 (2,0 KN/m²)", "4 (3,0 KN/m²)", "Sonstiges", "1 (0,75 KN/m²)"]}, {"id": 15, "parentId": 11, "title": "Lastklasse Sonstiges", "type": "string"}, {"id": 16, "parentId": 11, "title": "Breitenklasse", "type": "combobox", "required": "true", "value": ["W06", "W09", "Sonstiges"]}, {"id": 17, "parentId": 11, "title": "Breitenklasse Sonstiges", "type": "string"}, {"id": 18, "title": "Freigabe + Verlängerung", "type": "headline"}, {"id": 19, "parentId": 18, "title": "Datum", "type": "date", "default": "${CURRENT_DATE}"}, {"id": 20, "parentId": 18, "title": "Name <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 21, "parentId": 18, "title": "Unterschrift Gerüstbauer", "type": "signatureField"}, {"id": 22, "title": "Abnahme durch Gerüstnutzer", "type": "headline"}, {"id": 23, "parentId": 22, "title": "Datum", "type": "date", "default": "${CURRENT_DATE}"}, {"id": 24, "parentId": 22, "title": "Name <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 25, "parentId": 22, "title": "Unterschrift Gerüstnutzer", "type": "signatureField"}]}