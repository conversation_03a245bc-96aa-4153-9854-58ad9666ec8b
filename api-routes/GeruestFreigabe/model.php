<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_GeruestFreigabe
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $documentData = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl)['fullDocument'];
        $data['schema'] = PrintoutHelper::mapDocumentChildrenToValues($documentData, injectParentTitle: true);
        $documentType = $documentData['documentRelType'] ?? '';
        $language = PrintoutHelper::getLanguage();
        $data['translatedSchemaKeys'] = $this->getTranslatedSchemaKeys($language);

        if ($documentType === 'project' || $documentType === 'workingOrder') {
            $data['schema']['location'] = $data['schema'][$data['translatedSchemaKeys']['location']] ?? '';
            $data['schema']['requester'] = $data['schema'][$data['translatedSchemaKeys']['requester']] ?? '';
            $projectData = PrintoutHelper::downloadProject($documentData['documentRelKey1'], 'customerName', $curl);
            $data['schema']['customer'] = $projectData['customerName'] ?? '';
        } elseif ($documentType === 'scaffold') {
            $data['scaffoldList'] = $this->downloadScaffoldList($documentData['documentRelKey1'], $documentData['documentRelKey2'], $curl);
            $data['schema']['location'] = $data['scaffoldList'][0]['einsatzort'] ?? '';

            $projectData = PrintoutHelper::downloadProject($documentData['documentRelKey1'], 'customerName', $curl);
            $data['schema']['customer'] = $projectData['customerName'] ?? '';

            if (!empty($data['scaffoldList'][0]['requesteeVirtualNo'])) {
                $partner = $this->downloadPartner($data['scaffoldList'][0]['requesteeVirtualNo'], $curl);
                $name = empty($partner) ? '' : "{$partner[0]['firstName']} {$partner[0]['lastName']}";
                $data['schema']['requester'] = $name;
            }
        }

        if ($documentData) {
            $employeeMapping = [
                'Job-Ausführender' => 'Ausführungsteam Job-Ausführender',
                'Job-Verantwortlicher' => 'Ausführungsteam Job-Verantwortlicher',
                'Job-Manager' => 'Execution Team Job Manager',
                'Job-Executor' => 'Execution Team Job Executor'
            ];

            foreach ($employeeMapping as $dataKey => $schemaKey) {
                $data[$dataKey] = !empty($data['schema'][$schemaKey])
                    ? (PrintoutHelper::downloadEmployee($data['schema'][$schemaKey], $curl)['displayName'] ?? '')
                    : '';
            }
            $data['companyLogo'] = PrintoutHelper::downloadSettings($curl)['logo'];
            $data['dokumentNr'] = $documentId;
            $data['blankTitle'] = $documentData['title'];
        }

        $data['scaffoldTypeOptions'] = ($language === 'en')
            ? ['Work Scaffold', 'Hanging Scaffold', 'Load-bearing Scaffold', 'Mobile Scaffold', 'Protective Scaffold', 'Other']
            : ['Arbeitsgerüst', 'Hängegerüst', 'Lastgerüst', 'Fahrgerüst', 'Schutzgerüst', 'Sonstiges'];

        $data['loadClassValues'] = ($language === 'en')
            ? ["1 (0.75 KN/m²)", "2 (1.5 KN/m²)", "3 (2.0 KN/m²)", "4 (3.0 KN/m²)"]
            : ["1 (0,75 KN/m²)", "2 (1,5 KN/m²)", "3 (2,0 KN/m²)", "4 (3,0 KN/m²)"];

        $data['widthClassValues'] = ($language === 'en')
            ? ['W06', 'W09', 'Other']
            : ['W06', 'W09', 'Sonstiges'];

        return $data;
    }

    /**
     * @return array<string, string>
     */
    private function getTranslatedSchemaKeys(string $language): array
    {
        return [
            'work_description' => ($language === 'en')
                ? 'Scaffold Release v2 Work Description'
                : 'Gerüstfreigabe v2 Arbeitsbeschreibung',
            'location' => ($language === 'en')
                ? 'Scaffold Release v2 Location'
                : 'Gerüstfreigabe v2 Örtlichkeit',
            'level' => ($language === 'en')
                ? 'Scaffold Release v2 Level'
                : 'Gerüstfreigabe v2 Ebene',
            'coordinates' => ($language === 'en')
                ? 'Scaffold Release v2 Coordinates'
                : 'Gerüstfreigabe v2 Koordinaten',
            'requester' => ($language === 'en')
                ? 'Scaffold Release v2 Requester'
                : 'Gerüstfreigabe v2 Anforderer',
            'job_manager' => ($language === 'en')
                ? 'Job-Manager'
                : 'Job-Verantwortlicher',
            'job_manager_signature' => ($language === 'en')
                ? 'Execution Team Signature of Job Manager'
                : 'Ausführungsteam Unterschrift Job-Verantwortlicher',
            'job_executor' => ($language === 'en')
                ? 'Job-Executor'
                : 'Job-Ausführender',
            'job_executor_signature' => ($language === 'en')
                ? 'Execution Team Signature of Job Executor'
                : 'Ausführungsteam Unterschrift Job-Ausführender',
            'approval_date' => ($language === 'en')
                ? 'Approval & Extension Date'
                : 'Freigabe + Verlängerung Datum',
            'approval_name' => ($language === 'en')
                ? 'Approval & Extension Scaffolder Name'
                : 'Freigabe + Verlängerung Name Gerüstbauer',
            'approval_signature' => ($language === 'en')
                ? 'Approval & Extension Signature of Scaffolder'
                : 'Freigabe + Verlängerung Unterschrift Gerüstbauer',
            'user_acceptance_date' => ($language === 'en')
                ? 'Scaffold User Acceptance Date'
                : 'Abnahme durch Gerüstnutzer Datum',
            'user_acceptance_name' => ($language === 'en')
                ? 'Scaffold User Acceptance Scaffold User Name'
                : 'Abnahme durch Gerüstnutzer Name Gerüstnutzer',
            'user_acceptance_signature' => ($language === 'en')
                ? 'Scaffold User Acceptance Signature of Scaffold User'
                : 'Abnahme durch Gerüstnutzer Unterschrift Gerüstnutzer',
            'scaffold_type' => ($language === 'en')
                ? 'Type of Scaffold Type of Scaffold'
                : 'Gerüstart Gerüstart',
            'load_class' => ($language === 'en')
                ? 'Type of Scaffold Load Class'
                : 'Gerüstart Lastklasse',
            'width_class' => ($language === 'en')
                ? 'Type of Scaffold Width Class'
                : 'Gerüstart Breitenklasse',
        ];
    }

    /**
     * @return array<mixed>
     */
    public function downloadScaffoldList(string $documentRelKey1, string $documentRelKey2, PrintoutCurl $curl): array
    {
        $url = "v1/scaffoldlists/select/$documentRelKey1/$documentRelKey2";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }

    /**
     * @return array<mixed>
     */
    private function downloadPartner(int $virtualNo, PrintoutCurl $curl): array
    {
        $url = "v1/partners?virtualNo=$virtualNo";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }
}
