{"name": "Scaffold Release v2", "description": "Protocol for Scaffold Release", "status": "active", "createdBy": "365", "createdOn": "2025-04-16T07:02:54Z", "positions": [{"id": 1, "title": "Work Description", "type": "string", "default": "${WORKING_ORDER_TITLE} ${WORKING_ORDER_DESCRIPTION}"}, {"id": 2, "title": "Location", "type": "string"}, {"id": 3, "title": "Level", "type": "string"}, {"id": 4, "title": "Coordinates", "type": "string"}, {"id": 5, "title": "Requester", "type": "string"}, {"id": 6, "title": "Execution Team", "type": "headline"}, {"id": 7, "parentId": 6, "title": "Job Manager", "type": "EMPLOYEESELECTOR"}, {"id": 8, "parentId": 6, "title": "Signature of Job Manager", "type": "signatureField"}, {"id": 9, "parentId": 6, "title": "Job Executor", "type": "EMPLOYEESELECTOR"}, {"id": 10, "parentId": 6, "title": "Signature of Job Executor", "type": "signatureField"}, {"id": 11, "title": "Type of Scaffold", "type": "headline"}, {"id": 12, "parentId": 11, "title": "Type of Scaffold", "type": "combobox-multi", "required": "true", "value": ["Work Scaffold", "Hanging Scaffold", "Load-bearing Scaffold", "Mobile Scaffold", "Protective Scaffold", "Other"]}, {"id": 13, "parentId": 11, "title": "Other Scaffold Type", "type": "string"}, {"id": 14, "parentId": 11, "title": "Load Class", "type": "combobox", "required": "true", "value": ["2 (1.5 KN/m²)", "3 (2.0 KN/m²)", "4 (3.0 KN/m²)", "Other", "1 (0.75 KN/m²)"]}, {"id": 15, "parentId": 11, "title": "Other Load Class", "type": "string"}, {"id": 16, "parentId": 11, "title": "Width Class", "type": "combobox", "required": "true", "value": ["W06", "W09", "Other"]}, {"id": 17, "parentId": 11, "title": "Other Width Class", "type": "string"}, {"id": 18, "title": "Approval & Extension", "type": "headline"}, {"id": 19, "parentId": 18, "title": "Date", "type": "date", "default": "${CURRENT_DATE}"}, {"id": 20, "parentId": 18, "title": "Scaffolder Name", "type": "string"}, {"id": 21, "parentId": 18, "title": "Signature of Scaffolder", "type": "signatureField"}, {"id": 22, "title": "Scaffold User Acceptance", "type": "headline"}, {"id": 23, "parentId": 22, "title": "Date", "type": "date", "default": "${CURRENT_DATE}"}, {"id": 24, "parentId": 22, "title": "Scaffold User Name", "type": "string"}, {"id": 25, "parentId": 22, "title": "Signature of Scaffold User", "type": "signatureField"}]}