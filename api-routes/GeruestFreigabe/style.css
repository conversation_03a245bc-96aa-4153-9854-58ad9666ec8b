.page {
    height: 297mm;
    width: 210mm;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    width: 230mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

* {
    font-family: Arial, sans-serif;
    font-size: 13px;
}

table td {
    padding: 2px !important;
    page-break-inside: avoid;
}

table {
    width: 100%;
    border: 1px solid black;
    border-collapse: collapse;
}

.header-table td {
    border: 1px solid black;
}

.header {
    width: 100%;
    mso-cellspacing: 0;
    border-collapse: collapse;
    border-bottom: none !important;
}

.header tr td {
    border: 1px solid black;
}

.logo {
    width: 70px;
}

.logo > img {
    height: 50px;
    width: 50px;
}

.mainContent {
    width: 100%;
    border-collapse: collapse;
    mso-cellspacing: 0;
}

.mainContent tr td {
    border-top: 0;
    border-left: 1px solid black;
    border-right: 1px solid black;
    border-bottom: 1px solid black;
    padding: 0;
}

.lastklasse-breitenklasse {
    width: 100%;
    border: none !important;
}

.lastklasse-breitenklasse td {
    border: none !important;
}

.header-abnahme {
    width: 100%;
    mso-cellspacing: 0;
    border-collapse: collapse;
    border-top: 0;
}

.header-abnahme tr td {
    padding: 0;
    border-left: 1px solid black;
    border-right: 1px solid black;
    border-bottom: 1px solid black;
}

.client-title {
    width: 417px;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 30px;
    text-align: center;
}

.row-color {
    background-color: lightgrey;
}

.textSize {
    font-size: 13px;
    padding: 2px;
}

.gerust-table {
    width: 100%;
    border-collapse: collapse;
    border-bottom: hidden;
}

.gerust-table tr td {
    border: 1px solid black;
}

.textUnderImages {
    width: 100%;
    border-collapse: collapse;
    mso-cellspacing: 0;
}

.signature {
    height: 45px;
    max-height: 45px;
    object-fit: contain;
    width: 75px;
    max-width: 100%;
}

.textUnderImages tr {
    border-left: 1px solid black;
    border-right: 1px solid black;
    padding: 0;
}

.verboten_text {
    border-collapse: collapse;
}

.verboten_text tr td {
    border: 1px solid black;
    padding: 0;
}

.eigenmahtige-text {
    color: #ffffff;
    text-decoration: underline;
    text-transform: uppercase;
    font-weight: bold;
    font-size: 18px;
    text-align: center;
}

.eigenmahtige-text2 {
    color: #ffffff;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    padding: 0;
}

.gerustGesperrt-title {
    text-align: center;
    background-color: lightgrey;
    height: 20%;
    font-weight: 900;
    font-size: 40px;
    letter-spacing: 3px;
}

.gerust-row-border > td {
    border: 1px solid black !important;
}

.gerust-row-border.cell-titles {
    font-size: x-large;
}

.images-gerust > img {
    height: 150px;
    margin-right: 10px;
}

.images-gerust > img:nth-child(2) {
    height: 220px;
    margin-top: 5px;
}

table.images-underText > td.images-text.row-color {
    font-size: 20px;
    font-weight: bold;
}

.undersigns-text {
    white-space: nowrap;
    font-weight: bold;
    font-size: 26px;
    letter-spacing: 3px;
}

.text-title {
    background-color: darkgrey;
    color: #ffffff;
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    padding: 0;
}