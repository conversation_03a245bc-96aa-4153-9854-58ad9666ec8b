<?php /** @noinspection DuplicatedCode */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ErsteHilfeMassnahme())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @throws Exception
 */
function formatDateTime(string|null $date = null, string|null $time = null): string
{
    if ($date == null && $time == null) {
        return "";
    }
    if ($date == null) {
        return $time;
    }
    $dateString = (new DateTime($date))->format('d.m.Y');
    if ($time == null) {
        return $dateString;
    }
    return $dateString . ' ' . $time;
}

/**
 * @param array<string, mixed> $data
 */
function displayField(array $data, string $field, string $default = ''): string
{
    return $data[$field] ?? $default;
}

?>

<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Dokumentation von Erste-Hilfe-Leistungen</title>
</head>
<body>
<div class="container">
    <div class="heading">
        Dokumentation von Erste-Hilfe-Leistungen
        <span class="heading-sec">(§ 24 Abs. 6 der DGUV Vorschrift 1 "Grundsätze der Prävention")</span>
    </div>
    <table class="form-name">
        <tr class="table-label">
            <td>Name der verletzten bzw. erkrankten Person</td>
        </tr>
        <tr class="table-field">
            <td><?= nl2br(displayField($data, 'Erste-Hilfe Maßnahme Name der verletzten bzw. erkrankten Person')) ?></td>
        </tr>
    </table>
    <table class="form-table">
        <tr>
            <th colspan="2">Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens</th>
        </tr>
        <tr class="table-label">
            <td>Datum/Uhrzeit</td>
        </tr>
        <tr class="table-field">
            <td>
                <?=
                /** @noinspection PhpUnhandledExceptionInspection */
                formatDateTime(
                    $data['Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Datum'] ?? null,
                    $data['Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Uhrzeit'] ?? null
                )
                ?>
            </td>
        </tr>
        <tr class="table-label">
            <td>Ort (Unternehmensteil)</td>
        </tr>
        <tr class="table-field">
            <td>
                <?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Ort (Unternehmensteil)')) ?>
            </td>
        </tr>
        <tr class="table-label">
            <td>Hergang</td>
        </tr>
        <tr class="table-field">
            <td>
                <?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Hergang')) ?>
            </td>
        </tr>
        <tr class="table-label">
            <td>Art und Umfang der Verletzung/Erkrankung</td>
        </tr>
        <tr class="table-field">
            <td>
                <?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Art und Umfang der Verletzung/Erkrankung')) ?>
            </td>
        </tr>
        <tr class="table-label">
            <td>Name der Zeugen</td>
        </tr>
        <tr class="table-field">
            <td>
                <?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Name der Zeugen')) ?>
            </td>
        </tr>
        <tr class="table-label">
            <th colspan="2">Erste-Hilfe-Leistungen</th>
        </tr>
        <tr class="table-label">
            <td>Datum/Uhrzeit</td>
        </tr>
        <tr class="table-field">
            <td>
                <?=
                /** @noinspection PhpUnhandledExceptionInspection */
                formatDateTime(
                    $data['Erste-Hilfe-Leistungen Datum'] ?? null,
                    $data['Erste-Hilfe-Leistungen Uhrzeit'] ?? null
                )
                ?>
            </td>
        </tr>
        <tr class="table-label">
            <td>Art und Weise der Erste-Hilfe-Maßnahmen</td>
        </tr>
        <tr class="table-field">
            <td>
                <?= nl2br(displayField($data, 'Erste-Hilfe-Leistungen Art und Weise der Erste-Hilfe-Maßnahmen')) ?>
            </td>
        </tr>
        <tr class="table-label">
            <td>Name des Ersthelfers/der Ersthelferin</td>
        </tr>
        <tr class="table-field">
            <td>
                <?= nl2br(displayField($data, 'Erste-Hilfe-Leistungen Name des Ersthelfers/der Ersthelferin')) ?>
            </td>
        </tr>
    </table>
    <div class="page-footer">
        <p class="stand-date">Stand: 01/2023 </p>
    </div>
    <div class="footer-section">
        <div>
            <strong>Aufzeichnung der Erste-Hilfe-Leistung</strong>
            <p>Über jede Erste-Hilfe-Leistung müssen nach §24 Abs. 6 der DGUV Vorschrift 1 „Grundsätze der Prävention“
                Aufzeichnungen geführt und <strong>fünf</strong> Jahre lang aufbewahrt werden. Die Aufzeichnungen sind
                vertraulich zu
                behandeln.</p>
            <p>Die Angaben dienen als Nachweis, dass die Verletzung/Erkrankung bei einer versicherten Tätigkeit ein-
                bzw. aufgetreten ist. Diese Aufzeichnungen können sehr wichtig sein, wenn z.B. Spätfolgen eintreten
                sollten.</p>
            <p>Diese Aufzeichnungen der im Betrieb erfolgten Erste-Hilfe-Leistungen sind nicht zuletzt auch
                Informationsquelle für die Erfassung, Untersuchung und Auswertung von nicht
                meldepflichtigenArbeitsunfällen, die vom Betriebsarzt oder der Betriebsärztin und von der Fachkraft für
                Arbeitssicherheit durchzuführen sind.</p>
        </div>
        <hr>
        <div>
            <strong>Verfahrenshinweis</strong>
            <p>Diese Formulare sollten idealerweise gemeinsam mit dem Erste-Hilfe-Material aufbewahrt werden.</p>
            <p>Die ausgefüllten Formulare sollten an einem Ort gesammelt werden, an dem der Zugriff Unbefugter vermieden
                werden kann.</p>
        </div>
    </div>
</div>
</body>
</html>
