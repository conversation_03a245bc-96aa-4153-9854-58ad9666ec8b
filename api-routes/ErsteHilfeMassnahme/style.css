body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 5px;
}

.form-name, .form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    border: 1px solid #0c0c0c;
}

.form-table th {
    background-color: #565655;
    color: #fbf7f7;
    padding: 10px;
    font-weight: bold;
    text-align: center;
}

.form-table td, .form-name td {
    padding: 10px 10px 4px;
    vertical-align: top;
    background-color: transparent;
}

.table-field {
    background-color: #e5e8f4;
    min-height: 25px;
    display: block;
    margin-left: 10px;
    margin-right: 10px;
    margin-bottom: 6px;
}

.table-label {
    border-top: 1px solid black;
}

.table-label td {
    font-weight: bold;
}


.table-padding {
    padding: 10px;
}

.section-header {
    font-size: 1.5em;
    margin-top: 20px;
    text-align: center;
    text-transform: uppercase;
    color: #0c0c0c;
}

.heading {
    font-size: 22px;
    color: #444;
    text-align: center;
    font-weight: 900;
    padding-top: 20px;
    padding-bottom: 60px;
    display: grid;
}

.heading-sec {
    font-weight: 600;
}

.footer-section {
    page-break-before: always;
    font-size: 0.9em;
    padding-top: 200px;
}

.footer-section span {
    display: block;
    text-align: start;
    margin: 20px;
    padding-top: 10px;
    padding-bottom: 10px;
}

.stand-date {
    bottom: 10px;
    font-size: 0.9em;
    color: #333;
}

.page-footer {
    margin: 0 auto;
}

hr {
    margin: 60px 0;
}