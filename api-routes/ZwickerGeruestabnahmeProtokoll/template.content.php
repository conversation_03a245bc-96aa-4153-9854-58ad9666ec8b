<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ZwickerGeruestabnahmeProtokoll())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<string, mixed> $data
 */
function getChecks(array $data, int $index): string
{
    $element = $data['mappedChildren'][$index];
    if (empty($element['value'])) {
        return "&#9744;";
    }
    if ($element['value'] === true) {
        return "&#9745;";
    } else {
        return "&#9744;";
    }
}

/**
 * @param array<string, mixed> $data
 */
function displayNutzlast(array $data, string $option): string
{
    $element = $data['mappedChildren'][7];
    if (empty($element['value'])) {
        return "&#9744;";
    }
    if ($element['value'] === $option)
        return "&#9745;";
    return "&#9744;";
}

/**
 * @param array<string, mixed> $data
 */
function getTitle(array $data, int $index): string
{
    $element = $data['mappedChildren'][$index];
    $exceptionalTitles = ['Absturzsicherung Läufe (innen + aussen) vorhanden',
        'Spenglerlauf: Durchgangsbreite min. 60cm + SPL-Netz nach Vorschrift'];
    if (empty($element['all']['title'])) {
        return '';
    }
    if (in_array($element['all']['title'], $exceptionalTitles)) {
        return handleExceptionalTitle($element['value'], $element['all']['title']);
    }
    return $element['all']['title'];
}

function handleExceptionalTitle(string $reportedValue, string $title): string
{
    if ($title === 'Absturzsicherung Läufe (innen + aussen) vorhanden') {
        if ($reportedValue === 'in Ordnung - Läufe innen') {
            return 'Absturzsicherung Läufe (innen) vorhanden';
        }
        if ($reportedValue === 'in Ordnung - Läufe außen') {
            return 'Absturzsicherung Läufe (außen) vorhanden';
        }
        return $title;
    }
    if ($title === 'Spenglerlauf: Durchgangsbreite min. 60cm + SPL-Netz nach Vorschrift') {
        if ($reportedValue === 'in Ordnung - ohne SPL-Netz') {
            return 'Durchgangsbreite min. 60cm ohne SPL-Netz nach Vorschrift';
        }
        return $title;
    }
    die('Unknown title passed to handleExceptionalTitle(): ' . $title);
}

/**
 * @param array<string, mixed> $data
 */
function getComboboxOption(array $data, int $index): string
{
    $element = $data['mappedChildren'][$index];
    if (empty($element['value'])) {
        return "<td>&#9744;</td><td>&#9744;</td><td>&#9744;</td>";
    }
    $ioOptions = ['in Ordnung', 'in Ordnung - Läufe innen', 'in Ordnung - Läufe außen', 'in Ordnung - Läufe innen + außen',
        'in Ordnung - mit SPL-Netz', 'in Ordnung - ohne SPL-Netz'];
    if (in_array($element['value'], $ioOptions)) {
        return
            "<td>&#9745;</td>" .
            "<td>&#9744;</td>" .
            "<td>&#9744;</td>";
    } elseif ($element['value'] === 'nicht in Ordnung') {
        return
            "<td>&#9744;</td>" .
            "<td>&#9745;</td>" .
            "<td>&#9744;</td>";
    } else {
        return
            "<td>&#9744;</td>" .
            "<td>&#9744;</td>" .
            "<td>&#9745;</td>";
    }
}

/**
 * @param array<string, mixed> $data
 */
function getReportedValue(array $data, int $index, string $type = ''): string
{
    if (empty($data['mappedChildren'][$index]['value']))
        return '';
    $value = $data['mappedChildren'][$index]['value'];
    if ($type === 'date') {
        return PrintoutHelper::dateConverter($value, 'd.m.Y');
    } elseif ($type === 'photo') {
        return sprintf('<img src="%s" style="max-height: 150px; max-width: 200px"  alt=""/>', $value);
    }
    return $data['mappedChildren'][$index]['value'];
}

/**
 * @param array<string, mixed> $data
 */
function displayPhoto(array $data, int $index, string $parentTitle): void
{
    if (empty($data['mappedChildren'][$index]['value']))
        return;
    $photos = $data['mappedChildren'][$index]['value'];

    foreach ($photos as $photo) {
        echo '<p style="page-break-before: always"></p>';
        echo '<div class="image-container">';
        echo '<h4>' . $parentTitle . '</h4>';
        echo '<img src="' . ($photo['filePath'] ?? '') . '" alt="Image">';
        echo '</div>';
    }
}

/**
 * @param array<string, mixed> $data
 */
function displayComments(array $data, int $index): string
{
    $comment = $data['mappedChildren'][$index]['value'] ?? '';
    if (strtolower($comment) === 'keine bemerkungen') {
        return '';
    }
    return $comment;
}

?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Zwicker Gerüstabnahme-Protokoll</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<table class="header-table">
    <tr>
        <td>Zwicker Gerüste AG</td>
        <td rowspan="2">Jedes Gerüst wird vor dem Gebrauch durch den Benutzer kontrolliert! Entspricht das Gerüst nicht
            den Vorschriften, wird sofort der Bauleitung Meldung erstattet. Bei erheblicher Gefahr wird die Arbeit
            ausgesetzt.
            Mit Eingang des Gerüstabnahme-Protokolls geht die Aufsichts- & Instandhaltungspflicht an den Besteller über.
            Mit diesem Protokoll erfolgt die Übergabe des Gerüsts an den Besteller (Art. 370 OR). Mit der Benützung des
            Gerüsts durch den Auftraggeber oder Dritte, gilt die stillschweigende Genehmigung der Betriebsbereitschaft.
            Die Freigabe des Gerüsts an die Benutzer erfolgt durch den Besteller.
        </td>
    </tr>
    <tr>
        <td class="header-title">Gerüstabnahme-Protokoll</td>
    </tr>
</table>

<p class="font-sm">Stand: 22.04.2020</p>

<table class="sub-header-table">
    <tr>
        <td><?= getChecks($data, 2) ?></td>
        <td>Neubau</td>
        <td><?= getChecks($data, 3) ?></td>
        <td>Fassade</td>
        <td><?= getChecks($data, 4) ?></td>
        <td>Dach/Dachdecker</td>
        <td><?= getChecks($data, 5) ?></td>
        <td>Etappe</td>
        <td><?= getChecks($data, 6) ?></td>
        <td>
            Kontrolle (regelmäßig)
        </td>
        <td>&nbsp;</td>
        <td style="text-align: right">Nutzlast</td>
        <td style="text-align: right"><?= displayNutzlast($data, '2,0kN/m²') ?></td>
        <td>
            2 kN/m²
        </td>
    </tr>

    <tr>
        <td><?= getChecks($data, 8) ?></td>
        <td>Renovation</td>
        <td><?= getChecks($data, 9) ?></td>
        <td>Spengler</td>
        <td><?= getChecks($data, 10) ?></td>
        <td>Lukamen</td>
        <!-- sonstiges not in this schema so leave blank -->
        <td>&#9744;</td>
        <td>&nbsp;</td>
        <td>&#9744;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td style="text-align: right"><?= displayNutzlast($data, '3,0kN/m²') ?></td>
        <td>
            3 kN/m²
        </td>
    </tr>
    <tr>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td style="text-align: right"><?= displayNutzlast($data, '4,5kN/m²') ?></td>
        <td style="width: 65px">
            4,5 kN/m²
        </td>
    </tr>
</table>

<div class="main">
    <table class="main-table-head">
        <tr>
            <!--Auftrag seems to be always empty since Hinweistext is not in this schema-->
            <td>Auftrag:</td>
            <td>Objekt: <?= $data['project']['projectName'] ?? '' ?></td>
        </tr>
    </table>

    <table class="main-table">
        <tr>
            <td>Kontrollpunkte</td>
            <td>i.O.</td>
            <td>n. i.O.</td>
            <td>nicht relevant</td>
            <td>Bemerkungen</td>
        </tr>
        <tr>
            <td><?= getTitle($data, 12) ?></td>
            <?= getComboboxOption($data, 12) ?>
            <td><?= displayComments($data, 32) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 13) ?></td>
            <?= getComboboxOption($data, 13) ?>
            <td><?= displayComments($data, 33) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 14) ?></td>
            <?= getComboboxOption($data, 14) ?>
            <td><?= displayComments($data, 34) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 15) ?></td>
            <?= getComboboxOption($data, 15) ?>
            <td><?= displayComments($data, 35) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 16) ?></td>
            <?= getComboboxOption($data, 16) ?>
            <td><?= displayComments($data, 36) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 17) ?></td>
            <?= getComboboxOption($data, 17) ?>
            <td><?= displayComments($data, 37) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 18) ?></td>
            <?= getComboboxOption($data, 18) ?>
            <td><?= displayComments($data, 38) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 19) ?></td>
            <?= getComboboxOption($data, 19) ?>
            <td><?= displayComments($data, 39) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 20) ?></td>
            <?= getComboboxOption($data, 20) ?>
            <td><?= displayComments($data, 40) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 21) ?></td>
            <?= getComboboxOption($data, 21) ?>
            <td><?= displayComments($data, 41) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 22) ?></td>
            <?= getComboboxOption($data, 22) ?>
            <td><?= displayComments($data, 42) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 23) ?></td>
            <?= getComboboxOption($data, 23) ?>
            <td><?= displayComments($data, 43) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 24) ?></td>
            <?= getComboboxOption($data, 24) ?>
            <td><?= displayComments($data, 44) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 25) ?></td>
            <?= getComboboxOption($data, 25) ?>
            <td><?= displayComments($data, 45) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 26) ?></td>
            <?= getComboboxOption($data, 26) ?>
            <td><?= displayComments($data, 46) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 27) ?></td>
            <?= getComboboxOption($data, 27) ?>
            <td><?= displayComments($data, 47) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 28) ?></td>
            <?= getComboboxOption($data, 28) ?>
            <td><?= displayComments($data, 48) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 29) ?></td>
            <?= getComboboxOption($data, 29) ?>
            <td><?= displayComments($data, 49) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 30) ?></td>
            <?= getComboboxOption($data, 30) ?>
            <td><?= displayComments($data, 50) ?></td>
        </tr>
        <tr>
            <td><?= getTitle($data, 31) ?></td>
            <?= getComboboxOption($data, 31) ?>
            <td></td>
        </tr>
    </table>

    <p>Mängelbehebung bis: <?= getReportedValue($data, 51, 'date') ?></p>
</div>

<div class="signature">
    <table class="signature-table">
        <tr>
            <td>Datum & Unterschriften…</td>
            <td> Protokoll gesendet am (Datum)</td>
            <td><?= PrintoutHelper::dateConverter($data['fullDocument']['documentCreatedOn'], 'd.m.Y') ?></td>
            <td>per</td>
            <td>
                <div>E-Mail</div>
                <div style="font-size: 18px">
                    <?php PrintoutHelper::echoCheckbox(in_array('E-Mail', $data['mappedChildren'][52]['value'] ?? [])); ?>
                </div>
            </td>
            <td>
                <div>Fax</div>
                <div style="font-size: 18px">
                    <?php PrintoutHelper::echoCheckbox(in_array('Fax', $data['mappedChildren'][52]['value'] ?? [])) ?>
                </div>
            </td>
            <td>
                <div>Post</div>
                <div style="font-size: 18px">
                    <?php PrintoutHelper::echoCheckbox(in_array('Post', $data['mappedChildren'][52]['value'] ?? [])) ?>
                </div>
            </td>
        </tr>
        <tr>
            <td>...der Chefmonteur:</td>
            <td style="padding: 10px 0;">
                <div><?= $data['Chefmonteur'] ?></div>
                <div>
                    <?= getReportedValue($data, 56, 'photo') ?>
                </div>
            </td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td colspan="3">
                <div>an Besteller:</div>
                <div><?= getReportedValue($data, 59) ?></div>
            </td>
        </tr>
        <tr>
            <td> ...der KOPAS:</td>
            <td style="padding: 10px 0;">
                <div><?= $data['KOPAS'] ?></div>
                <div>
                    <?= getReportedValue($data, 58, 'photo') ?>
                </div>
            </td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td colspan="3">
                <div>(Fax-Nr./Adr.)</div>
                <div><?= getReportedValue($data, 60) ?></div>
            </td>
        </tr>
    </table>
</div>
</body>
</html>