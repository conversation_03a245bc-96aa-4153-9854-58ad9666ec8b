{"name": "Gerüstabnahme-Protokoll v15", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2023-02-15T15:40:00Z", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["8"], "positions": [{"id": 1, "title": "Das Gerüst ist für folgende Arbeiten erstellt:", "type": "headline"}, {"id": 2, "title": "Neubau", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 3, "title": "Fassade", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 4, "title": "<PERSON><PERSON>/<PERSON>", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 5, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 6, "title": "Kontrolle (regelmäßig)", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 7, "title": "Nutzlast", "type": "combobox", "parentId": 1, "values": ["2,0kN/m²", "3,0kN/m²", "4,5kN/m²"]}, {"id": 8, "title": "Renovation", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 9, "title": "<PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 10, "title": "Lukamen", "type": "checkbox", "parentId": 1, "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": 11, "title": "Kontrollpunkte", "type": "headline"}, {"id": 12, "title": "Baustellensignalisation ausreichend", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 13, "title": "Bemerkungen", "displayInside": 12, "type": "string"}, {"id": 14, "title": "Hinweisschilder / Passantenschutz montiert", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 15, "title": "Bemerkungen", "displayInside": 14, "type": "string"}, {"id": 16, "title": "Fundament / Abstellbasis sauber", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 17, "title": "Bemerkungen", "displayInside": 16, "type": "string"}, {"id": 18, "title": "<PERSON>zahl Verankerungen genügend vorhanden", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 19, "title": "Bemerkungen", "displayInside": 18, "type": "string"}, {"id": 20, "title": "Aussenabstützung nach Vorschrift", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 21, "title": "Bemerkungen", "displayInside": 20, "type": "string"}, {"id": 22, "title": "Abstände zur Fassade max. 30cm", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 23, "title": "Bemerkungen", "displayInside": 22, "type": "string"}, {"id": 24, "title": "Absturzsicherung Läufe (innen + aussen) vorhanden", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 25, "title": "Bemerkungen", "displayInside": 24, "type": "string"}, {"id": 26, "title": "Absturzsicherung Übergänge / Ecken", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 27, "title": "Bemerkungen", "displayInside": 26, "type": "string"}, {"id": 28, "title": "Absturzsicherung stirnseitig (3-teilig)", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 29, "title": "Bemerkungen", "displayInside": 28, "type": "string"}, {"id": 30, "title": "Zugänge bei Treppenaufgängen / Anzahl Treppenaufgänge", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 31, "title": "Bemerkungen", "displayInside": 30, "type": "string"}, {"id": 32, "title": "Leiternaufgänge g<PERSON>t", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 33, "title": "Bemerkungen", "displayInside": 32, "type": "string"}, {"id": 34, "title": "Spenglerlauf: Durchgangsbreite min. 60cm + SPL-Netz nach Vorschrift", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 35, "title": "Bemerkungen", "displayInside": 34, "type": "string"}, {"id": 36, "title": "Absturzsicherung im Dachbereich längsseitig (Traufe) min. 80cm", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 37, "title": "Bemerkungen", "displayInside": 36, "type": "string"}, {"id": 38, "title": "Absturzsicherung im Dachbereich giebelseitig 100cm", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 39, "title": "Bemerkungen", "displayInside": 38, "type": "string"}, {"id": 40, "title": "Qualität Gerüstmaterial (insbesondere Gerüstbeläge)", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 41, "title": "Bemerkungen", "displayInside": 40, "type": "string"}, {"id": 42, "title": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 43, "title": "Bemerkungen", "displayInside": 42, "type": "string"}, {"id": 44, "title": "Aufbau und Systemvorschriften eingehalten", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 45, "title": "Bemerkungen", "displayInside": 44, "type": "string"}, {"id": 46, "title": "Allgemeiner Eindruck", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 47, "title": "Bemerkungen", "displayInside": 46, "type": "string"}, {"id": 48, "title": "Baustelle aufgeräumt + Restmaterial aufgeladen", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 49, "title": "Bemerkungen", "displayInside": 48, "type": "string"}, {"id": 50, "title": "Nutzlast angeben", "type": "combobox", "parentId": 11, "values": ["in Ordnung", "nicht in Ordnung", "nicht relevant"]}, {"id": 52, "title": "Mängelbehebung bis", "type": "date", "collapsed": "1"}, {"id": 53, "title": "per", "type": "combobox-multi", "values": ["E-Mail", "Fax", "Post"]}, {"id": 54, "title": "Protokoll gesendet am (Datum)", "type": "date"}, {"id": 55, "title": "Datum", "type": "date"}, {"id": 56, "title": "Chefmonteur", "type": "EMPLOYEESELECTOR"}, {"id": 57, "title": "Unterschrift der Chefmonteur", "type": "signatureField"}, {"id": 58, "title": "KOPAS", "type": "EMPLOYEESELECTOR"}, {"id": 59, "title": "Unterschrift der KOPAS", "type": "signatureField"}, {"id": 60, "title": "an Besteller:", "type": "string"}, {"id": 61, "title": "(Fax-Nr./Adr.):", "type": "string"}]}