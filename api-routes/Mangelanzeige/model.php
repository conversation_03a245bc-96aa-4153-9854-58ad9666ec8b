<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Mangelanzeige
{
    /**
     * @return array<mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true);
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        $data['localId'] = $doc['fullDocument']['localId'];
        return $data;
    }
}