<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Mangelanzeige())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Mangelanzeige</title>
    <style>
        body {
            border: 0;
            margin: 0;
            font-family: Arial, sans-serif;
            padding: 0;
        }

        .image-bottom {
            max-height: 200mm;
            max-width: 180mm;
            object-fit: contain
            margin-bottom: 20mm;
        }

        .table-padding {
            padding-left: 5px;
            padding-top: 5px;
            padding-bottom: 5px;
            border-bottom: 2px solid black;
        }

        .table-padding-no-border {
            padding-left: 5px;
            padding-top: 5px;
            padding-bottom: 5px;
        }

        .checkbox-width {
            width: 4%;
            border-bottom: 2px solid black;
        }

        .checkbox-width-no-border {
            width: 4%;
        }
    </style>
</head>
<body>
<table style="width: 100%; border: 2px solid black; border-collapse: collapse">
    <tr>
        <td class="table-padding" style="width: 58%; height: 50px"><b>Schäfer Baulogistik GmbG</b></td>
        <td colspan="2" style="text-align: center; vertical-align: top; border-left: 2px solid black">Objekt</td>
    </tr>
    <tr>
        <td></td>
        <td colspan="2"
            style="vertical-align: bottom; text-align: center; border-left: 2px solid black; border-bottom: 2px solid black">
            Universelle, Science Park 3 in Ulm
        </td>
    </tr>
    <tr>
        <td style="text-align: center"><img src="<?= $data['logo'] ?>" alt=""></td>
        <!-- use height only until we have the logo -->
        <td style="vertical-align: top; text-align: center; border-left: 2px solid black">
            Mangel Bericht-Nr.
            <br>
            <?= $data['localId'] ?>
        </td>
        <td style="vertical-align: top; text-align: center; border-left: 2px solid black">Datum
            <br> <?php
            if (!empty($data['Mangelanzeige Datum'])) {
                /** @noinspection PhpUnhandledExceptionInspection */
                $datum = new DateTime($data['Mangelanzeige Datum']);
                echo $datum->format('d.m.Y');
            }
            ?>
        </td>
    </tr>
</table>
<table
        style="width: 100%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; border-collapse: collapse">
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Bauleiter:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Bauleiter'] ?? '' ?></td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Kurzbeschreibung des Mangels:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Kurzbeschreibung des Mangels'] ?? '' ?></td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Auftragnehmer:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Auftragnehmer'] ?? '' ?></td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Feststellungsdatum:</td>
        <td class="table-padding" colspan="5">
            <?php
            if (!empty($data['Mangelanzeige Feststellungsdatum'])) {
                /** @noinspection PhpUnhandledExceptionInspection */
                $datum = new DateTime($data['Mangelanzeige Feststellungsdatum']);
                echo $datum->format('d.m.Y'); // Ausgabe im Format DD.MM.YYYY
            }
            ?>
        </td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Gewerk:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Gewerk'] ?? '' ?></td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Lagebestimmung:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Lagebestimmung'] ?? '' ?></td>
    </tr>
    <tr>
        <td class="table-padding" style="width: 7%">Bauteil:</td>
        <td class="checkbox-width">
            <?php if (isset($data['Mangelanzeige Bauteil'][0]) && $data['Mangelanzeige Bauteil'][0] == "OB1") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> OB1
        </td>
        <td class="checkbox-width"> <?php if (isset($data['Mangelanzeige Bauteil'][1]) && $data['Mangelanzeige Bauteil'][1] == "OB2") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> OB2
        </td>
        <td class="checkbox-width"> <?php if (isset($data['Mangelanzeige Bauteil'][2]) && $data['Mangelanzeige Bauteil'][2] == "OB3") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> OB3
        </td>
        <td colspan="2" style="border-bottom: 2px solid black"></td>
    </tr>
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
    <tr>
        <td class="table-padding-no-border">Geschoß/Ebene:</td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][0]) && $data['Mangelanzeige Geschoß/Ebene'][0] == "TG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> TG
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][1]) && $data['Mangelanzeige Geschoß/Ebene'][1] == "H1") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> H1
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][2]) && $data['Mangelanzeige Geschoß/Ebene'][2] == "Z1") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> Z1
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][3]) && $data['Mangelanzeige Geschoß/Ebene'][3] == "HZ") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> HZ
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][4]) && $data['Mangelanzeige Geschoß/Ebene'][4] == "EG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> EG
        </td>
    </tr>
    <tr>
        <td class="table-padding-no-border" style="color: white">a</td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][5]) && $data['Mangelanzeige Geschoß/Ebene'][5] == "1. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 1. OG
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][6]) && $data['Mangelanzeige Geschoß/Ebene'][6] == "2. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 2. OG
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][7]) && $data['Mangelanzeige Geschoß/Ebene'][7] == "3. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 3. OG
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][8]) && $data['Mangelanzeige Geschoß/Ebene'][8] == "4.OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 4. OG
        </td>
        <td class="checkbox-width-no-border"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][9]) && $data['Mangelanzeige Geschoß/Ebene'][9] == "5. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 5. OG
        </td>
    </tr>
    <tr>
        <td class="table-padding" style="color: white">a</td>
        <td class="checkbox-width"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][10]) && $data['Mangelanzeige Geschoß/Ebene'][10] == "6. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 6. OG
        </td>
        <td class="checkbox-width"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][11]) && $data['Mangelanzeige Geschoß/Ebene'][11] == "7. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 7. OG
        </td>
        <td class="checkbox-width"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][12]) && $data['Mangelanzeige Geschoß/Ebene'][12] == "8. OG") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> 8. OG
        </td>
        <td class="checkbox-width"><?php if (isset($data['Mangelanzeige Geschoß/Ebene'][13]) && $data['Mangelanzeige Geschoß/Ebene'][13] == "Technik") {
                ?> &#9745; <?php
            } else { ?>
                &#9744; <?php
            }
            ?> Technik
        </td>
        <td style="border-bottom: 2px solid black"></td>
    </tr>
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
    <tr>
        <td class="table-padding">Achse:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Achse'] ?? '' ?></td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Ergänzende Lokalisierung:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Ergänzende Lokalisierung'] ?? '' ?></td>
    </tr>
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Mangelbeschreibung:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Mangelbeschreibung'] ?? '' ?></td>
    </tr>
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Anlagen:</td>
        <td class="checkbox-width">
            <?php PrintoutHelper::echoCheckboxByReportedValue(count($data['Mangelanzeige Skizzen'] ?? []) >= 1) ?>
            Skizzen
        </td>
        <td class="checkbox-width"></td>
        <td class="checkbox-width">
            <?php PrintoutHelper::echoCheckboxByReportedValue(count($data['Mangelanzeige Fotos'] ?? []) >= 1) ?>
            Fotos
        </td>
        <td class="checkbox-width"></td>
        <td class="checkbox-width">
            <?php PrintoutHelper::echoCheckboxByReportedValue(count($data['Mangelanzeige Planausschnitte'] ?? []) >= 1) ?>
            Planausschnitte
        </td>
    </tr>
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
    <tr>
        <td class="table-padding" style="font-weight: bold">Fristsetzung:</td>
        <td class="table-padding" colspan="5"><?= $data['Mangelanzeige Mangelbeschreibung'] ?? '' ?></td>
    </tr>
    <tr>
        <td colspan="6" class="table-padding" style="color: white">a</td>
    </tr>
</table>
<table
        style="width: 100%; border-left: 2px solid black; border-right: 2px solid black; border-bottom: 2px solid black; border-collapse: collapse">
    <tr>
        <td colspan="5" style="height: 30px"></td>
    </tr>
    <tr style="height: 30px">
        <td style="font-weight: bold; width: 25%; padding-left: 5px">Feststellung SBL:</td>
        <td>
            <?php
            if (!empty($data['Feststellung SBL Datum'])) {
                /** @noinspection PhpUnhandledExceptionInspection */
                $datum = new DateTime($data['Feststellung SBL Datum']);
                echo $datum->format('d.m.Y');
            }
            ?>
        </td>
        <td style=" width: 3%"></td>
        <td>
            <img style="max-height: 40px; width: auto"
                 src="<?= $data['Feststellung SBL Unterschrift'] ?? '' ?>" alt=""></td>
        <td style=" width: 3%"></td>
    </tr>
    <tr style="height: 5px">
        <td></td>
        <td style="font-size: 70%; vertical-align: top; border-top: 1px solid black">Datum</td>
        <td></td>
        <td style="font-size: 70%; vertical-align: top; border-top: 1px solid black">Unterschrift</td>
        <td style=" width: 3%"></td>
    </tr>

    <tr style="height: 30px">
        <td style="font-weight: bold; width: 32%; padding-left: 5px">Freimeldung AN:</td>
        <td>
            <?php
            if (!empty($data['Freimeldung AN Datum'])) {
                /** @noinspection PhpUnhandledExceptionInspection */
                $datum = new DateTime($data['Freimeldung AN Datum']);
                echo $datum->format('d.m.Y');
            }
            ?>
        </td>
        <td style=" width: 3%"></td>
        <td><img style="max-height: 40px; width: auto"
                 src="<?= $data['Freimeldung AN Unterschrift'] ?? '' ?>" alt=""></td>
        <td style=" width: 3%"></td>
    </tr>
    <tr style="height: 5px">
        <td></td>
        <td style="font-size: 70%; vertical-align: top; border-top: 1px solid black">Datum</td>
        <td></td>
        <td style="font-size: 70%; vertical-align: top; border-top: 1px solid black">Unterschrift</td>
        <td style=" width: 3%"></td>
    </tr>

    <tr style="height: 30px">
        <td style="font-weight: bold; width: 32%; padding-left: 5px">Bestätigung SBL:</td>
        <td>
            <?php
            if (!empty($data['Bestätigung SBL Datum'])) {
                /** @noinspection PhpUnhandledExceptionInspection */
                $datum = new DateTime($data['Bestätigung SBL Datum']);
                echo $datum->format('d.m.Y');
            }
            ?>
        </td>
        <td style=" width: 3%"></td>
        <td>
            <img style="max-height: 40px; width: auto"
                 src="<?= $data['Bestätigung SBL Unterschrift'] ?? '' ?>" alt=""></td>
        <td style=" width: 3%"></td>
    </tr>
    <tr style="height: 5px">
        <td></td>
        <td style="font-size: 70%; vertical-align: top; border-top: 1px solid black">Datum</td>
        <td></td>
        <td style="font-size: 70%; vertical-align: top; border-top: 1px solid black">Unterschrift</td>
        <td style=" width: 3%"></td>
    </tr>
    <tr>
        <td colspan="5" style="height: 30px"></td>
    </tr>
</table>

<?php

/**
 * @param array<mixed> $data
 */
function displayImages(array $data, string $key): void
{
    $fullKey = 'Mangelanzeige ' . $key;
    if (!array_key_exists($fullKey, $data)) {
        return;
    }
    $files = $data[$fullKey];
    if (count($files) >= 1) { ?>
        <p style="font-weight: bold; font-size: 30px; margin-top: 20mm"><?= $key ?></p>
        <?php foreach ($files as $file) { ?>
            <img class="image-bottom" src="<?= $file['filePath'] ?>" alt="">
            <?php
        }
    }
}

displayImages($data, 'Skizzen');
displayImages($data, 'Fotos');
displayImages($data, 'Planausschnitte');
?>

</body>
</html>
