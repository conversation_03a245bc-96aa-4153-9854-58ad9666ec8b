{"name": "Mangelanzeige", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 3, "title": "Datum", "type": "date"}, {"id": 4, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 5, "title": "Kurzbeschreibung des Mangels", "type": "string"}, {"id": 6, "title": "Auftragnehm<PERSON>", "type": "string"}, {"id": 7, "title": "Feststellungsdatum", "type": "date"}, {"id": 8, "title": "Gewerk", "type": "string"}, {"id": 9, "title": "Lagebestimmung", "type": "string"}, {"id": 10, "title": "Bauteil", "type": "combobox-multi", "required": "false", "values": [{"value": "OB1"}, {"value": "OB2"}, {"value": "OB3"}]}, {"id": 11, "title": "Geschoß/Ebene", "type": "combobox-multi", "required": "false", "values": [{"value": "TG"}, {"value": "H1"}, {"value": "Z1"}, {"value": "HZ"}, {"value": "EG"}, {"value": "1. OG"}, {"value": "2. O<PERSON>"}, {"value": "3. <PERSON><PERSON>"}, {"value": "4. <PERSON><PERSON>"}, {"value": "5. <PERSON><PERSON>"}, {"value": "6. <PERSON><PERSON>"}, {"value": "7. <PERSON><PERSON>"}, {"value": "8. <PERSON><PERSON>"}, {"value": "Technik"}]}, {"id": 12, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 13, "title": "Ergänzende Lokalisierung", "type": "string"}, {"id": 14, "title": "Mangelbeschreibung", "type": "string"}, {"id": 151, "title": "Skizzen", "type": "photo"}, {"id": 152, "title": "Fotos", "type": "photo"}, {"id": 153, "title": "Planausschnitte", "type": "photo"}, {"id": 16, "title": "Fristsetzung", "type": "string"}, {"id": 17, "title": "Feststellung SBL", "type": "headline"}, {"id": 18, "parentId": 17, "title": "Datum", "type": "date"}, {"id": 19, "parentId": 17, "title": "Unterschrift", "type": "signatureField"}, {"id": 20, "title": "Freimeldung AN", "type": "headline"}, {"id": 21, "parentId": 20, "title": "Datum", "type": "date"}, {"id": 22, "parentId": 20, "title": "Unterschrift", "type": "signatureField"}, {"id": 23, "title": "Bestätigung SBL", "type": "headline"}, {"id": 24, "parentId": 23, "title": "Datum", "type": "date"}, {"id": 25, "parentId": 23, "title": "Unterschrift", "type": "signatureField"}]}