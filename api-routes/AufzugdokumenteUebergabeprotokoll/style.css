@page {
    size: 210mm 297mm;
}

* {
    font-family: Arial, sans-serif;
    font-size: 15px;
}

body {
    width: 230mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

u {
    text-underline-offset: 2px;
}

.page {
    page-break-before: always;
    max-height: 297mm;
    min-height: 297mm;
    min-width: 210mm;
    max-width: 210mm;
    box-sizing: border-box;
}

.page-text {
    width: 100%;
    line-height: 1.5;
    text-align: left;
    page-break-before: always;
}

h1 {
    font-size: 18px;
    text-align: center;
    text-decoration: underline;
    font-weight: unset;
    margin-bottom: 20px;
}

h2 {
    font-size: 18px;
    text-align: center;
    font-weight: bold;
    margin: 30px 0 10px;
}

ul {
    margin: 20px 0;
    padding-left: 20px;
}

li {
    margin-bottom: 2px;
}

li strong {
    font-weight: bold;
}

.main-table-text {
    width: 100%;
    text-align: left
}

.first-header-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    font-size: 24px !important;
}

.first-header-table td,
.main-table td {
    padding: 5px;
    border-right: 1px solid black;
}

.header-info-table {
    width: 100%;
    margin: 40px 0;
}

.main-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    margin-top: 5px;
}

.main-table td:first-child {
    width: 70%;
}

.main-table td {
    border-bottom: 1px solid black;
}

.main-table td:last-child {
    height: 40px;
}

.last-table {
    width: 100%;
    page-break-inside: avoid;
    page-break-before: always;
}

.secondary-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    margin-top: 15px;
    margin-bottom: 40px;
}

.secondary-table td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
    width: 20%;
    padding: 5px;
}

.third-table {
    width: 60%;
    border-collapse: collapse;
    border: 1px solid black;
    margin-top: 15px;
    margin-bottom: 40px;
}

.third-table td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
    width: 20%;
    padding: 5px;
}

.checkbox-size {
    font-size: 24px;
    background-color: #3965af;
    color: white;
    margin-right: .5rem;
    display: inline-block;
    width: 40px;
    height: 30px;
    text-align: center;
}

.checkbox-size.unchecked {
    color: #3965af;
}

.last-section {
    vertical-align: bottom;
}

.signature {
    max-height: 200px;
    object-fit: contain;
    max-width: 90%;
}

.list-padding {
    padding-bottom: 20px;
}

.date-signature {
    display: table;
    width: 100%;
    page-break-inside: avoid;
}

.date-field {
    display: table-cell;
    width: 50%;
    text-align: center;
    padding-top: 40px;
}

.img-date-container {
    margin: 0 50px;
    display: flex;
    align-items: end;
}

.border-top {
    border-top: 1px solid black;
    padding-top: 10px;
    margin: 0 50px;
}

.left {
    float: left;
}

.right {
    float: right;
}