<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_AufzugdokumenteUebergabeprotokoll
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $schemaId = (int)$schemaId;
        $documentId = (int)$documentId;
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data['schema'] = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], injectParentTitle: true);
        return $data;
    }
}