<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection HtmlDeprecatedAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_AufzugdokumenteUebergabeprotokoll())->getData($_GET['schemaId'], $_GET['documentId']);
}
/**
 * @param array<string, mixed> $data
 * @param array<string> $values
 */
function renderMultiCheckbox(array $data, string $key, array $values): void
{
    foreach ($values as $value) {
        $checked = in_array($value, $data[$key] ?? []);
        $classes = 'checkbox-size' . ($checked ? '' : ' unchecked');
        $icon = $checked ? '&#10003;' : '&#9744;';
        echo "<span class=\"$classes\">$icon</span> $value";
    }
}

?>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Aufzugdokumente Übergabeprotokoll</title>
</head>
<body>
<div class="page">
    <table class="first-header-table">
        <tr>
            <td colspan="1">
                <?php renderMultiCheckbox($data['schema'], 'Übergabeprotokoll Zahnstangenaufzüge Übergabeprotokoll Zahnstangenaufzüge', ['Anlieferung']); ?>
            </td>
            <td colspan="3"></td>
        </tr>
        <tr>
            <td colspan="1">
                <?php renderMultiCheckbox($data['schema'], 'Übergabeprotokoll Zahnstangenaufzüge Übergabeprotokoll Zahnstangenaufzüge', ['Montage']); ?>
            </td>
            <td colspan="3"><u><strong style="font-size: 20px">Übergabeprotokoll Zahnstangenaufzüge</strong></u></td>
        </tr>
        <tr>
            <td colspan="1">
                <?php renderMultiCheckbox($data['schema'], 'Übergabeprotokoll Zahnstangenaufzüge Übergabeprotokoll Zahnstangenaufzüge', ['Einweisung']); ?>
            </td>
            <td colspan="3"></td>
        </tr>
    </table>
    <table class="header-info-table">
        <tr>
            <td colspan="4">Übernommen durch:</td>
        </tr>
        <tr>
            <td colspan="1">Firma:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Firma'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1">Herr/Frau:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Herr/Frau'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1">Anschrift:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Anschrift'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1">Baustelle:</td>
            <td colspan="3"><u><?= $data['schema']['Übernommen durch Baustelle'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="1" style="min-width: 80px">Fabrik-Nr.:</td>
            <td colspan="1"><u><?= $data['schema']['Übernommen durch Fabrik-Nr.'] ?? '' ?></u></td>
            <td colspan="1"><strong>Typ: Transportbühne GEDA 500/1200/1500 ZP/ZZP</strong></td>
            <td colspan="1">
                <u><?= $data['schema']['Aufzugdokumente Übergabeprotokoll Typ: Transportbühne GEDA 500/1200/1500 ZP/ZZP'] ?? '' ?></u>
            </td>
        </tr>
        <tr>
            <td colspan="1" style="min-width: 80px">Fabrik-Nr.:</td>
            <td colspan="1"><u><?= $data['schema']['Übernommen durch Fabrik-Nr.2'] ?? '' ?></u></td>
            <td colspan="1"><strong>Typ: Transportbühne ALIMAK</strong></td>
            <td colspan="1">
                <u><?= $data['schema']['Aufzugdokumente Übergabeprotokoll Typ: Transportbühne ALIMAK'] ?? '' ?></u></td>
        </tr>
    </table>
    <div class="main-table-text">
        <p>
            <strong>Transportbühne</strong> wurde gemäß Stücklisten-Nr.:
            <u><?= $data['schema']['Übergabeprotokoll Zahnstangenaufzüge Transportbühne wurde gemäß Stücklisten-Nr.:'] ?? '' ?></u>
            übergeben.
        </p>
    </div>
    <table class="main-table">
        <tr>
            <td colspan="1" style="overflow:hidden;">
                <div class="left"><strong>Prüfprotokoll</strong></div>
                <div class="right"><u>gepr./eingew.</u></div>
            </td>
            <td colspan="1"><strong>Bemerkungen</strong></td>
        </tr>
        <tr>
            <td colspan="1">Unterbauung des Grundmastes und des Fußteiles (evtl. Spindeln)</td>
            <td colspan="1"><?= $data['schema']['Unterbauung des Grundmastes und des Fußteiles (evtl. Spindeln) Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">
                Masthalterung<br>
                &nbsp;- Anordnung / Verankerung (Dübel, Gerüst; Durchgangsschrauben)
            </td>
            <td colspan="1"><?= $data['schema']['Masthalterung Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">
                Mastteile<br>
                &nbsp;- Verschraubung Stahlmast (Anzugsmoment 150 Nm)<br>
                &nbsp;- Zahnstange gefettet
            </td>
            <td colspan="1"><?= $data['schema']['Mastteile Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">
                Schleppkabel<br>
                &nbsp;- Reibungsfreie Spulung (eingefettet, talkumiert)<br>
                &nbsp;- Schleppkabelführungen (Abstände, freier Schleppkabelweg)
            </td>
            <td colspan="1"><?= $data['schema']['Schleppkabel Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">
                Elektrik (überprüfen)<br>
                &nbsp;- Stromzuführung (Kontrolleuchte; Querschnitt)<br>
                &nbsp;- Not-Aus Schlagtaster<br>
                &nbsp;- Überlast-Kontrolleuchte<br>
                &nbsp;- Klappenendschalter<br>
                &nbsp;- Endschalter Fangvorrichtung<br>
                &nbsp;- Auf/Ab-Endschalter<br>
                &nbsp;- Sicherheitsstop (ca. 2 m Höhe) mit mind. 2 Sek. akustischer Warnung
            </td>
            <td colspan="1"><?= $data['schema']['Elektrik (überprüfen) Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">Sicherheitsabstände (50 cm)</td>
            <td colspan="1"><?= $data['schema']['Sicherheitsabstände (50 cm) Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">Absturzsicherung / Etageneinrichtung (Anzahl)</td>
            <td colspan="1"><?= $data['schema']['Absturzsicherung / Etageneinrichtung (Anzahl) Bemerkungen'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="1">Probefahrt durchgeführt</td>
            <td colspan="1"><?= $data['schema']['Probefahrt durchgeführt Bemerkungen'] ?? '' ?></td>
        </tr>
    </table>
    <div class="page-text">
        <ul>
            <li>Der Aufzug ist nur gemäß vorheriger Einweisung zu bedienen!</li>
            <li> Die Benutzung des Notablass ist <b>NICHT</b> zulässig!</li>
            <li> Bei Zuwiderhandlungen oder falscher Verwendung des Nutzers/Kunden werden die dafür aufkommenden
                Kosten unseres Monteurs nach Aufwand separat berechnet.
            </li>
        </ul>
        <strong>Eingewiesene Personen/Bühnenführer:</strong>
        <table class="secondary-table">
            <tr>
                <td>Name</td>
                <td><?= $data['schema']['1 Name'] ?? '' ?></td>
                <td><?= $data['schema']['2 Name'] ?? '' ?></td>
                <td><?= $data['schema']['3 Name'] ?? '' ?></td>
                <td><?= $data['schema']['4 Name'] ?? '' ?></td>
            </tr>
            <tr>
                <td>Firma</td>
                <td><?= $data['schema']['1 Firma'] ?? '' ?></td>
                <td><?= $data['schema']['2 Firma'] ?? '' ?></td>
                <td><?= $data['schema']['3 Firma'] ?? '' ?></td>
                <td><?= $data['schema']['4 Firma'] ?? '' ?></td>
            </tr>
            <tr>
                <td>Unterschrift</td>
                <td>
                    <?php if (isset($data['schema']['1 Eingewiesene Person/Bühnenführer 1 Unterschrift'])) { ?>
                        <img src="<?= $data['schema']['1 Eingewiesene Person/Bühnenführer 1 Unterschrift'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </td>
                <td>
                    <?php if (isset($data['schema']['2 Eingewiesene Person/Bühnenführer 2 Unterschrift'])) { ?>
                        <img src="<?= $data['schema']['2 Eingewiesene Person/Bühnenführer 2 Unterschrift'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </td>
                <td>
                    <?php if (isset($data['schema']['3 Eingewiesene Person/Bühnenführer 3 Unterschrift'])) { ?>
                        <img src="<?= $data['schema']['3 Eingewiesene Person/Bühnenführer 3 Unterschrift'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </td>
                <td>
                    <?php if (isset($data['schema']['4 Eingewiesene Person/Bühnenführer 4 Unterschrift'])) { ?>
                        <img src="<?= $data['schema']['4 Eingewiesene Person/Bühnenführer 4 Unterschrift'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Schlüssel erhalt. Ronis 421 (Bühne)</td>
                <td><?= $data['schema']['1 Schlüssel erhalten Ronis 421 (Bühne)'] ?? '' ?></td>
                <td><?= $data['schema']['2 Schlüssel erhalten Ronis 421 (Bühne)'] ?? '' ?></td>
                <td><?= $data['schema']['3 Schlüssel erhalten Ronis 421 (Bühne)'] ?? '' ?></td>
                <td><?= $data['schema']['4 Schlüssel erhalten Ronis 421 (Bühne)'] ?? '' ?></td>
            </tr>
        </table>
        <strong>Eingewiesene, sachkundige Personen:</strong>
        <table class="third-table">
            <tr>
                <td>Name</td>
                <td><?= $data['schema']['1 Name2'] ?? '' ?></td>
                <td><?= $data['schema']['2 Name2'] ?? '' ?></td>
            </tr>
            <tr>
                <td>Firma</td>
                <td><?= $data['schema']['1 Firma2'] ?? '' ?></td>
                <td><?= $data['schema']['2 Firma2'] ?? '' ?></td>
            </tr>
            <tr>
                <td>Unterschrift</td>
                <td>
                    <?php if (isset($data['schema']['1 Eingewiesene, sachkundige Person 1 Unterschrift'])) { ?>
                        <img src="<?= $data['schema']['1 Eingewiesene, sachkundige Person 1 Unterschrift'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </td>
                <td>
                    <?php if (isset($data['schema']['2 Eingewiesene, sachkundige Person 2 Unterschrift'])) { ?>
                        <img src="<?= $data['schema']['2 Eingewiesene, sachkundige Person 2 Unterschrift'] ?>"
                             alt="signature" class="signature">
                    <?php } ?>
                </td>
            </tr>
            <tr>
                <td>Schlüssel Ronis 455 übergeben bei ZP Maschinen</td>
                <td><?= $data['schema']['1 Schlüssel Ronis 455 übergeben bei ZP Maschinen'] ?? '' ?></td>
                <td><?= $data['schema']['2 Schlüssel Ronis 455 übergeben bei ZP Maschinen'] ?? '' ?></td>
            </tr>
        </table>
        <strong>Die eingewiesenen Personen wurden insbesondere auf die Beachtung der Bedienungsanleitung und
            der beiliegend aufgeführten Anwendungsvorschriften hingewiesen.
        </strong>
        <div class="date-signature">
            <div class="date-field">
                <div class="img-date-container">
                    <div>
                        <?php
                        $rawDate = $data['schema']['Servicemonteur/Servicemonteurin Datum'] ?? '';
                        if ($rawDate && ($ts = strtotime($rawDate)) !== false) {
                            echo date('d.m.Y', $ts);
                        }
                        ?>
                    </div>
                    <div>
                        <?php if (isset($data['schema']['Servicemonteur/Servicemonteurin Servicemonteur/Servicemonteurin Unterschrift'])) { ?>
                            <img src="<?= $data['schema']['Servicemonteur/Servicemonteurin Servicemonteur/Servicemonteurin Unterschrift'] ?>"
                                 alt="signature" class="signature">
                        <?php } ?>
                    </div>
                </div>
                <div class="border-top">
                    Datum, Unterschrift <br>
                    <strong>(Servicemonteur/Servicemonteurin)</strong>
                </div>
            </div>
            <div class="date-field">
                <div class="img-date-container">
                    <div>
                        <?php
                        $rawDate = $data['schema']['Bühnenführer/Bühnenführerin,Betreiber/Betreiberin Datum'] ?? '';
                        if ($rawDate && ($ts = strtotime($rawDate)) !== false) {
                            echo date('d.m.Y', $ts);
                        }
                        ?>
                    </div>
                    <div>
                        <?php if (isset($data['schema']['Bühnenführer/Bühnenführerin,Betreiber/Betreiberin Bühnenführer/Bühnenführerin,Betreiber/Betreiberin Unterschrift'])) { ?>
                            <img src="<?= $data['schema']['Bühnenführer/Bühnenführerin,Betreiber/Betreiberin Bühnenführer/Bühnenführerin,Betreiber/Betreiberin Unterschrift'] ?>"
                                 alt="signature" class="signature">
                        <?php } ?>
                    </div>
                </div>
                <div class="border-top">
                    Datum, Unterschrift <br>
                    <strong>(Bühnenführer/Bühnenführerin, Betreiber/Betreiberin)</strong>
                </div>
            </div>
        </div>
    </div>
    <div class="page-text">
        <h1>Anhang zum Übergabeprotokoll GEDA 500/1200/1500ZP/ZZP</h1>
        <p>
            Die Transportbühne ist von einer akkreditierten Prüf- und Zertifizierungsstelle als Maschine
            im Sinne der Richtlinie 89/392/EWG, Anhang IV, geprüft und abgenommen.
        </p>
        <h2>
            Folgende Punkte sind beim Einsatz der Transportbühne GEDA 500/1200/1500ZP/ZZP<br>
            im Besonderen zu beachten:
        </h2>
        <ul>
            <li class="list-padding">
                Die Bühne ist zum vorübergehenden Einsatz auf Baustellen zum Personen- und Materialtransport
                vorgesehen. Sie darf nur von <strong>eingewiesenem Personal</strong> auf Baustellen verwendet werden.
            </li>
            <li class="list-padding">
                Die maximale Anzahl der Personen auf der Transportbühne ist inklusive dem Bühnenführer auf
                <b>3/5 Personen</b> (500ZP/500ZZP), <b>5 Personen</b> (1200ZP), bzw. <b>7 Personen</b>
                (1500ZP/ZZP) begrenzt.
            </li>
            <li class="list-padding">
                Bei 500ZP; 1200ZP und 1500ZP gilt:<br>
                Das Gerät kann nur in Totmannsteuerung von der Bühne aus betrieben werden, ist also nicht
                auf definierte Haltestellen ausgelegt. Eine Bedienung mittels weiterer Steuerstellen
                ist nicht zulässig.
            </li>
            <li class="list-padding">
                Bei 500ZZP und 1500ZZP gilt:<br>
                Die Maschine kann von der Bühne in Totmannsteuerung durch den Bühnenführer gesteuert werden,
                in der Betriebsart "Materialaufzug" kann die Maschine von externen Steuerstellen (Bodenstation
                oder Etagen) gesteuert werden. Eine Personenbeförderung in dieser Betriebsart ist nicht zulässig.
            </li>
            <li class="list-padding">
                Bei 500ZP; 1200ZP und 1500ZP gilt:<br>
                Die Steuerung an der Bodenstation darf nur von <strong>berechtigten und sachkundigen Personen</strong>,
                die Zugriff zu dem entsprechenden Schlüssel haben, bedient werden. Diese Steuerung dient nur
                zum Verladen der Bühne, zur Durchführung der Fangprobe und zur Störungsbeseitigung. Nach
                Benutzung dieser Steuerung ist der Schlüssel abzuziehen und zu verwahren.
            </li>
            <li class="list-padding">
                Die Transportbühne GEDA 500ZP/1200ZP/1500ZP, sowie ZZP darf ohne untere Umwehrung betrieben
                werden, da die Bühne, wenn sie nach unten gefahren wird, in ca. 2 m Höhe selbständig stoppt
                und einen Warnton aussendet, bevor sie ganz nach unten gefahren werden kann. Der Gefahrenbereich
                ist entsprechend VBG 35 § 24 Abs. 1 bauseits abzusichern.
            </li>
            <li class="list-padding">
                Jeder Übergang von der Bühne zum Bauwerk muß durch eine Etageneinrichtung (Art.-Nr. 01177
                oder 01202 oder 01212; 1 Stk. im Lieferumfang der Grundeinheit enthalten) gesichert werden.
            </li>
        </ul>
    </div>
</div>
</body>
</html>