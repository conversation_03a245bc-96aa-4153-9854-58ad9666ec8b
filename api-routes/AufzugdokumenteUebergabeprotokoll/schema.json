{"name": "Aufzugdokumente Übergabeprotokoll", "status": "active", "createdBy": "364", "createdOn": "2025-04-30T00:00:00Z", "positions": [{"id": 1, "title": "Übergabeprotokoll Zahnstangenaufzüge", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Übergabeprotokoll Zahnstangenaufzüge", "type": "combobox-multi", "value": ["Anlieferung", "Montage", "Einweisung"]}, {"id": 3, "parentId": 1, "title": "Übernommen durch", "type": "headline"}, {"id": 4, "parentId": 3, "title": "Firma", "type": "string"}, {"id": 5, "parentId": 3, "title": "<PERSON>/<PERSON>", "type": "string"}, {"id": 6, "parentId": 3, "title": "Anschrift", "type": "string"}, {"id": 7, "parentId": 3, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 8, "parentId": 3, "title": "Fabrik-Nr.", "type": "string"}, {"id": 9, "displayInside": 8, "title": "Typ: Transportbühne GEDA 500/1200/1500 ZP/ZZP", "type": "string"}, {"id": 10, "parentId": 3, "title": "Fabrik-Nr.", "type": "string"}, {"id": 11, "displayInside": 10, "title": "Typ: Transportbühne ALIMAK", "type": "string"}, {"id": 12, "parentId": 1, "title": "Transportbühne wurde gemäß Stücklisten-Nr.:", "type": "int"}, {"id": 13, "parentId": 1, "title": "Prüfprotokoll", "type": "headline"}, {"id": 14, "parentId": 13, "title": "Unterbauung des Grundmastes und des Fußteiles (evtl. Spindeln)", "type": "headline"}, {"id": 15, "parentId": 14, "title": "Bemerkungen", "type": "string"}, {"id": 16, "parentId": 13, "title": "Masthalterung", "type": "headline"}, {"id": 17, "parentId": 16, "title": "Bemerkungen", "type": "string"}, {"id": 18, "parentId": 13, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 19, "parentId": 18, "title": "Bemerkungen", "type": "string"}, {"id": 20, "parentId": 13, "title": "Schleppkabel", "type": "headline"}, {"id": 21, "parentId": 20, "title": "Bemerkungen", "type": "string"}, {"id": 22, "parentId": 13, "title": "Elektrik (überprüfen)", "type": "headline"}, {"id": 23, "parentId": 22, "title": "Bemerkungen", "type": "string"}, {"id": 24, "parentId": 13, "title": "Sicherheitsabstände (50 cm)", "type": "headline"}, {"id": 25, "parentId": 24, "title": "Bemerkungen", "type": "string"}, {"id": 26, "parentId": 13, "title": "Absturzsicherung / Etageneinrichtung (Anzahl)", "type": "headline"}, {"id": 27, "parentId": 26, "title": "Bemerkungen", "type": "string"}, {"id": 28, "parentId": 13, "title": "Probefahrt durchgeführt", "type": "headline"}, {"id": 29, "parentId": 28, "title": "Bemerkungen", "type": "string"}, {"id": 30, "parentId": 1, "title": "Eingewiesene Personen/Bühnenführer", "type": "headline"}, {"id": 31, "parentId": 30, "title": "1", "type": "headline"}, {"id": 32, "parentId": 31, "title": "Name", "type": "string"}, {"id": 33, "parentId": 31, "title": "Firma", "type": "string"}, {"id": 34, "parentId": 31, "title": "Eingewiesene Person/Bühnenführer 1 Unterschrift", "type": "signatureField"}, {"id": 35, "parentId": 31, "title": "Schlüssel erhalten Ronis 421 (<PERSON><PERSON><PERSON><PERSON>)", "type": "string"}, {"id": 36, "parentId": 30, "title": "2", "type": "headline"}, {"id": 37, "parentId": 36, "title": "Name", "type": "string"}, {"id": 38, "parentId": 36, "title": "Firma", "type": "string"}, {"id": 39, "parentId": 36, "title": "Eingewiesene Person/Bühnenführer 2 Unterschrift", "type": "signatureField"}, {"id": 40, "parentId": 36, "title": "Schlüssel erhalten Ronis 421 (<PERSON><PERSON><PERSON><PERSON>)", "type": "string"}, {"id": 41, "parentId": 30, "title": "3", "type": "headline"}, {"id": 42, "parentId": 41, "title": "Name", "type": "string"}, {"id": 43, "parentId": 41, "title": "Firma", "type": "string"}, {"id": 44, "parentId": 41, "title": "Eingewiesene Person/Bühnenführer 3 Unterschrift", "type": "signatureField"}, {"id": 45, "parentId": 41, "title": "Schlüssel erhalten Ronis 421 (<PERSON><PERSON><PERSON><PERSON>)", "type": "string"}, {"id": 46, "parentId": 30, "title": "4", "type": "headline"}, {"id": 47, "parentId": 46, "title": "Name", "type": "string"}, {"id": 48, "parentId": 46, "title": "Firma", "type": "string"}, {"id": 49, "parentId": 46, "title": "Eingewiesene Person/Bühnenführer 4 Unterschrift", "type": "signatureField"}, {"id": 50, "parentId": 46, "title": "Schlüssel erhalten Ronis 421 (<PERSON><PERSON><PERSON><PERSON>)", "type": "string"}, {"id": 51, "parentId": 1, "title": "Eingewiesene, sachkundige Personen", "type": "headline"}, {"id": 52, "parentId": 51, "title": "1", "type": "headline"}, {"id": 53, "parentId": 52, "title": "Name", "type": "string"}, {"id": 54, "parentId": 52, "title": "Firma", "type": "string"}, {"id": 55, "parentId": 52, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sachkundige Person 1 Unterschrift", "type": "signatureField"}, {"id": 56, "parentId": 52, "title": "Schlüssel Ronis 455 übergeben bei ZP Maschinen", "type": "string"}, {"id": 57, "parentId": 51, "title": "2", "type": "headline"}, {"id": 58, "parentId": 57, "title": "Name", "type": "string"}, {"id": 59, "parentId": 57, "title": "Firma", "type": "string"}, {"id": 60, "parentId": 57, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, sachkundige Person 2 Unterschrift", "type": "signatureField"}, {"id": 61, "parentId": 57, "title": "Schlüssel Ronis 455 übergeben bei ZP Maschinen", "type": "string"}, {"id": 62, "parentId": 1, "title": "Servicemonteur/Servicemonteurin", "type": "headline"}, {"id": 63, "parentId": 62, "title": "Datum", "type": "date"}, {"id": 64, "parentId": 62, "title": "Servicemonteur/Servicemonteurin <PERSON>chrift", "type": "signatureField"}, {"id": 65, "parentId": 1, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 66, "parentId": 65, "title": "Datum", "type": "date"}, {"id": 67, "parentId": 65, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON><PERSON>f<PERSON><PERSON>,<PERSON><PERSON>iber/<PERSON>re<PERSON><PERSON>chrift", "type": "signatureField"}]}