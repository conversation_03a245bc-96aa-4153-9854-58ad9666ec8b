<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_Abfahrtskontrolle
{
    /** @return array<string, mixed> */
    public function getData(string $schemaId, string $documentId): array
    {
        $schemaId = (int)$schemaId;
        $documentId = (int)$documentId;
        $curl = new PrintoutCurl();
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $settings = PrintoutHelper::downloadSettings($curl);
        $data['logo'] = $settings['logo'] ?? '';
        $data['document'] = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], injectParentTitle: true);

        if (!empty($data['document']['Abfahrtskontrolle für LKW im Lager v2 Amtl. Kennzeichen'])) {
            $data['vehicleResource'] = PrintoutHelper::downloadResource(
                $data['document']['Abfahrtskontrolle für LKW im Lager v2 Amtl. Kennzeichen'],
                $curl
            );
        }
        return $data;
    }
}