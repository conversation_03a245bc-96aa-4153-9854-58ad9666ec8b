* {
    font-family: Helvetica, sans-serif;
    font-size: 14px;
}

body {
    width: 230mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

.page {
    page-break-before: always;
    max-height: 297mm;
    min-height: 297mm;
    min-width: 210mm;
    max-width: 210mm;
    box-sizing: border-box;
}

.signature {
    max-height: 150px;
    width: auto;
    object-fit: contain;
    max-width: 100%;
}

.title {
    font-size: x-large;
    font-weight: bold;
}

.sub-title {
    font-size: small;
}

.checkliste {
    font-size: xx-large;
    font-weight: bold;
    color: #c2410c;
}

.orange {
    color: #c2410c;
}

.size-16 {
    font-size: 16px !important;
}

.float-left {
    width: 45%;
    float: left;
}

.float-right {
    width: 45%;
    float: right;
}

.container {
    overflow: hidden;
}

.headers {
    font-size: medium;
    font-weight: bold;
    color: #c2410c;
}

.subheaders {
    font-size: small;
    font-weight: bold;
    color: #c2410c;
}

.checkboxes tr th {
    border: 1px solid black;
}

.checkboxes {
    border-collapse: collapse;
    width: 100%;
}

.main-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
}

.main-table td,
.main-table th {
    border: 1px solid #000;
    padding: 0.5em;
}

.notes {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    page-break-inside: avoid;
}

.notes tr {
    border: 1px solid black;
    page-break-inside: avoid;
}

.notes tr td {
    vertical-align: top;
    border: 1px solid black;
    page-break-inside: avoid;
}

.ort {
    border-right: 1px solid white !important;
}

.date {
    border-right: 1px solid white !important;
    border-left: 1px solid white !important;
}

.right {
    float: right;
}

.left {
    float: left;
}

.checkliste-font {
    font-size: 14px;
}

.content-padding {
    padding: 10px;
}

.logo {
    max-height: 150px;
    width: 100%;
    object-fit: contain;
    max-width: 100%;
}

.checkbox-size {
    font-size: 24px;
    color: #c2410c;
}

.checkliste-text {
    font-size: 30px;
    color: #c2410c;
    font-weight: 500;
}

.width-75 {
    width: 75%;
}

.width-35 {
    width: 35%;
}

.width-25 {
    width: 25%;
}

.width-40 {
    width: 40%;
}

.width-70 {
    width: 70%;
}

.width-30 {
    width: 30%;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.margin-20-0 {
    margin: 20px 0;
}