<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Abfahrtskontrolle())->getData(
        schemaId: $_GET['schemaId'] ?? null,
        documentId: $_GET['documentId'] ?? null
    );
}

function renderCheckboxRow(string $value): void
{
    $isChecked = PrintoutHelper::isCheckboxTickedByReportedValue($value);
    $checkedSymbol = '&#9745;';
    $uncheckedSymbol = '&#9744;';
    $symbols = $isChecked ? [$checkedSymbol, $uncheckedSymbol] : [$uncheckedSymbol, $checkedSymbol];
    foreach ($symbols as $symbol) {
        echo sprintf('<td class="text-center"><span class="checkbox-size">%s</span></td>', $symbol);
    }
}

?>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Abfahrtskontrolle</title>
</head>
<body>
<div class="page">
    <table class="main-table">
        <tr>
            <td colspan="2" class="title width-75 text-center">
                Abfahrtskontrolle für LKW im Lager
            </td>
            <td class="width-25">
                <?php if (!empty($data['logo'])) { ?>
                    <img src="<?= $data['logo'] ?>" alt="Logo" class="logo">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td class="sub-title content-padding width-35">
                <u>Amtl. Kennzeichen:</u><br>
                <?= $data['vehicleResource']['kurzname'] ?? '' ?>
            </td>
            <td class="sub-title content-padding width-25">
                <u>Uhrzeit der Kontrolle:</u><br>
                <?php
                $time = $data['document']['Abfahrtskontrolle für LKW im Lager v2 Uhrzeit der Kontrolle'] ?? '';
                if ($time) {
                    echo $time . ' Uhr';
                }
                ?>
            </td>
            <td class="sub-title content-padding width-40">
                <u>Vor- und Zuname des Fahrers in Druckbuchstaben:</u><br>
                <?= $data['document']['Abfahrtskontrolle für LKW im Lager v2 Vor- u. Zuname des Fahrers in Druckbuchstaben'] ?? '' ?>
            </td>
        </tr>
        <tr>
            <td colspan="3" class="content-padding">
                Gemäß § 36 der Unfallverhütungsvorschrift DGUV Vorschrift 70 bzw. BGV D 27 ist der Fahrzeugführer
                verpflichtet,
                vor Beginn jeder Arbeitsschicht die Wirksamkeit der Betätigungs- und Sicherheitseinrichtungen zu prüfen
                und
                während der Arbeitsschicht den Zustand des Fahrzeuges auf augenfällige Mängel hin zu beobachten.<br><br>
                Der Fahrzeugführer hat festgestellte Mängel dem zuständigen Aufsichtführenden, bei Wechsel des
                Fahrzeugführers auch dem Ablöser, mitzuteilen. Bei Mängeln, die die Betriebssicherheit gefährden,
                hat der Fahrzeugführer den Betrieb einzustellen.
                <p class="checkliste-text margin-20-0 text-center">Checkliste</p>
                Vor Abfahrt des LKW aus dem Lager ist die nachfolgende Checkliste abzuarbeiten. Hierdurch wird die
                Verpflichtung aus der Vorschrift eingehalten und der Ist-Zustand des Fahrzeuges, die Ladungssicherung
                sowie die Vollständigkeit evtl. benötigter Behelfsgegenstände dokumentiert.
            </td>
        </tr>
    </table>

    <br>

    <div class="container">
        <div class="float-left">
            <table class="checkboxes">
                <tr>
                    <th rowspan="2" class="headers content-padding text-left width-70">
                        Zu prüfen sind
                    </th>
                    <th colspan="2" class="subheaders content-padding text-center width-30">
                        In Ordnung
                    </th>
                </tr>
                <tr class="headers">
                    <th class="content-padding text-center">ja</th>
                    <th class="content-padding text-center">nein</th>
                </tr>
                <?php
                $prefix = 'Zu prüfen sind/in Ordnung ';
                $leftItems = [
                    'Abblendlicht', 'Blinker', 'Nebelscheinwerfer', 'Umrissleuchten',
                    'Reifenprofil (Mindestprofiltiefe)', 'Außenspiegel', 'Sichtbarer Ölverlust',
                    'Ladungssicherung', 'Pylone / Warnbake etc', 'Verbandskasten', 'Warnwesten',
                    'Tätigkeitsnachweis der vorausgegangenen 28 Kalendertage'
                ];
                foreach ($leftItems as $item) {
                    echo '<tr><td>' . $item . '</td>';
                    renderCheckboxRow($data['document'][$prefix . $item] ?? '');
                    echo '</tr>';
                }
                ?>
            </table>
        </div>

        <div class="float-right">
            <table class="checkboxes">
                <tr>
                    <th rowspan="2" class="headers content-padding text-left width-70">
                        Zu prüfen sind
                    </th>
                    <th colspan="2" class="subheaders content-padding text-center width-30">
                        In Ordnung
                    </th>
                </tr>
                <tr class="headers">
                    <th class="content-padding text-center">ja</th>
                    <th class="content-padding text-center">nein</th>
                </tr>
                <?php
                $rightItems = [
                    'Fernlicht', 'Warnblinker', 'Kennzeichenbeleuchtung', 'Luftdruck (Reifen)',
                    'Reifenzustand (Beschädigungen)', 'Windschutzscheibe',
                    'Anhängerkupplung (nur Hängerbeir.)', 'Zurrgurte', 'Bremstest durchgeführt',
                    'Unterlegkeile', 'Unfallschäden',
                    'Ordnungsgemäße Funktion des Kontrollgerätes gewährleistet'
                ];
                foreach ($rightItems as $item) {
                    echo '<tr><td class="checkbox-padding">' . $item . '</td>';
                    renderCheckboxRow($data['document'][$prefix . $item] ?? '');
                    echo '</tr>';
                }
                ?>
            </table>
        </div>
    </div>

    <br>

    <table class="notes">
        <tr>
            <td colspan="3" class="content-padding">
                <p class="orange"><u><span class="size-16">Sonstiges/Bemerkungen</span></u></p><br>
                <?= $data['document']['Abfahrtskontrolle für LKW im Lager v2 Sonstiges/Bemerkungen'] ?? '' ?>
            </td>
        </tr>
        <tr>
            <td class="footer content-padding" colspan="3">
                Die Abfahrtskontrolle wurde durch den Fahrzeugführer nach den Vorgaben der
                Checkliste ordnungsgemäß vor Beginn der Fahrt durchgeführt. Die gemachten
                Angaben entsprechen der Wahrheit.
            </td>
        </tr>
        <tr>
            <td class="ort content-padding text-center">
                <span class="size-16">Ort: </span><br>
                <?= $data['document']['Abfahrtskontrolle für LKW im Lager v2 Ort'] ?? '' ?>
            </td>
            <td class="date content-padding text-center">
                <span class="size-16">Datum:</span> <br>
                <?php
                $date = strtotime($data['document']['Abfahrtskontrolle für LKW im Lager v2 Datum'] ?? '');
                if ($date) {
                    echo date('d.m.Y', $date);
                }
                ?>
            </td>
            <td class="content-padding text-center">
                <span class="size-16">Unterschrift des Fahrzeugführers:</span><br>
                <?php if (!empty($data['document']['Abfahrtskontrolle für LKW im Lager v2 Unterschrift des Fahrzeugführers'])) { ?>
                    <img
                            src="<?= $data['document']['Abfahrtskontrolle für LKW im Lager v2 Unterschrift des Fahrzeugführers'] ?>"
                            alt="signature"
                            class="signature"
                    >
                <?php } ?>
            </td>
        </tr>
    </table>
</div>
</body>
</html>