<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_ZulassungsbescheinigungTeil2
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        $document = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $rnr = $document['fullDocument']['documentRelKey1'];

        return $this->getResourceData($rnr, $curl);
    }

    /**
     * @return array<string, mixed>
     */
    private function getResourceData(string $rnr, PrintoutCurl $curl): array
    {
        $resourceUrl = "v1/zb2vehicles/select_resource/$rnr";
        $propertyUrl = "v1/zb2vehicles/properties?rnr=$rnr";

        $options = PrintoutHelper::getHeadersForApiCalls();
        $data = $curl->_multi_call('get', [$resourceUrl, $propertyUrl], [], $options);

        $vehicleResource = [];
        $vehicleProperty = [];
        if ($data[0]['statusCode'] === 200) {
            $vehicleResource = $data[0]['response'];
        }
        if ($data[1]['statusCode'] === 200) {
            $vehicleProperty = end($data[1]['response']);
        }

        return [
            'vehicleResource' => $vehicleResource,
            'vehicleProperty' => $vehicleProperty,
        ];
    }
}