body {
    padding: 0;
    margin: 0;
    font-size: 14px;
    font-family: 'Ubuntu Mono', monospace;
    /*noinspection CssNonIntegerLengthInPixels*/
    letter-spacing: 0.2px;
}

.overlay-image {
    position: absolute;
    width: 210mm;
    height: 297mm;
    opacity: 0.5;
}

.page {
    height: 297mm;
    width: 210mm;
    padding: 0;
    box-sizing: border-box;
}

.main {
    width: 119.64mm;
    position: absolute;
    left: 37mm;
    top: 166mm;
}

.right-div, .left-div {
    position: relative;
}

.left-div div {
    position: absolute;
}

.left-div__second-div {
    top: 4.50mm;
}

.left-div__third-div {
    top: 9mm;
}

.left-div__fourth-div {
    top: 13mm;
}

.left-div__sixth-div {
    top: 22mm;
}

.left-div__seventh-div {
    top: 26.50mm;
}

.left-div__eight-div {
    top: 31mm;
}

.left-div__ninth-div {
    top: 35mm;
}

.left-div__twelfth-div {
    top: 43.50mm;
}

.left-div__sixteenth-div {
    top: 61mm;
}

.left-div div {
    width: 92mm;
}

.left-div__seventh-div,
.left-div__ninth-div,
.left-div__thirteenth-div,
.left-div__fourteen-div {
    width: 30mm !important;
}

.left-div__eight-div,
.left-div__twelfth-div,
.left-div__fifteenth-div,
.left-div__sixteenth-div {
    width: 58mm !important;
}

.right-div div {
    overflow: hidden;
    position: absolute;
}

.right-div__first-div, .right-div__third-div {
    left: 52mm;
    width: 44mm;
}

.right-div__first-div {
    top: 26mm;
}

.right-div__second-div {
    left: 90mm;
    top: 30mm;
    width: 6mm;
}

.right-div__third-div {
    top: 35mm;
}

.right-div__fourth-div {
    left: 88mm;
    top: 47mm;
    width: 7.50mm;
}

.right-div__sixth-div {
    left: 85mm;
    top: 55.50mm;
    width: 10mm;
}

.right-div__seventh-div {
    left: 70mm;
    top: 60.50mm;
    width: 26mm;
}
