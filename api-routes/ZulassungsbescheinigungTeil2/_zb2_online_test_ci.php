<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/562
// left in CI, even though this is not default_test because the schemas seem to be stable
printoutGenerator(
    Principal::ZB2_ONLINE_TEST(),
    Route::ZulassungsbescheinigungTeil2,
    //fileType: printoutFileType::Html,
    // does not exist anymore
    params: [
        "schemaId" => "8",
        "documentId" => "2021"
//        "documentId" => "2018"
    ]
);