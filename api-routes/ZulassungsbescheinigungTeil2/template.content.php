<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ZulassungsbescheinigungTeil2())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<string, mixed> $data
 */
function displayResource(string $title, array $data): string
{
    if (empty($data['vehicleResource'][$title])) {
        return '-';
    }
    return $data['vehicleResource'][$title];
}

?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <style>
        @font-face {
            font-family: "Ubuntu Mono";
            src: url("<?= PrintoutHelper::translateLocalPathToServerPath( __DIR__ . "/fonts/UbuntuMono-Regular.ttf") ?>");
        }
    </style>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Zb2VehicleData</title>
</head>
<body>
<div class="page">

    <?php // comment out to show the overlay ?>
    <!--    <img src="<?php /*= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/overlay.jpg') */ ?>"
         alt="Overlay Image" class="overlay-image">
-->

    <div class="main">
        <div class="left-div">
            <div style="top: 0.25mm"><?= displayResource('Make', $data) ?></div>
            <div class="left-div__second-div"><?= displayResource('type', $data) ?></div>
            <div class="left-div__third-div"><?= displayResource('variant', $data) ?></div>
            <div class="left-div__fourth-div"><?= displayResource('VersionDescription', $data) ?></div>
            <div style="top: 17.5mm"><?= displayResource('CommercialName', $data) ?></div>
            <div class="left-div__sixth-div"><?= displayResource('kurzname', $data) ?></div>
            <div class="left-div__seventh-div"><?= displayResource('CodeOfManufacturer', $data) ?></div>
            <div class="left-div__eight-div"><?= displayResource('vin', $data) ?></div>
            <div class="left-div__ninth-div"><?= displayResource('VehicleCategoryCode', $data) ?></div>
            <div style="top: 39.25mm;"><?= displayResource('_VehicleCategoryDescription', $data) ?></div>
            <div class="left-div__eleventh-div">&nbsp;</div>
            <div class="left-div__twelfth-div"><?= displayResource('_DescriptionOfBodywork', $data) ?></div>
            <div style="top: 48mm;"><?= displayResource('ColourDescription', $data) ?></div>
            <div style="top: 52mm;"><?= displayResource('Capacity', $data) ?></div>
            <div style="top: 56.5mm;"><?= displayResource('FuelCode', $data) ?></div>
            <div class="left-div__sixteenth-div"><?= displayResource('NumberTypeApproval', $data) ?></div>
        </div>
        <div class="right-div">
            <div class="right-div__first-div">
                <?= displayResource('CodeOfType', $data) . ' ' .
                displayResource('CodeOfVariantVersion', $data) . ' ' .
                displayResource('CheckDigitCodeOfVariantVersion', $data) ?>
            </div>
            <div class="right-div__second-div"><?= displayResource('vinCheckDigit', $data) ?></div>
            <div class="right-div__third-div"><?= displayResource('CodeOfBodywork', $data) ?></div>
            <div class="right-div__fourth-div"><?= displayResource('CodeOfColour', $data) . displayResource('CodeOfSecondColour', $data) ?></div>
            <div style="left: 65mm; top: 51.75mm; width: 31mm;">
                <?= displayResource('MaximumPower', $data) . ' / ' .
                displayResource('RatedSpeed', $data) ?>
            </div>
            <div class="right-div__sixth-div">
                <?= displayResource('FuelCode', $data) ?>
            </div>
            <div class="right-div__seventh-div"><?= displayResource('DateTypeApproval', $data) ?></div>
        </div>
    </div>
</div>
</body>
</html>
