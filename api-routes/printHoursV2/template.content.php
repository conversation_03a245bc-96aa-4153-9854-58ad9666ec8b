<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_printHoursV2())->getData($_GET['personalNo'] ?? null,
        $_GET['from'] ?? null, $_GET['to'] ?? null, $_GET['wageType'] ?? null,
        $_GET['ktrAanr'] ?? null, $_GET['groupedByPeriod'] ?? null, $_GET['showBonusOnly'] ?? null,
        $_GET['sortingOrder'] ?? null, $_GET['sortingColumn'] ?? null);
}

//all the different types of hours are combined and if the total is 0 u
// (when hours are related to specific pnr-s , months or non-approved) the footer should be hidden
// by adding a css class
function hideFooter(string $totalHours): void
{
	if ((float)$totalHours == 0) {
		echo ' class=hideFooter';
	} else {
		echo '';
	}
}

function refactorNote(string $note): void
{
	if (str_starts_with(strtolower($note), "input")) {
		//no comment in the beginning
		$comment = '';

	} else {
		// there is a comment in the beginning
		$split = explode('|', $note);
		$comment = $split[0];
	}

	if (str_contains($note, 'TTE')) {
		$inputType = PrintoutHelper::lang('timeTrackEvent');
	} else if (str_contains($note, 'Approver')) {
		$inputType = PrintoutHelper::lang('approver');
	} else {
		$inputType = PrintoutHelper::lang('hours|d');
	}

	preg_match('/([^@]+$)/', $note, $output_array);
	$dateOrTimeStamp = $output_array[0];

	if (strlen(trim($dateOrTimeStamp)) > 10) {
		//timestamp
		$newDate = date("d.m.Y H:i:s", strtotime($dateOrTimeStamp));
	} else {
		//date
		$newDate = date("d.m.Y", strtotime($dateOrTimeStamp));
	}

	echo !empty($comment) ? $comment . '<br>' : '';
	echo $inputType . '<br>' . $newDate;
}

?>

<html lang="de">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
	<title>Print hours v2</title>
</head>
<body>
<!-- add class to hide total rows if noGrouping is selected -->
<?php if ($data['groupedByPeriod'] == 'noGrouping') {
	$hideTotalsClass = ' hideTotals ';
} else {
	$hideTotalsClass = '';
}
?>
<div class="title"><?= PrintoutHelper::lang('hours') . ' ' . $data['personalNo']; ?>
	<?= empty($data['employeeName']) ? '' : ' - ' . $data['employeeName']; ?>
	<?= $data['employeeBirthday'] ?? '' ?></div>
<div class="title"><?= PrintoutHelper::lang('view_your_hours'); ?></div>
<br>
<table class="hours">
	<tr>
		<th width="8%" align="center"></th>
		<th width="10%" align="center"></th>
		<th width="5%" align="center"><?= PrintoutHelper::lang('drive_to_time') ?></th>
		<th width="14%" align="center"><?= PrintoutHelper::lang('working_time') ?></th>
		<th width="9%" align="center"><?= PrintoutHelper::lang('break_time') ?></th>
		<th width="5%" align="center"><?= PrintoutHelper::lang('drive_from_time') ?></th>
		<th width="5%" align="center"><?= PrintoutHelper::lang('total_drive_time') ?></th>
		<th width="14%" align="center"><?= PrintoutHelper::lang('total_working_time_brackets') ?></th>
		<th width="17%" align="center"><?= PrintoutHelper::lang('work_and_drive_time'); ?></th>
		<th width="13%" align="center"><?= PrintoutHelper::lang('notes') ?></th>
	</tr>
	<?php if ($data['groupedByPeriod'] == 'year') { ?>
		<?php foreach ($data as $k => $v) { ?>
			<?php if (is_numeric($k)) { ?>
				<tr>
					<td align="left" class="background" colspan="10">
						<b><?= PrintoutHelper::lang('year') . ' ' . $k; ?></b>
						<b><?= PrintoutHelper::lang('bookings') . ': ' . count($v['hours']); ?></b>
						<b>
							<?= PrintoutHelper::lang('hours') . ': ' . number_format((float)$v['totalHours'], 2, '.', ''); ?>
						</b>
						<b><?= PrintoutHelper::lang('days') . ': ' . count($v['days']); ?></b>
					</td>
				</tr>
			<?php } ?>
		<?php } ?>
	<?php } else if ($data['groupedByPeriod'] == 'month') { ?>
		<!-- adding the rows for every  month  -->
		<?php foreach ($data as $k => $v) { ?>
			<?php if (is_numeric($k)) { ?>
				<tr>
					<td align="left" class="background" colspan="10">
						<b><?= PrintoutHelper::lang('year') . ' ' . $k; ?></b>
						<b><?= PrintoutHelper::lang('bookings') . ': ' .
							count(array_filter(array_keys($v['yearlyBookings']), 'is_int')); ?></b>
						<b>
							<?= PrintoutHelper::lang('hours') . ': ' . number_format((float)$v['yearlyTotalHours'], 2, '.', ''); ?>
						</b>
						<b><?= PrintoutHelper::lang('days') . ': ' . count($v['days']); ?></b>
					</td>
				</tr>
				<?php foreach ($v['months'] as $month => $hours) { ?>
					<tr>
						<td align="left" class="background" colspan="10">
							<b><?= substr($month, 2); ?></b>
						</td>
					</tr>
					<tr>
						<td align="left" class="background" colspan="10">
							<b><?= PrintoutHelper::lang('bookings') . ': ' . count($v['months'][$month]['hours']); ?></b>
							<b>
								<?= PrintoutHelper::lang('hours') . ': ' .
								number_format((float)$v['months'][$month]['totalHours'], 2, '.', ''); ?>
							</b>
							<b><?= PrintoutHelper::lang('days') . ': ' . count($v['months'][$month]['days']); ?></b>
						</td>
					</tr>
				<?php } ?>
			<?php } ?>
		<?php } ?>
	<?php } else { ?>
		<!-- adding the rows for every  day . GroupByPeriod is day or noGrouping  -->
		<?php foreach ($data as $k => $v) { ?>
			<?php if (is_numeric($k)) { ?>
				<tr class="<?= $hideTotalsClass; ?>">
					<td align="left" class="background" colspan="10"><b>
							<?= PrintoutHelper::lang('year') . ' ' . $k; ?>
						</b></td>
				</tr>
				<tr class="<?= $hideTotalsClass; ?>">
					<td align="left" class="background" colspan="10">
						<b><?= PrintoutHelper::lang('bookings') . ': ' . count($v['yearlyBookings']); ?></b>
						<b><?= PrintoutHelper::lang('hours') . ': ' .
							number_format((float)$v['yearlyTotalHours'], 2, '.', ''); ?></b>
						<b><?= PrintoutHelper::lang('days') . ': ' . count($v['days']); ?></b>
				</tr>
				<?php foreach ($v['months'] as $month => $monthlyData) { ?>
					<tr class="<?= $hideTotalsClass; ?>">
						<td align="left" class="background" colspan="10">
							<b><?= substr($month, 2); ?></b>
						</td>
					</tr>
					<?php $rowCounter = 1;
					$classRowColor = ''; ?>
					<?php foreach ($monthlyData as $kk => $vv) { ?>
						<?php if (is_numeric($kk)) { ?>
							<?php if ($rowCounter % 2 == 0) { ?>
								<?php $classRowColor = 'class = lightGrey'; ?>
							<?php } else {
								$classRowColor = '';

							} ?>
							<tr>
							<td align="center" <?= $classRowColor; ?>>
								<?= PrintoutHelper::getWeekDayName((int)date('N', strtotime($vv['date']))) . ' ' .
								date('d.m.Y', strtotime($vv['date'])); ?>
							</td>
							<td align="center" <?= $classRowColor; ?>><?= $vv['name']; ?></td>
							<td align="center" <?= $classRowColor; ?>><?= $vv['startDriveTo']; ?></td>
							<td align="center" <?= $classRowColor; ?>>
								<?= $vv['start'] . '-' . $vv['end']; ?>
								<?= '</br>'; ?>
								<span class="totalWork"><?= trim(number_format($vv['hours'], 2)) ?></span>
								<span><?= '-'; ?></span>
								<span class="breakTime"><?= number_format($vv['pause'], 2); ?></span>
								<span><?= '='; ?></span>
								<span
									class="workNoBreak"><?= number_format($vv['hours'] - $vv['pause'], 2); ?></span>
							</td>
							<td align="center" <?= $classRowColor; ?>>
													
													<span class="">
														<?= $vv['startBreak']; ?>
														<?php if (!is_null($vv['startBreak'])) { ?>
															<?= '-'; ?>
														<?php } ?>
														<?= $vv['endBreak']; ?>
													</span>
								<br>
								<span class="breakTime"><?= number_format($vv['pause'], 2); ?></span>
							</td>
							<td align="center" <?= $classRowColor; ?>><?= $vv['endDriveTo']; ?></td>
							<td align="center" <?= $classRowColor; ?>>
								<span class="drivingTime">
									<?= number_format((float)$vv['reisestd'], 2, '.', ''); ?>
								</span>
							</td>
							<td align="center" <?= $classRowColor; ?>>
								<?= number_format((float)$vv['hours'], 2, '.', ''); ?>
								<?= '</br>' ?>
								<span class="totalWork"><?= number_format($vv['hours'], 2); ?></span>
								<span><?= '+'; ?></span>
								<span class="drivingTime"><?= (number_format($vv['reisestd'], 2)); ?></span>
								<span><?= '=' ?></span>
								<span class="">
														<?= number_format($vv['hours'] + $vv['reisestd'], 2); ?>
													</span>
							</td>
							<td align="center" <?= $classRowColor; ?>>
									<span
										class="workNoBreak"><?= number_format($vv['hours'] - $vv['pause'], 2); ?></span>
								<span><?= '+'; ?></span>
								<span class="drivingTime"><?= (number_format($vv['reisestd'], 2)); ?></span>
								<span><?= '=' ?></span>
								<span class="">
														<?= number_format($vv['hours'] - $vv['pause'] + $vv['reisestd'], 2); ?>
													</span>
							</td>
							<td class="comment.<?= $classRowColor; ?>"
								align="center" <?= $classRowColor; ?>>
								<?php refactorNote($vv['comment']); ?>
							</td>
							</tr>
							<?php
							$rowCounter++;
						} ?>
					<?php } ?>

					<tr>
						<td align="left" class="background <?= $hideTotalsClass; ?>" colspan="10">
							<b><?= PrintoutHelper::lang('bookings') . ': ' .
								count(array_filter(array_keys($v['months'][$month]), 'is_int')); ?></b>
							<b><?= PrintoutHelper::lang('hours') . ': ' .
								number_format((float)$v['months'][$month]['totalHours'], 2, '.', ''); ?></b>
							<b><?= PrintoutHelper::lang('days') . ': ' .
								count($v['months'][$month]['days']); ?></b>
						</td>
					</tr>
				<?php } ?>
			<?php } ?>
		<?php } ?>
	<?php } ?>
	<tr <?php hideFooter($data['totalHours']); ?> >
		<td colspan="8"></td>
		<td align="left"
			colspan="2"><?= PrintoutHelper::lang('approved_total') . ': ' .
			number_format((float)$data['approvedHours'], 2, '.', ''); ?></td>
	</tr>
	<tr <?php hideFooter($data['totalHours']); ?> >
		<td colspan="8"></td>
		<td align="left"
			colspan="2"><?= PrintoutHelper::lang('unapproved_total') . ': ' .
			number_format((float)$data['unapprovedHours'], 2, '.', ''); ?></td>
	</tr>
	<tr <?php hideFooter($data['totalHours']); ?> >
		<td colspan="8"></td>
		<td align="left"
			colspan="2"><?= PrintoutHelper::lang('total_drive_time') . ': ' .
			number_format((float)$data['totalDriveHours'], 2, '.', ''); ?></td>
	</tr>
	<tr <?php hideFooter($data['totalHours']); ?> >
		<td colspan="8"></td>
		<td align="left"
			colspan="2"><?= PrintoutHelper::lang('hours_total') . PrintoutHelper::lang('x_hours') . ': ' .
			number_format((float)$data['totalHours'], 2, '.', ''); ?></td>
	</tr>
</table>
</body>
</html>


<style>
	body {
		font-family: Arial, sans-serif;
	}

	.hours, th, td {
		border: 1px solid white;
		border-collapse: collapse;
		padding: 0;
		mso-cellspacing: 0;
	}

	.hours td {
		font-size: 10px;
		padding-left: 5px;
		vertical-align: top !important;
	}

	.hours th {
		font-size: 10px;
		vertical-align: top;
	}

	.hours {
		width: 100%;
	}

	.title {
		font-size: x-large;
	}

	.background {
		background-color: lightgrey;
	}

	.hideFooter {
		visibility: hidden;
	}

	.lightGrey {
		background-color: #ebebeb;
	}

	td.lightGrey {
		border-left: #ebebeb;
		border-right: #ebebeb;
	}

	.totalWork {
		background-color: #ff6e54;
		border-radius: 10px;
	}

	.workNoBreak {
		background-color: #73f8ff;
		border-radius: 10px;
	}

	.drivingTime {
		background-color: lawngreen;
		border-radius: 10px;
	}

	.breakTime {
		background-color: yellow;
		border-radius: 10px;
	}

	.hideTotals {
		display: none;
	}
</style>
