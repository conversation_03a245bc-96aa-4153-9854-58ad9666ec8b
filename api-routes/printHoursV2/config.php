<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "personalNo", required: false);
$config->addParameter(RouteConfigParameterType::date, "from", required: false);
$config->addParameter(RouteConfigParameterType::date, "to", required: false);
$config->addParameter(RouteConfigParameterType::string, "wageType", required: false);
$config->addParameter(RouteConfigParameterType::ktrAanr, "ktrAanr", required: false);
$config->addParameter(RouteConfigParameterType::string, "groupedByPeriod", required: false);
$config->addParameter(RouteConfigParameterType::bool, "showBonusOnly", required: false);
$config->addParameter(RouteConfigParameterType::string, "sortingOrder", required: false);
$config->addParameter(RouteConfigParameterType::string, "sortingColumn", required: false);
return $config;