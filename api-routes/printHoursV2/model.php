<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_printHoursV2
{
    /**
     * @return array<string, mixed>
     */
    public function getData(
        string|null $personalNo,
        string|null $from,
        string|null $to,
        string|null $wageType,
        string|null $ktrAanr,
        string|null $groupedByPeriod,
        string|null $showBonusOnly,
        string|null $sortingOrder,
        string|null $sortingColumn): array
    {
        if ($ktrAanr != '' && !is_numeric($ktrAanr)) {
            $wo = explode('-', $ktrAanr);
            $ktr = (int)$wo[0];
            $aanr = (int)$wo[1];
        } else if (is_numeric($ktrAanr)) {
            $ktr = (int)$ktrAanr;
            $aanr = 0;
        } else {
            $ktr = 0;
            $aanr = 0;
        }

        $curl = new PrintoutCurl();
        $params = [
            'resnr' => $personalNo,
            'ktr' => $ktr,
            'aanr' => $aanr,
            'type' => 'mixed',
            'wageType' => $wageType,
            'includeExpenseAllowanceInHoursEntry' => $showBonusOnly ? 1 : 0
        ];

        if ($from) {
            $params['date'] = $from;
        }
        if ($to) {
            $params['to_date'] = $to;
        }


        $base = PrintoutHelper::getApiBaseUrl();
        $hours = json_decode($curl->_simple_call('get', "$base/v1/hours/all", $params,
            PrintoutHelper::getHeadersForApiCalls()), true);

        /** @noinspection PhpUnhandledExceptionInspection */
        $hours = PrintoutHelper::adjustHoursByOffsetHeader($hours);

        $groupedHours = $this->groupHours($hours, $groupedByPeriod);
        $groupedHours['groupedByPeriod'] = $groupedByPeriod;
        $groupedHours['personalNo'] = $personalNo;

        $employee = PrintoutHelper::downloadEmployee($personalNo, $curl);
        $groupedHours['employeeName'] = $employee['displayName'];

        if ($employee['birthDate']) {
            $groupedHours['employeeBirthday'] = '(' . date('d.m.Y', strtotime($employee['birthDate'])) . ') ';
        }

        if ($groupedByPeriod == 'day') {
            $groupedHours = $this->sortHours($groupedHours, $sortingOrder, $sortingColumn);
        }
        return $groupedHours;
    }

    /**
     * @param array<string, mixed> $hours
     * @return array<string, mixed>
     */
    private function groupHours(array $hours, string|null $groupedByPeriod): array
    {
        $mappedMonths = [];
        foreach (range(1, 12) as $month) {
            // add leading zero if month is less than 10
            $month = str_pad(strval($month), 2, "0", STR_PAD_LEFT);
            $formatter = new IntlDateFormatter(
                PrintoutHelper::getLanguage(),
                IntlDateFormatter::NONE,
                IntlDateFormatter::NONE,
                null,
                null,
                'MMMM'
            );
            $mappedMonths[$month] = $formatter->format(strtotime("$month/01"));
        }

        $groupedHours = [];
        $groupedHours['approvedHours'] = 0;
        $groupedHours['unapprovedHours'] = 0;
        $groupedHours['totalDriveHours'] = 0;
        $groupedHours['totalHours'] = 0;

        if ($groupedByPeriod == 'year') {
            // year grouping of the hours
            foreach ($hours as $v) {
                $timestamp = strtotime($v['date']);
                $year = date("Y", $timestamp);
                $groupedHours[$year]['hours'][] = $v;

                if ($v['origin'] == 'hours') {
                    $groupedHours['approvedHours'] += $v['hours'];
                } else {
                    $groupedHours['unapprovedHours'] += $v['hours'];
                }

                $groupedHours['totalHours'] += $v['hours'];
                $groupedHours['totalHours'] += $v['reisestd'];
                $groupedHours['totalDriveHours'] += $v['reisestd'];
                $groupedHours[$year]['totalHours'] += $v['hours'];

                if (!array_key_exists('days', $groupedHours[$year])) {
                    $groupedHours[$year]['days'][] = date('d-m-Y', $timestamp);
                } else {
                    if (!in_array(date('d-m-Y', $timestamp), $groupedHours[$year]['days'])) {
                        $groupedHours[$year]['days'][] = date('d-m-Y', $timestamp);
                    }
                }
            }

        } else if ($groupedByPeriod == 'month') {
            foreach ($hours as $v) {
                $timestamp = strtotime($v['date']);
                $year = date("Y", $timestamp);
                $fullDate = date('d-m-Y', $timestamp);

                if ($v['origin'] == 'hours') {
                    $groupedHours['approvedHours'] += $v['hours'];
                } else {
                    $groupedHours['unapprovedHours'] += $v['hours'];
                }

                $groupedHours['totalHours'] += $v['hours'];
                $groupedHours['totalHours'] += $v['reisestd'];
                $groupedHours['totalDriveHours'] += $v['reisestd'];
                $groupedHours[$year]['yearlyBookings'][] = $v;
                $groupedHours[$year]['yearlyTotalHours'] += $v['hours'];

                if (array_key_exists('days', $groupedHours[$year])) {
                    if (!in_array($fullDate, $groupedHours[$year]['days'])) {
                        $groupedHours[$year]['days'][] = $fullDate;
                    }
                } else {
                    $groupedHours[$year]['days'][] = $fullDate;
                }

                $monthKey = date('m', $timestamp) . $mappedMonths[date('m', $timestamp)];
                $groupedHours[$year]['months'][$monthKey]['hours'][] = $v;
                $groupedHours[$year]['months'][$monthKey]['totalHours'] += $v['hours'];

                if (array_key_exists('days', $groupedHours[$year]['months'][$monthKey])) {
                    if (!in_array($fullDate, $groupedHours[$year]['months'][$monthKey]['days'])) {
                        $groupedHours[$year]['months'][$monthKey]['days'][] = $fullDate;
                    }
                } else {
                    $groupedHours[$year]['months'][$monthKey]['days'][] = $fullDate;
                }
            }

        } else {
            //grouped hours by day
            foreach ($hours as &$v) {
                $timestamp = strtotime($v['date']);
                $year = date("Y", $timestamp);
                $fullDate = date('d-m-Y', $timestamp);

                if (isset($v['breaks']['start'])) {
                    $v['startBreak'] = $v['breaks']['start'];
                }
                if (isset($v['breaks']['end'])) {
                    $v['endBreak'] = $v['breaks']['end'];
                }

                if ($v['origin'] == 'hours') {
                    $groupedHours['approvedHours'] += $v['hours'];
                } else {
                    $groupedHours['unapprovedHours'] += $v['hours'];
                }

                $groupedHours['totalHours'] += $v['hours'];
                $groupedHours['totalHours'] += $v['reisestd'];
                $groupedHours['totalDriveHours'] += $v['reisestd'];

                $groupedHours[$year]['yearlyBookings'][] = $v;

                if (!isset($groupedHours[$year]['yearlyTotalHours'])) {
                    $groupedHours[$year]['yearlyTotalHours'] = 0;
                }
                $groupedHours[$year]['yearlyTotalHours'] += $v['hours'];

                if (array_key_exists('days', $groupedHours[$year])) {
                    if (!in_array($fullDate, $groupedHours[$year]['days'])) {
                        $groupedHours[$year]['days'][] = $fullDate;
                    }
                } else {
                    $groupedHours[$year]['days'][] = $fullDate;
                }

                $dateTime = new DateTime($v['date']);
                $monthDateTime = $dateTime->format('m');

                $monthKey = $monthDateTime . $mappedMonths[$monthDateTime];
                $groupedHours[$year]['months'][$monthKey][] = $v;
                $month = $monthKey;

                if (!isset($groupedHours[$year]['months'][$month]['totalHours'])) {
                    $groupedHours[$year]['months'][$month]['totalHours'] = 0;
                }
                $groupedHours[$year]['months'][$month]['totalHours'] += $v['hours'];

                if (array_key_exists('days', $groupedHours[$year]['months'][$monthKey])) {
                    if (!in_array($fullDate,
                        $groupedHours[$year]['months'][$monthKey]['days'])) {
                        $groupedHours[$year]['months'][$monthKey]['days'][] = $fullDate;
                    }
                } else {
                    $groupedHours[$year]['months'][$monthKey]['days'][] = $fullDate;
                }
            }
        }

        // the order of the years and moths is always ascending (based on the behaviour of the web portal)
        ksort($groupedHours);
        foreach ($groupedHours as $key => $hours) {
            if (is_numeric($key)) {
                ksort($hours['months']);
            }
        }

        return $groupedHours;
    }

    /**
     * @param array<string, mixed> $groupedHours
     * @return array<string, mixed>
     */
    private function sortHours(array $groupedHours, string $sortingOrder, string $sortingColumn): array
    {
        $sortingParameter = '';
        //one of the properties of each hours record will be used for sorting
        //mapping between the API node parameter and the properties in the hours response
        switch ($sortingColumn) {
            case 'date':
                $sortingParameter = 'date';
                break;
            case 'entity':
                $sortingParameter = 'name';
                break;
            case 'driveTimeTo':
                $sortingParameter = 'startDriveTo';
                break;
            case 'driveTimeFrom':
                $sortingParameter = 'startDriveFrom';
                break;
            case 'breakTime':
                $sortingParameter = 'pause';
                break;
            case 'workingTime':
                $sortingParameter = 'start';
                break;
            case 'totalDriveTime':
                $sortingParameter = 'reisestd';
                break;
            case 'totalWorkingTime':
                $sortingParameter = 'hours';
                break;
            case 'notes':
                $sortingParameter = 'ltext';
                break;
            // this one is only valid for showBonusOnly: true
//            case 'totalAmount':
            //TO DO
//                $sortingParameter = '';
        }

        foreach ($groupedHours as $k => $v) {
            if (is_numeric($k)) {
                foreach ($v['months'] as $month => $hours) {
                    $order = array();
                    foreach ($v['months'][$month] as $key => $row) {
                        $order[$key] = $row[$sortingParameter];
                    }

                    if ($sortingOrder == 'ascending') {
                        array_multisort($order, SORT_ASC, $v['months'][$month]);
                    } else {
                        array_multisort($order, SORT_DESC, $v['months'][$month]);
                    }
                }
            }
        }

        return $groupedHours;
    }
}
