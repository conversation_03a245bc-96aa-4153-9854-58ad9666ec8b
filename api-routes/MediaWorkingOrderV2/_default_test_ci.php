<?php
require_once __DIR__ . '/../../scripts/CiEntityCreator.php';
require_once __DIR__ . '/../../PrintoutCompiler.php';

$workingOrderData = CiEntityCreator::downloadAnyWorkingOrderAndGetId();
$projectNo = $workingOrderData['projectNo'];
$workingOrderNo = $workingOrderData['workingOrderNo'];

// Upload images to the working order
$imageFilePaths = [
    __DIR__ . '/../../tests/images/190x50.png',
    __DIR__ . '/../../tests/images/300x50.png',
];
$uploads = CiEntityCreator::uploadImagesToWorkingOrder($projectNo, $workingOrderNo, $imageFilePaths);

$images = [
    'saveHardCopy' => false,
    'images' => [
        [
            'fileId' => $uploads[0]['fid'],
            'description' => 'Test image 1: ' . $uploads[0]['filename'],
            'showWorkingOrderNumber' => true,
            'showUploader' => true,
            'showComments' => true,
            'showUploadDate' => true,
            'showCreationDate' => true,
        ],
        [
            'fileId' => $uploads[1]['fid'],
            'description' => 'Test image 2: ' . $uploads[1]['filename'],
            'showWorkingOrderNumber' => false,
            'showUploader' => false,
            'showComments' => false,
            'showUploadDate' => false,
            'showCreationDate' => false,
        ]
    ],
];

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::MediaWorkingOrderV2,
    apiRouteSuffix: "/$projectNo/$workingOrderNo",
    method: RequestMethod::POST,
    requestBodyForPost: json_encode($images),
    params: [
        "hidePageNumbers" => "0"
    ]
);
