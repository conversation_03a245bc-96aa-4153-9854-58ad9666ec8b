<?php

use MediaPrintV2\HardCopyUpload;

require_once __DIR__ . '/../MediaPrintV2/HardCopyUpload.php';

$config = new RouteConfig();
$config->usePlaywright(new PlaywrightConfig(
    marginTopMm: 30,
    marginBottomMm: 10,
    marginLeftMm: 10,
    marginRightMm: 10
));
$config->addRouteSegment(RouteConfigParameterType::int, "projectNo", required: true);
$config->addRouteSegment(RouteConfigParameterType::int, "workingOrderNo", required: true);
$config->addParameter(RouteConfigParameterType::bool, "hidePageNumbers", required: false);
$config->redirectToRoute = 'MediaPrintV2';
$config->beforeRouteCallback = function (&$query) {
    $query['projectNo'] = $_GET['projectNo'] = Request::$query['projectNo'] ?? '';
    $query['workingOrderNo'] = $_GET['workingOrderNo'] = Request::$query['workingOrderNo'] ?? '';
};
$config->afterPdfGenerationCallback = function (string $pdfPath) {
    if (!(Request::$query['saveHardCopy'] ?? false)) {
        return;
    }
    HardCopyUpload::uploadHardCopy($pdfPath, "MediaWorkingOrderV2");
};
$config->requestMethod = PrintoutRequestMethod::POST;
return $config;