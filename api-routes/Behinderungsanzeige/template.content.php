<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Behinderungsanzeige())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<html lang="de">
<head>
    <title>Behinderungsanzeige</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">

    <style>
        p {
            margin-left: 70px;
        }

        .lmargin {
            margin-left: 110px;
        }

        .signature {
            max-height: 200px;
            object-fit: contain;
            max-width: 100%;
        }


        html {
            font-family: Arial, serif;
        }

        .margin-checkbox {
            margin-left: 30px;

        }

        .checkbox-size {
            font-size: 20px;
            font-weight: bold;
        }
    </style>

</head>

<body>
<p style="font-size: 16pt;"><b>Behinderungsanzeige gem. § 6 Abs. 1 VOB/B </b></p>
<p>Sehr geehrte Damen und Herren, </p>
<p> als anerkannte Fachfirma sind wir stets bemüht, Ihnen eine sach- und fachgerechte Leistung zu erbringen. Wir
    sehen es
    deshalb als unsere Pflicht an, Ihnen von folgenden Umständen, die uns in der weiteren Ausführung unserer
    Gerüstbauarbeiten
</p>

<table style="width: 100%; padding-left: 70px; margin-top: 20px; margin-bottom: 10px">
    <tr>
        <td style="width: 5%">
            <div class="checkbox-size"><?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B behindern']) ? "&#9745;" : "&#9744;"; ?> </div>
        </td>
        <td style="width: 25%">behindern,</td>
        <td style="width: 5%">
            <div class="checkbox-size"><?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B voraussichtlich behindern werden...']) ? "&#9745;" : "&#9744;"; ?> </div>
        </td>
        <td>voraussichtlich behindern werden,</td>
</table>

<p>hiermit schriftlich gem. § 6 Abs. 1 VOB/B Anzeige zu erstatten: </p>

<span style="margin-top: 38px; margin-bottom: 38px; font-weight: bold; margin-left: 70px">
    <?= $data["Behinderungsanzeige gem. § 6 Abs. 1 VOB/B hiermit schriftlich gem. § 6 Abs. 1 VOB/B Anzeige zu erstatten"] ?? "" ?>
</span>

<table style="width: 100%; padding-left: 70px; margin-top: 20px; margin-bottom: 10px">
    <tr>
        <td style="width: 5%">
            <div class="checkbox-size"><?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Diese Umstände sind Ihnen bekannt']) ? "&#9745;" : "&#9744;"; ?> </div>
        </td>
        <td>Diese Umstände sind Ihnen bekannt</td>
</table>

<p>Selbstverständlich werden wir unsere Arbeiten unverzüglich wieder aufnehmen, sobald die Behinderung weggefallen ist.</p>

<table style="width: 100%; margin-top: 15px; padding-left: 70px">
    <tr>
        <td class="checkbox-size"> <?php
            if (isset($data['Da die Behinderung u. E. verursacht ist durch Streik']) || isset($data['Da die Behinderung u. E. verursacht ist durch eine von unserer Berufsvertretung angeordneten Aussperrung']) || isset($data['Da die Behinderung u. E. verursacht ist durch höhere Gewalt']) || isset($data['Da die Behinderung u. E. verursacht ist durch einen von Ihnen zu vertretenden Umstand']) || isset($data['Da die Behinderung u. E. verursacht ist durch einen für uns unabwendbaren Umstand'])) {
                echo("&#9745;");
            } else {
                echo("&#9744;");
            }

            ?> </td>
        <td colspan="2">Diese Umstände sind Ihnen bekannt. Da die Behinderung u. E. verursacht ist durch</td>
    </tr>
    <tr>
        <td></td>
        <td style="width: 5%"
            class="checkbox-size"><?= isset($data['Da die Behinderung u. E. verursacht ist durch einen von Ihnen zu vertretenden Umstand']) ? "&#9745;" : "&#9744;"; ?></td>
        <td style="width: 90%">einen von Ihnen zu vertretenden Umstand,</td>
    </tr>
    <tr>
        <td></td>
        <td class="checkbox-size"><?= isset($data['Da die Behinderung u. E. verursacht ist durch Streik']) ? "&#9745;" : "&#9744;"; ?></td>
        <td>Streik</td>
    </tr>
    <tr>
        <td></td>
        <td class="checkbox-size"><?= isset($data['Da die Behinderung u. E. verursacht ist durch eine von unserer Berufsvertretung angeordneten Aussperrung']) ? "&#9745;" : "&#9744;"; ?></td>
        <td>eine von unserer Berufsvertretung angeordneten Aussperrung</td>
    </tr>
    <tr>
        <td></td>
        <td class="checkbox-size"><?= isset($data['Da die Behinderung u. E. verursacht ist durch höhere Gewalt']) ? "&#9745;" : "&#9744;"; ?></td>
        <td>höhere Gewalt</td>
    </tr>
    <tr>
        <td></td>
        <td class="checkbox-size"><?= isset($data['Da die Behinderung u. E. verursacht ist durch einen für uns unabwendbaren Umstand']) ? "&#9745;" : "&#9744;"; ?></td>
        <td>einen für uns unabwendbaren Umstand</td>
    </tr>
    <tr>
        <td></td>
        <td colspan="2">möchten wir es nicht versäumen, Sie schon jetzt darauf aufmerksam zu
            machen, dass sich<br>die Ausführungsfristen verlängern werden.
        </td>
    </tr>
</table>

<table style="width: 100%; padding-left: 70px; margin-top: 20px; margin-bottom: 10px">
    <tr>
        <td style="width: 5%">
            <div class="checkbox-size"><?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Die Dauer der Behinderung ist noch nicht absehbar. Wir verweisen jedoch auf § 6 Abs. 4 VOB/B.']) ? "&#9745;" : "&#9744;"; ?> </div>
        </td>
        <td>Die Dauer der Behinderung ist noch nicht absehbar. Wir verweisen jedoch auf § 6 Abs. 4 VOB/B.</td>
</table>

<table style="width: 100%; padding-left: 70px; margin-top: 10px; margin-bottom: 10px">
    <tr>
        <td style="width: 5%; vertical-align: top">
            <div class="checkbox-size"><?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Schon jetzt ist die Dauer der Behinderung absehbar, so dass sich die Verlängerung der Ausführungsfristen gem. § 6 Abs. 4 VOB/B wie folgt errechnet:']) ? "&#9745;" : "&#9744;"; ?> </div>
        </td>
        <td>
            Schon jetzt ist die Dauer der Behinderung absehbar, sodass sich die Verlängerung der Ausführungsfristen gem. § 6 Abs. 4 VOB/B wie folgt errechnet:
        </td>
</table>

<br>

<table class="lmargin">
    <tbody>
    <tr>
        <td><span
                    class="checkbox-size"><?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Dauer der Behinderung']) ? "&#9745;" : "&#9744;"; ?> </span>
        </td>
        <td style="/*! padding-right: 30%; */width: 700px;"><span class="margin-checkbox">Dauer der Behinderung,</span>
        </td>
        <td style="width: 120px">
            <b><?= $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Dauer der Behinderung'] ?? '' ?></b>
            Tage,
        </td>

    </tr>
    <tr>
        <td><span
                    class="checkbox-size"> <?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B zzgl. einer Zeitspanne für die Wiederaufnahme']) ? "&#9745;" : "&#9744;"; ?> </span>
        </td>
        <td><span class="margin-checkbox">zzgl. einer Zeitspanne für die Wiederaufnahme der Arbeiten, </span></td>
        <td style="/*! padding-left: 30%; */">
            <b><?= $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B zzgl. einer Zeitspanne für die Wiederaufnahme'] ?? '' ?></b>
            Tage,
        </td>
    </tr>
    <tr>
        <td style="vertical-align: top" class="checkbox-size">
            <?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B zzgl. einer Zeitspanne wegen Verschiebung']) ? "&#9745;" : "&#9744;"; ?>
        </td>
        <td><span class="margin-checkbox">zzgl. einer Zeitspanne wegen Verschiebung der Arbeiten </span><br> <span
                    style="margin-left: 30px;">in eine ungünstigere Jahreszeit, </span></td>
        <td style="/*! padding-left: 30%; */">
            <b><?= $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B zzgl. einer Zeitspanne wegen Verschiebung'] ?? '' ?></b>
            Tage
        </td>

    </tr>
    <tr>
        <td></td>
        <td></td>
        <td style="border-bottom: 1px dashed black;padding-left: 30%;"></td>
    </tr>
    <tr>
        <td></td>
        <td></td>
        <td style="border-bottom: 2px solid black"><b><?php
                $a = $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Dauer der Behinderung'] ?? 0;
                $b = $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B zzgl. einer Zeitspanne für die Wiederaufnahme'] ?? 0;
                $c = $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B zzgl. einer Zeitspanne wegen Verschiebung'] ?? 0;

                $sum = $a + $b + $c;
                if ($sum === 0) {
                    echo "Tage.";
                } else {
                    echo $sum . " Tage.";
                }
                ?>
        </td>

    </tr>

    <tr>
        <td><span class="checkbox-size">
                <?= isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Neuer Fertigstellungstermin']) ? "&#9745;" : "&#9744;"; ?>
            </span>
        </td>
        <td><span class="margin-checkbox">Neuer Fertigstellungstermin wäre demnach: </span>
            <b>
                <?php
                if (isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Neuer Fertigstellungstermin'])) {
                    echo date_format(date_create($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Neuer Fertigstellungstermin']),
                        'd.m.Y');
                }
                ?>
            </b>
        </td>
    </tr>
    </tbody>
</table>
<br>
<p>Bei eventuellen Rückfragen stehen wir Ihnen selbstverständlich gerne zur Verfügung. In diesem Fall bitten wir Sie um
    unverzügliche Benachrichtigung, ggf. um Benennung eines Besprechungstermins.</p>
<table style="width:100%; padding-left: 70px;">
    <tbody>
    <tr>
        <td style="vertical-align: bottom; padding-right: 60px">

            <?php echo $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Ort Auftragnehmer'] ?? '';

            $needsComma = isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Ort Auftragnehmer']) &&
                isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Datum Auftragnehmer']);
            echo $needsComma ? ', ' : '';

            if (isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Datum Auftragnehmer'])) {
                echo date_format(date_create($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Datum Auftragnehmer']),
                    'd.m.Y');
            }
            ?>

        <td style="width: 5%"></td>
        <td style="width: 50%">
            <?php if (isset($data["Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Auftragnehmer"])) { ?>

                <img style="max-height: 75px; width: auto"
                     src="<?= $data["Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Auftragnehmer"] ?>"
                     alt="" class="signature">

            <?php } ?>
    </tr>
    <tr>
        <td style="border-top: 1px solid black">Ort/Datum</td>
        <td style="width: 5%"></td>
        <td style="border-top: 1px solid black">Auftragnehmer</td>
    </tr>
    </tbody>
</table>
<p>Wir bitten Sie, uns unsere Mitteilung durch Unterschrift und Rückgabe eines Exemplares</p>
<p style="text-align: center; font-weight:bold; margin: 30px 0 "> bis
    zum&nbsp; <?php
    if (isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Neuer Fertigstellungstermin'])) {
        echo date_format(date_create($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Neuer Fertigstellungstermin']),
            'd.m.Y');
    }
    ?>

</p>
<p>zu bestätigen.</p>

<table style="width:100%; padding-left: 70px;">
    <tbody>
    <tr>
        <td style="vertical-align: bottom; padding-right: 60px">

            <?php echo $data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Ort Auftraggeber'] ?? '';

            $needsComma = isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Ort Auftraggeber']) &&
                isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Datum Auftraggeber']);
            echo $needsComma ? ', ' : '';

            if (isset($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Datum Auftraggeber'])) {
                echo date_format(date_create($data['Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Datum Auftraggeber']),
                    'd.m.Y');
            }
            ?>
        <td style="width: 5%"></td>
        <td style="width: 50%">

            <?php if (isset($data["Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Auftraggeber"])) { ?>

                <img style="max-height: 75px; width: auto"
                     src="<?= $data["Behinderungsanzeige gem. § 6 Abs. 1 VOB/B Auftraggeber"] ?>"
                     alt="" class="signature">

            <?php } ?>
        </td>
    </tr>
    <tr>
        <td style="border-top: 1px solid black">Ort/Datum</td>
        <td style="width: 5%"></td>
        <td style="border-top: 1px solid black">Auftraggeber</td>
    </tr>
    </tbody>
</table>

</body>
</html>
