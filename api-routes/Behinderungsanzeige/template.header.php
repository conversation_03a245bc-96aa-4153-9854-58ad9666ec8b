<?php
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Behinderungsanzeige())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>


<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title></title>
    <style>
        html, body {
            margin: 0;
            padding: 0;
            font-family: Arial, serif;
            width: 100%;
        }
    </style>
</head>
<body>
<div style="display: -webkit-box; -webkit-box-pack: center;">
    <img alt="" style="display: inline-block; max-width: 300px; max-height: 60px; object-fit: contain"
         src="<?= $data['logo'] ?>"/>
    <div style="margin-left: 15px; display: -webkit-box; -webkit-box-pack: center; -webkit-box-orient: block-axis">
        <p style="margin: 0; font-weight: lighter; display: inline-block; font-size: 0.9em; color: rgb(102, 102, 102);">
            <?= $data['addressWithPhone'] ?>
        </p>
    </div>
</div>

<div style="height: 20px;"></div>
</body>
</html>