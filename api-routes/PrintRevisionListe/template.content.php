<?php
namespace PrintRevisionListe;

use C_PrintRevisionListe;
use PrintoutHelper;

require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_PrintRevisionListe())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<string, mixed> $data
 */
function getRow(array $data, int $index, int $bemerkungIndex): string
{
    $checkbox = $data['mappedChildren'][$index];
    $bemerkung = $data['mappedChildren'][$bemerkungIndex]['value'] ?? '';
    $checkboxTitle = $checkbox['all']['title'] ?? '';

    $tr = "<tr>";
    $tr .= "<td>" . $checkboxTitle . "</td>";
    $tr .= getChecks($checkbox['all']);
    $tr .= "<td>" . $bemerkung . "</td>";
    $tr .= "</tr>";

    return $tr;
}

/**
 * @param array<string, mixed> $checkbox
 */
function getChecks(array $checkbox): string
{
    $checked = PrintoutHelper::isCheckboxTicked($checkbox);
    $checkMark = "<td class='checkbox-td'>&#9745;</td>";
    $uncheckMark = "<td class='checkbox-td'>&#9744;</td>";

    if ($checked) {
        return $checkMark . $uncheckMark;
    }
    return $uncheckMark . $checkMark;
}

/**
 * @param array<string, mixed> $data
 */
function createHeaderRow(array $data, int $index): string
{
    $title = $data['mappedChildren'][$index]['all']['title'] ?? '';
    return sprintf("
            <th>%s</th>
            <th>%s</th>
            <th>%s</th>
            <th>%s</th>
            ", $title, PrintoutHelper::lang('yes'), PrintoutHelper::lang('no'), PrintoutHelper::lang('remark_measures'));
}

/**
 * @param array<string, mixed> $data
 */
function displayMiscellaneous(array $data): string
{
    $reportedValue = $data['mappedChildren'][119]['value'] ?? '';

    $tr = sprintf('<td colspan="3" class="bordered">10) %s </td>', PrintoutHelper::lang('miscellaneous'));
    $tr .= sprintf('<td>%s</td>', $reportedValue);
    return $tr;
}

/**
 * @param array<string, mixed> $data
 */
function displayFooterRow(array $data): string
{
    $ort = $data['mappedChildren'][121]['value'];
    $date = $data['mappedChildren'][122]['value'];
    $time = $data['mappedChildren'][123]['value'];
    $signature = $data['mappedChildren'][124]['value'];
    $tr = "<tr>";

    $tr .= sprintf("<th style='text-align: center;'>%s</th>", $ort);

    if (empty($date)) {
        $tr .= "<th></th>";
    } else {
        $tr .= "<th>" . date('d.m.Y', strtotime($date)) . "</th>";
    }

    if (empty($time)) {
        $tr .= "<th></th>";
    } else {
        $tr .= "<th>" . date('d.m.Y', strtotime($time)) . "</th>";
    }

    if (empty($signature)) {
        $tr .= "<th></th>";
    } else {
        /** @noinspection HtmlUnknownTarget */
        $tr .= sprintf('<th><img class="signature-img" src="%s" alt="Signature"></th>', $signature);
    }
    $tr .= "</tr>";
    return $tr;
}

?>

<html lang="<?= PrintoutHelper::getLanguage() ?>">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Revisionsliste zur Gefährdungsbeurteilung</title>
</head>
<body>
<table class="headerTable">
    <tr>
        <td class="site-address">
            <b><?= PrintoutHelper::lang('construction_site_address') ?></b>
            <?php
            $address = PrintoutHelper::formatAddress(
                $data['projectData']['projectSiteAddress'],
                $data['projectData']['projectSiteZipCode'],
                $data['projectData']['projectSiteCity']);
            if ($address !== "") {
                echo "<br>" . $address;
            }
            ?>
        </td>
        <th class="secondHeader">
            <?= PrintoutHelper::lang('site_supervisor') ?>
        </th>
        <th class="thirdHeader">
            <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("img/SchaeferGeruestbauLogo.png") ?>"
                 alt="SchaeferGeruestbauLogo" class="companyLogo">
        </th>
    </tr>
    <tr>
        <td colspan="3">
            <p class="header-intro-text"><?= PrintoutHelper::lang('risk_management_list') ?></p>
            <?= PrintoutHelper::lang('intro_text') ?>
        </td>
    </tr>
</table>
<table class="checkboxes-table">
    <tr class="bordered">
        <?= createHeaderRow($data, 1) ?>
    </tr>
    <tr>
        <?= getRow($data, 2, 6); ?>
    </tr>
    <tr>
        <?= getRow($data, 3, 7); ?>
    </tr>
    <tr>
        <?= getRow($data, 4, 8); ?>
    </tr>
    <tr>
        <?= getRow($data, 5, 9); ?>
    </tr>
    <tr class="bordered">
        <?= createHeaderRow($data, 10) ?>
    </tr>
    <tr>
        <?= getRow($data, 11, 16); ?>
    </tr>
    <tr>
        <?= getRow($data, 12, 17); ?>
    </tr>
    <tr>
        <?= getRow($data, 13, 18); ?>
    </tr>
    <tr>
        <?= getRow($data, 14, 19); ?>
    </tr>
    <tr>
        <?= getRow($data, 15, 20); ?>
    </tr>
    <tr class="bordered">
        <?= createHeaderRow($data, 21) ?>
    </tr>
    <tr>
        <?= getRow($data, 22, 27); ?>
    </tr>
    <tr>
        <?= getRow($data, 23, 28); ?>
    </tr>
    <tr>
        <?= getRow($data, 24, 29); ?>
    </tr>
    <tr>
        <?= getRow($data, 25, 30); ?>
    </tr>
    <tr>
        <?= getRow($data, 26, 31); ?>
    </tr>
</table>

<table class="checkboxes-table page-break-before">
    <tr class="bordered">
        <?= createHeaderRow($data, 32) ?>
    </tr>
    <tr>
        <?= getRow($data, 33, 49); ?>
    </tr>
    <tr>
        <?= getRow($data, 34, 50); ?>
    </tr>
    <tr>
        <?= getRow($data, 35, 51); ?>
    </tr>
    <tr>
        <?= getRow($data, 36, 52); ?>
    </tr>
    <tr>
        <?= getRow($data, 37, 53); ?>
    </tr>
    <tr>
        <?= getRow($data, 38, 54); ?>
    </tr>
    <tr>
        <?= getRow($data, 39, 55); ?>
    </tr>
    <tr>
        <?= getRow($data, 40, 56); ?>
    </tr>
    <tr>
        <?= getRow($data, 41, 57); ?>
    </tr>
    <tr>
        <?= getRow($data, 42, 58); ?>
    </tr>
    <tr>
        <?= getRow($data, 43, 59); ?>
    </tr>
    <tr>
        <?= getRow($data, 44, 60); ?>
    </tr>
    <tr>
        <?= getRow($data, 45, 61); ?>
    </tr>
    <tr>
        <?= getRow($data, 46, 62); ?>
    </tr>
    <tr>
        <?= getRow($data, 47, 63); ?>
    </tr>
    <tr>
        <?= getRow($data, 48, 64); ?>
    </tr>
    <tr class="bordered">
        <?= createHeaderRow($data, 65) ?>
    </tr>
    <tr>
        <?= getRow($data, 66, 70); ?>
    </tr>
    <tr>
        <?= getRow($data, 67, 71); ?>
    </tr>
    <tr>
        <?= getRow($data, 68, 72); ?>
    </tr>
    <tr>
        <?= getRow($data, 69, 73); ?>
    </tr>
    <tr class="bordered">
        <?= createHeaderRow($data, 74) ?>
    </tr>
    <tr>
        <?= getRow($data, 75, 79); ?>
    </tr>
    <tr>
        <?= getRow($data, 76, 80); ?>
    </tr>
    <tr>
        <?= getRow($data, 77, 81); ?>
    </tr>
    <tr>
        <?= getRow($data, 78, 82); ?>
    </tr>
</table>

<table class="checkboxes-table page-break-before">
    <tr class="bordered">
        <?= createHeaderRow($data, 83) ?>
    </tr>
    <tr>
        <?= getRow($data, 84, 92); ?>
    </tr>
    <tr>
        <?= getRow($data, 85, 93); ?>
    </tr>
    <tr>
        <?= getRow($data, 86, 94); ?>
    </tr>
    <tr>
        <?= getRow($data, 87, 95); ?>
    </tr>
    <tr>
        <?= getRow($data, 88, 96); ?>
    </tr>
    <tr>
        <?= getRow($data, 89, 97); ?>
    </tr>
    <tr>
        <?= getRow($data, 90, 98); ?>
    </tr>
    <tr>
        <?= getRow($data, 91, 99); ?>
    </tr>
    <tr class="bordered">
        <?= createHeaderRow($data, 100) ?>
    </tr>
    <tr>
        <?= getRow($data, 101, 105); ?>
    </tr>
    <tr>
        <?= getRow($data, 102, 106); ?>
    </tr>
    <tr>
        <?= getRow($data, 103, 107); ?>
    </tr>
    <tr>
        <?= getRow($data, 104, 108); ?>
    </tr>
    <tr class="bordered">
        <?= createHeaderRow($data, 109) ?>
    </tr>
    <tr>
        <?= getRow($data, 110, 114); ?>
    </tr>
    <tr>
        <?= getRow($data, 111, 115); ?>
    </tr>
    <tr>
        <?= getRow($data, 112, 116); ?>
    </tr>
    <tr>
        <?= getRow($data, 113, 117); ?>
    </tr>
    <?= displayMiscellaneous($data) ?>
    <tr>
        <td colspan="4"></td>
    </tr>
    <tr>
        <td colspan="4" class="bordered">
            <?= PrintoutHelper::lang('outro_text') ?>
        </td>
    </tr>
</table>

<table class="footer">
    <?= displayFooterRow($data) ?>
    <tr class="bordered">
        <td style="text-align: center"><?= PrintoutHelper::lang('location') ?></td>
        <td><?= PrintoutHelper::lang('date') ?></td>
        <td><?= PrintoutHelper::lang('time') ?></td>
        <td><?= PrintoutHelper::lang('site_supervisor_signature') ?></td>
    </tr>
</table>
</body>
</html>