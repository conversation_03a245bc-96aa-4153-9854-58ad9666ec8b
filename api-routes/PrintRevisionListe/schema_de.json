{"name": "Revisionsliste zur Gefährdungsbeurteilung", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2019-12-17T16:40:00Z", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["8"], "positions": [{"id": 1, "title": "1) Dokumentation", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 Schriftlicher Arbeitsauftrag vorhanden", "type": "checkbox"}, {"id": 3, "title": "Bemerkung", "type": "string", "displayInside": 2}, {"id": 4, "parentId": 1, "title": "1.2 Fahrtbeschreibung vorhanden", "type": "checkbox"}, {"id": 5, "title": "Bemerkung", "type": "string", "displayInside": 4}, {"id": 6, "parentId": 1, "title": "1.3 Montageanweisung (Auf-,Um- oder Abbau) vorhanden", "type": "checkbox"}, {"id": 7, "title": "Bemerkung", "type": "string", "displayInside": 6}, {"id": 8, "parentId": 1, "title": "1.4 Techn. Zeichnungen vorhanden", "type": "checkbox"}, {"id": 9, "title": "Bemerkung", "type": "string", "displayInside": 8}, {"id": 10, "title": "2) Allg. Verkehrssicherung (Straßen, Durchfahrt, Gehweg)", "type": "headline"}, {"id": 11, "parentId": 10, "title": "2.1 Einweiser zum Einparken erforderlich", "type": "checkbox"}, {"id": 12, "title": "Bemerkung", "type": "string", "displayInside": 11}, {"id": 13, "parentId": 10, "title": "2.2 Einweiser zur Verkehrssicherung erforderlich", "type": "checkbox"}, {"id": 14, "title": "Bemerkung", "type": "string", "displayInside": 13}, {"id": 15, "parentId": 10, "title": "2.3 Fahrzeugsicherung (Pylone, Warnbake etc.) durchgeführt", "type": "checkbox"}, {"id": 16, "title": "Bemerkung", "type": "string", "displayInside": 15}, {"id": 17, "parentId": 10, "title": "2.4 Lade- und Transportbereich unmittelbar an Straßen, <PERSON><PERSON><PERSON><PERSON>ten, Fahrrad oder Gehwegen", "type": "checkbox"}, {"id": 18, "title": "Bemerkung", "type": "string", "displayInside": 17}, {"id": 19, "parentId": 10, "title": "•<PERSON><PERSON><PERSON>, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich", "type": "checkbox"}, {"id": 20, "title": "Bemerkung", "type": "string", "displayInside": 19}, {"id": 21, "title": "3) Baustellene<PERSON>richtung", "type": "headline"}, {"id": 22, "parentId": 21, "title": "3.1 WC bzw. mobiles WC vorhanden", "type": "checkbox"}, {"id": 23, "title": "Bemerkung", "type": "string", "displayInside": 22}, {"id": 24, "parentId": 21, "title": "3.2 Waschgelegenheit vorhanden", "type": "checkbox"}, {"id": 25, "title": "Bemerkung", "type": "string", "displayInside": 24}, {"id": 26, "parentId": 21, "title": "3.3 Pausenraum vorhanden", "type": "checkbox"}, {"id": 27, "title": "Bemerkung", "type": "string", "displayInside": 26}, {"id": 28, "parentId": 21, "title": "3.4 Parkmöglichkeiten für unsere Fahrzeuge ausreichend vorhanden", "type": "checkbox"}, {"id": 29, "title": "Bemerkung", "type": "string", "displayInside": 28}, {"id": 30, "parentId": 21, "title": "3.5 Abstellflächen zur Materiallagerung ausreichend vorhanden", "type": "checkbox"}, {"id": 31, "title": "Bemerkung", "type": "string", "displayInside": 30}, {"id": 32, "title": "4) Baustellengegebenheiten", "type": "headline"}, {"id": 33, "parentId": 32, "title": "4.1 Montagebereich unmittelbar an Straßen, <PERSON><PERSON><PERSON><PERSON><PERSON>, Fahrrad oder Gehwegen", "type": "checkbox"}, {"id": 34, "title": "Bemerkung", "type": "string", "displayInside": 33}, {"id": 35, "parentId": 32, "title": "•<PERSON><PERSON><PERSON>, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich", "type": "checkbox"}, {"id": 36, "title": "Bemerkung", "type": "string", "displayInside": 35}, {"id": 37, "parentId": 32, "title": "4.2 Baustromverteiler zum Anschluss der Geräte (z.B. Ladegerät) vorhanden", "type": "checkbox"}, {"id": 38, "title": "Bemerkung", "type": "string", "displayInside": 37}, {"id": 39, "parentId": 32, "title": "•Strombedarf über Hausanschluss notwendig", "type": "checkbox"}, {"id": 40, "title": "Bemerkung", "type": "string", "displayInside": 39}, {"id": 41, "parentId": 32, "title": "•<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (PRCD) zwischengeschalten", "type": "checkbox"}, {"id": 42, "title": "Bemerkung", "type": "string", "displayInside": 41}, {"id": 43, "parentId": 32, "title": "4.3 Elektrische Freileitungen in der Nähe des Montagebereiches vorhanden", "type": "checkbox"}, {"id": 44, "title": "Bemerkung", "type": "string", "displayInside": 43}, {"id": 45, "parentId": 32, "title": "•Sicherheitsabstand gegen unbeabsichtigte Berührung auch mit Montagematerial ausreichend", "type": "checkbox"}, {"id": 46, "title": "Bemerkung", "type": "string", "displayInside": 45}, {"id": 47, "parentId": 32, "title": "•Freileitung gesichert (spannungsfrei abgeschalten, gegen Berührung abgedeckt oder abgeschrankt)", "type": "checkbox"}, {"id": 48, "title": "Bemerkung", "type": "string", "displayInside": 47}, {"id": 49, "parentId": 32, "title": "4.4 Verkehrswege (Laufflächen) zum Materialtransport geeignet", "type": "checkbox"}, {"id": 50, "title": "Bemerkung", "type": "string", "displayInside": 49}, {"id": 51, "parentId": 32, "title": "•Trittsicherheit gegeben (Untergrund, Breite, Stolperstellen)", "type": "checkbox"}, {"id": 52, "title": "Bemerkung", "type": "string", "displayInside": 51}, {"id": 53, "parentId": 32, "title": "•Bodenöffnungen, steile Böschungen vorhanden", "type": "checkbox"}, {"id": 54, "title": "Bemerkung", "type": "string", "displayInside": 53}, {"id": 55, "parentId": 32, "title": "4.5 Montageunterg<PERSON><PERSON> befestigt, zur Montage ohne zusätzl. <PERSON><PERSON><PERSON><PERSON> geeignet", "type": "checkbox"}, {"id": 56, "title": "Bemerkung", "type": "string", "displayInside": 55}, {"id": 57, "parentId": 32, "title": "4.6 <PERSON>en, Starkregen, Wind, Sturm etc.", "type": "checkbox"}, {"id": 58, "title": "Bemerkung", "type": "string", "displayInside": 57}, {"id": 59, "parentId": 32, "title": "•Besondere Schutzmaßnahmen (z.B. sofort ankern, Fall-stecker, zusätzl. Sicherungsperson etc.) erforderlich", "type": "checkbox"}, {"id": 60, "title": "Bemerkung", "type": "string", "displayInside": 59}, {"id": 61, "parentId": 32, "title": "4.7 Helligkeit ausreichend", "type": "checkbox"}, {"id": 62, "title": "Bemerkung", "type": "string", "displayInside": 61}, {"id": 63, "parentId": 32, "title": "•Zusätzliche Beleuchtung erforderlich", "type": "checkbox"}, {"id": 64, "title": "Bemerkung", "type": "string", "displayInside": 63}, {"id": 65, "title": "5) Ma<PERSON><PERSON> und Geräte (Montage-, Transporthilfe)", "type": "headline"}, {"id": 66, "parentId": 65, "title": "5.1 Einsatz eines Turmdrehkranes bzw. Autokranes", "type": "checkbox"}, {"id": 67, "title": "Bemerkung", "type": "string", "displayInside": 66}, {"id": 68, "parentId": 65, "title": "5.2 Einsatz eines Teleskopstaplers oder Flurförderzeuges (Stapler)", "type": "checkbox"}, {"id": 69, "title": "Bemerkung", "type": "string", "displayInside": 68}, {"id": 70, "parentId": 65, "title": "5.3 Einsatz einer kraftbetriebenen Arbeitsbühne", "type": "checkbox"}, {"id": 71, "title": "Bemerkung", "type": "string", "displayInside": 70}, {"id": 72, "parentId": 65, "title": "5.4 Einsatz sonstiger Baumaschinen oder Transportgeräte", "type": "checkbox"}, {"id": 73, "title": "Bemerkung", "type": "string", "displayInside": 72}, {"id": 74, "title": "6) Erste-<PERSON><PERSON><PERSON> / Brandschutz", "type": "headline"}, {"id": 75, "parentId": 74, "title": "6.1 Verbandsmaterial mit Augenspülflasche vorhanden", "type": "checkbox"}, {"id": 76, "title": "Bemerkung", "type": "string", "displayInside": 75}, {"id": 77, "parentId": 74, "title": "6.2 <PERSON>rsthel<PERSON> vor Ort", "type": "checkbox"}, {"id": 78, "title": "Bemerkung", "type": "string", "displayInside": 77}, {"id": 79, "parentId": 74, "title": "6.3 Feuerlöscher vor Ort", "type": "checkbox"}, {"id": 80, "title": "Bemerkung", "type": "string", "displayInside": 79}, {"id": 81, "parentId": 74, "title": "6.4 Notfallplan (Notruf-Nr., Vorgehensweise etc.) vorhanden", "type": "checkbox"}, {"id": 82, "title": "Bemerkung", "type": "string", "displayInside": 81}, {"id": 83, "title": "7) Montagebedingungen", "type": "headline"}, {"id": 84, "parentId": 83, "title": "7.1 Montage nach Regelausführung bzw. Aufbau- und Verwendungsanleitung des Herstellers", "type": "checkbox"}, {"id": 85, "title": "Bemerkung", "type": "string", "displayInside": 84}, {"id": 86, "parentId": 83, "title": "•Abweichung der Regelausführung notwendig", "type": "checkbox"}, {"id": 87, "title": "Bemerkung", "type": "string", "displayInside": 86}, {"id": 88, "parentId": 83, "title": "•Rechnerischer Nachweis er<PERSON>lich", "type": "checkbox"}, {"id": 89, "title": "Bemerkung", "type": "string", "displayInside": 88}, {"id": 90, "parentId": 83, "title": "7.2 <PERSON><PERSON><PERSON> von Montagesiche<PERSON> (MSG) erforderlich", "type": "checkbox"}, {"id": 91, "title": "Bemerkung", "type": "string", "displayInside": 90}, {"id": 92, "parentId": 83, "title": "7.3 Einsatz von zusätzlicher PSA gegen Absturz erforderlich", "type": "checkbox"}, {"id": 93, "title": "Bemerkung", "type": "string", "displayInside": 92}, {"id": 94, "parentId": 83, "title": "7.4 Einsatz von Warnkleidung erforderlich", "type": "checkbox"}, {"id": 95, "title": "Bemerkung", "type": "string", "displayInside": 94}, {"id": 96, "parentId": 83, "title": "7.5 Einsatz von Wetterschutzkleidung erforderlich", "type": "checkbox"}, {"id": 97, "title": "Bemerkung", "type": "string", "displayInside": 96}, {"id": 98, "parentId": 83, "title": "7.6 Sonstige PSA z.B. Overall, Staubmaske etc. erforderlich", "type": "checkbox"}, {"id": 99, "title": "Bemerkung", "type": "string", "displayInside": 98}, {"id": 100, "title": "8) Handmas<PERSON>en, Werkzeuge, Zubehör", "type": "headline"}, {"id": 101, "parentId": 100, "title": "8.1 Elektr. <PERSON><PERSON><PERSON> augenscheinlich auf Mängel geprüft", "type": "checkbox"}, {"id": 102, "title": "Bemerkung", "type": "string", "displayInside": 101}, {"id": 103, "parentId": 100, "title": "8.2 Elektr. Leitungen augenscheinlich auf Mängel geprüft", "type": "checkbox"}, {"id": 104, "title": "Bemerkung", "type": "string", "displayInside": 103}, {"id": 105, "parentId": 100, "title": "8.3 Werkzeuge augenscheinlich auf Mängel geprüft", "type": "checkbox"}, {"id": 106, "title": "Bemerkung", "type": "string", "displayInside": 105}, {"id": 107, "parentId": 100, "title": "8.4 Kabeltrommel (entspr. isoliert bzw. aus Kunststoff) und Stromleitungen (mind. HO7…… oder mehr) entsprechen der BGI 608", "type": "checkbox"}, {"id": 108, "title": "Bemerkung", "type": "string", "displayInside": 107}, {"id": 109, "title": "9) Personal", "type": "headline"}, {"id": 110, "parentId": 109, "title": "9.1 Qualifiziertes Personal für die Arbeitsaufgabe vorhanden", "type": "checkbox"}, {"id": 111, "title": "Bemerkung", "type": "string", "displayInside": 110}, {"id": 112, "parentId": 109, "title": "9.2 Personalstärke für die Arbeitsaufgabe ausreichend", "type": "checkbox"}, {"id": 113, "title": "Bemerkung", "type": "string", "displayInside": 112}, {"id": 114, "parentId": 109, "title": "9.3 Zeitvorgaben (Vorplanung) für die Arbeitsaufgabe realistisch", "type": "checkbox"}, {"id": 115, "title": "Bemerkung", "type": "string", "displayInside": 114}, {"id": 116, "parentId": 109, "title": "9.4 Mitar<PERSON><PERSON> mit besonderer Fürsorgepflicht vor Ort (Auszubildende, Praktikanten, neue Kollegen im Betrieb etc.)", "type": "checkbox"}, {"id": 117, "title": "Bemerkung", "type": "string", "displayInside": 116}, {"id": 118, "title": "10) Sonstiges", "type": "headline"}, {"id": 119, "title": "Bemerkung", "type": "string", "displayInside": 118}, {"id": 120, "title": "11) <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 121, "parentId": 120, "title": "Ort", "type": "string"}, {"id": 122, "parentId": 120, "title": "Datum", "type": "date"}, {"id": 123, "parentId": 120, "title": "Uhrzeit", "type": "time"}, {"id": 124, "parentId": 120, "title": "Unterschrift", "type": "signatureField"}]}