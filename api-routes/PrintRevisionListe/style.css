html {
    font-family: Arial, sans-serif;
}

.headerTable {
    border-spacing: 0;
    padding: 0;
}

.headerTable, th, td {
    width: 100%;
    border: 1px solid black;
}

.site-address {
    width: 45%;
    padding-left: 6px;
    text-align: center;
}

.header-intro-text {
    text-align: center;
    color: #FF6600;
    font-size: 30px;
    margin: 12px 0;
}

.checkboxes-table {
    border: 1px solid black;
    width: 100%;
    border-spacing: 0;
    padding: 0;
}

.bordered {
    font-weight: bold;
    background-color: lightgray
}

th.firstColumn {
    width: 50%;
}

th.secondColumn {
    width: 8%;
}

th.thirdColumn {
    width: 8%;
}

th.fourthColumn {
    width: 34%;
}

.checkboxes-table, .headerTable {
    table-layout: fixed;
}

th.secondHeader {
    width: 35%;
}

th.thirdHeader {
    width: 30%;
}

.companyLogo {
    padding: 10px;
    width: 70%;
    height: auto;
}

.footer {
    border-collapse: collapse;
}

.footer td {
    padding: 2px 5px;
}

.dot {
    font-size: 30px;
}

.checkboxes-table th:nth-child(2),
.checkboxes-table th:nth-child(3) {
    text-align: center;
    width: 80px !important;
}

.checkboxes-table td:nth-child(2),
.checkboxes-table td:nth-child(3) {
    text-align: center;
    color: #FF6600;
    width: 80px !important;
}

.checkboxes-table th:first-child {
    text-align: left;
}

.checkboxes-table td, .checkboxes-table th {
    padding: 2px 5px;
}

.checkbox-td {
    width: 45px;
    font-size: 20px
}

.footer td, th {
    padding: 5px 10px;
}

.signature-img {
    max-height: 150px;
    max-width: 150px;
    height: auto;
    width: auto;
}

.page-break-before {
    page-break-before: always !important;
}