{"name": "PROCIJENA RIKIKA OPASNOSTI NA GARDILISTU", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2019-12-17T16:40:00Z", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["8"], "positions": [{"id": 1, "title": "1) Dokumentacija", "type": "headline"}, {"id": 2, "parentId": 1, "title": "1.1 <PERSON><PERSON><PERSON><PERSON> pismeni radni nalog", "type": "checkbox"}, {"id": 3, "title": "Opis", "type": "string", "displayInside": 2}, {"id": 4, "parentId": 1, "title": "1.2 Dostupan opis putovanja- Navigacija", "type": "checkbox"}, {"id": 5, "title": "Opis", "type": "string", "displayInside": 4}, {"id": 6, "parentId": 1, "title": "1.3 Dostupne upute za montažu (montaža ili demontaža)", "type": "checkbox"}, {"id": 7, "title": "Opis", "type": "string", "displayInside": 6}, {"id": 8, "parentId": 1, "title": "1.4 Dostupni tehnički crteži", "type": "checkbox"}, {"id": 9, "title": "Opis", "type": "string", "displayInside": 8}, {"id": 10, "title": "2) Zakon bezbjednost saobraćaja (putevi, prilaznice, trotoari)", "type": "headline"}, {"id": 11, "parentId": 10, "title": "2.1 Potreban je vodič za parkiranje", "type": "checkbox"}, {"id": 12, "title": "Opis", "type": "string", "displayInside": 11}, {"id": 13, "parentId": 10, "title": "2.2 Potreban je vodič za sigurnost saobraćaja", "type": "checkbox"}, {"id": 14, "title": "Opis", "type": "string", "displayInside": 13}, {"id": 15, "parentId": 10, "title": "2.3 Sp<PERSON>eno osiguranje vozila ( signalni farovi, itd.).", "type": "checkbox"}, {"id": 16, "title": "Opis", "type": "string", "displayInside": 15}, {"id": 17, "parentId": 10, "title": "2.4 Prostor za utovar i transport neposredno uz ulice, prolaze, biciklističke staze ili trotoare", "type": "checkbox"}, {"id": 18, "title": "Opis", "type": "string", "displayInside": 17}, {"id": 19, "parentId": 10, "title": "•<PERSON><PERSON><PERSON><PERSON>, bari<PERSON>e ili oz<PERSON> (npr. traka <PERSON>, ograde za barijere, itd.) su potrebne", "type": "checkbox"}, {"id": 20, "title": "Opis", "type": "string", "displayInside": 19}, {"id": 21, "title": "3) Objekt / lokacije", "type": "headline"}, {"id": 22, "parentId": 21, "title": "3.1 WC ili mobilni WC dostupan", "type": "checkbox"}, {"id": 23, "title": "Opis", "type": "string", "displayInside": 22}, {"id": 24, "parentId": 21, "title": "3.2 Mogućnost pranja ruku", "type": "checkbox"}, {"id": 25, "title": "Opis", "type": "string", "displayInside": 24}, {"id": 26, "parentId": 21, "title": "3.3 Prostorija za odmor", "type": "checkbox"}, {"id": 27, "title": "Opis", "type": "string", "displayInside": 26}, {"id": 28, "parentId": 21, "title": "3.4 Ima dovoljno parking mjesta za naša vozila", "type": "checkbox"}, {"id": 29, "title": "Opis", "type": "string", "displayInside": 28}, {"id": 30, "parentId": 21, "title": "3.5. Na raspolaganju je dovoljno prostora za skladištenje materijala", "type": "checkbox"}, {"id": 31, "title": "Opis", "type": "string", "displayInside": 30}, {"id": 32, "title": "4) <PERSON><PERSON><PERSON> g<PERSON>", "type": "headline"}, {"id": 33, "parentId": 32, "title": "4.1 Montažna površina direktno na ulici, prolazima, biciklisticke staze ili trotoare", "type": "checkbox"}, {"id": 34, "title": "Opis", "type": "string", "displayInside": 33}, {"id": 35, "parentId": 32, "title": "•<PERSON><PERSON><PERSON><PERSON>, bari<PERSON>e ili oz<PERSON> (npr. traka <PERSON>, ograde za barijere, itd.) su potrebne", "type": "checkbox"}, {"id": 36, "title": "Opis", "type": "string", "displayInside": 35}, {"id": 37, "parentId": 32, "title": "4.2 Dostupan distributer struje za povezivanje uređaja (npr. Agregat )", "type": "checkbox"}, {"id": 38, "title": "Opis", "type": "string", "displayInside": 37}, {"id": 39, "parentId": 32, "title": "•Potrebna struja preko kućnog priključka", "type": "checkbox"}, {"id": 40, "title": "Opis", "type": "string", "displayInside": 39}, {"id": 41, "parentId": 32, "title": "•Ukljucen i distributer zaštite (PRCD).", "type": "checkbox"}, {"id": 42, "title": "Opis", "type": "string", "displayInside": 41}, {"id": 43, "parentId": 32, "title": "4.3 električni nadzemni vodovi u blizini montažnog prostora", "type": "checkbox"}, {"id": 44, "title": "Opis", "type": "string", "displayInside": 43}, {"id": 45, "parentId": 32, "title": "•Dovoljno sigurnosno rastojanje od nenamjernog kontakta, čak i sa montažnim materijalom", "type": "checkbox"}, {"id": 46, "title": "Opis", "type": "string", "displayInside": 45}, {"id": 47, "parentId": 32, "title": "•Nadzemni vod osiguran (isključen, pokriven ili ograđen kako bi se spriječio kontakt)", "type": "checkbox"}, {"id": 48, "title": "Opis", "type": "string", "displayInside": 47}, {"id": 49, "parentId": 32, "title": "4.4 Saobraćajni putevi (šetališta) pogodni za transport materijala", "type": "checkbox"}, {"id": 50, "title": "Opis", "type": "string", "displayInside": 49}, {"id": 51, "parentId": 32, "title": "•Sigurnost vožnje preko provoditelja struje  (pod zemljom, širina, opasnosti od spoticanja)", "type": "checkbox"}, {"id": 52, "title": "Opis", "type": "string", "displayInside": 51}, {"id": 53, "parentId": 32, "title": "•<PERSON><PERSON>i ot<PERSON>i, prisutni strmi nasipi", "type": "checkbox"}, {"id": 54, "title": "Opis", "type": "string", "displayInside": 53}, {"id": 55, "parentId": 32, "title": "4.5 Kablovi prelaze preko montaznih dijelova i nisu pricvrsceni", "type": "checkbox"}, {"id": 56, "title": "Opis", "type": "string", "displayInside": 55}, {"id": 57, "parentId": 32, "title": "4.6 <PERSON><PERSON>, j<PERSON>, <PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON><PERSON> itd.", "type": "checkbox"}, {"id": 58, "title": "Opis", "type": "string", "displayInside": 57}, {"id": 59, "parentId": 32, "title": "•Potrebne su posebne zaštitne mjere (npr. odmah ucvrscivanje, ankeri).", "type": "checkbox"}, {"id": 60, "title": "Opis", "type": "string", "displayInside": 59}, {"id": 61, "parentId": 32, "title": "4.7 Dovoljna osvijetljenost", "type": "checkbox"}, {"id": 62, "title": "Opis", "type": "string", "displayInside": 61}, {"id": 63, "parentId": 32, "title": "•Potrebna dodatna rasvjeta", "type": "checkbox"}, {"id": 64, "title": "Opis", "type": "string", "displayInside": 63}, {"id": 65, "title": "5) Mašine i uređaji (montaža, transportna pomoć)", "type": "headline"}, {"id": 66, "parentId": 65, "title": "5.1 Upotreba krana ili druge pokretne dizalice", "type": "checkbox"}, {"id": 67, "title": "Opis", "type": "string", "displayInside": 66}, {"id": 68, "parentId": 65, "title": "5.2 Upotreba teleskopskog viljuškara ili industrijskog kamiona (viljuškara)", "type": "checkbox"}, {"id": 69, "title": "Opis", "type": "string", "displayInside": 68}, {"id": 70, "parentId": 65, "title": "5.3 Korištenje pogonske radne platforme", "type": "checkbox"}, {"id": 71, "title": "Opis", "type": "string", "displayInside": 70}, {"id": 72, "parentId": 65, "title": "5.4 Upotreba drugih građevinskih mašina ili transportne opreme", "type": "checkbox"}, {"id": 73, "title": "Opis", "type": "string", "displayInside": 72}, {"id": 74, "title": "6) Prva pomoc / Zastita od pozara", "type": "headline"}, {"id": 75, "parentId": 74, "title": "6.1 Dostupan materijal za previjanje sa bocom za ispiranje očiju", "type": "checkbox"}, {"id": 76, "title": "Opis", "type": "string", "displayInside": 75}, {"id": 77, "parentId": 74, "title": "6.2 Prva pomoć na licu mjesta", "type": "checkbox"}, {"id": 78, "title": "Opis", "type": "string", "displayInside": 77}, {"id": 79, "parentId": 74, "title": "6.3 Aparat za gašenje požara na licu mjesta", "type": "checkbox"}, {"id": 80, "title": "Opis", "type": "string", "displayInside": 79}, {"id": 81, "parentId": 74, "title": "6.4 Dostupan plan za hitne sluč<PERSON>eve (broj hitne pomo<PERSON>, procedura, itd.).", "type": "checkbox"}, {"id": 82, "title": "Opis", "type": "string", "displayInside": 81}, {"id": 83, "title": "7) Uslovi gradnje", "type": "headline"}, {"id": 84, "parentId": 83, "title": "7.1 Montaža prema standardnoj verziji ili uputama proizvođača za montažu i upotrebu", "type": "checkbox"}, {"id": 85, "title": "Opis", "type": "string", "displayInside": 84}, {"id": 86, "parentId": 83, "title": "•Neophodno je odstupanje od izvršenja pravila", "type": "checkbox"}, {"id": 87, "title": "Opis", "type": "string", "displayInside": 86}, {"id": 88, "parentId": 83, "title": "•Potreban je racunski dokaz", "type": "checkbox"}, {"id": 89, "title": "Opis", "type": "string", "displayInside": 88}, {"id": 90, "parentId": 83, "title": "7.2 Potrebna je upotreba montažnih sigurno<PERSON>nih og<PERSON> (MSG).", "type": "checkbox"}, {"id": 91, "title": "Opis", "type": "string", "displayInside": 90}, {"id": 92, "parentId": 83, "title": "7.3 Potrebna je upotreba dodatne Licne Zastitne Opreme protiv padova", "type": "checkbox"}, {"id": 93, "title": "Opis", "type": "string", "displayInside": 92}, {"id": 94, "parentId": 83, "title": "7.4 Potrebna je upotreba odeće visoke vidljivosti", "type": "checkbox"}, {"id": 95, "title": "Opis", "type": "string", "displayInside": 94}, {"id": 96, "parentId": 83, "title": "7.5 Obavezna upotreba odeće za zaštitu od vremenskih prilika", "type": "checkbox"}, {"id": 97, "title": "Opis", "type": "string", "displayInside": 96}, {"id": 98, "parentId": 83, "title": "7.6 <PERSON><PERSON><PERSON><PERSON> je druga OZO, npr. <PERSON><PERSON><PERSON><PERSON><PERSON>, maska za prašinu itd", "type": "checkbox"}, {"id": 99, "title": "Opis", "type": "string", "displayInside": 98}, {"id": 100, "title": "8) <PERSON><PERSON><PERSON><PERSON>, al<PERSON>, pribor", "type": "headline"}, {"id": 101, "parentId": 100, "title": "8.1 Električne mašine su očito provjerene od kvarova", "type": "checkbox"}, {"id": 102, "title": "Opis", "type": "string", "displayInside": 101}, {"id": 103, "parentId": 100, "title": "8.2 Očigledno je da su električni vodovi provjereni na kvarove", "type": "checkbox"}, {"id": 104, "title": "Opis", "type": "string", "displayInside": 103}, {"id": 105, "parentId": 100, "title": "8.3 Alati su očito provjereni od ostecenja", "type": "checkbox"}, {"id": 106, "title": "Opis", "type": "string", "displayInside": 105}, {"id": 107, "parentId": 100, "title": "8.4 <PERSON>b<PERSON><PERSON> <PERSON>a <PERSON><PERSON> (odgovarajuće izolovan ili napravljen od plastike) i električni vodovi (najmanje HO7...... ili više) su u skladu sa BGI 60", "type": "checkbox"}, {"id": 108, "title": "Opis", "type": "string", "displayInside": 107}, {"id": 109, "title": "9) Personal", "type": "headline"}, {"id": 110, "parentId": 109, "title": "9.1 Kvalificirano osoblje dostupno za radni zadatak", "type": "checkbox"}, {"id": 111, "title": "Opis", "type": "string", "displayInside": 110}, {"id": 112, "parentId": 109, "title": "9.2 Dovoljno osoblja za obavljanje planiranih obaveza", "type": "checkbox"}, {"id": 113, "title": "Opis", "type": "string", "displayInside": 112}, {"id": 114, "parentId": 109, "title": "9.3 Vremenske specifikacije (predplaniranje) za radni zadatak su realne", "type": "checkbox"}, {"id": 115, "title": "Opis", "type": "string", "displayInside": 114}, {"id": 116, "parentId": 109, "title": "9.4 Radnici sa posebnom strucnoscu na licu mesta (šegrti, pripravnici, nove kolege u kompaniji itd.)", "type": "checkbox"}, {"id": 117, "title": "Opis", "type": "string", "displayInside": 116}, {"id": 118, "title": "10) Razno", "type": "headline"}, {"id": 119, "title": "Opis", "type": "string", "displayInside": 118}, {"id": 120, "title": "11) Odobravanje", "type": "headline"}, {"id": 121, "parentId": 120, "title": "Grad", "type": "string"}, {"id": 122, "parentId": 120, "title": "Datum", "type": "date"}, {"id": 123, "parentId": 120, "title": "<PERSON><PERSON>je<PERSON>", "type": "time"}, {"id": 124, "parentId": 120, "title": "Potpis", "type": "signatureField"}]}