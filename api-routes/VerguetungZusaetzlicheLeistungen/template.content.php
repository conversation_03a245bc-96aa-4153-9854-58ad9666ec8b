<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_VerguetungZusaetzlicheLeistungen())->getData($_GET['schemaId'], $_GET['documentId']);
}

$dateFormat = 'd.m.Y';
?>

<html lang="de">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="<?= PrintoutHelper::translateLocalPathToServerPath(
        __DIR__ . "/style.css") ?>">
    <title>Vergütung</title>
</head>
<body>

<table class="main-table full-width">
    <tr>
        <td>
            <table id="sender" style="margin-bottom: 15px">
                <tr>
                    <td>Absender</td>
                </tr>
                <tr>
                    <td><?= $data['companyTitle']; ?>, <?= $data['companyName']; ?></td>
                </tr>
                <tr>
                    <td><?= $data['companyAddress']; ?></td>
                </tr>
                <tr>
                    <td><?= $data['companyPostCode'] . ' ' . $data['companyCity']; ?></td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td class="full-width">
            <table class="client">
                <tr>
                    <td>
                        <table id="client">
                            <tr>
                                <td>Auftraggeber</td>
                            </tr>
                            <tr>
                                <td><?= $data['principalDisplayName']; ?></td>
                            </tr>
                            <tr>
                                <td><?= $data['principalRealAddress']; ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <?= $data['principalAddressCountryCode'] . ' ' .
                                    $data['principalPostcode'] . ' ' . $data['principalAddressCity'] ?>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td style="padding-left: 200px">
                        <table id="construction-project">
                            <tr>
                                <td style="border-collapse: collapse; width: 100%;"
                                    class="full-width; padding-right: 50px;">
                                    Bauvorhaben
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td style="border-collapse: collapse; border-bottom: 2px solid black; width: 100%;">
                                    <p style="width: 100%; padding-right: 50px;"><?= $data['projectName']; ?></p>
                                </td>
                                <td style="border-bottom: 2px solid black;" colspan="2"></td>
                            </tr>
                            <tr>
                                <td style="width: 100%;"><p><?= $data['projectSiteAddress']; ?></p></td>
                                <td style="width: 100%;"><p><?= $data['projectSiteCity']; ?></p></td>
                                <td style="width: 100%;"><p><?= $data['projectSiteZipCode']; ?></p></td>
                            </tr>
                            <tr>
                                <td class="align-bottom">Auftrag vom
                                    <b><?= PrintoutHelper::dateConverter($data['projectValidStartDate'], 'd.m.Y'); ?></b>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<section id="body-table-section">
    <table class="message-table">
        <tr>
            <td colspan="3">
                <h2 style="margin-top: 30px;">Vergütung für zusätzliche Leistungen gem. § 2 Abs. 6 VOB/B</h2>
                <p>Sehr geehrte Damen und Herren,</p>
                <p>in vorbezeichneter Angelegenheit erlauben wir uns, Sie darauf aufmerksam zu
                    machen, dass am <?= PrintoutHelper::dateConverter($data['documentDate'], $dateFormat); ?> von
                </p>
                <table id="checkboxes-table">
                    <tr>
                        <td align="top" class="checkbox">
                            <?php PrintoutHelper::echoCheckboxIfContained($data, 'Ihnen als Auftraggeber') ?>
                        </td>
                        <td>Ihnen als Auftraggeber</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td class="checkbox">
                            <?php PrintoutHelper::echoCheckboxIfContained($data, 'Ihre(r/m) Bevollmächtigten, Frau/Herrn') ?>
                        </td>
                        <td style="vertical-align: bottom; margin: 0; padding: 0;">
                            Ihre(r/m) Bevollmächtigten, Frau/Herrn <?= $data['Bevollmächtigt:'] ?? ""; ?>
                        </td>
                    </tr>
            </td>
            <td>
            </td>
        </tr>
        <tr>
            <td colspan="3" class="full-width">folgende Leistungen, die nicht vertraglich vorgesehen waren,
                gefordert worden sind:
            </td>
        </tr>
        <tr>
            <td colspan="3" style="padding-top: 15px">
                <p> <?= $data['folgende Leistungen, die nicht vertraglich vorgesehen waren, gefordert worden sind:'] ?? ""; ?> </p>
            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td colspan="3">
                <p class="quote-article">
                    Gem. § 2 Abs. 6 VOB/B steht in diesem Fall dem Auftragnehmer ein Anspruch auf eine besondere
                    Vergütung zu
                    sich nach den Grundlagen der Preisermittlung für die vertragliche Leistung und den
                    besonderen Kosten der geforderten
                    Leistung bestimmt.
                </p>
            </td>
        </tr>
        <tr>
            <td class="checkbox">
                <?php PrintoutHelper::echoCheckboxIfContained($data, 'Entsprechend dieser Bestimmung erlauben wir uns, hiermit einen zusätzlichen Vergütungsanspruch anzukündigen.
 Unser Preisangebot für diese zusätzlichen Leistungen werden wir in Kürze überreichen.'); ?>
            </td>
            <td> Entsprechend dieser Bestimmung erlauben wir uns, hermit einen zusätzlichen Vergütungsanspruch
                anzukündigen. Unser Preisangebot für diese zusätzlichen Leistungen werden wir in Kürze
                überreichen.
            </td>
            <td>
            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td class="checkbox">
                <?php PrintoutHelper::echoCheckboxIfContained($data, 'Für die zusätzlichen Leistungen bieten wir folgende Preise an:'); ?>
            </td>
            <td>Für die zusätzlichen Leistungen bieten wir folgende Preise an:</td>
            <td>
            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <?= $data['Zusätzliche Leistungen & Preise beschreiben:'] ?? ""; ?>
            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">Wir bitten Sie, unser Nachtragsangebot anzunehmen</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">bzw. die ermittelten neuen Preise durch Unterschrift und Rückgabe eines Exemplares
                <b>bis zum</b>
                <?php
                $date = $data['Wir bitten Sie, unser Nachtragsangebot anzunehmen
 bzw. die ermittelten neuen Preise durch Unterschrift und Rückgabe eines Exemplares bis zum ... zu bestätigen.'] ?? null;
                if ($date) {
                    echo PrintoutHelper::dateConverter($date, $dateFormat);
                }
                ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="3"> zu bestätigen.</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <section class="hidden"></section>
            </td>
        </tr>
        <tr>
            <td colspan="3" class="hidden">-</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                Die vorgenannten Nachtragspreise haben wir soweit im Hauptvertrag vorhanden - auf den Grundlagen
                der
                Preisermittlung für die vertraglichen Leistungen und den besonderen Kosten der zusätzlichen
                Leistungen
                kalkuliert.
            </td>
        </tr>
        <tr>
            <td class="hidden"><br></td>
        </tr>
        <tr>
            <td class="checkbox">
                <?php PrintoutHelper::echoCheckboxIfContained($data, 'Wir bitten Sie, unser Nachtragsangebot anzunehmen'); ?>
            </td>
            <td colspan="2">
                Wir bitten Sie, unser Nachtragsangebot anzunehmen<br>
                bzw. die ermittelten neuen Preise durch Unterschrift und Rückgabe eines Exemplares
                <b>bis zum</b>
                <?php
                $date = $data['bzw. die ermittelten neuen Preise durch Unterschrift und Rückgabe eines Exemplares bis zum ... zu bestätigen.'] ?? null;
                if ($date) {
                    echo PrintoutHelper::dateConverter($date, $dateFormat);
                }
                ?>
                zu bestätigen.
            </td>
        </tr>
        <tr>
            <td colspan="3" class="hidden">-</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2"><p> Die Nachtragspreise haben wir - soweit im Hauptvertrag vorhanden - auf den
                    Grundlagen der Preisermittlung für
                    die vertraglichen Leistungen und den besonderen Kosten der geforderten zusätzlichen Leistung
                    kalkuliert.</p>
            </td>
        </tr>
    </table>
    <section style="height: 25px;" class="hidden">
        -
    </section>
    <table>
        <tr>
            <td>
                <section style="width: 400px !important;">
                    <?php
                    if ($data['Datum/Auftragnehmer'] ?? null) {
                        echo ($data['Ort/Auftragnehmer'] ?? "") . ", " . PrintoutHelper::dateConverter($data['Datum/Auftragnehmer'], $dateFormat);
                    } else {
                        echo $data['Ort/Auftragnehmer'] ?? "";
                    }
                    ?>
                </section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section style="width: 400px !important;">
                    <?php
                    if ($data['Datum/Auftraggeber'] ?? null) {
                        echo ($data['Ort/Auftraggeber'] ?? "") . ", " . PrintoutHelper::dateConverter($data['Datum/Auftraggeber'], $dateFormat);
                    } else {
                        echo $data['Ort/Auftraggeber'] ?? "";
                    }
                    ?>
                </section>
            </td>
        </tr>
        <tr>
            <td class="border-top">Ort/Datum</td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td class="border-top">Ort/Datum</td>
        </tr>
        <tr>
            <td style="width: 400px;">
                <?php if (isset($data['Auftragnehmer'])) { ?>
                    <img src="<?= $data['Auftragnehmer'] ?>" alt="" class="signature">
                <?php } ?>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px">-</section>
            </td>
            <td style="width: 400px;">
                <?php if (isset($data['Auftraggeber'])) { ?>
                    <img src="<?= $data['Auftraggeber'] ?>" alt="" class="signature">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td class="border-top" style="width: 400px;">Auftragnehmer</td>
            <td>
                <section class="hidden" style="width: 50px;">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px;">-</section>
            </td>
            <td>
                <section class="hidden" style="width: 50px;">-</section>
            </td>
            <td class="border-top" style="width: 400px;">Auftraggeber</td>
        </tr>
    </table>
</section>

<div class="bordered-all-sides text-align-center" id="footer-table" style="margin-top: 15px">
    <i>Baustelle/Büro - BI-Formular 2: Vergütung für zusätzliche Leistungen gem. § 2 Abs. 6 VOB/B</i>
</div>

</body>
</html>
