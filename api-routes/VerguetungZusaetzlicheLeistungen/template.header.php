<?php
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_VerguetungZusaetzlicheLeistungen())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css"
          href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Vergütung</title>
</head>
<body style="margin: 0">

<div style="display: -webkit-box; -webkit-box-pack: center; height: 65px">
    <img alt="" style="display: inline-block; max-width: 300px; max-height: 60px; object-fit: contain"
         src="<?= $data['logo'] ?>"/>
    <div style="margin-left: 15px; display: -webkit-box; -webkit-box-pack: center; -webkit-box-orient: block-axis">
        <p style="margin: 0; font-weight: lighter; display: inline-block; font-size: 0.9em; color: rgb(102, 102, 102);">
            <?= $data['addressWithPhone'] ?>
        </p>
    </div>
</div>

</body>
</html>
