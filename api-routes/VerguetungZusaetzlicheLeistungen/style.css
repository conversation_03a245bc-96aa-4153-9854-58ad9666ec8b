* {
    font-family: <PERSON><PERSON>, sans-serif;
}

.hidden-border {
    border: none !important;
}

.checkbox {
    vertical-align: top;
    font-weight: bold;
    font-size: 24px;
    line-height: 1;
}

table {
    border-collapse: collapse;
}

#checkboxes-table td:nth-child(1) {
    width: 50px !important;
}

#logo {
    width: 150px;
}

#logo-table td:nth-child(3) {
    width: 450px;
}

#logo-table td:nth-child(4) {
    width: 250px;
}

#logo-table td:nth-child(1) {
    width: 250px;
}

#sender td, #sender tbody, #client tbody, #client td {
    width: 850px;
}


#client tr:last-child {
    border: none !important;
}


#client td {
    border-bottom: 1px solid black !important;
}

#client td {
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
}

#bottom-table {
    transform: scaleX(1.1); /* Stretch horizontally */
    font-size: 11px;
}

#logo-table td {
    width: 200px;
}

.full-width {
    width: 100% !important;
}

.bold-border-top {
    border-top: 1px solid black;
}

.quote-article {
    padding-bottom: 15px;
}

#disclaimer {
    font-size: 0.8em;
    margin-bottom: 150px;
}

.bordered-all-sides {
    border: 1px solid black;
}

.text-align-center {
    text-align: center;
}

#footer-table {
    font-size: 0.8em;
}

#sender, #client, #sender td, #client td {
    border: 1px solid black;
    width: 250px;
    height: 100%;
    padding: 5px;
}

#sender, #client {
    border: 3px solid black !important;
    width: 400px;
    height: 100%;
}

#sender td, #client td {
    border-bottom: 1px solid black !important;
}

#client td:first-child, #sender td:first-child {
    border: 0;
    border-bottom: 1px solid black;
}


#client td:nth-child(2n), #sender td:nth-child(2n) {
    border-top: none;
    border-left: none;
    border-right: none;
    width: 50px;
}

#client tr:last-child td, #sender tr:last-child td {
    border: none !important;
}

.hidden {
    visibility: hidden;
}

.border-bottom {
    border-bottom: 1px solid black !important;
}

.align-bottom {
    vertical-align: bottom !important;
}

.align-top {
    vertical-align: top !important;
}

#construction-project td {
    font-weight: bold !important;
}

#construction-project td:nth-child(2n) {
    border: none;
    width: 100%;
}

.message-table {
    width: 100%;
}

.signature {
    max-height: 90px;
    max-width: 100%;
}

#body-table-section {
    font-size: 0.8em;
}

.border-top {
    border-top: 1px solid black;
}

.border-bottom {
    border-bottom: 1px solid black;
}
