html {
    font-family: Arial, sans-serif;
}

.header-table, .main-table, .footer-table {
    width: 100%;
}

.table-row {
    height: 40px;
}

.header-table tr:first-child td:last-child,
.header-table tr:nth-child(2) td:last-child {
    border: 1px solid black;
    width: 20%;
}

.header-table td, .main-table td {
    padding: 3px 1px;
}

.header-table tr td:nth-child(2),
.header-table tr td:nth-child(3) {
    width: 27%
}

.header-title {
    width: 100%;
    padding-top: 20px;
}

.header-text {
    padding-right: 60%;
}

.text-align-center {
    text-align: center !important;
    padding-top: 50px
}

.font-weight-bold {
    font-weight: bold;
}

.text-align-right {
    text-align: right !important;
}

.bold {
    font-weight: bold;
}

.text-align-center {
    text-align: center;
}

.rental-table {
    width: 100%;
    border-collapse: collapse;
}

.rental-table th, .rental-table td {
    border: 1px solid black;
    padding: 8px;
    text-align: center;
}

.rental-table th {
    background-color: #f2f2f2;
}

.table-header {
    font-weight: bold;
}

.table-italic {
    font-style: italic;
}

.table-red-text {
    color: red;
    font-weight: bold;
}

.vertical-text {
    writing-mode: vertical-rl;
    transform: rotate(180deg);
}

.area-box {
    border: 1px solid black;
    padding: 5px;
    display: inline-block;
    width: 150px;
    text-align: center;
    margin-left: 5px;
}

.remarkstable {
    margin-top: 15px;
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
}

.remarkstable td, .remarkstable th {
    border: 1px solid black;
    padding: 10px;
    vertical-align: top;
}

.remarkstable .header {
    font-weight: bold;
    font-size: 14px;
    border: thick;
}

.remarkstable .calculation {
    font-style: italic;
    font-weight: bold;
    font-size: 18px;
    border: none;
    padding: 50px 25px;
}

.remarkstable .date {
    font-weight: bold;
}

.remarkstable .signature {
    font-style: italic;
    font-weight: bold;
}

.date-field {
    padding-left: 10px;
}

.footer {
    padding-top: 20px;
}

.footer-page {
    width: 100%;
}

.leftAlign {
    text-align: left;
}

.rightAlign {
    text-align: right;
}

.underlined {
    border-bottom: 2px solid black;
}

.underline {
    border-bottom: 1px solid black;
    margin: 5px 0;
}