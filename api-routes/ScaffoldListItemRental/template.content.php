<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ScaffoldListItemRental())->getData($_GET['ktr'], $_GET['intnr']);
}
$head = $data['scaffoldListItem'];
$startDate = !empty($head['vhbeg']) ? date("d.m.Y", strtotime($head['vhbeg'])) . 'г.' : '';
$endDate = !empty($head['vhend'])
    ? date("d.m.Y", strtotime($head['vhend'])) . 'г.'
    : (!empty($head['vhvorlend']) ? date("d.m.Y", strtotime($head['vhvorlend'])) . 'г.' : '');
$totalDays = $head['vhverl'] ?? '';
$tableStart = !empty($head['vhbeg']) ? date("d.m", strtotime($head['vhbeg'])) : '';
$tableEnd = !empty($head['vhend'])
    ? date("d.m", strtotime($head['vhend']))
    : (!empty($head['vhvorlend']) ? date("d.m", strtotime($head['vhvorlend'])) : '');

$row = "<tr class='table-row'>
            <td></td><td colspan='2'></td><td></td><td></td><td></td>
        </tr>";

?>

<!doctype html>
<html lang="bg">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Activity SHEET</title>
</head>
<body>
<h3 class="text-align-center">ACTIVITY SHEET / ПРИСЪСТВЕНА ФОРМА</h3>
<?php require_once __DIR__ . "/../../api-routes/ScaffoldListItem/header_table.php"; ?>
<div class="header-title">
    <b class="header-text">компресор на пневмотранспорт</b>
    <b>14573774</b>
</div>

<table class="rental-table">
    <tr>
        <td colspan="3">Date/Дата</td>
        <td colspan="3" class="table-header table-italic"><?= $startDate ?></td>
    </tr>
    <tr>
        <td class="table-header"></td>
        <td colspan="2" class="table-header">Name/Име</td>
        <td class="vertical-text table-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="100" viewBox="0 0 80 150">
                <g transform="rotate(-90, 40, 75)">
                    <text x="50" y="70" text-anchor="middle" dominant-baseline="middle" style="font-size: 30px;">
                        <tspan x="40" dy="0"> Start/Начало</tspan>
                    </text>
                </g>
            </svg>
        </td>
        <td class="vertical-text table-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="100" viewBox="0 0 80 150">
                <g transform="rotate(-90, 40, 75)">
                    <text x="50" y="70" text-anchor="middle" dominant-baseline="middle" style="font-size: 30px;">
                        <tspan x="40" dy="0">End/Край</tspan>
                    </text>
                </g>
            </svg>
        </td>
        <td class="vertical-text table-header">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="100" viewBox="0 0 80 150">
                <g transform="rotate(-90, 40, 75)">
                    <text x="50" y="70" text-anchor="middle" dominant-baseline="middle" style="font-size: 30px;">
                        <tspan x="40" dy="0">Total days /</tspan>
                        <tspan x="40" dy="35">Общо дни</tspan>
                    </text>
                </g>
            </svg>
        </td>
    </tr>
    <?php
    $counter = 1;
    foreach ($data['scaffoldListMeasurement'] as $measurement) {
        echo "<tr>";
        echo "<td>" . $counter . "</td>";
        echo "<td colspan='2' class='font-weight-bold'>" . $measurement['shortDescription'] . "</td>";
        echo "<td>" . $tableStart . "</td>";
        echo "<td>" . $tableEnd . "</td>";
        echo "<td>" . $totalDays . "</td>";
        echo "</tr>";
        $counter++;
    }
    echo str_repeat($row, 7);
    ?>
</table>

<table class="remarkstable">
    <tr>
        <td colspan="3" class="header">
            REMARKS / USED MATERIALS<br>
            ЗАБЕЛЕЖКИ / ИЗПОЛЗВАНИ МАТЕРИАЛИ
        </td>
    </tr>
    <tr>
        <td colspan="3" style="border: none">
            <div class="calculation">
                <?php
                foreach ($data['scaffoldListMeasurement'] as $measurement) {
                    $quantityRent = (float)($measurement['quantityRent'] ?? 0);
                    $unitPriceRent = (float)($measurement['unitPriceRent'] ?? 0);
                    $currency = !empty($measurement['currency']) ? $measurement['currency'] : "лв.";
                    $dailyRent = $quantityRent * $unitPriceRent;
                    $totalRent = (float)$totalDays * $dailyRent;
                    $dailyRentFormatted = number_format($dailyRent, 2);
                    $totalRentFormatted = number_format($totalRent, 2);

                    echo "{$measurement['quantityRent']} {$measurement['measuringUnitKey']} x $unitPriceRent $currency = $dailyRentFormatted $currency (за ден)<br><br>";
                    echo "$totalDays дни x $dailyRentFormatted $currency = $totalRentFormatted $currency<br><br>";
                }
                ?>
            </div>
        </td>
    </tr>
    <tr>
        <td class="date">DATE / ДАТА<br><br>
            <span class="date-field">
                <?= !empty($head['datum']) ? date("d.m.Y", strtotime($head['datum'])) . 'г.' : '' ?>
            </span>
        </td>
        <td>
            Name + signature Company<br>
            Име + подпис за фирмата<br><br>
            <span class="signature"><?= $data['employee']['displayName'] ?? '' ?></span>
        </td>
        <td>
            Name + signature Aurubis<br>
            Име + подпис за Аурубис<br><br>
            <span class="signature"><?= $data['partnerName'] ?? '' ?></span>
        </td>
    </tr>
</table>

<div class="footer">
    <span>Remark: Obligation of the company is to fill in and present this form</span><br>
    <span>Забележка: Задължение на фирмата е да попълва и представя тази форма</span>
</div>

</body>
</html>