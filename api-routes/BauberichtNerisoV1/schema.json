{"name": "Bautagebuch", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2019-07-04T09:14:38Z", "editedBy": null, "editedOn": "2023-05-31T08:56:23Z", "applicableEntities": [], "printOptions": ["42"], "positions": [{"id": -1, "title": "Wetter", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "Temperatur", "type": "int", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -1, "title": "Wetter", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["trocken", "bedeckt", "<PERSON><PERSON><PERSON>", "Regen", "<PERSON><PERSON><PERSON>", "Wind", "Sc<PERSON><PERSON>"], "values": [{"value": "trocken", "colorCode": null}, {"value": "bedeckt", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Regen", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Wind", "colorCode": null}, {"value": "Sc<PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -1, "title": "wetterbedingte Einschränkungen", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -5, "title": "Anwesende Firmen/Personen", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -6, "parentId": -5, "title": "Anwesende", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -7, "title": "Feststellungen/Leistungsstand", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "Feststellungen/Leistungsstand", "type": "string", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -9, "title": "Fotos", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -10, "parentId": -9, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -11, "parentId": -9, "title": "Foto 2", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -12, "parentId": -9, "title": "Foto 3", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -13, "parentId": -9, "title": "Foto 4", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -14, "parentId": -9, "title": "Foto 5", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -15, "parentId": -9, "title": "Foto 6", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -16, "parentId": -9, "title": "Foto 7", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -17, "parentId": -9, "title": "Foto 8", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -18, "parentId": -9, "title": "Foto 9", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -19, "parentId": -9, "title": "Foto 10", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -21, "parentId": -20, "title": "Datum des Besuches", "type": "date", "required": "true", "default": "${CURRENT_DATE}", "isRequiredSignature": "0", "visible": true}, {"id": -22, "parentId": -20, "title": "Name Auftraggeber/Vertreter", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "parentId": -20, "title": "Unterschrift Auftraggeber/Vertreter", "type": "signatureField", "isRequiredSignature": "0", "visible": true}, {"id": -24, "parentId": -20, "title": "Name Handwerker", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -25, "parentId": -20, "title": "Unterschrift Handwerker", "type": "signatureField", "isRequiredSignature": "0", "visible": true}, {"id": -26, "parentId": -20, "title": "Name <PERSON><PERSON>", "type": "string", "required": "true", "default": "${CURRENT_USER_DISPLAYNAME}", "isRequiredSignature": "0", "visible": true}, {"id": -27, "parentId": -20, "title": "Unterschrift Bauleiter", "type": "signatureField", "required": "true", "isRequiredSignature": "0", "visible": true}]}