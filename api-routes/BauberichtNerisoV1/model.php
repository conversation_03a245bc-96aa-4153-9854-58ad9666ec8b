<?php
require_once __DIR__ . '/../../printout.helper.php';

enum DisplayIdType: string
{
    case DocumentId = 'DocumentId';
    case LocalId = 'LocalId';
    case HideId = 'HideId';
}

class C_BauberichtNerisoV1
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);

        if (isset($data['Datum des Besuches']) && $data['Datum des Besuches']) {
            /** @noinspection PhpUnhandledExceptionInspection */
            $dateTime = new DateTime($data['Datum des Besuches']);
            $data['dateOfVisit'] = $dateTime->format('d.m.Y');
        } else {
            /** @noinspection PhpUnhandledExceptionInspection */
            $utcDateTime = new DateTime($doc['fullDocument']['documentCreatedOn'], new DateTimeZone('UTC'));
            $berlinDateTimeZone = new DateTimeZone('Europe/Berlin');
            $utcDateTime->setTimezone($berlinDateTimeZone);
            $data['dateOfVisit'] = $utcDateTime->format('d.m.Y H:i');
        }

        $project = PrintoutHelper::downloadProject((int)$doc['documentRelKey1'], "projectName,customerName", $curl);
        $data['projectName'] = $project['projectName'];
        $data['customerName'] = $project['customerName'];
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'] ?? null;

        $setDisplayIdType = DisplayIdType::DocumentId;
        $displayIdType = $_GET['displayIdType'] ?? null;
        if (is_string($displayIdType)) {
            $parsed = DisplayIdType::tryFrom($displayIdType);
            if ($parsed == null) {
                $allowedValues = implode(', ', array_map(fn($case) => $case->value, DisplayIdType::cases()));
                die_with_response_code(Response::BAD_REQUEST,
                    "Unknown display ID type: $displayIdType. Allowed values are: $allowedValues");
            }
            $setDisplayIdType = $parsed;
        }

        $data['documentId'] = match ($setDisplayIdType) {
            DisplayIdType::DocumentId => $documentId,
            DisplayIdType::LocalId => $doc['fullDocument']['localId'],
            default => null,
        };

        if (isset($data['Wetter'])) {
            if (count($data['Wetter']) >= 1) {
                $data['Verhältnis'] = implode(', ', $data['Wetter']);
            }
        }

        return $data;
    }
}
