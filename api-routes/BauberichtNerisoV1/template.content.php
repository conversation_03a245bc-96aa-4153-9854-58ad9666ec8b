<?php /** @noinspection HtmlUnknownTarget */
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_BauberichtNerisoV1())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<string, mixed> $data
 */
function displayImage(array $data): void
{
    $keys = ["Foto", "Foto 2", "Foto 3", "Foto 4", "Foto 5", "Foto 6", "Foto 7", "Foto 8", "Foto 9", "Foto 10"];
    foreach ($keys as $key) {
        if (!isset($data[$key])) {
            continue;
        }
        echo "<div class='break-before'></div>";
        $imageCounter = 0;

        foreach ($data[$key] as $image) {
            echo sprintf("<img class='img' src='%s' alt='photo'>", $image['filePath']);
            $imageCounter++;

            if ($imageCounter == 2) {
                echo "<div class='break-before'></div>";
                $imageCounter = 0;
            }
        }
    }
}

?>

<!DOCTYPE html>
<html lang="de">
<head>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Baubericht Neriso</title>
</head>

<body style="padding-top: 15px">

<?php
$weatherDetails = [];
if (isset($data['Temperatur'])) {
    $weatherDetails[] = $data['Temperatur'] . "°C";
}
if (isset($data['Wetter'])) {
    $weatherDetails[] = implode(", ", $data['Wetter']);
}
if (!empty($weatherDetails)) {
    echo "<b>Wetter: " . implode("&nbsp;", $weatherDetails) . "</b><br>";
}
?>

<b>Wetterbedingte Einschränkungen: <?= $data['wetterbedingte Einschränkungen'] ?? 'keine'; ?></b>
<br>
<div class="border-bottom" style="padding-top: 9px"></div>
<br>
<b>Anwesende Firmen/Personen:</b>
<br>
<?= $data['Anwesende'] ?? ''; ?>
<br><br>
<b>Feststellungen/Leistungsstand: </b>
<br>
<?= nl2br($data['Feststellungen/Leistungsstand'] ?? ''); ?>
<br><br>

<div class='main-table'><?php displayImage($data); ?></div>

<br>
<div class="border-bottom"></div>
<br>

Datum: <?= $data['dateOfVisit'] ?? ''; ?>
<br>
<br>
<b>Unterschriften:</b>

<table class="signatures" style="margin-top: 21px; margin-left: -2px;">
    <tr>
        <td>
            Auftraggeber / Vertreter: <?= $data['Name Auftraggeber/Vertreter'] ?? ''; ?>
        </td>
        <td>
            Handwerker: <?= $data['Name Handwerker'] ?? ''; ?>
        </td>
        <td>
            Bauleiter: <?= $data['Name Bauleiter'] ?? ''; ?>
        </td>
    </tr>
    <tr>
        <td>
            <?php
            if (isset($data['Unterschrift Auftraggeber/Vertreter'])) {
                echo sprintf('<img src="%s" alt="">', $data['Unterschrift Auftraggeber/Vertreter']);
            }
            ?>
        </td>
        <td>
            <?php
            if (isset($data['Unterschrift Handwerker'])) {
                echo sprintf('<img src="%s" alt="">', $data['Unterschrift Handwerker']);
            }
            ?>
        </td>
        <td>
            <?php
            if (isset($data['Unterschrift Bauleiter'])) {
                echo sprintf('<img src="%s" alt="">', $data['Unterschrift Bauleiter']);
            }
            ?>
        </td>
    </tr>
</table>

</body>
</html>
