<?php
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_BauberichtNerisoV1())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!DOCTYPE HTML>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Baubericht Neriso Header</title>
    <style>
        * {
            font-family: sans-serif;
        }

        body {
            margin: 0 !important;
        }

        #logo {
            max-height: 150px;
            max-width: 100%;
            object-fit: contain
        }

        h4 {
            display: block;
            height: 0;
        }

        h2 {
            display: block;
            padding: 0 !important;
        }

        .no-margin-no-padding {
            margin: 0 !important;
            padding: 0 !important;
        }

        p {
            font-weight: normal;
            margin: 0 !important;
            padding: 0 !important;
            height: 0 !important;
        }

    </style>
</head>
<body>

<?php if ($data['logo']) { ?>
    <img id="logo" src="<?= $data['logo']; ?>" alt="Logo">
<?php } ?>

<?php if (!empty($data['documentId'])) { ?>
    <h2 style="margin: 15px 0 21px 0">Baubericht <?= $data['documentId']; ?></h2>
<?php } ?>

<div style="border-bottom: 1px solid black; padding-bottom: 30px;">
    <h4 class="no-margin-no-padding">
        Projekt:
        <span style="font-size: 16px; font-weight: 400"><?= $data['projectName'] ?? ''; ?></span>
    </h4>
</div>

</body>
</html>
