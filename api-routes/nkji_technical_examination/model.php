<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_NKJI_Technical_Examination
{
	/**
	 * @return array<string, mixed>
	 */
	public function getData(string $schemaId, string $documentId): array
	{
		$curl = new PrintoutCurl();

		$schemaChildren = PrintoutHelper::downloadSchema($schemaId, $curl)['children'];
		$checkBoxes = [];
		foreach ($schemaChildren as $child) {
			if ($child['title'] !== 'Местоположение:' &&
				$child['title'] !== 'Инвентарен №' &&
				$child['title'] !== 'Забележки' &&
				$child['title'] !== 'Подпис') {
				$checkBoxes[] = $child['title'];
			}
		}

		$doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
		try {
			$date = new DateTime($doc['fullDocument']['documentCreatedOn']);
		} catch (Exception $e) {
			die("doc['fullDocument']['documentCreatedOn'] can not be parsed!");
		}
		$date->setTimezone(new DateTimeZone('Europe/Sofia'));
		$data['documentDate'] = $date->format('d/m/Y');

		$checks = [];
		foreach ($checkBoxes as $checkBox) {
			$has = false;
			foreach ($doc['fullDocument']['children'] as $docElement) {
				if ($docElement['title'] === $checkBox) {
					$has = true;
					$checks[$checkBox] = 'yes';
					break;
				}
			}
			if (!$has) {
				$checks[$checkBox] = null;
			}
		}
		$data['checks'] = $checks;

		$mapped = [];
		foreach ($doc['fullDocument']['children'] as $d) {
			if (isset($d['reportedValue'])) {
				if ($d['type'] === 'signatureField') {
					$mapped[$d['title']] = $d['filePath'];
				} else {
					$mapped[$d['title']] = $d['reportedValue'];
				}
			}
		}
		$data['location'] = $mapped['Местоположение:'];
		$data['assetNum'] = $mapped['Инвентарен №'];
		$data['remarks'] = $mapped['Забележки'];
        $data['signature'] = $mapped['Подпис'] ?? '';
		$data['documentId'] = $doc['fullDocument']['documentId'];

		$author = $doc['fullDocument']['author'];
		$base = PrintoutHelper::getApiBaseUrl();
		$employee = json_decode($curl->_simple_call('get', "$base/v3/employees/$author",
			[], PrintoutHelper::getHeadersForApiCalls()), true);
		$data['author'] = $employee;

		return $data;
	}
}
