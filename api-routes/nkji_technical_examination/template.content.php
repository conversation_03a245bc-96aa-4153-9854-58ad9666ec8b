<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_NKJI_Technical_Examination())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<html lang="bg">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>ПРОТОКОЛ за извършване на ежесменен преглед (ЕП) на РССМ</title>
    <style>
        .signature {
            max-height: 120px;
        }

        .table, .table th, .table td {
            border: 1px solid black;
            border-collapse: collapse;
        }

        td:first-child, td:last-child {
            text-align: center;
        }

        .leftColumn {
            float: left;
            width: 47%;
            margin-left: 20px;
        }

        .rightColumn {
            float: right;
            width: 47%;
            margin-left: 20px;
        }

        .textRight {
            text-align: right;
        }

        .row:after {
            content: "";
            display: table;
            clear: both;
        }

        .title {
            font-size: large;
            font-weight: bold;
            text-align: center;
            margin: auto;
        }

        body {
            overflow-x: hidden;
        }
    </style>
</head>
<body>

<div class="row">
    <table style="width: 100%">
        <tr>
            <td>
                <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/logo.png') ?>"
                     alt="NKJI Logo" style="max-height: 36px">
            </td>
            <td>
                <div class="title">
                    ПРОТОКОЛ за извършване на ежесменен преглед (ЕП) на РССМ
                    <br>
                    <span style="font-size: initial">
						№ <?= $data['documentId'] ?>
					</span>
                </div>
            </td>
            <td>
                <div class="textRight">
                    <p style="margin-bottom: 0">ФБ –РП – 2.55 – 08 – 14</p>
                    <p style="margin-top: 0; margin-bottom: 0">Версия 03 / 2021</p>
                </div>
            </td>
        </tr>
    </table>
</div>

<div class="row">
    <p style="margin-bottom: 0">Местоположение: <?= $data['location']; ?></p>
</div>
<div class="row">
    <p style="margin-top: 0">Инвентарен № <?= $data['assetNum']; ?></p>
</div>
<div class="row">
    <table class="table">
        <tr class="blueBackground">
            <th>Номер по ред</th>
            <th>ОПИСАНИЕ НА ОПЕРАЦИИТЕ</th>
            <th>Резултат</th>
        </tr>
        <?php
        $i = 1;
        foreach ($data['checks'] as $title => $reportedValue) { ?>
            <tr class="whiteBackground">
                <td class="cell"><?php echo $i;
                    $i++ ?>.
                </td>
                <td><?= $title ?></td>
                <td> <?= $reportedValue ? "&#9745;" : "&squ;"; ?></td>
            </tr>
        <?php } ?>
    </table>
</div>
<div class="row">
    <p>Забележки, които не създават опасност за движението на машината:</p>
    <p><?= empty($data['remarks']) ? "няма" : $data['remarks'] ?></p>
</div>
<div class="row">
    <p>Всички забелязани неизправности и влошени показатели на някои от системите, които
        не могат да бъдат отстранени при прегледите от машиниста, но не създават опасност за
        безопасността на движението и качеството на работа на РССМ, след съгласуване с прекия
        ръководител (отговорник за съответната машина по места), се записват в бордовия дневник със
        срок за отстраняване.</p>
    <p>Постъпващият на работа машинист записва резултатите от извършения ЕП в бордовия
        дневник и се подписва.</p>
    <p>Машинистът отговаря за техническото състояние на РССМ в рамките на смяната си.</p>
    <p>Машинистът, притежаващ необходимата правоспособност за управление на
        повдигателни съоръжения, записва резултатите от извършените прегледи на съоръженията с
        повишена опасност в сменен дневник за преглед на повдигателните съоръжения и се подписва.</p>
</div>

<div>
    <b>Ежесменен преглед (ЕП) - извършва се преди започване на смяната съгласно формуляр по безопасност ФБ - РП - 2.55 -
        08 - 14. ЕП на РССМ се извършват от постъпващия на смяна машинист. След извършване на ЕП, машинистът е длъжен да
        запише в бордовия дневник "Е.П. - извършен" и да се подпише;</b>
</div>

<div class="row">
    <div class="leftColumn" style="margin-top: 15px">
        <p style="margin-top: 0">Дата:</p>
        <p><?= $data['documentDate']; ?></p>
    </div>
    <div class="rightColumn" style="display: -webkit-box; margin-top: 15px">
        <div>
            <p style="margin-top: 0">Подпис машинист:</p>
            <p><?= $data['author']['displayName'] ?? ''; ?></p>
        </div>
        <?php if (isset($data['signature']) && $data['signature']) { ?>
            <img src="<?= $data['signature'] ?>" alt="Signature" class="signature">
        <?php } ?>
    </div>
</div>
</body>
</html>

