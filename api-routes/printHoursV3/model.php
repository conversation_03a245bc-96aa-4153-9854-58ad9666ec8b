<?php
require_once __DIR__ . '/../printHours/model.php';

enum DisplayTimeFormat: string
{
    case DECIMAL = 'DECIMAL';
    case HOUR_STYLE = 'HOURSTYLE';
}

class C_printHoursV3
{
    /**
     * @return array<int, array<string, mixed>>
     */
    public function getData(string $personalNo, ?string $from, ?string $to, ?string $ktrAanr, ?string $displayTimeFormat): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $hoursV1 = (new C_PrintHours())->getData($personalNo, $from, $to, $ktrAanr);
        $allPnrs = array_map('intval', explode(',', $personalNo));

        $permissionsToAllPnrs = [];
        $contractsToAllPnrs = [];
        foreach ($allPnrs as $pnr) {
            $permissionsToAllPnrs[$pnr] = PrintoutHelper::downloadPermissions($pnr, $curl);
            $contractsToAllPnrs[$pnr] = $this->downloadEmployeeContracts($pnr, $curl);
        }

        /*        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                foreach ($contractsToAllPnrs as &$contracts) {
                    foreach ($contracts as &$contract) {
                        foreach ($days as $day) {
                            $startTime = $day . 'StartTime';
                            $endTime = $day . 'EndTime';
                            $contract[$startTime] = "08:00";
                            $contract[$endTime] = "14:00";
                            $contract['weeklyWorkingHours'] = 30;
                            $contract['monthlyWorkingHours'] = 120;
                        }
                    }
                }*/

        if (is_null($displayTimeFormat)) {
            $settings = PrintoutHelper::downloadSettings($curl);
            $apiTimeFormat = $settings['displayTimeFormat'] ?? "";
            $timeFormat = DisplayTimeFormat::tryFrom($apiTimeFormat);
            if ($timeFormat == null) {
                $timeFormat = DisplayTimeFormat::HOUR_STYLE;
            }
        } else {
            $timeFormat = DisplayTimeFormat::tryFrom($displayTimeFormat);
            if ($timeFormat == null) {
                die_with_response_code(Response::BAD_REQUEST,
                    "Unknown display time format passed: $displayTimeFormat. Allowed ones are: DECIMAL and HOURSTYLE");
            }
        }

        $internalProjects = $this->downloadInternalProjects($curl);

        $data = [];
        foreach ($allPnrs as $pnr) {
            $hours = $hoursV1['hours'][$pnr];
            $employee = $hoursV1['employees'][$pnr];
            $contracts = $contractsToAllPnrs[$pnr];
            $permissions = $permissionsToAllPnrs[$pnr];
            $data[$pnr] = $this->processHourEntries($hours, $contracts, $timeFormat);

            $canShowExternalEmployeeNo = in_array('adm_emp_show_external_employee_no', $permissions);
            if ($canShowExternalEmployeeNo && !empty($employee['externalEmployeeNo'])) {
                $data[$pnr]['employeeNo'] = $employee['externalEmployeeNo'];
            } else {
                $data[$pnr]['employeeNo'] = $employee['employeeNo'];
            }

            $data[$pnr]['employeeDisplayName'] = $employee['displayName'] ?? '';
            $data[$pnr]['employeeBirthDate'] = empty($employee['birthDate']) ? '' : date('(d.m.Y)', strtotime($employee['birthDate']));

            $data[$pnr]['statistics'] = $this->mapStatistics($internalProjects, $hours['hours_data']);
            $data[$pnr]['totalOvertimeDecimal'] = $this->calculateTotalOvertime($from, $to, $hours['hours_data'], $contracts);
            $data[$pnr]["timeFormat"] = $timeFormat;
        }

        return $data;
    }

    /**
     * @param array<string, mixed> $hours
     * @param array<string, mixed> $contracts
     * @return array<string, mixed>
     */
    private function processHourEntries(array $hours, array $contracts, DisplayTimeFormat $showHourFormat): array
    {
        $totalWork = 0;
        $totalDrive = 0;
        $totalPause = 0;
        $totalTime = 0;
        $totalSoll = 0;
        $totalOvertime = 0;
        $totalAttendance = 0;

        $entries = $hours['hours_data'];

        usort($entries, function ($a, $b) {
            // note that startUTC can be null for vacation bookings
            $timeA = isset($a['startUTC']) ? strtotime($a['startUTC']) : PHP_INT_MIN;
            $timeB = isset($b['startUTC']) ? strtotime($b['startUTC']) : PHP_INT_MIN;
            return $timeA <=> $timeB;
        });

        $data = [];
        foreach ($entries as $hour) {
            $temp['name'] = $hour['name'];
            $temp['date'] = PrintoutHelper::dateConverter($hour['date'], 'd.m.Y');
            $temp['start'] = $hour['start'];
            $temp['end'] = $hour['end'];
            $temp['hours'] = $hour['hours'];
            $temp['comment'] = $this->getComment($hour);

            $date = strtotime($hour['date']);
            $temp['day'] = substr(PrintoutHelper::getWeekDayName((int)date('N', $date), 'de'), 0, 2);

            $driveTimeFrom = '-';
            if (!empty($hour['startDriveFrom']) && !empty($hour['endDriveFrom'])) {
                $driveTimeFrom = "{$hour['startDriveFrom']} - {$hour['endDriveFrom']}";
            }
            $temp['driveTimeFrom'] = $driveTimeFrom;

            $driveTimeTo = '-';
            if (!empty($hour['startDriveTo']) && !empty($hour['endDriveTo'])) {
                $driveTimeTo = "{$hour['startDriveTo']} - {$hour['endDriveTo']}";
            }
            $temp['driveTimeTo'] = $driveTimeTo;

            $breaks = '-';
            if (isset($hour['breaks'])) {
                $breaks = implode('<br>', array_map(function ($break) {
                    return "{$break['start']} - {$break['end']}";
                }, $hour['breaks']));
            }
            $temp['breaks'] = $breaks;

            $dailyTotalPause = round((float)$hour['pause'], 2);
            $temp['dailyTotalPause'] = $dailyTotalPause;

            $dailyTotalDrive = round((float)$hour['reisestd'], 2);
            $temp['dailyTotalDrive'] = $dailyTotalDrive;

            $dailyTotal = $dailyTotalDrive + floatval($hour['hours']);
            $temp['dailyTotal'] = $dailyTotal;

            $dailyTotalAttendance = $dailyTotalDrive + $dailyTotalPause + floatval($hour['hours']);
            $temp['dailyTotalAttendance'] = $dailyTotalAttendance;

            $temp['soll'] = $this->getSoll($hour['date'], $contracts);
            $temp['overtime'] = empty($temp['soll']) ? 0 : ($dailyTotal - $temp['soll']);

            $totalWork += floatval($hour['hours']);
            $totalDrive += $dailyTotalDrive;
            $totalPause += $dailyTotalPause;
            $totalTime += $dailyTotal;
            $totalAttendance += $dailyTotalAttendance;
            $totalSoll += $temp['soll'];
            $totalOvertime += floatval($temp['overtime']);

            $data[] = $temp;
        }

        $isSollSet = !empty(array_filter(array_column($data, 'soll'), fn($item) => !empty($item)));

        // Organize data by year-month for easier display in the template
        $hours = [];
        foreach ($data as $item) {
            $month = date('Y-m', strtotime($item['date']));

            if (!isset($hours[$month])) {
                $hours[$month] = [];
            }

            $hours[$month][] = $item;
        }

        $monthlyBookings = $this->getMonthlyBookings($hours);

        if ($showHourFormat === DisplayTimeFormat::HOUR_STYLE) {
            $totalWork = PrintoutHelper::convertDecimalToTime($totalWork);
            $totalDrive = PrintoutHelper::convertDecimalToTime($totalDrive);
            $totalPause = PrintoutHelper::convertDecimalToTime($totalPause);
            $totalTime = PrintoutHelper::convertDecimalToTime($totalTime);
            $totalSoll = PrintoutHelper::convertDecimalToTime($totalSoll);
            $totalOvertime = PrintoutHelper::convertDecimalToTime($totalOvertime);
            $totalAttendance = PrintoutHelper::convertDecimalToTime($totalAttendance);
        } else {
            // DisplayTimeFormat::DECIMAL
            $totalWork = PrintoutHelper::formatTimeNumber($totalWork);
            $totalDrive = PrintoutHelper::formatTimeNumber($totalDrive);
            $totalPause = PrintoutHelper::formatTimeNumber($totalPause);
            $totalTime = PrintoutHelper::formatTimeNumber($totalTime);
            $totalSoll = PrintoutHelper::formatTimeNumber($totalSoll);
            $totalOvertime = PrintoutHelper::formatTimeNumber($totalOvertime);
            $totalAttendance = PrintoutHelper::formatTimeNumber($totalAttendance);
        }

        return [
            'hours' => $hours,
            'totalWork' => $totalWork,
            'totalDrive' => $totalDrive,
            'totalPause' => $totalPause,
            'totalTime' => $totalTime,
            'totalSoll' => $totalSoll,
            'totalOvertime' => $totalOvertime,
            'totalAttendance' => $totalAttendance,
            'monthlyBookings' => $monthlyBookings,
            'isSollSet' => $isSollSet
        ];
    }

    /**
     * See https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/477 for spec.
     * @param array<string, mixed> $hour
     */
    private function getComment(array $hour): string
    {
        $comment = $hour['comment'];
        if (str_starts_with($comment, "Input via ")) {
            $count = substr_count($comment, "Input via ");
            if ($count == 1) {
                return "";
            }
            return $comment;
        }
        $pos = strrpos($comment, '| Input via ');
        if ($pos !== false) {
            $comment = substr($comment, 0, $pos);
        }
        return trim($comment);
    }

    /**
     * @param array<string, mixed> $hours
     * @return array<string, mixed>
     */
    private function getMonthlyBookings(array $hours = []): array
    {
        $result = [];

        foreach ($hours as $key => $values) {
            $dates = array_values(array_unique(array_column($values, 'date')));
            $result[$key] = count($dates);
        }

        return $result;
    }

    /**
     * @param string $date 2018-01-01
     * @param array<string, mixed> $contracts the API response of GET v3/employees/{id}/contracts
     */
    private function getSoll(string $date, array $contracts): float
    {
        $difference = 0;
        $dailyContract = $this->getValidContract($date, $contracts);
        if (empty($dailyContract)) {
            return $difference;
        }

        // generate keys like "mondayStartTime" and "mondayEndTime"
        $date = strtotime($date);
        $weekDay = strtolower(date('l', $date));
        $startTimeKey = "{$weekDay}StartTime";
        $endTimeKey = "{$weekDay}EndTime";

        // the values are in the format "HH:MM"
        if (!empty($dailyContract[$startTimeKey]) && !empty($dailyContract[$endTimeKey])) {
            $startTime = strtotime($dailyContract[$startTimeKey]);
            $endTime = strtotime($dailyContract[$endTimeKey]);
            $work = $endTime - $startTime;

            $startBreakTimeKey = "{$weekDay}StartBreakTime";
            $endBreakTimeKey = "{$weekDay}EndBreakTime";

            $break = 0;
            if (!empty($dailyContract[$startBreakTimeKey]) && !empty($dailyContract[$endBreakTimeKey])) {
                $startBreakTime = strtotime($dailyContract[$startBreakTimeKey]);
                $endBreakTime = strtotime($dailyContract[$endBreakTimeKey]);
                $break = $endBreakTime - $startBreakTime;
            }

            $difference = ($work - $break) / 3600;
        }

        return $difference;
    }

    /**
     * @return array<string, mixed>
     */
    private function downloadEmployeeContracts(int|string $pnr, PrintoutCurl $curl): array
    {
        $url = "v3/employees/$pnr/contracts";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true) ?? [];
    }

    /**
     * @return array<string, mixed>
     */
    private function downloadInternalProjects(PrintoutCurl $curl): array
    {
        $partial = "projectNo";
        $url = "v3/projects?filter[projectStatus]=2&filter[appusage][lt]=3&filter[projectType][lt]=3&partial=" . $partial;
        return json_decode($curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    /**
     * @param array<string, mixed> $internalProjects
     * @param array<string, mixed> $hours
     * @return array<string, mixed>
     */
    private function mapStatistics(array $internalProjects, array $hours): array
    {
        $data = [];
        $projectIds = array_column($internalProjects, 'projectNo');
        foreach ($hours as $hour) {
            if (!in_array($hour['ktr'], $projectIds)) {
                continue;
            }
            $temp['ktr'] = $hour['ktr'];
            $temp['name'] = $hour['name'];
            if (!isset($data[$hour['ktr']])) {
                $temp['hours'] = doubleval($hour['hours']);
                $data[$hour['ktr']] = $temp;
            } else {
                $data[$hour['ktr']]['hours'] = $data[$hour['ktr']]['hours'] + doubleval($hour['hours']);
            }
            $data[$hour['ktr']]['dates'][] = $hour['date'];
        }
        return $data;
    }

    /**
     * @param string $date 2018-01-01
     * @param array<string, mixed> $contracts the API response of GET v3/employees/{id}/contracts
     * @return array<string, mixed>
     */
    private function getValidContract(string $date, array $contracts): array
    {
        if (empty($contracts)) {
            return [];
        }

        $date = strtotime($date);
        $dailyContracts = array_values(array_filter($contracts, function ($contract) use ($date) {
            $validFrom = strtotime($contract['validFrom']);
            $validTo = strtotime($contract['validTo']);
            return $date >= $validFrom && $date <= $validTo;
        }));

        if (empty($dailyContracts)) {
            return [];
        }
        return $dailyContracts[0];
    }

    /**
     * @param string $from 2018-01-01
     * @param string $to 2018-01-01
     * @param array<string, mixed> $hours the API response of GET v3/hours
     * @param array<string, mixed> $contracts the API response of GET v3/employees/{id}/contracts
     */
    private function calculateTotalOvertime(string $from, string $to, array $hours, array $contracts): float
    {
        $currentDate = $from;
        $totalOvertime = 0;

        while (strtotime($currentDate) <= strtotime($to)) {
            $sollHours = $this->getSoll($currentDate, $contracts);
            if ($sollHours == 0.0) {
                $currentDate = date("Y-m-d", strtotime($currentDate . ' +1 day'));
                continue;
            }

            $workedHours = 0;
            foreach ($hours as $hour) {
                if ($hour['date'] === $currentDate) {
                    $workedHours = doubleval($hour['hours']);
                    break;
                }
            }
            $dailyOvertime = $workedHours - $sollHours;
            $dailyOvertimes[$currentDate] = $dailyOvertime;

            $totalOvertime += $dailyOvertime;
            $currentDate = date("Y-m-d", strtotime($currentDate . ' +1 day'));
        }

        return $totalOvertime;
    }
}
