<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::PrintHoursV3,
    baseFileName: "output-printHoursV3-displayTimeFormat",
    params: [
        "personalNo" => "260,4",
        "from" => "2018-01-01",
        "to" => "2018-01-20",
        "displayTimeFormat" => "HOURSTYLE",
    ]
);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::PrintHoursV3,
    baseFileName: "output-printHoursV3-displayTimeFormat",
    params: [
        "personalNo" => "260,4",
        "from" => "2018-01-01",
        "to" => "2018-01-20",
        "displayTimeFormat" => "DECIMAL",
    ]
);