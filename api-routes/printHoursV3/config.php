<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::arrayInt, "personalNo", required: true);
$config->addParameter(RouteConfigParameterType::date, "from", required: false);
$config->addParameter(RouteConfigParameterType::date, "to", required: false);
$config->addParameter(RouteConfigParameterType::ktrAanr, "ktrAanr", required: false);
$config->addParameter(RouteConfigParameterType::string, "displayTimeFormat", required: false);
$config->wkhtmltopdfArguments = [
    "--margin-bottom", "10mm",
    "--margin-top", "10mm",
    "--margin-left", "5mm",
    "--margin-right", "5mm"
];
return $config;
