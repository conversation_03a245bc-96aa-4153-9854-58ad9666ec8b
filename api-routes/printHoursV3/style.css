body {
    font-family: Arial, sans-serif;
    font-size: 10px;
}

table {
    width: 100%;
}

.text-gray {
    color: gray !important;
}

.text-left {
    text-align: left !important;
}

.font-bold {
    font-weight: bold;
}

.icon {
    width: 15px;
    height: 15px;
}

.employee {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
}

.header-table .icon-label {
    font-weight: bold;
    padding-right: 5px;
    vertical-align: middle;
}

.header-table img {
    vertical-align: middle;
}

.main-table {
    border-collapse: collapse;
    border: 1px solid black;
    margin-top: 0;
}

.main-table td {
    padding: 4px 5px;
}

.main-table .header-row,
.main-table .month-row {
    font-weight: bold;
    background: #eee;
}

.main-table .header-row,
.main-table .month-row,
.main-table .total-row {
    border: 1px solid black;
}

.main-table td:first-child {
    width: 12%;
}

.main-table td:nth-child(n+6):nth-child(-n+13) {
    width: 6%;
    text-align: right;
}

.main-table td:nth-child(n+2):nth-child(-n+5) {
    text-align: right;
}

.main-table .odd-row {
    background: #eee;
}

.main-table .even-row td,
.main-table .odd-row td {
    vertical-align: top;
}

.statistics {
    width: 50%;
    margin-top: 10px;
    margin-bottom: 20px;
    border-collapse: collapse;
}

.statistics td {
    padding: 4px 0;
}