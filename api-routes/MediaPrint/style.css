html, body {
    /* fix emoji issues */
    text-rendering: optimizeLegibility;
    font-family: Arial, sans-serif;
    margin: 0;
}

body {
    padding: 0;
    margin: 0;
}

.page {
    height: 297mm;
    width: 210mm;
    padding: 5mm;
    box-sizing: border-box;
}

.logo img {
    max-width: 70px;
    max-height: 70px;
    height: auto;
    width: auto;
}

.one-img-per-page {
    max-height: 260mm;
    width: 100%;
    overflow: hidden;
}

.text-size {
    font-size: 14px;
}

.one-img-per-page .text-size {
    font-size: 14px;
}

.two-img-per-page td, .two-img-per-page .text-size {
    font-size: 12px;
}

.one-img-per-page img {
    margin-top: 6px;
    max-height: 200mm; /*leave space for comments*/
    max-width: 200mm;
    height: auto;
    width: auto;
}

.four-img-per-page {
    max-height: 500px;
    margin-top: 10px;
    overflow: hidden;
    max-width: 50%;
    border: 1px solid black;
    padding: 5px;
}

.four-img-per-page td {
    font-size: 12px;
}

.four-img-per-page img {
    max-height: 350px;
    max-width: 100%;
    height: auto;
    width: auto;
}

.two-img-per-page {
    margin-bottom: 5px;
    max-height: 475px;
    width: 100%;
    overflow: hidden;
    padding: 5px;
    border: 1px solid black;
	box-sizing: border-box;
}

.two-img-per-page img {
    max-height: 300px; /*leave space for comments*/
    max-width: 100%;
    height: auto;
    width: auto;
}

.two-img-per-page p {
    margin: 5px 0;
}

.four-img-per-page p {
    margin: 2px 0;
}

.one-img-per-page td:first-child {
    width: 20%;
}

.two-img-per-page td:first-child {
    width: 16%;
}

.four-img-per-page td:first-child {
    width: 25%;
}

img.emoji {
    height: 1em;
    width: 1em;
    margin: 0 .05em 0 .1em;
    vertical-align: -0.1em;
}