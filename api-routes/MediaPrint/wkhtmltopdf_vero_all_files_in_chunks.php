<?php

require_once __DIR__ . '/../../Pdf.php';

Pdf::$apiUrl = 'https://portal-sofia.vero.bg:8443/api/index.php/';
Pdf::$principal = 'vero';

$offset = 0;
$length = 100;

//print 1800 files in chunks of 100
for ($i = 0; $i < 18; $i++) {
    $size = $offset + $length;
    Pdf::$outputFile = 'output-' . $offset . '-' . $size . '.pdf';
    Pdf::compile(__DIR__, [
        'projectNo' => 2308026,
        'imagesPerPage' => 1,
        'offset' => $offset,
        'length' => $length
    ]);
    $offset += $length;
}
