<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "employeeNo", required: false);
$config->addParameter(RouteConfigParameterType::date, "fromDate", required: false);
$config->addParameter(RouteConfigParameterType::date, "toDate", required: false);
$config->addParameter(RouteConfigParameterType::string, "wageType", required: false);
$config->addParameter(RouteConfigParameterType::ktrAanr, "ktrAanr", required: false);
$config->addParameter(RouteConfigParameterType::string, "groupedByPeriod", required: false);
$config->addParameter(RouteConfigParameterType::bool, "showBonusOnly", required: false);
$config->addParameter(RouteConfigParameterType::string, "sortingOrder", required: false);
$config->addParameter(RouteConfigParameterType::string, "sortingColumn", required: false);
$config->redirectToRoute = 'printHoursV2';
$config->beforeRouteCallback = function (&$query) {
    $query['personalNo'] = $_GET['personalNo'] = $_GET['employeeNo'];
    $query['from'] = $_GET['from'] = $_GET['fromDate'];
    $query['to'] = $_GET['to'] = $_GET['toDate'];
    unset($query['employeeNo'], $query['fromDate'], $query['toDate']);
};
return $config;