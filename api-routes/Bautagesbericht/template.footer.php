<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Bautagesbericht Footer</title>
    <style>
        footer {
            font-family: Arial, 'sans-serif';
            width: 100%;
            text-align: center;
        }

        p {
            font-family: Arial, 'sans-serif';
            color: #000000;
            font-size: 10px;
            padding: 0;
            margin: 0;
        }

        .page-number {
            font-size: 14px;
            font-weight: 500;
        }
    </style>

    <script>
        function subst() {
            var vars = {};
            var query_strings_from_url = document.location.search.substring(1).split('&');
            for (var query_string in query_strings_from_url) {
                if (query_strings_from_url.hasOwnProperty(query_string)) {
                    var temp_var = query_strings_from_url[query_string].split('=', 2);
                    vars[temp_var[0]] = decodeURI(temp_var[1]);
                }
            }
            var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
            for (var css_class in css_selector_classes) {
                if (css_selector_classes.hasOwnProperty(css_class)) {
                    var element = document.getElementsByClassName(css_selector_classes[css_class]);
                    for (var j = 0; j < element.length; ++j) {
                        element[j].textContent = vars[css_selector_classes[css_class]];
                    }
                }
            }
        }
    </script>
</head>
<body onload="subst()">
<footer>
    <p class="page-number">Seite <span class="page"></span> von <span class="topage"></span></p>
    <p>Bautagesbericht erstellt nach §4 VOB-ZVB</p>
</footer>
</body>
</html>