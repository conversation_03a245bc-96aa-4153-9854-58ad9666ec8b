<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    try {
        $data = (new C_Bautagesbericht())->getData($_GET['schemaId'], $_GET['documentId']);
    } catch (Exception $e) {
        $data = [];
    }
}

$date = isset($data['date']) ? PrintoutHelper::dateConverter($data['date'], 'd.m.Y') : '';
if (isset($data['time'])) {
    $time = new DateTime($data['time']);
    $time = $time->format('H:i');
} else {
    $time = '';
}
?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Bautagesbericht</title>
    <style>
        * {
            font-family: Arial, sans-serif;
        }

        table td {
            vertical-align: top;
        }

        .company-info {
            width: 350px;
        }

        .document-info {
            width: 700px;
            font-size: 12px;
        }

        .document-highlight {
            font-size: 20px;
        }

        .client-table th, .client-table td {
            text-align: start;
        }

        .client-table td {
            padding: 0 20px;
            width: 100%;
            color: gray;
        }
    </style>
</head>
<body>
<table>
    <tr>
        <td rowspan="3">
            <table>
                <tr>
                    <td class="document-info document-highlight">
                        Bautagesbericht
                    </td>
                </tr>

                <tr>
                    <td class="document-info">
                        Nr. <?= $data['documentId'] ?>
                    </td>
                </tr>
            </table>
        </td>
        <td></td>
        <td colspan="2" rowspan="2">
            <table class="company-info">
                <tr>
                    <td>VERO Scaffolding EOOD</td>
                    <td rowspan="6">
                        <img width="110" height="110"
                             src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("img/vero_logo.png") ?>"
                             alt="vero-logo"></td>
                </tr>
                <tr>
                    <td>
                        Niederlassung Deutschland
                    </td>
                </tr>
                <tr>
                    <td>
                        Merseburger Straße 6-8
                    </td>
                </tr>
                <tr>
                    <td>
                        33106 Paderborn
                    </td>
                </tr>
                <tr>
                    <td>
                        +49 52 51 874 100
                    </td>
                </tr>
                <tr>
                    <td>
                        <EMAIL>
                    </td>
                </tr>
                <tr>
                    <td>
                        www.vero.de
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

<table class="client-table">
    <tr>
        <th>Datum:</th>
        <td><?= $date ?></td>
    </tr>
    <tr>
        <th>Uhrzeit:</th>
        <td><?= $time ?></td>
    </tr>
    <tr>
        <th>Auftraggeber:</th>
        <td><?= $data['client'] ?></td>
    </tr>
    <tr>
        <th>Projekt:</th>
        <td><?= $data['project'] ?></td>
    </tr>
    <tr>
        <th>Autor:</th>
        <td><?= $data['employee']['firstName'] . ' ' . $data['employee']['lastName'] ?></td>
    </tr>
</table>
</body>
</html>