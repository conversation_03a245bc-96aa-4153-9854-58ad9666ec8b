<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Bautagesbericht
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data['mappedChildren'] = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);

        $data['employee'] = PrintoutHelper::downloadEmployee($doc['fullDocument']['author'], $curl);

        $partial = "projectName,customerNo,projectSiteAddress,projectSiteZipCode,projectSiteCity,projectValidStartDate";
        $partial = null;
        $project = PrintoutHelper::downloadProject($doc['fullDocument']['documentRelKey1'], $partial, $curl);

        $data['documentId'] = $documentId;
        $data['date'] = PrintoutHelper::dateConverter($doc['fullDocument']['documentCreatedOn'], 'd-m-Y');
        $data['time'] = PrintoutHelper::dateConverter($doc['fullDocument']['documentCreatedOn'], 'H:i:s');
        $data['client'] = $this->downloadCustomerName($project['customerNo'], $curl);
        $data['project'] = $project['projectName'];

        /*        if (isset($project['projectSiteCity'])) {
                    $city = $project['projectSiteCity'];
                    $dateTime = new DateTime($doc['fullDocument']['documentCreatedOn']);
                    $date = $dateTime->format('Y-m-d');

                    if ($city != "") {
                        if (isset($project['gpsLongitude']) && isset($project['gpsLatitude'])) {
                            $weatherData = $this->downloadWeatherData($date, $date, $city, $project['gpsLongitude'], $project['gpsLatitude']);
                        } else {
                            $weatherData = $this->downloadWeatherData($date, $date, $city);
                        }

                        if ($weatherData != null) {
                            $data['weather'] = $weatherData;
                        }
                    }
                }*/

        $shouldDownloadHoursData = PrintoutHelper::isCheckboxTickedByReportedValue(
            $data['mappedChildren']['Arbeitskräfte-Daten von Stunden abrufen'] ?? '');
        if ($shouldDownloadHoursData) {
            $ktr = $doc['fullDocument']['documentRelKey1'];
            $hours = json_decode($curl->_simple_call('get', "v1/hours/all?ktr=$ktr&type=approved", [],
                PrintoutHelper::getHeadersForApiCalls()), true);
            //take only first five as per https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/70
            $hours = array_slice($hours, 0, 5);
            $data['hours'] = $this->prepareWorksData($hours, $curl);
        }
        return $data;
    }

    public function downloadCustomerName(string $customerNo, PrintoutCurl $curl): string
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get', "$base/v1/addresses/$customerNo",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($json)->displayName;
    }

    // WARNING: Do not use since meteo is not free for commercial use!
    /**
     * @param string|float|null $gpsLongitude
     * @param string|float|null $gpsLatitude
     * @return array<string, mixed>|null
     */
    public function downloadWeatherData(string $startDate, string $endDate, string $cityName, $gpsLongitude = null, $gpsLatitude = null): array|null
    {
        $weatherData = array();

        if ($gpsLongitude == null || $gpsLatitude == null) {
            $url = "https://geocoding-api.open-meteo.com/v1/search?name=" . urlencode($cityName);

            $json = file_get_contents($url);
            $coords = json_decode($json, true);

            //in case a city name is incorrectly given, api returns 200 status code but empty body
            if (!isset($coords['results'])) {
                return null;
            }

            $gpsLatitude = $coords['results'][0]['latitude'];
            $gpsLongitude = $coords['results'][0]['longitude'];
        }

        $url = "https://api.open-meteo.com/v1/forecast?latitude=$gpsLatitude&longitude=$gpsLongitude&"
            . "hourly=temperature_2m,weather_code&start_date=$startDate&end_date=$endDate";
        $json = file_get_contents($url);
        $data = json_decode($json, true);

        $minMaxArray = [];
        // Check if the 'hourly' and 'temperature_2m' keys exist in the response
        if (isset($data['hourly']['temperature_2m'])) {
            $hourlyTemperatures = $data['hourly']['temperature_2m'];

            // Find the minimum and maximum values and their corresponding time values
            $minValue = min($hourlyTemperatures);
            $maxValue = max($hourlyTemperatures);
            $minValueIndex = array_search($minValue, $hourlyTemperatures);
            $maxValueIndex = array_search($maxValue, $hourlyTemperatures);
            $minTime = $data['hourly']['time'][$minValueIndex];
            $maxTime = $data['hourly']['time'][$maxValueIndex];

            // Create a separate array with the min and max values and their corresponding time values
            $minMaxArray = array(
                'min' => array(
                    'time' => $minTime,
                    'value' => $minValue
                ),
                'max' => array(
                    'time' => $maxTime,
                    'value' => $maxValue
                )
            );
        }

        $weatherData['minMaxArray'] = $minMaxArray;
        $weatherData['min'] = $minMaxArray['min']['value'];
        $weatherData['max'] = $minMaxArray['max']['value'];
        $weatherData['city'] = $cityName;
        $weatherData['hourlyWeatherCode'] = $data['hourly']['weather_code'];

        //resolve WMO code for current time
        $currentTime = date('G');
        $timeOfDay = ($currentTime >= 6 && $currentTime <= 18) ? "day" : "night";
        $currentTimeIndex = intval($currentTime);
        $wmoCode = $data['hourly']['weather_code'][$currentTimeIndex];
        $weatherData['wmo'] = $this->mapWmoToIcon($wmoCode, $timeOfDay);
        $weatherData['wmo']['rainInfo'] = $this->getRainInfo($wmoCode);
        return $weatherData;
    }

    /**
     * @param array<mixed> $hours
     * @return array<mixed>
     */
    public function prepareWorksData(array $hours, PrintoutCurl $curl): array
    {
        usort($hours, function ($a, $b) {
            return $a['pnr'] <=> $b['pnr'] ?: strtotime($a['start']) <=> strtotime($b['start']);
        });
        $data = [];
        $employees = [];
        foreach ($hours as $hour) {
            if (array_key_exists($hour['pnr'], $employees)) {
                $temp['profession'] = $employees[$hour['pnr']];
            } else {
                $employee = PrintoutHelper::downloadEmployee($hour['pnr'], $curl);
                $temp['profession'] = $employee['profession'] ?? '';
                $employees[$hour['pnr']] = $employee['profession'];
            }
            $temp['pnr'] = $hour['pnr'];
            $temp['workDuration'] = $hour['start'] . ' - ' . $hour['end'];
            $temp['breakDuration'] = $hour['startBreak'] . ' - ' . $hour['endBreak'];
            $data[] = $temp;
        }
        return $data;
    }

    /**
     * @return array<string, mixed>
     */
    private function mapWmoToIcon(int $code, string $dayTime): array
    {
        $data = json_decode(file_get_contents(__DIR__ . '/wmoIconMapper.json'), true);
        return $data[$code][$dayTime];
    }

    private function getRainInfo(int $code): string
    {
        $rainCodes = [51, 53, 55, 61, 63, 65, 66, 67, 80, 81, 82];
        if (in_array($code, $rainCodes)) {
            return "Regnerisch";
        }
        return "Kein Regen";
    }
}