{"name": "Bautagesbericht", "description": "", "status": "active", "createdBy": "4", "createdOn": "2023-05-17T00:00:00Z", "printOptions": ["8"], "positions": [{"id": 1, "title": "Bautagesbericht", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Arbeitskräfte-Daten von Stunden abrufen", "type": "checkbox"}, {"id": 3, "parentId": 1, "title": "Arbeitskraft #1", "type": "headline"}, {"id": 4, "parentId": 3, "title": "Nr.", "type": "int"}, {"id": 5, "parentId": 3, "title": "Qualifikation", "type": "string"}, {"id": 6, "parentId": 3, "title": "Arbeitszeit Start", "type": "time"}, {"id": 7, "parentId": 3, "title": "Arbeitszeit Ende", "type": "time"}, {"id": 8, "parentId": 3, "title": "Pause Start", "type": "time"}, {"id": 9, "parentId": 3, "title": "<PERSON><PERSON>", "type": "time"}, {"id": 10, "parentId": 1, "title": "Arbeitskraft #2", "type": "headline"}, {"id": 11, "parentId": 10, "title": "Nr.", "type": "int"}, {"id": 12, "parentId": 10, "title": "Qualifikation", "type": "string"}, {"id": 13, "parentId": 10, "title": "Arbeitszeit Start", "type": "time"}, {"id": 14, "parentId": 10, "title": "Arbeitszeit Ende", "type": "time"}, {"id": 15, "parentId": 10, "title": "Pause Start", "type": "time"}, {"id": 16, "parentId": 10, "title": "<PERSON><PERSON>", "type": "time"}, {"id": 17, "parentId": 1, "title": "Arbeitskraft #3", "type": "headline"}, {"id": 18, "parentId": 17, "title": "Nr.", "type": "int"}, {"id": 19, "parentId": 17, "title": "Qualifikation", "type": "string"}, {"id": 20, "parentId": 17, "title": "Arbeitszeit Start", "type": "time"}, {"id": 21, "parentId": 17, "title": "Arbeitszeit Ende", "type": "time"}, {"id": 22, "parentId": 17, "title": "Pause Start", "type": "time"}, {"id": 23, "parentId": 17, "title": "<PERSON><PERSON>", "type": "time"}, {"id": 24, "parentId": 1, "title": "Arbeitskraft #4", "type": "headline"}, {"id": 25, "parentId": 24, "title": "Nr.", "type": "int"}, {"id": 26, "parentId": 24, "title": "Qualifikation", "type": "string"}, {"id": 27, "parentId": 24, "title": "Arbeitszeit Start", "type": "time"}, {"id": 28, "parentId": 24, "title": "Arbeitszeit Ende", "type": "time"}, {"id": 29, "parentId": 24, "title": "Pause Start", "type": "time"}, {"id": 30, "parentId": 24, "title": "<PERSON><PERSON>", "type": "time"}, {"id": 31, "parentId": 1, "title": "Arbeitskraft #5", "type": "headline"}, {"id": 32, "parentId": 31, "title": "Nr.", "type": "int"}, {"id": 33, "parentId": 31, "title": "Qualifikation", "type": "string"}, {"id": 34, "parentId": 31, "title": "Arbeitszeit Start", "type": "time"}, {"id": 35, "parentId": 31, "title": "Arbeitszeit Ende", "type": "time"}, {"id": 36, "parentId": 31, "title": "Pause Start", "type": "time"}, {"id": 37, "parentId": 31, "title": "<PERSON><PERSON>", "type": "time"}, {"id": 38, "parentId": 1, "title": "Tätigkeiten/Leistungsergebnissee", "type": "string"}, {"id": 39, "parentId": 1, "title": "Bemerkungen", "type": "string"}, {"id": 40, "parentId": 1, "title": "Fotodokumentation", "type": "photo"}, {"id": 41, "parentId": 1, "title": "Auftraggeber", "type": "signatureField"}, {"id": 42, "parentId": 1, "title": "<PERSON><PERSON>", "type": "signatureField"}, {"id": 43, "parentId": 1, "title": "<PERSON><PERSON><PERSON>", "type": "signatureField"}]}