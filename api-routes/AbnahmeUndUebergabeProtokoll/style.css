body {
    font-family: Arial, sans-serif;
    border: 0;
    margin: 0;
    padding: 0;
}

.text-sm {
    font-size: 70%;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}

.info-table {
    font-size: 70%;
    width: 100%;
    border: 1px solid black;
}

.info-table tr td {
    vertical-align: top;
}

.info-table tr td:first-child {
    text-align: right;
}

.checkpoints-table {
    width: 100%;
    font-size: 70%;
    margin-top: 5px;
    border-collapse: collapse;
}

.checkpoints-table tr td {
    border: 1px solid black;
}

.checkpoints-table .border-none {
    border: none;
}

.checkpoints-table tr td:first-child {
    text-align: center;
}

.checkpoints-table tr td:nth-child(n+5) {
    padding: 3px;
}

.checkbox {
    font-size: 20px;
}

.comments-table {
    width: 100%;
    font-size: 70%;
    margin-top: 5px;
    border-collapse: collapse;
}

.comments-table tr td {
    border: 1px solid black;
}

.comments-table tr td:first-child {
    border-right: none;
}

.comments-table tr td:nth-child(2) {
    border-left: none;
    text-align: center;
}

.comments-table tr td:nth-child(n+3) {
    padding: 3px;
}

.signature {
    max-height: 40px;
    width: auto;
    object-fit: contain;
    max-width: 100%;
}