{"name": "Abnahme- und Übergabeprotokoll", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 2, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 3, "title": "Masch.-Nr.", "type": "int"}, {"id": 4, "title": "G<PERSON><PERSON>", "type": "string"}, {"id": 5, "title": "Förd.höhe/HS in m", "type": "float"}, {"id": 6, "title": "Förd.höhe/HS in Stk.", "type": "int"}, {"id": 7, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 8, "title": "Montage durch", "type": "string"}, {"id": 9, "title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 10, "parentId": 9, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 11, "parentId": 9, "title": "Bemerkung", "type": "string"}, {"id": 12, "title": "2. Lastverteilung/Unterpallung", "type": "headline"}, {"id": 13, "parentId": 12, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 14, "parentId": 12, "title": "Bemerkung", "type": "string"}, {"id": 15, "title": "3. Verkehrssicherung/Umfeld", "type": "headline"}, {"id": 16, "parentId": 15, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 17, "parentId": 15, "title": "Bemerkung", "type": "string"}, {"id": 18, "title": "4. Plattform und Umwehrung", "type": "headline"}, {"id": 19, "parentId": 18, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 20, "parentId": 18, "title": "Bemerkung", "type": "string"}, {"id": 21, "title": "5. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 22, "parentId": 21, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 23, "parentId": 21, "title": "Bemerkung", "type": "string"}, {"id": 24, "title": "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> Klapprampe", "type": "headline"}, {"id": 25, "parentId": 24, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 26, "parentId": 24, "title": "Bemerkung", "type": "string"}, {"id": 27, "title": "7. <PERSON><PERSON><PERSON><PERSON> zu Wan<PERSON>/<PERSON><PERSON> > 50cm", "type": "headline"}, {"id": 28, "parentId": 27, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 29, "parentId": 27, "title": "Bemerkung", "type": "string"}, {"id": 30, "title": "8. <PERSON><PERSON><PERSON> und <PERSON>", "type": "headline"}, {"id": 31, "parentId": 30, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 32, "parentId": 30, "title": "Bemerkung", "type": "string"}, {"id": 33, "title": "9. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>", "type": "headline"}, {"id": 34, "parentId": 33, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 35, "parentId": 33, "title": "Bemerkung", "type": "string"}, {"id": 36, "title": "10. <PERSON><PERSON> 2 m Stopp", "type": "headline"}, {"id": 37, "parentId": 36, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 38, "parentId": 36, "title": "Bemerkung", "type": "string"}, {"id": 39, "title": "11. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 40, "parentId": 39, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 41, "parentId": 39, "title": "Bemerkung", "type": "string"}, {"id": 42, "title": "12. Probefahrt mit Bremsprobe", "type": "headline"}, {"id": 43, "parentId": 42, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 44, "parentId": 42, "title": "Bemerkung", "type": "string"}, {"id": 45, "title": "13. <PERSON><PERSON> + Verankerungen", "type": "headline"}, {"id": 46, "parentId": 45, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 47, "parentId": 45, "title": "Bemerkung", "type": "string"}, {"id": 48, "title": "14. <PERSON><PERSON>", "type": "headline"}, {"id": 49, "parentId": 48, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 50, "parentId": 48, "title": "Bemerkung", "type": "string"}, {"id": 51, "title": "15. Betriebs-/Notendschalter oben", "type": "headline"}, {"id": 52, "parentId": 51, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 53, "title": "16. <PERSON><PERSON><PERSON>-/Notends<PERSON><PERSON> unten", "type": "headline"}, {"id": 54, "parentId": 53, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 55, "title": "17. <PERSON><PERSON>", "type": "headline"}, {"id": 56, "parentId": 55, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 57, "parentId": 55, "title": "Bemerkung", "type": "string"}, {"id": 58, "title": "18. Bedienungsanleitung übergeben", "type": "headline"}, {"id": 59, "parentId": 58, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 60, "parentId": 58, "title": "Bemerkung", "type": "string"}, {"id": 61, "title": "19. Prüfbuch/Schlüssel übergeben", "type": "headline"}, {"id": 62, "parentId": 61, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 63, "parentId": 61, "title": "Bemerkung", "type": "string"}, {"id": 64, "title": "20. Bauleitung/Aufsicht vor Ort", "type": "headline"}, {"id": 65, "parentId": 64, "title": "Zustand in Ordnung", "type": "checkbox"}, {"id": 66, "parentId": 64, "title": "Bemerkung", "type": "string"}, {"id": 67, "title": "In den ordnungsgemäßen Betrieb wurden eingewiesen", "type": "headline"}, {"id": 68, "parentId": 67, "title": "1. Person", "type": "headline"}, {"id": 69, "parentId": 68, "title": "Vor- und Zuname", "type": "string"}, {"id": 70, "parentId": 68, "title": "Firma", "type": "string"}, {"id": 71, "parentId": 68, "title": "Unterschrift", "type": "signatureField"}, {"id": 72, "parentId": 67, "title": "2. Person", "type": "headline"}, {"id": 73, "parentId": 72, "title": "Vor- und Zuname", "type": "string"}, {"id": 74, "parentId": 72, "title": "Firma", "type": "string"}, {"id": 75, "parentId": 72, "title": "Unterschrift", "type": "signatureField"}, {"id": 76, "parentId": 67, "title": "3. Person", "type": "headline"}, {"id": 77, "parentId": 76, "title": "Vor- und Zuname", "type": "string"}, {"id": 78, "parentId": 76, "title": "Firma", "type": "string"}, {"id": 79, "parentId": 76, "title": "Unterschrift", "type": "signatureField"}, {"id": 80, "parentId": 67, "title": "4. Person", "type": "headline"}, {"id": 81, "parentId": 80, "title": "Vor- und Zuname", "type": "string"}, {"id": 82, "parentId": 80, "title": "Firma", "type": "string"}, {"id": 83, "parentId": 80, "title": "Unterschrift", "type": "signatureField"}, {"id": 84, "parentId": 67, "title": "5. Person", "type": "headline"}, {"id": 85, "parentId": 84, "title": "Vor- und Zuname", "type": "string"}, {"id": 86, "parentId": 84, "title": "Firma", "type": "string"}, {"id": 87, "parentId": 84, "title": "Unterschrift", "type": "signatureField"}, {"id": 88, "parentId": 67, "title": "6. Person", "type": "headline"}, {"id": 89, "parentId": 88, "title": "Vor- und Zuname", "type": "string"}, {"id": 90, "parentId": 88, "title": "Firma", "type": "string"}, {"id": 91, "parentId": 88, "title": "Unterschrift", "type": "signatureField"}, {"id": 92, "parentId": 67, "title": "7. Person", "type": "headline"}, {"id": 93, "parentId": 92, "title": "Vor- und Zuname", "type": "string"}, {"id": 94, "parentId": 92, "title": "Firma", "type": "string"}, {"id": 95, "parentId": 92, "title": "Unterschrift", "type": "signatureField"}, {"id": 96, "parentId": 67, "title": "8. Person", "type": "headline"}, {"id": 97, "parentId": 96, "title": "Vor- und Zuname", "type": "string"}, {"id": 98, "parentId": 96, "title": "Firma", "type": "string"}, {"id": 99, "parentId": 96, "title": "Unterschrift", "type": "signatureField"}, {"id": 100, "title": "Au<PERSON><PERSON>tsf<PERSON>hrender", "type": "headline"}, {"id": 101, "parentId": 100, "title": "Vor- und Zuname", "type": "string"}, {"id": 102, "parentId": 100, "title": "Firma", "type": "string"}, {"id": 103, "parentId": 100, "title": "Unterschrift", "type": "signatureField"}, {"id": 104, "title": "Beanstandungen/Bemer<PERSON>ngen", "type": "headline"}, {"id": 105, "parentId": 104, "title": "1. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 106, "parentId": 105, "title": "Bemerkung", "type": "string"}, {"id": 107, "parentId": 105, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 108, "parentId": 105, "title": "am", "type": "date"}, {"id": 109, "parentId": 105, "title": "durch", "type": "string"}, {"id": 110, "parentId": 104, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 111, "parentId": 110, "title": "Bemerkung", "type": "string"}, {"id": 112, "parentId": 110, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 113, "parentId": 110, "title": "am", "type": "date"}, {"id": 114, "parentId": 110, "title": "durch", "type": "string"}, {"id": 115, "parentId": 104, "title": "3. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 116, "parentId": 115, "title": "Bemerkung", "type": "string"}, {"id": 117, "parentId": 115, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 118, "parentId": 115, "title": "am", "type": "date"}, {"id": 119, "parentId": 115, "title": "durch", "type": "string"}, {"id": 120, "parentId": 104, "title": "4. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 121, "parentId": 120, "title": "Bemerkung", "type": "string"}, {"id": 122, "parentId": 120, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 123, "parentId": 120, "title": "am", "type": "date"}, {"id": 124, "parentId": 120, "title": "durch", "type": "string"}, {"id": 125, "parentId": 104, "title": "5. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 126, "parentId": 125, "title": "Bemerkung", "type": "string"}, {"id": 127, "parentId": 125, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox"}, {"id": 128, "parentId": 125, "title": "am", "type": "date"}, {"id": 129, "parentId": 125, "title": "durch", "type": "string"}, {"id": 130, "title": "Gegen den Betrieb liegen keine sicherheitstechnischen Bedenken vor.", "type": "checkbox"}, {"id": 131, "title": "Nach Erledigung der o.g. Mängel liegen gegen den Betrieb keine sicherheitstechnischen Bedenken vor.", "type": "checkbox"}, {"id": 132, "title": "Übergabe an", "type": "headline"}, {"id": 133, "parentId": 132, "title": "Name", "type": "string"}, {"id": 134, "parentId": 132, "title": "Ort", "type": "string"}, {"id": 135, "parentId": 132, "title": "Datum", "type": "date"}, {"id": 136, "parentId": 132, "title": "Unterschrift", "type": "signatureField"}, {"id": 137, "title": "Prüfung/Einweisung durchgeführt", "type": "headline"}, {"id": 138, "parentId": 137, "title": "Name", "type": "string"}, {"id": 139, "parentId": 137, "title": "Ort", "type": "string"}, {"id": 140, "parentId": 137, "title": "Datum", "type": "date"}, {"id": 141, "parentId": 137, "title": "Unterschrift", "type": "signatureField"}]}