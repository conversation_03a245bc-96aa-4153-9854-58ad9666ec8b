<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_AbnahmeUndUebergabeProtokoll())->getData($_GET['schemaId'], $_GET['documentId']);
}

/** @param array<string, mixed> $data */
function displayCheck(array $data, string $title): void
{
    if (!isset($data[$title])) {
        echo '<td class="checkbox text-center" style="width: 2%">&#9744;</td>';
        echo '<td class="checkbox text-center" style="width: 2%">&#9744;</td>';
    } else {
        if (PrintoutHelper::isCheckboxTickedByReportedValue($data[$title])) {
            echo '<td class="checkbox text-center" style="width: 2%">&#9745;</td>';
            echo '<td class="checkbox text-center" style="width: 2%">&#9744;</td>';
        } else {
            echo '<td class="checkbox text-center" style="width: 2%">&#9744;</td>';
            echo '<td class="checkbox text-center" style="width: 2%">&#9745;</td>';
        }
    }
}

/** @param array<string, mixed> $data */
function echoSingleCheck(array $data, string $title): void
{
    if (!isset($data[$title])) {
        echo "&#9744;";
    } else {
        PrintoutHelper::echoCheckboxByReportedValue($data[$title]);
    }
}

?>

<!DOCTYPE html>
<html lang="de">
<head>
    <title>AbnahmeUndUebergabeProtokoll</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body style="font-family: Arial, sans-serif">
<div class="text-sm">
    <div style="margin-bottom: 10px">
        <b style="font-size: 160%">Abnahme- und Übergabeprotokoll</b><span style="margin-left: 280px">rev.1</span><br>
        <span style="font-size: 110%">Prüfung vor Inbetriebnahme nach §14 BetrSichV</span>
    </div>
    <b style="font-size: 110%">Formular für Alimak Hek-Transportbühnen und Baugüteraufzüge</b><br>
    <table class="info-table">
        <tr>
            <td>Baustelle:</td>
            <td colspan="4" style="width:40%"><?= $data['Abnahme- und Übergabeprotokoll Baustelle'] ?? '' ?></td>
            <td class="text-right" style="width:9%; padding-left: 10px"> Betreiber:</td>
            <td style="width: 40%"><?= $data['Abnahme- und Übergabeprotokoll Betreiber'] ?? '' ?></td>
        </tr>
        <tr>
            <td>Standort:</td>
            <td colspan="4"><?= $data['Abnahme- und Übergabeprotokoll Standort'] ?? '' ?></td>
            <td class="text-right" style="padding-left: 10px; width:10%">Montage durch:</td>
            <td rowspan="4" style="width: 45%"><?= $data['Abnahme- und Übergabeprotokoll Montage durch'] ?? '' ?></td>
        </tr>
        <tr>
            <td>Masch.-Nr.:</td>
            <td colspan="4"><?= $data['Abnahme- und Übergabeprotokoll Masch.-Nr.'] ?? '' ?></td>
        </tr>
        <tr>
            <td>Gerät:</td>
            <td colspan="4"><?= $data['Abnahme- und Übergabeprotokoll Gerät'] ?? '' ?></td>
        </tr>
        <tr>
            <td>Förd.höhe/HS:</td>
            <td style="width:13.5%; text-align: right"><?= $data['Abnahme- und Übergabeprotokoll Förd.höhe/HS in m'] ?? '' ?></td>
            <td style="width:3%; text-align: left">m/</td>
            <td style="width:13.5%; text-align: right"><?= $data['Abnahme- und Übergabeprotokoll Förd.höhe/HS in Stk.'] ?? '' ?></td>
            <td style="width: 5%; text-align: left">Stk.</td>
        </tr>
    </table>
    <table class="checkpoints-table">
        <tr class="text-center">
            <td colspan="2" rowspan="2" style="width: 20%; font-size: 110%"><b>Kontrollpunkte</b></td>
            <td colspan="2" style="width: 4%; font-size: 75%">Zustand/Funktion in Ordnung</td>
            <td rowspan="2" style="width: 17%; font-size: 110%"><b>Bemerkung</b></td>
            <td rowspan="2" class="border-none" style="width: 3%"></td>
            <td rowspan="2" colspan="3" style="width: 56%; font-size: 110%">
                <b>In den ordnungsgemäßen Betrieb wurden eingewiesen:</b>
            </td>
        </tr>
        <tr class="text-center" style="font-size: 75%">
            <td style="vertical-align: bottom; padding-top: 3px; width: 2%">ja</td>
            <td style="vertical-align: bottom; padding-top: 3px; width: 2%">nein</td>
        </tr>
        <tr>
            <td>1.</td>
            <td>Stromzufuhr</td>
            <?php displayCheck($data, '1. Stromzufuhr Zustand in Ordnung') ?>
            <td style="max-height: 20px"><?= $data['1. Stromzufuhr Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td class="text-center" style="width: 20%; font-size: 110%"><b>Vor- und Zuname:</b></td>
            <td class="text-center" style="width: 12%; font-size: 110%"><b>Firma:</b></td>
            <td class="text-center" style="width: 15%; font-size: 110%"><b>Unterschrift:</b></td>
        </tr>
        <tr>
            <td>2.</td>
            <td>Lastverteilung/Unterpallung</td>
            <?php displayCheck($data, '2. Lastverteilung/Unterpallung Zustand in Ordnung') ?>
            <td>
                <span style="max-height: 20px"><?= $data['2. Lastverteilung/Unterpallung Bemerkung'] ?? '' ?></span>
            </td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['1. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['1. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['1. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['1. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>3.</td>
            <td>Verkehrssicherung/Umfeld</td>
            <?php displayCheck($data, '3. Verkehrssicherung/Umfeld Zustand in Ordnung') ?>
            <td><?= $data['3. Verkehrssicherung/Umfeld Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>4.</td>
            <td>Plattform und Umwehrung</td>
            <?php displayCheck($data, '4. Plattform und Umwehrung Zustand in Ordnung') ?>
            <td><?= $data['4. Plattform und Umwehrung Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['2. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['2. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['2. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['2. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>5.</td>
            <td>Beschilderung</td>
            <?php displayCheck($data, '5. Beschilderung Zustand in Ordnung') ?>
            <td><?= $data['5. Beschilderung Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>6.</td>
            <td>Zustiegstür und Klapprampe</td>
            <?php displayCheck($data, '6. Zustiegstür und Klapprampe Zustand in Ordnung') ?>
            <td><?= $data['6. Zustiegstür und Klapprampe Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['3. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['3. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['3. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['3. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>7.</td>
            <td>Abstand zu Wand/Gerüst > 50cm</td>
            <?php displayCheck($data, '7. Abstand zu Wand/Gerüst > 50cm Zustand in Ordnung') ?>
            <td><?= $data['7. Abstand zu Wand/Gerüst > 50cm Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>8.</td>
            <td>Freier Lauf Plattform und Kabel</td>
            <?php displayCheck($data, '8. Freier Lauf Plattform und Kabel Zustand in Ordnung') ?>
            <td><?= $data['8. Freier Lauf Plattform und Kabel Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['4. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['4. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['4. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['4. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>9.</td>
            <td>Funktionsprüfung Auf, Ab, Etage</td>
            <?php displayCheck($data, '9. Funktionsprüfung Auf, Ab, Etage Zustand in Ordnung') ?>
            <td><?= $data['9. Funktionsprüfung Auf, Ab, Etage Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>10.</td>
            <td> Funktion 2 m Stopp</td>
            <?php displayCheck($data, '10. Funktion 2 m Stopp Zustand in Ordnung') ?>
            <td><?= $data['10. Funktion 2 m Stopp Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['5. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['5. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['5. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['5. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>11.</td>
            <td>Notablass</td>
            <?php displayCheck($data, '11. Notablass Zustand in Ordnung') ?>
            <td><?= $data['11. Notablass Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>12.</td>
            <td>Probefahrt mit Bremsprobe</td>
            <?php displayCheck($data, '12. Probefahrt mit Bremsprobe Zustand in Ordnung') ?>
            <td><?= $data['12. Probefahrt mit Bremsprobe Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['6. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['6. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['6. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['6. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>13.</td>
            <td>Maste + Verankerungen</td>
            <?php displayCheck($data, '13. Maste + Verankerungen Zustand in Ordnung') ?>
            <td><?= $data['13. Maste + Verankerungen Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>14.</td>
            <td>Funktion Haltestellen</td>
            <?php displayCheck($data, '14. Funktion Haltestellen Zustand in Ordnung') ?>
            <td><?= $data['14. Funktion Haltestellen Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['7. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['7. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['7. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['7. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>15.</td>
            <td>Betriebs-/Notendschalter oben</td>
            <?php displayCheck($data, '15. Betriebs-/Notendschalter oben Zustand in Ordnung') ?>
            <td>incl. Schaltkurven</td>
        </tr>
        <tr>
            <td>16.</td>
            <td>Betriebs-/Notendschalter unten</td>
            <?php displayCheck($data, '16. Betriebs-/Notendschalter unten Zustand in Ordnung') ?>
            <td>incl. Schaltkurven</td>
            <td class="border-none"></td>
            <td rowspan="2"><?= $data['8. Person Vor- und Zuname'] ?? '' ?></td>
            <td rowspan="2"><?= $data['8. Person Firma'] ?? '' ?></td>
            <td rowspan="2">
                <?php if (isset($data['8. Person Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['8. Person Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>17.</td>
            <td>Fangtest</td>
            <?php displayCheck($data, '17. Fangtest Zustand in Ordnung') ?>
            <td><?= $data['17. Fangtest Bemerkung'] ?? '' ?></td>
        </tr>
        <tr>
            <td>18.</td>
            <td>Bedienungsanleitung übergeben</td>
            <?php displayCheck($data, '18. Bedienungsanleitung übergeben Zustand in Ordnung') ?>
            <td><?= $data['18. Bedienungsanleitung übergeben Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td style="text-align: center; border-bottom: none; border-collapse: collapse; vertical-align: top; padding-top: 10px">
                <b>Aufsichtsführender:</b>
            </td>
            <td rowspan="3"><?= $data['Aufsichtsführender Firma'] ?? '' ?></td>
            <td rowspan="3">
                <?php if (isset($data['Aufsichtsführender Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['Aufsichtsführender Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td>19.</td>
            <td>Prüfbuch/Schlüssel übergeben</td>
            <?php displayCheck($data, '19. Prüfbuch/Schlüssel übergeben Zustand in Ordnung') ?>
            <td><?= $data['19. Prüfbuch/Schlüssel übergeben Bemerkung'] ?? '' ?></td>
            <td class="border-none"></td>
            <td rowspan="2" style="border-top: none; vertical-align: top">
                <?= $data['Aufsichtsführender Vor- und Zuname'] ?? '' ?>
            </td>
        </tr>
        <tr>
            <td>20.</td>
            <td>Bauleitung/Aufsicht vor Ort</td>
            <?php displayCheck($data, '20. Bauleitung/Aufsicht vor Ort Zustand in Ordnung') ?>
            <td><?= $data['20. Bauleitung/Aufsicht vor Ort Bemerkung'] ?? '' ?></td>
        </tr>
    </table>
    <div class="text-sm" style="border: 1px solid black; margin-top: 5px">
        <div style="text-align: center; padding-top: 4px; padding-bottom: 4px; border-bottom: 1px solid black">
            <span style="padding-right: 20px">&#9888;</span>
            <b style="font-size: 110%; width: 100%; text-align: center">
                <u style="text-align: center">
                    Bedienung der Anlage nur durch eingewiesenes Personal. Wichtiger Auszug aus BGI 825 (und BGR 500 sinngemäß):
                </u>
            </b>
            <span style="padding-left: 20px">&#9888;</span>
        </div>
        <div style="text-indent: 10px">
            Der Unternehmer darf mit dem selbständigen Führen von Transportbühnen (bzw. Baugüteraufzügen) nur Versicherte beschäftigen,
        </div>
        <br>
        <span style="padding-left: 10px"> - die das 18. Lebensjahr vollendet haben, </span> <br>
        <span style="padding-left: 10px"> - bei denen gesundheitlich keine Bedenken gegen diese Tätigkeit bestehen, </span>
        <br>
        <span style="padding-left: 10px"> - die im Führen von Transportbühnen unterwiesen sind und ihre Befähigung hierzu ggü. dem Unternehmernachgewiesen haben, </span>
        <br>
        <span style="padding-left: 10px"> - von denen zu erwarten ist, dass sie die ihnen übertragenen Aufgaben zuverlässig erfüllen. </span>
        <br>
        <span style="padding-left: 10px"> - Transportbühnenführer müssen vom Unternehmer schriftlich beauftragt sein. </span>
        <br>
        <span style="padding-left: 10px"> - Der Transportbühnenführer/Aufzugsführer hat täglich vor Arbeitsbeginn die Funktion der Betriebsendschalter und
            durch Inaugenscheinnahme die gesamte Transportbühne/Aufzug einschließlich der </span>
        <div style="text-indent: 5px; padding-left: 10px">
            Fahrbahn, der Verankerungen sowie der Hinweis- und Warnschilder zu prüfen.
        </div>
        <span style="padding-left: 10px"> - Der Transportbühnenführer, das Montagepersonal und die Benutzer der Transportbühne haben die Betriebsanleitung und die Betriebsanweisung zu beachten. </span>
    </div>
    <table class="comments-table">
        <tr>
            <td style="width:50%">
                <span style="font-size: 110%">Beanstandungen/Bemerkungen</span>
                <span style="font-size: 90%"> (ggf. Formular-Rückseite benutzen):</span>
            </td>
            <td style="font-size: 110%; text-align: center; width: 10%">erledigt</td>
            <td style="font-size: 110%; text-align: center; width: 7%">am:</td>
            <td style="font-size: 110%; text-align: center; width: 33%">durch:</td>
        </tr>
        <tr>
            <td><?= $data['1. Bemerkung Bemerkung'] ?? '' ?></td>
            <td class="checkbox">
                <?php echoSingleCheck($data, '1. Bemerkung erledigt') ?>
            </td>
            <td>
                <?php if (isset($data['1. Bemerkung am'])) {
                    echo(date('d.m.Y', strtotime($data['1. Bemerkung am'])));
                } ?>
            </td>
            <td><?= $data['1. Bemerkung durch'] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= $data['2. Bemerkung Bemerkung'] ?? '' ?></td>
            <td class="checkbox">
                <?php echoSingleCheck($data, '2. Bemerkung erledigt') ?>
            </td>
            <td>
                <?php if (isset($data['2. Bemerkung am'])) {
                    echo(date('d.m.Y', strtotime($data['2. Bemerkung am'])));
                } ?>
            </td>
            <td><?= $data['2. Bemerkung durch'] ?? '' ?></td>
        <tr>
            <td><?= $data['3. Bemerkung Bemerkung'] ?? '' ?></td>
            <td class="checkbox">
                <?php echoSingleCheck($data, '3. Bemerkung erledigt') ?>
            </td>
            <td>
                <?php if (isset($data['3. Bemerkung am'])) {
                    echo(date('d.m.Y', strtotime($data['3. Bemerkung am'])));
                } ?>
            </td>
            <td><?= $data['3. Bemerkung durch'] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= $data['4. Bemerkung Bemerkung'] ?? '' ?></td>
            <td class="checkbox">
                <?php echoSingleCheck($data, '4. Bemerkung erledigt') ?>
            </td>
            <td>
                <?php if (isset($data['4. Bemerkung am'])) {
                    echo(date('d.m.Y', strtotime($data['4. Bemerkung am'])));
                } ?>
            </td>
            <td><?= $data['4. Bemerkung durch'] ?? '' ?></td>
        </tr>
        <tr>
            <td><?= $data['5. Bemerkung Bemerkung'] ?? '' ?></td>
            <td class="checkbox">
                <?php echoSingleCheck($data, '5. Bemerkung erledigt') ?>
            </td>
            <td>
                <?php if (isset($data['5. Bemerkung am'])) {
                    echo(date('d.m.Y', strtotime($data['5. Bemerkung am'])));
                } ?>
            </td>
            <td><?= $data['5. Bemerkung durch'] ?? '' ?></td>
        </tr>
    </table>
    <table class="text-sm" style="width: 100%; border-collapse: collapse">
        <tr style="border-bottom: 1px solid black; border-left: 1px solid black; border-right: 1px solid black">
            <td class="checkbox" style="text-align: center">
                <?php echoSingleCheck($data, 'Abnahme- und Übergabeprotokoll Gegen den Betrieb liegen keine sicherheitstechnischen Bedenken vor.') ?>
            </td>
            <td style="padding-left: 20px">Gegen den Betrieb liegen keine sicherheitstechnischen Bedenken vor.</td>
        </tr>
        <tr style="border-bottom: 1px solid black; border-left: 1px solid black; border-right: 1px solid black">
            <td class="checkbox" style="text-align: center">
                <?php echoSingleCheck($data, 'Abnahme- und Übergabeprotokoll Nach Erledigung der o.g. Mängel liegen gegen den Betrieb keine sicherheitstechnischen Bedenken vor.') ?>
            </td>
            <td style="padding-left: 20px">
                Nach Erledigung der o.g. Mängel liegen gegen den Betrieb keine sicherheitstechnischen Bedenken vor.
            </td>
        </tr>
    </table>
    <table class="text-sm" style="width: 100%; border-collapse: collapse; margin-top: 5px">
        <tr>
            <td style="width: 50%; border-right: 1px solid black; border-left: 1px solid black; border-top: 1px solid black">
                Übergabe an:
            </td>
            <td style="width: 50%; border-right: 1px solid black; border-top: 1px solid black">
                Prüfung/Einweisung durchgeführt:
            </td>
        </tr>
        <tr>
            <td style="width: 50%; border-left: 1px solid black; border-bottom: 1px solid black; height: 30px">
                <?php
                $parts = [];
                if (isset($data['Übergabe an Name']) && $data['Übergabe an Name']) {
                    $parts[] = $data['Übergabe an Name'];
                }
                if (isset($data['Übergabe an Ort']) && $data['Übergabe an Ort']) {
                    $parts[] = $data['Übergabe an Ort'];
                }
                if (isset($data['Übergabe an Datum'])) {
                    $parts[] = date('d.m.Y', strtotime($data['Übergabe an Datum']));
                }
                echo implode(", ", $parts);

                if (isset($data['Übergabe an Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['Übergabe an Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
            <td style="width: 50%; border-left: 1px solid black; border-bottom: 1px solid black; border-right: 1px solid black">
                <?php
                $parts = [];
                if (isset($data['Prüfung/Einweisung durchgeführt Name']) &&
                    $data['Prüfung/Einweisung durchgeführt Name']) {
                    $parts[] = $data['Prüfung/Einweisung durchgeführt Name'];
                }
                if (isset($data['Prüfung/Einweisung durchgeführt Ort']) && $data['Prüfung/Einweisung durchgeführt Ort']) {
                    $parts[] = $data['Prüfung/Einweisung durchgeführt Ort'];
                }
                if (isset($data['Prüfung/Einweisung durchgeführt Datum'])) {
                    $parts[] = date('d.m.Y', strtotime($data['Prüfung/Einweisung durchgeführt Datum']));
                }
                echo implode(", ", $parts);

                if (isset($data['Prüfung/Einweisung durchgeführt Unterschrift'])) { ?>
                    <img class="signature" src="<?= $data['Prüfung/Einweisung durchgeführt Unterschrift'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td style="width: 50%; border-bottom: 1px solid black; border-right: 1px solid black; border-left: 1px solid black">
                Name, Ort, Datum, Unterschrift
            </td>
            <td style="width: 50%; border-bottom: 1px solid black; border-right: 1px solid black">
                Name, Ort, Datum, Unterschrift
            </td>
        </tr>
    </table>
</div>
</body>
</html>
