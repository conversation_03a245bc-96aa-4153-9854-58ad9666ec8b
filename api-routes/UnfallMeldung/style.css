body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    margin: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    border-radius: 5px;
}

.form-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px black solid;
}

table.form-table .label-row td {
    border: none;
}

.form-table th, .form-table td {
    padding: 12px 15px;
    border-bottom: 1px solid black;
}

.field-row td {
    padding-top: 0;
}

.form-table tr td:first-child {
    border-right: none;
}

.form-table tr td:not(:first-child) {
    border-left: none;
}

.form-table th {
    text-align: left;
    font-weight: normal;
}

.section-header {
    font-size: 20px;
    background-color: #dcdcdc;
    font-weight: bold !important;
    text-align: left;
    padding: 10px;
}
