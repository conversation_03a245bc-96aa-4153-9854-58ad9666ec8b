<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_UnfallMeldung
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $hierarchicalDocument = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        return PrintoutHelper::mapDocumentChildrenToValues($hierarchicalDocument['fullDocument'], true);
    }
}