<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_UnfallMeldung())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @throws Exception
 */
function formatDateTime(string|null $date = null, string|null $time = null): string
{
    if ($date == null && $time == null) {
        return "";
    }
    if ($date == null) {
        return $time;
    }
    $dateString = (new DateTime($date))->format('d.m.Y');
    if ($time == null) {
        return $dateString;
    }
    return $dateString . ' ' . $time;
}

/**
 * @param array<string, mixed> $data
 */
function displayField(array $data, string $field, string $default = ''): string
{
    return $data[$field] ?? $default;
}

?>

<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Unfallmeldung</title>
</head>
<body>
<div class="container">
    <table class="form-table">
        <!-- Section Header -->
        <tr>
            <th colspan="2" class="section-header">Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens</th>
        </tr>
        <tr class="label-row">
            <td>Name der/des Verletzten bzw. Erkrankten</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Name der/des Verletzten bzw. Erkrankten')) ?></td>
        </tr>
        <tr class="label-row">
            <td>Datum/Uhrzeit</td>
        </tr>
        <tr class="field-row">
            <td>
                <?=
                /** @noinspection PhpUnhandledExceptionInspection */
                formatDateTime(
                    $data['Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Datum'] ?? null,
                    $data['Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Uhrzeit'] ?? null
                ) ?>
            </td>
        </tr>
        <tr class="label-row">
            <td>Abteilung/Arbeitsbereich</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Abteilung/Arbeitsbereich')) ?></td>
        </tr>
        <tr class="label-row">
            <td>Hergang</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Hergang')) ?></td>
        </tr>

        <tr class="label-row">
            <td>Art und Umfang der Verletzung/Erkrankung</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Art und Umfang der Verletzung/Erkrankung')) ?></td>
        </tr>
        <tr class="label-row">
            <td>Name der Zeugen</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Angaben zum Hergang des Unfalls bzw. des Gesundheitsschadens Name der Zeugen')) ?></td>
        </tr>
        <tr>
            <th colspan="2" class="section-header">Erste-Hilfe-Leistungen</th>
        </tr>
        <tr class="label-row">
            <td>Datum/Uhrzeit</td>
        </tr>
        <tr class="field-row">
            <td>
                <?=
                /** @noinspection PhpUnhandledExceptionInspection */
                formatDateTime(
                    $data['Erste-Hilfe-Leistungen Datum'] ?? null,
                    $data['Erste-Hilfe-Leistungen Uhrzeit'] ?? null
                ) ?>
            </td>
        </tr>
        <tr class="label-row">
            <td>Art und Weise der Maßnahme</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Erste-Hilfe-Leistungen Art und Weise der Maßnahme')) ?></td>
        </tr>
        <tr class="label-row">
            <td>Name des Erste-Hilfe-Leistenden</td>
        </tr>
        <tr class="field-row">
            <td><?= nl2br(displayField($data, 'Erste-Hilfe-Leistungen Name des Erste-Hilfe-Leistenden')) ?></td>
        </tr>
    </table>
</div>
</body>
</html>
