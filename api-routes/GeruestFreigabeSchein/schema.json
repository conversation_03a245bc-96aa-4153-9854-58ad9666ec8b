{"name": "Gerüstfreigabeschein", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "Auftraggeber (Holcim Ansprechperson)", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Name", "type": "string"}, {"id": 3, "parentId": 1, "title": "Tel.", "type": "string"}, {"id": 4, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(befähigte Person)", "type": "headline"}, {"id": 5, "parentId": 4, "title": "Name", "type": "string"}, {"id": 6, "parentId": 4, "title": "Tel.", "type": "string"}, {"id": 7, "title": "Verantwortliche<PERSON> (Firma/Ansprechperson)", "type": "headline"}, {"id": 8, "parentId": 7, "title": "Name", "type": "string"}, {"id": 9, "parentId": 7, "title": "Tel.", "type": "string"}, {"id": 10, "title": "Arbeitsort (HAC/Gebäude/Anlage, ...)", "type": "string"}, {"id": 11, "title": "Unterschrift 1: Freigabe (Gerüstersteller)", "type": "headline"}, {"id": 12, "parentId": 11, "title": "Datum", "type": "date"}, {"id": 13, "parentId": 11, "title": "Name", "type": "string"}, {"id": 14, "parentId": 11, "title": "Unterschrift", "type": "signatureField"}, {"id": 15, "title": "Unterschrift 2: <PERSON><PERSON><PERSON><PERSON> (Verantw. Gerüstnutzer)", "type": "headline"}, {"id": 16, "parentId": 15, "title": "Datum", "type": "date"}, {"id": 17, "parentId": 15, "title": "Name", "type": "string"}, {"id": 18, "parentId": 15, "title": "Unterschrift", "type": "signatureField"}, {"id": 19, "default_collapse": true, "title": "Unterschrift 3", "type": "headline"}, {"id": 20, "parentId": 19, "title": "Datum", "type": "date"}, {"id": 21, "parentId": 19, "title": "Name", "type": "string"}, {"id": 22, "parentId": 19, "title": "Unterschrift", "type": "signatureField"}, {"id": 23, "default_collapse": true, "title": "Unterschrift 4", "type": "headline"}, {"id": 24, "parentId": 23, "title": "Datum", "type": "date"}, {"id": 25, "parentId": 23, "title": "Name", "type": "string"}, {"id": 26, "parentId": 23, "title": "Unterschrift", "type": "signatureField"}, {"id": 27, "default_collapse": true, "title": "Unterschrift 5", "type": "headline"}, {"id": 28, "parentId": 27, "title": "Datum", "type": "date"}, {"id": 29, "parentId": 27, "title": "Name", "type": "string"}, {"id": 30, "parentId": 27, "title": "Unterschrift", "type": "signatureField"}, {"id": 31, "default_collapse": true, "title": "Unterschrift 6", "type": "headline"}, {"id": 32, "parentId": 31, "title": "Datum", "type": "date"}, {"id": 33, "parentId": 31, "title": "Name", "type": "string"}, {"id": 34, "parentId": 31, "title": "Unterschrift", "type": "signatureField"}, {"id": 35, "default_collapse": true, "title": "Unterschrift 7", "type": "headline"}, {"id": 36, "parentId": 35, "title": "Datum", "type": "date"}, {"id": 37, "parentId": 35, "title": "Name", "type": "string"}, {"id": 38, "parentId": 35, "title": "Unterschrift", "type": "signatureField"}, {"id": 39, "default_collapse": true, "title": "Unterschrift 8", "type": "headline"}, {"id": 40, "parentId": 39, "title": "Datum", "type": "date"}, {"id": 41, "parentId": 39, "title": "Name", "type": "string"}, {"id": 42, "parentId": 39, "title": "Unterschrift", "type": "signatureField"}, {"id": 43, "default_collapse": true, "title": "Unterschrift 9", "type": "headline"}, {"id": 44, "parentId": 43, "title": "Datum", "type": "date"}, {"id": 45, "parentId": 43, "title": "Name", "type": "string"}, {"id": 46, "parentId": 43, "title": "Unterschrift", "type": "signatureField"}, {"id": 47, "default_collapse": true, "title": "Unterschrift 10", "type": "headline"}, {"id": 48, "parentId": 47, "title": "Datum", "type": "date"}, {"id": 49, "parentId": 47, "title": "Name", "type": "string"}, {"id": 50, "parentId": 47, "title": "Unterschrift", "type": "signatureField"}, {"id": 51, "default_collapse": true, "title": "Unterschrift 11", "type": "headline"}, {"id": 52, "parentId": 51, "title": "Datum", "type": "date"}, {"id": 53, "parentId": 51, "title": "Name", "type": "string"}, {"id": 54, "parentId": 51, "title": "Unterschrift", "type": "signatureField"}, {"id": 55, "default_collapse": true, "title": "Unterschrift 12", "type": "headline"}, {"id": 56, "parentId": 55, "title": "Datum", "type": "date"}, {"id": 57, "parentId": 55, "title": "Name", "type": "string"}, {"id": 58, "parentId": 55, "title": "Unterschrift", "type": "signatureField"}, {"id": 59, "default_collapse": true, "title": "Unterschrift 13", "type": "headline"}, {"id": 60, "parentId": 59, "title": "Datum", "type": "date"}, {"id": 61, "parentId": 59, "title": "Name", "type": "string"}, {"id": 62, "parentId": 59, "title": "Unterschrift", "type": "signatureField"}, {"id": 63, "title": "<PERSON><PERSON><PERSON> zum Gerüst nach DIN EN 12811 oder DIN 4420-1", "type": "headline"}, {"id": 64, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 63}, {"id": 65, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 63}, {"id": 66, "title": "Treppenturm", "type": "checkbox", "parentId": 63}, {"id": 67, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 63}, {"id": 68, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 63}, {"id": 69, "title": "Podest", "type": "checkbox", "parentId": 63}, {"id": 70, "title": "Fahrgerüst / Rollgerüst", "type": "checkbox", "parentId": 63}, {"id": 71, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 63}, {"id": 72, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "parentId": 63}, {"id": 73, "parentId": 63, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 74, "parentId": 73, "title": "Beschreibung Sondergerüst", "type": "string"}, {"id": 75, "parentId": 63, "title": "Lastklasse", "type": "headline"}, {"id": 76, "parentId": 75, "title": "2  (1,5 kN/m²)", "type": "checkbox"}, {"id": 77, "parentId": 75, "title": "3  (2,0 kN/m²)", "type": "checkbox"}, {"id": 78, "parentId": 75, "title": "4  (3,0 kN/m²)", "type": "checkbox"}, {"id": 79, "parentId": 75, "title": "Andere Lastklasse", "type": "headline"}, {"id": 80, "parentId": 79, "title": "Beschreibung der Lastklasse", "type": "string"}, {"id": 81, "parentId": 63, "title": "Breitenklasse", "type": "headline"}, {"id": 82, "parentId": 81, "title": "W 06 (60 cm)", "type": "checkbox"}, {"id": 83, "parentId": 81, "title": "W 09 (90cm)", "type": "checkbox"}, {"id": 84, "parentId": 81, "title": "Andere Breitenklasse", "type": "headline"}, {"id": 85, "title": "Beschreibung der Breitenklasse", "parentId": 84, "type": "string"}, {"id": 86, "title": "Bekleidung", "type": "headline"}, {"id": 87, "title": "<PERSON>zen", "type": "checkbox"}, {"id": 88, "title": "Planen", "type": "checkbox"}, {"id": 89, "title": "teilweise mit Netzen/Planen", "type": "checkbox"}, {"id": 90, "title": "Prüfungsanweisung X - angekreuzt = geprüft und in Ordnung", "type": "headline"}, {"id": 91, "parentId": 90, "title": "1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 92, "parentId": 91, "title": "Alle Beläge, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> und Halterungen sind augenscheinlich unbeschädigt", "type": "checkbox"}, {"id": 93, "parentId": 90, "title": "2. Standsicherheit", "type": "headline"}, {"id": 94, "parentId": 93, "title": "Tragfähigkeit der Aufstandfläche", "type": "checkbox"}, {"id": 95, "parentId": 93, "title": "Lastverteilung ausreichend", "type": "checkbox"}, {"id": 96, "parentId": 93, "title": " Verstrebungen ausreichend", "type": "checkbox"}, {"id": 97, "parentId": 93, "title": " Gitterträger mit Druckgurtaussteifungen", "type": "checkbox"}, {"id": 100, "parentId": 93, "title": " Sonderkonstruktion nach Vorgabe ", "type": "checkbox"}, {"id": 101, "parentId": 93, "title": " Fahrrollen, Bremsen", "type": "checkbox"}, {"id": 102, "parentId": 93, "title": "Spindelauszugslänge", "type": "checkbox"}, {"id": 103, "parentId": 93, "title": "Verankerungen nach Montageanweisung", "type": "checkbox"}, {"id": 104, "parentId": 90, "title": "3. <PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 105, "parentId": 104, "title": "Rahmentafeln/Gerüstbohlen gegen Abheben gesichert", "type": "checkbox"}, {"id": 106, "parentId": 104, "title": "Gerüstbohlen gegen W<PERSON>pen g<PERSON>t", "type": "checkbox"}, {"id": 107, "parentId": 104, "title": "Genutzte Gerüstlagen sind vollständig mit Bohlen oder Rahmentafeln ausgelegt", "type": "checkbox"}, {"id": 108, "parentId": 104, "title": "Der Belag ist um die Ecke in voller Breite herumgeführt", "type": "checkbox"}, {"id": 109, "parentId": 90, "title": "4. Arbeits- und Betriebssicherheit", "type": "headline"}, {"id": 110, "parentId": 109, "title": "Seitenschutz 3-teilig (Geländerholm, Zwischenholm, sind augenscheinlich unbeschädigt Bordbrett - beidseitig)", "type": "checkbox"}, {"id": 111, "parentId": 109, "title": " Seitenschutz 3-teilig an Stirnseiten und Öffnungen", "type": "checkbox"}, {"id": 112, "parentId": 109, "title": "Wandabstand max. 0,30 m", "type": "checkbox"}, {"id": 113, "parentId": 109, "title": "Aufstiege, Zugänge (besser innenliegend - stolperfrei)", "type": "checkbox"}, {"id": 114, "parentId": 109, "title": "Verkehrssicherung (Kanten, Ecken, Schwellen)", "type": "checkbox"}, {"id": 115, "parentId": 109, "title": "Beleuchtung angemessen", "type": "checkbox"}, {"id": 116, "parentId": 90, "title": "5. <PERSON>forderungen an Schutzgerüste", "type": "headline"}, {"id": 117, "parentId": 116, "title": "Belagfläche bei Dachfanggerüst (> 0,60 m Breite)", "type": "checkbox"}, {"id": 118, "parentId": 116, "title": "Belag bei Dachfanggerüst (< 1,5 m unter Absturzkante)", "type": "checkbox"}, {"id": 119, "parentId": 116, "title": "Min. Abstand Schutzwand und Absturzkante (> 0,70 m)", "type": "checkbox"}, {"id": 120, "parentId": 90, "title": "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 121, "parentId": 120, "title": "Kennzeichnung an allen Zugängen angebracht", "type": "checkbox"}, {"id": 122, "parentId": 120, "title": "Weitere Checkbox", "type": "headline"}, {"id": 123, "parentId": 122, "title": "Name der Prüfung", "type": "string"}, {"id": 124, "parentId": 120, "title": "Weitere Checkbox 2", "type": "headline"}, {"id": 125, "parentId": 124, "title": "Name der Prüfung 2", "type": "string"}]}