<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Geruestfreigabeschein())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!DOCTYPE html>
<html lang="de">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title></title>

    <style>
        @page {
            size: 210mm 297mm;
        }

        body {
            width: 230mm;
            height: 100%;
            text-align: -webkit-center;
            margin: 0 auto;
            padding: 0;
        }

        * {
            box-sizing: border-box;
        }

        .page {
            page-break-before: always;
            max-height: 297mm;
            min-height: 297mm;
            min-width: 210mm;
            max-width: 210mm;
            padding: 5mm;
            box-sizing: border-box;
        }

        .rotate-90 {
            display: block;
            transform: rotate(90deg);
        }

        .signature {
            max-height: 35px;
            object-fit: contain;
            max-width: 100%;
        }

        .padding-left {
            padding-left: 5px;
        }

        .title {
            font-style: italic;
            padding-top: 3px;
            padding-bottom: 3px;
        }

        .table-style {
            padding-top: 3px;
            padding-bottom: 3px;
            border-bottom: 2px solid black;
        }

        .checkbox-size {
            font-size: 20px;
        }

    </style>
</head>

<body style="font-family: Arial, sans-serif">
<div class="page">
    <table
            style="width:100%; border-collapse: collapse; border: 2px solid black;">
        <tr style="background-color: blue">
            <td style="width: 33%; text-align: center;  padding-left: 10px; padding-right: 10px">
                <span style="font-weight: bold; color: white; font-size: 170%;  ">Gerüstfreigabeschein</span></td>
            <td style="width: 36%; text-align: center; background: white"><img
                        src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/warningSigns.jpg") ?>"
                        alt=""
                        width="250px" height="auto">
            </td>
            <td style="width: 31%;  text-align: right;   ">
                <img
                        style="text-align: left; padding-right: 30px"
                        src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/holcimLogo.jpg") ?>"
                        alt="" height="70px" width="auto">
            </td>
    </table>

    <div style="display: -webkit-box">
        <div style="width: 95%">
            <table
                    style="width:100%; border-collapse: collapse; border-left: 2px solid black;  border-right: 2px solid black; font-size: 15px">
                <tr>
                    <td colspan="4"
                        style="width: 95%;height: 10px;border-right: 2px solid black;  background-color: orange"></td>
                </tr>
                <tr>
                    <td class="padding-left"
                        style="width:30%; border-right: 2px solid black; border-bottom: 2px solid black; border-collapse: collapse">
                        <b>Auftraggeber</b> <br> (Holcim Ansprechperson)
                    </td>
                    <td style="width:45%;border-right: 2px solid black; border-bottom: 2px solid black;  border-collapse: collapse"><?= $data['Auftraggeber (Holcim Ansprechperson) Name'] ?? '' ?></td>
                    <td style="width:20%; border-bottom: 2px solid black; padding-bottom: 5px; border-collapse: collapse; vertical-align: top; border-right: 2px solid black;">
                        <b>Tel. </b>
                        <br>
                        <?= $data['Auftraggeber (Holcim Ansprechperson) Tel.'] ?? '' ?>
                    </td>


                </tr>
                <tr>
                    <td class="padding-left"
                        style="width:30%; border-right: 2px solid black; border-bottom: 2px solid black; border-collapse: collapse">
                        <b>Gerüstersteller</b> <br> (befähigte Person)
                    </td>
                    <td style="width:45%;border-right: 2px solid black; border-bottom: 2px solid black;  border-collapse: collapse"><?= $data['Gerüstersteller(befähigte Person) Name'] ?? '' ?></td>
                    <td style="width:20%; border-bottom: 2px solid black; padding-bottom: 5px; border-collapse: collapse; vertical-align: top; border-right: 2px solid black;">
                        <b>Tel. </b>
                        <br>
                        <?= $data['Gerüstersteller(befähigte Person) Tel.'] ?? '' ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="width:30%; border-right: 2px solid black; border-bottom: 2px solid black; border-collapse: collapse">
                        <b>Verantwortlicher Gerüstnutzer</b> <br> (Firma/Ansprechperson)
                    </td>
                    <td style="width:45%;border-right: 2px solid black; border-bottom: 2px solid black;  border-collapse: collapse">  <?= $data['Verantwortlicher Gerüstnutzer (Firma/Ansprechperson) Name'] ?? '' ?></td>
                    <td style="width:20%; border-bottom: 2px solid black; padding-bottom: 5px; border-collapse: collapse; vertical-align: top; border-right: 2px solid black;">
                        <b>Tel. </b>
                        <br>
                        <?= $data['Verantwortlicher Gerüstnutzer (Firma/Ansprechperson) Tel.'] ?? '' ?>
                    </td>
                </tr>
                <tr>
                    <td class="padding-left"
                        style="width:30%; border-right: 2px solid black; border-collapse: collapse">
                        <b>Arbeitsort</b> <br> (HAC/Gebäude/Anlage, ...)
                    </td>
                    <td colspan="3"><?= $data['Gerüstfreigabeschein Arbeitsort (HAC/Gebäude/Anlage, ...)'] ?? '' ?></td>
                </tr>
            </table>
            <table
                    style="width: 100%; border-collapse: collapse; border-left: 2px solid black;  border-right: 2px solid black">
                <tr>
                    <td style="background-color: red; width: 10%; border-left: 2px solid black; border-bottom: 2px dashed black; padding: 3px">
                        <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/arrow.jpg") ?>"
                             width="90px"
                             height="auto" alt="">
                    </td>
                    <td style="background-color: red; width:75%; text-align: center; border-bottom: 2px dashed black; font-size: 74%; color: white">

                        Freigabeschein bei Sperrung hier abreissen. <br>
                        Oberer Teil in der Hülle belassen. <br>
                        Unteren Teil dem verantwortlichen Gerüstnutzer mit der Begündung der "Sperrung" übergeben.

                    </td>
                    <td style="background-color: red; width: 10%; border-bottom: 2px dashed black; border-right: 2px solid black; padding: 3px">
                        <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/arrow.jpg") ?>"
                             width="90px" height="auto" alt="">
                    </td>

                </tr>
                <tr>
                    <td colspan="3"
                        style="padding-top: 1mm; padding-bottom: 1mm; width: 95%; border-left: 2px solid black; border-bottom: 3px solid black; text-align: center">
                        Der Ersteller (befähigte Person) des Gerüstes bestätigt mit seiner Unterschrift, dass der Aufbau
                        bzw.
                        auch der
                        spätere Abbau des Gerüstes gemäss der Montageanweisung, gesetzlichen Vorschriften wie
                        (BetrSichV,
                        Vorgaben der BG, der Holcim, oder der Gefährdungsbeurteilung) und unter Verwendung der
                        kompletten
                        persönlichen Schutzausrüstung (z.B. PSAgA, ...) erfolgt.
                    </td>
                </tr>

                <tr>
                    <td style="text-align: center; font-weight: bold; border-left: 2px solid black;border-right: 2px solid black; border-bottom: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        Datum
                    </td>
                    <td style="text-align: center; font-weight: bold; border-left: 2px solid black;border-right: 2px solid black; border-bottom: 2px solid black; width: 45% ">
                        Name
                    </td>
                    <td style="padding-left: 2mm; padding-right: 2mm; text-align: center; font-weight: bold; border-left: 2px solid black;border-right: 2px solid black; border-bottom: 2px solid black; width: 35% ">
                        Unterschrift
                    </td>
                </tr>
                <tr>
                    <td class="padding-left"
                        style="background-color: #6fa5d2; height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 1: Freigabe (Gerüstersteller) Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 1: Freigabe (Gerüstersteller) Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="background-color: #6fa5d2; border:2px solid black; width: 45% "><?= $data['Unterschrift 1: Freigabe (Gerüstersteller) Name'] ?? '' ?></td>
                    <td style="background-color: #6fa5d2; border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 1: Freigabe (Gerüstersteller) Unterschrift"])) { ?>
                        <img class="signature"
                             src="<?= $data["Unterschrift 1: Freigabe (Gerüstersteller) Unterschrift"] ?>"
                             alt="">
                        <?php } ?>
                    </td>
                </tr>

                <tr>
                    <td class="padding-left"
                        style="background-color: #6fa5d2; height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 2: Übernahme (Verantw. Gerüstnutzer) Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 2: Übernahme (Verantw. Gerüstnutzer) Datum']));
                        } ?>
                    </td>
                    <td class="padding-left" style="background-color: #6fa5d2; border:2px solid black; width: 45% ">
                        <?= $data['Unterschrift 2: Übernahme (Verantw. Gerüstnutzer) Name'] ?? '' ?>
                    </td>
                    <td style="background-color: #6fa5d2; border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 2: Übernahme (Verantw. Gerüstnutzer) Unterschrift"])) { ?>
                        <img class="signature"
                             src="<?= $data["Unterschrift 2: Übernahme (Verantw. Gerüstnutzer) Unterschrift"] ?>"
                             alt="">
                        <?php } ?>
                    </td>
                </tr>

                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 3 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 3 Datum']));
                        } ?>

                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 3 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 3 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 3 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 4 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 4 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 4 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 4 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 4 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 5 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 5 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 5 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 5 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 5 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 6 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 6 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 6 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 6 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 6 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 7 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 7 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 7 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 7 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 7 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 8 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 8 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 8 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 8 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 8 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 9 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 9 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 9 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 9 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 9 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 10 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 10 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 10 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 10 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 10 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 11 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 11 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 11 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 11 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 11 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 12 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 12 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 12 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 12 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 12 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>

                </tr>
                <tr>
                    <td class="padding-left"
                        style="height: 20px; border: 2px solid black; width:15%; padding-top: 5px; padding-bottom: 5px">
                        <?php if (isset($data['Unterschrift 13 Datum'])) {
                            echo date('d.m.Y', strtotime($data['Unterschrift 13 Datum']));
                        } ?>
                    </td>
                    <td class="padding-left"
                        style="border:2px solid black; width: 45% "><?= $data['Unterschrift 13 Name'] ?? '' ?></td>
                    <td style="border:2px solid black; width: 35% ">
                        <?php if (isset($data["Unterschrift 13 Unterschrift"])) { ?>
                            <img class="signature"
                                 src="<?= $data["Unterschrift 13 Unterschrift"] ?>"
                                 alt="">
                        <?php } ?>
                    </td>
                </tr>
            </table>
        </div>
        <div style="width: 5%; background-color: blue; border-right: 2px black solid">
            <div class="rotate-90"
                 style=" font-weight: bold; font-size: 120%;color: white; width: 100%; display: inline-block; margin-top: 400px">
                Gerüstfreigabeschein
            </div>
        </div>
    </div>

    <table style="width: 100%; border-collapse: collapse; border-left: 2px solid black ">
        <tr>
            <td style="background-color: red; width: 10%; border-top: 2px solid red"><img
                        src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/attentionSign.jpg") ?>"
                        width="80px" alt=""></td>
            <td style="text-align: center; width: 75%; font-size: 130%; background-color: red; border-top: 2px solid red; color: white">
                <b> Konstruktive
                    Veränderungen am Gerüst dürfen <br>
                    nur durch den Gerüstersteller ausgeführt werden! </b></td>
            <td style="background-color: red; width: 10%; border-top: 2px solid red"><img
                        src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/attentionSign.jpg") ?>"
                        width="80px" alt=""></td>
            <td rowspan="2"
                style="width: 5%;  background-color: blue; border-left: 2px solid black; border-right: 2px solid black; border-top: 2px solid blue; border-bottom: 2px solid black">

            </td>
        </tr>
        <tr>
            <td colspan="3"
                style="padding: 3px ;text-align: right; font-size: 80%; border-bottom: 2px solid black; color: red">

                12/2019 H&S DE
            </td>
        </tr>
    </table>
</div>

<div class="page" style="font-size: 94%">
    <table
            style="background-color: blue; border-left: 2px solid black; border-top: 2px solid black; border-right: 2px solid black; width: 100%; border-collapse: collapse">
        <tr>
            <td style="font-weight: bold; width: 75%; text-align: center; padding-top: 10px; padding-bottom: 10px; padding-left: 20px; font-size: 200%; color: white">
                Gerüstabnahme Checkliste
            </td>
            <td style="width: 25%;  text-align: right;   padding-right: 10px ">
                <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/holcimLogo.jpg") ?>"
                     alt="" height="50px" width="auto">
            </td>
        </tr>
        <tr>
            <td colspan="2" style="background-color: gold;height: 10px "></td>
        </tr>
    </table>
    <table
            style="border-left: 2px solid black; border-top: 2px solid black; border-right: 2px solid black; width: 100%; border-collapse: collapse">
        <tr>
            <td class="padding-left table-style" style="width: 2%">-</td>
            <td class="padding-left table-style"
                style=" width: 98%"> Jeder <b>"Benutzer"</b>
                muss das Gerüst <span style="color: red">täglich</span> einer Sichtkontrolle unterziehen (Nachweis nicht
                erforderlich)
            </td>
        </tr>
        <tr>
            <td class="padding-left table-style" style="vertical-align: top">-</td>
            <td class="padding-left table-style"> Der <b>"Verantwortliche
                    Gerüstnutzer"</b> muss das Gerüst einer <span style="color: red">wöchentlichen</span> Sichtkontrolle
                unterziehen <br>
                und dies schriftlich nachweisen. (ggf. zusätzlichen Freigabeschein verwenden)
            </td>
        </tr>
        <tr>
            <td class="padding-left table-style" style="vertical-align: top">-</td>
            <td class="padding-left table-style">
                Wird das
                Gerüst über einen Zeitraum von <span style="color:red">> 30 Tagen </span> nicht benutzt (siehe letztes
                Kontrolldatum) <br>
                ist das Gerüst zu sperren. Vor einer weiteren Benutzung muss das Gerüst durch eine <b>"Befähigte
                    Person"</b> <br>
                frisch abgenommen und dies schriftlich nachgewiesen werden.
            </td>
        </tr>
        <tr>
            <td class="padding-left table-style" style="vertical-align: top">-</td>
            <td class="padding-left table-style"
            > Nach einer
                Benutzungsdauer von <span style="color: red">15 Wochen</span>, gerechnet ab dem Erstabnahmedatum, muss
                das Gerüst
                <br>
                durch eine <b>"Befähigte Person"</b> auf dessen Standsicherheit und nach Checkliste erneut überprüft und
                durch
                <br>
                den <b>"Verantwortlichen Gerüstnutzer"</b> erneut abgenommen werden. Hierzu wird ein neuer
                Freigabeschein
                <br>
                verwendet. Der obere Teil des Original-Freigabescheins wird in der Hülle belassen.
            </td>
        </tr>
        <tr>
            <td colspan="2" class="padding-left"
                style="background-color: lightgray; padding-top: 2px; padding-bottom: 2px; border-bottom: 2px dashed black">
                <span style="font-size: 150%">Angaben zum Gerüst </span> nach DIN EN 12811 oder DIN 4420-1
            </td>
        </tr>
    </table>
    <div style="height: 2mm; border-left: 2px solid black; border-right: 2px solid black;"></div>
    <table style="width:100%; border-left: 2px solid black; border-right: 2px solid black; border-collapse: collapse">
        <tr>
            <td class="padding-left checkbox-size" style="width: 3%">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Fassadengerüst'] ?? false); ?>
            </td>
            <td class="padding-left" style="width: 30%">Fassadengerüst</td>
            <td class="checkbox-size" style="width: 3%">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Raumgerüst'] ?? false) ?>
            </td>
            <td style="width: 30%"> Raumgerüst</td>
            <td class="checkbox-size" style="width: 3%">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Fahrgerüst / Rollgerüst'] ?? false) ?>
            </td>
            <td style="width: 30%; border-right: 2px solid black"> Fahrgerüst / Rollgerüst</td>
        <tr>
            <td class="padding-left checkbox-size">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Fanggerüst'] ?? false) ?>
            </td>
            <td class="padding-left">Fanggerüst</td>
            <td class="checkbox-size">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Dachfanggerüst'] ?? false) ?>
            </td>
            <td> Dachfanggerüst</td>
            <td class="checkbox-size">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Schutzdach'] ?? false) ?>
            </td>
            <td style="border-right: 2px solid black"> Schutzdach</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Treppenturm'] ?? false) ?>
            </td>
            <td class="padding-left">Treppenturm</td>
            <td class="checkbox-size">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Podest'] ?? false) ?>
            </td>
            <td> Podest</td>
            <td class="checkbox-size">
                <?php PrintoutHelper::echoCheckboxByReportedValue($data['Angaben zum Gerüst nach DIN EN 12811 oder DIN 4420-1 Hängegerüst'] ?? false) ?>
            </td>
            <td style="border-right: 2px solid black"> Hängegerüst</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size">
                <?php
                if (isset($data['Sondergerüst Beschreibung Sondergerüst'])) {
                    ?>
                    &#9745; <?php
                } else { ?>
                    &#9744; <?php
                }
                ?>
            </td>
            <td colspan="5" class="padding-left">
                Sondergerüst: <?= $data['Sondergerüst Beschreibung Sondergerüst'] ?? '' ?></td>
        </tr>
    </table>
    <table style="width: 100%; border-left: 2px solid black; border-right: 2px solid black">
        <tr>
            <td class="padding-left" style="width: 12%">Lastklasse:</td>
            <td class="checkbox-size"
                style="width: 2%"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Lastklasse 2  (1,5 kN/m²)'] ?? false) ?></td>
            <td style="width: 14%"><b>2</b> (1,5 kN/m²)</td>
            <td class="checkbox-size"
                style="width: 2%"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Lastklasse 3  (2,0 kN/m²)'] ?? false) ?></td>
            <td style="width: 14%"><b>3</b> (2,0 kN/m²)</td>
            <td class="checkbox-size"
                style="width: 2%"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Lastklasse 4  (3,0 kN/m²)'] ?? false) ?></td>
            <td style="width: 14%"><b>4</b> (3,0 kN/m²)</td>
            <td class="checkbox-size" style="width: 2%">
                <?php
                if (isset($data['Andere Lastklasse Beschreibung der Lastklasse'])) {
                    ?>
                    &#9745; <?php
                } else { ?>
                    &#9744; <?php
                }
                ?>
            </td>
            <td style="width: 38%"><?= $data['Andere Lastklasse Beschreibung der Lastklasse'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="padding-left">Breitenklasse:</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Breitenklasse W 06 (60 cm)'] ?? false) ?></td>
            <td><b>W 06 </b>(60 cm)</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Breitenklasse W 09 (90cm)'] ?? false) ?></td>
            <td><b>W 09 </b>(90 cm)</td>
            <td class="checkbox-size">
                <?php
                if (isset($data['Andere Breitenklasse Beschreibung der Breitenklasse'])) {
                    ?>
                    &#9745; <?php
                } else { ?>
                    &#9744; <?php
                }
                ?>
            </td>
            <td colspan="3"><?= $data['Andere Breitenklasse Beschreibung der Breitenklasse'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="padding-left">Bekleidung:</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Gerüstfreigabeschein Netzen'] ?? false) ?></td>
            <td> Netzen</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Gerüstfreigabeschein Planen'] ?? false) ?></td>
            <td>Planen</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['Gerüstfreigabeschein teilweise mit Netzen/Planen'] ?? false) ?></td>
            <td colspan="3">teilweise mit Netzen/Planen</td>
        </tr>
    </table>
    <div
            style="border-right: 2px solid black; border-left: 2px solid black ;background-color: lightgray;padding-top: 2px; padding-bottom: 2px">
        <span class="padding-left" style="font-size: 150%">Prüfungsanweisung</span> <span
                style="padding-left: 20px">X</span>
        - angekreuzt = geprüft und in Ordnung
    </div>
    <table
            style="width:100%; border-left: 2px solid black; border-right: 2px solid black; border-collapse: collapse; line-height: 1.4">
        <tr>
            <td class="padding-left title" colspan="2"><b>1. Gerüstbauteile:</b></td>
            <td class="title" colspan="2"><b>4. Arbeits- und Betriebssicherheit</b></td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"
                style="width: 3%; vertical-align: top"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['1. Gerüstbauteile Alle Beläge, Stützen, Rohre und Halterungen sind augenscheinlich unbeschädigt'] ?? false) ?></td>
            <td>Alle Beläge, Stützen, Rohre und Halterungen sind augenscheinlich
                unbeschädigt
            </td>
            <td class="checkbox-size"
                style="width: 3%;vertical-align: top "> <?php PrintoutHelper::echoCheckboxByReportedValue($data['4. Arbeits- und Betriebssicherheit Seitenschutz 3-teilig (Geländerholm, Zwischenholm, sind augenscheinlich unbeschädigt Bordbrett - beidseitig)'] ?? false) ?></td>
            <td style="width: 57%; vertical-align: top">Seitenschutz 3-teilig (Geländerholm, Zwischenholm, <br>
                Bordbrett - beidseitig)
            </td>
        </tr>
        <tr>
            <td class="padding-left title" colspan="2"><b>2. Standardsicherheit:</b></td>
            <td class="checkbox-size"
                style="width: 3%; "> <?php PrintoutHelper::echoCheckboxByReportedValue($data['4. Arbeits- und Betriebssicherheit Seitenschutz 3-teilig an Stirnseiten und Öffnungen'] ?? false) ?></td>
            <td> Seitenschutz 3-teilig an Stirnseiten und Öffnungen</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Tragfähigkeit der Aufstandfläche'] ?? false) ?></td>
            <td style="width: 43%">Tragfähigkeit der Aufstandsfläche</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['4. Arbeits- und Betriebssicherheit Wandabstand max. 0,30 m'] ?? false) ?></td>
            <td>Wandabstand max. 0,30 m</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Lastverteilung ausreichend'] ?? false) ?></td>
            <td>Lastverteilung ausreichend</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['4. Arbeits- und Betriebssicherheit Aufstiege, Zugänge (besser innenliegend - stolperfrei)'] ?? false) ?></td>
            <td> Aufstiege, Zugänge(besser innenliegend - stolperfrei)</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Verstrebungen ausreichend'] ?? false) ?></td>
            <td>Verstrebungen ausreichend</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['4. Arbeits- und Betriebssicherheit Verkehrssicherung (Kanten, Ecken, Schwellen)'] ?? false) ?></td>
            <td>Verkehrssicherung (Kanten, Ecken, Schwellen)</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Gitterträger mit Druckgurtaussteifungen'] ?? false) ?></td>
            <td>Gitterträger mit Druckgurtaussteifungen</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['4. Arbeits- und Betriebssicherheit Beleuchtung angemessen'] ?? false) ?></td>
            <td>Beleuchtung angemessen</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Sonderkonstruktion nach Vorgabe'] ?? false) ?></td>
            <td>Sonderkonstruktion nach Vorgabe</td>
            <td class="title" colspan="2"><b>5. Anforderungen an Schutzgerüste</b></td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Fahrrollen, Bremsen'] ?? false) ?></td>
            <td> Fahrrollen, Bremsen</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['5. Anforderungen an Schutzgerüste Belagfläche bei Dachfanggerüst (> 0,60 m Breite)'] ?? false) ?></td>
            <td> Belagfläche bei Dachfanggerüst (> 0,60 m Breite)</td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Spindelauszugslänge'] ?? false) ?></td>
            <td>Spindelauszugslänge</td>
            <td class="checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['5. Anforderungen an Schutzgerüste Belag bei Dachfanggerüst (< 1,5 m unter Absturzkante)'] ?? false) ?></td>
            <td> Belag bei Dachfanggerüst <span style="font-size: 95%">(< 1,5 m unter Absturzkante)</span></td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['2. Standsicherheit Verankerungen nach Montageanweisung'] ?? false) ?></td>
            <td>Verankerungen nach Montageanweisung</td>
            <td class="checkbox-size"
                style="vertical-align:top "> <?php PrintoutHelper::echoCheckboxByReportedValue($data['5. Anforderungen an Schutzgerüste Min. Abstand Schutzwand und Absturzkante (> 0,70 m)'] ?? false) ?></td>
            <td> Min. Abstand Schutzwand und Absturzkante <span style="font-size: 94%">(> 0,70 m)</span></td>
        </tr>
        <tr>
            <td class="padding-left title" colspan="2"><b>3. Beläge</b></td>
            <td class="title" colspan="2"><b>6. Prüfung abschliessen</b></td>
        </tr>
    </table>
    <table style="width:100%; border-left: 2px solid black; border-right: 2px solid black; border-collapse: collapse">
        <tr>
            <td class="padding-left checkbox-size"
                style="width: 2.5%; vertical-align: top"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['3. Beläge Rahmentafeln/Gerüstbohlen gegen Abheben gesichert'] ?? false) ?></td>
            <td style="width: 50%">Rahmentafeln/Gerüstbohlen gegen Abheben gesichert</td>
            <td class="checkbox-size"
                style="width:2%; padding-left: 8px; vertical-align: top"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['6. Prüfung abschliessen Kennzeichnung an allen Zugängen angebracht'] ?? false) ?></td>
            <td class="padding-left" style="width: 44%; border-right: 2px solid black;">
                Kennzeichnung an allen Zugängen
                angebracht
            </td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['3. Beläge Gerüstbohlen gegen Wippen gesichert'] ?? false) ?></td>
            <td colspan="4" style="border-right: 2px solid black">Gerüstbohlen gegen Wippen
                gesichert
            </td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"
                style="vertical-align: top"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['3. Beläge Genutzte Gerüstlagen sind vollständig mit Bohlen oder Rahmentafeln ausgelegt'] ?? false) ?></td>
            <td>Genutzte Gerüstlagen sind vollständig <br> mit Bohlen oder Rahmentafeln ausgelegt
            </td>
            <td class="checkbox-size" style="padding-left: 8px; vertical-align: top">
                <?php
                if (isset($data['Weitere Checkbox Name der Prüfung'])) {
                    ?>
                    &#9745; <?php
                } else { ?>
                    &#9744; <?php
                }
                ?>
            </td>
            <td style="border-right: 2px solid black"><?= $data['Weitere Checkbox Name der Prüfung'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="padding-left checkbox-size"> <?php PrintoutHelper::echoCheckboxByReportedValue($data['3. Beläge Der Belag ist um die Ecke in voller Breite herumgeführt'] ?? false) ?></td>
            <td>Der Belag ist um die Ecke in voller Breite herumgeführt</td>
            <td class="checkbox-size" style="padding-left: 8px; vertical-align: top">
                <?php
                if (isset($data['Weitere Checkbox 2 Name der Prüfung 2'])) {
                    ?>
                    &#9745; <?php
                } else { ?>
                    &#9744; <?php
                }
                ?>
            </td>
            <td style="border-right: 2px solid black"><?= $data['Weitere Checkbox 2 Name der Prüfung 2'] ?? '' ?></td>
        </tr>
        <tr>
            <td colspan="4" style="height: 10px; border-right: 2px solid black"></td>
        </tr>
        <tr>
            <td colspan="4"
                style="text-align: center; background-color: rgb(0,255,0); font-size: 135%; border-right: 2px solid black; padding: 3px 0">
                Verhalten bei
                Unfall / Brand / Sachschaden: siehe Ereignismanagement
            </td>
        </tr>
        <tr>
            <td colspan="4"
                style="padding:3px; text-align: right; font-size: 80%; border-bottom: 2px solid black; border-right: 2px solid black; color: red">
                12/2019 Freigeber: H&S DE
            </td>
        </tr>

    </table>
</div>
</body>

</html>
