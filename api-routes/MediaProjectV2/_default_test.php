<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$images = [
    'saveHardCopy' => false,
    'images' => [
        [
            'fileId' => 191946,
            'description' => 'test 1',
            'showWorkingOrderNumber' => true,
            'showUploader' => true,
            'showComments' => true,
            'showUploadDate' => true,
            'showCreationDate' => true,
        ],
        [
            'fileId' => 191943,
            'description' => 'test 2',
            'showWorkingOrderNumber' => false,
            'showUploader' => false,
            'showComments' => false,
            'showUploadDate' => false,
            'showCreationDate' => false,
        ]
    ],
];

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::MediaProjectV2,
    apiRouteSuffix: "/2502007",
    method: RequestMethod::POST,
    requestBodyForPost: json_encode($images),
);
