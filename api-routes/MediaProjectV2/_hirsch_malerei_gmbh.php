<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// not in CI because the credentials are not valid anymore

$images = [
    "imagesPerPage" => 2,
    "saveHardCopy" => false,
    "images" => [
        [
            "fileId" => 5591,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5590,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5589,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5588,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5587,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5586,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5585,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5584,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5583,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 5582,
            "description" => "",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
    ]
];

printoutGenerator(
    Principal::HIRSCH_MALEREI_GMBH(),
    Route::MediaProjectV2,
    apiRouteSuffix: "/101",
    method: RequestMethod::POST,
    requestBodyForPost: json_encode($images),
    params: [
        "hidePageNumbers" => "true"
    ]
);
