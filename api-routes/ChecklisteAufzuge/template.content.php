<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ChecklisteAufzuge())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param string|bool|int|null $value
 */
function renderCheckboxRow($value): void
{
    $checked = PrintoutHelper::translateLocalPathToServerPathFromRoot("img/checked2.jpg");
    $notChecked = PrintoutHelper::translateLocalPathToServerPathFromRoot("img/notChecked2.jpg");
    if (strtolower($value) == 'ja' || $value == '1' || strtolower($value) == 'true') {
        echo
            '<td style="width: 6%; padding: 5px; text-align: center">' . '<img src="' . $checked . '"' . ' alt="checked"  height="20px" width="auto">' . '</td>' .
            '<td style="width: 6%; padding: 5px; text-align: center">' . '<img src="' . $notChecked . '"' . ' alt="Notchecked" height="20px" width="auto">' . '</td>';
    } else {
        echo
            '<td style="width: 6%; padding: 5px; text-align: center">' . '<img src="' . $notChecked . '"' . ' alt="notChecked" height="20px" width="auto">' . '</td>' .
            '<td style="width: 6%; padding: 5px; text-align: center">' . '<img src="' . $checked . '"' . ' alt="checked" height="20px" width="auto">' . '</td>';
    }
}

?>
<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Checkliste Aufzuge</title>
</head>
<body>
<table class="headerTable">
    <tr>
        <td class="leftHeader">
            Adresse der Baustelle:
        </td>
        <td class="middleHeader">
            <div class="top-title">Verantwortlicher vor Ort (AvO)</div>
            <div class="bottom-title">Vor- und Nachname in Druckbuchstaben</div>
        </td>
        <td class="logoHeader"><img src="<?= $data['logo'] ?>" alt="SchaeferGeruestbauLogo" class="companyLogo"></td>
    </tr>
</table>
<br>
<table class="checkboxes">
    <tr>
        <td style="width: 50%; padding-left: 5px; text-align: left"><b>Checkliste Aufzüge</b></td>
        <td class="checkBox-title"><b>Ja</b></td>
        <td class="checkBox-title"><b>Nein</b></td>
    </tr>
    <tr>
        <?php foreach ($data['Checkliste Aufzüge'] as $key => $value) { ?>
        <?php if ($value != null && $key != 'Kommentar') { ?>
            <td><?= $key ?></td>
            <?php renderCheckboxRow($value) ?>
        <?php } ?>
    </tr>
    <tr>
        <?php if ($key == 'Kommentar') { ?>
        <td class="kommentar" colspan="3"><?= $value ?></td>
    </tr>
    <?php } ?>
    <?php } ?>
    <tr>
        <?php foreach ($data['Sichtprüfung'] as $key => $value) { ?>
        <?php if ($key != 'Kommentar') { ?>
            <td><?= $key ?></td>
            <?php renderCheckboxRow($value) ?>
        <?php } ?>
    </tr>
<tr>
<?php if ($key == 'Kommentar') { ?>
    <td class="kommentar" colspan="3"><?= $value ?></td>
    </tr>
<?php } ?>
<?php } ?>
    <tr>
        <?php foreach ($data['Funktionsprüfung'] as $key => $value) { ?>
        <?php if ($key != 'Kommentar') { ?>
            <td><?= $key ?></td>
            <?php renderCheckboxRow($value) ?>
        <?php } ?>
    </tr>
<tr>
<?php if ($key == 'Kommentar') { ?>
    <td class="kommentar" colspan="3"><?= $value ?></td>
    </tr>
<?php } ?>
<?php } ?>
</table>
<p style="page-break-before: always"></p>
<?php
if (isset($data['Fotos'])) {
    foreach ($data['Fotos'] as $key => $value) { ?>
        <p style="page-break-before: always"></p>
        <div class="photos">
            <p><?= $key ?></p>
            <img class="image" src="<?= $value ?>" alt="<?= $key ?>">
        </div>
    <?php }
} ?>
</body>
</html>

<style>
    .headerTable, th, td {
        width: 100%;
        border: 1px solid black;
        border-collapse: collapse;
        padding: 0;
        mso-cellspacing: 0;
    }

    .headerTable .leftHeader {
        width: 40%;
        height: 100px;
        text-align: left;
        vertical-align: top;
        text-decoration: underline;
        padding-left: 10px;
    }

    .headerTable .middleHeader {
        width: 35%;
        text-align: center;
        vertical-align: top;
    }

    .top-title {
        text-decoration: underline;
        margin-bottom: 30px;
    }

    .bottom-title {
        padding-top: 30px;
        margin-top: 10px;
        font-size: 14px;
    }

    .headerTable .logoHeader {
        width: 25%;
        text-align: center;
    }

    .companyLogo {
        max-width: 100%;
        max-height: 200px;
    }

    .checkboxes, td {
        width: 100%;
        border: 1px solid black;
        border-collapse: collapse;
        padding-left: 2px;
        mso-cellspacing: 0;
    }

    .checkboxes .checkBox-title {
        padding: 8px;
    }

    .row img {
        width: 30px;
        height: 30px;
        margin-top: 2px;
    }

    @media print {
        .page-break {
            page-break-before: always;
            page-break-after: always;
        }
    }

    .photos {
        text-align: center
    }

    .photos p {
        text-align: left;
        font-size: 18px;
        padding-left: 90px;
        margin-bottom: 5px;
        font-style: italic;
        font-weight: bold;
    }

    .image {
        width: 80%;
        height: auto;
    }
</style>
