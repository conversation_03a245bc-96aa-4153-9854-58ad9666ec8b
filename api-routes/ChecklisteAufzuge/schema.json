{"name": "Checkliste Aufzüge", "status": "active", "createdBy": "4", "createdOn": "", "positions": [{"id": -1, "title": "Sichtprüfung Aufzug: Steht die Bühne schief? Etwas eingeklemmt, Mast gerade?", "type": "checkbox"}, {"id": -2, "title": "Kommentar", "type": "string"}, {"id": -3, "title": "Sichtprüfung", "type": "headline"}, {"id": -4, "parentId": -3, "title": "Kabeltopf auf Dellen und Schmutz prüfen", "type": "checkbox"}, {"id": -5, "parentId": -3, "title": "Zahnstangen abgenutzt oder defekt?", "type": "checkbox"}, {"id": -6, "parentId": -3, "title": "Defekte Kabel zu sehen? Stecker oder Verschraubung gelöst?", "type": "checkbox"}, {"id": -7, "parentId": -3, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON>,Blindstecker vorhanden?", "type": "checkbox"}, {"id": -8, "parentId": -3, "title": "Kommentar", "type": "string"}, {"id": -9, "title": "Funktionsprüfung", "type": "headline"}, {"id": -10, "parentId": -9, "title": "Bühne auf Etagen fahren / Funktionstest", "type": "checkbox"}, {"id": -11, "parentId": -9, "title": "<PERSON><PERSON>?", "type": "checkbox"}, {"id": -12, "parentId": -9, "title": "Mastschrauben prüfen und bei Bedarf nachziehen", "type": "checkbox"}, {"id": -13, "parentId": -9, "title": "Kommentar", "type": "string"}, {"id": -14, "parentId": -9, "title": "Anleitung, Warnhinweise und Lieferschein vorhanden", "type": "checkbox"}, {"id": -15, "title": "Fotos", "type": "headline"}, {"id": -16, "parentId": -15, "title": "Aufzugsnummer", "type": "photo"}, {"id": -17, "parentId": -15, "title": "TÜV + Typenschild", "type": "photo"}, {"id": -18, "parentId": -15, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "photo"}, {"id": -19, "parentId": -15, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "photo"}, {"id": -20, "parentId": -15, "title": "<PERSON><PERSON><PERSON>e innen", "type": "photo"}, {"id": -21, "parentId": -15, "title": "Etagentore", "type": "photo"}, {"id": -22, "parentId": -15, "title": "Fettpumpe", "type": "photo"}, {"id": -23, "title": "Name Gruppenleiter", "type": "EMPLOYEESELECTOR"}]}