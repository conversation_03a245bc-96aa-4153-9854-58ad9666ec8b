{"name": "Montagerapport", "description": "This is the description section of Montagerapport", "status": "active", "createdBy": "4", "createdOn": "2022-12-23T08:47:19Z", "editedOn": "2022-12-23T10:07:02Z", "positions": [{"id": -38, "title": "Datum", "type": "date"}, {"id": -1, "title": "Abnahmeprotokoll zur Lieferung am", "description": "Kunde: Kamil Sertbolat LS-Nr.: 52302416 Kaufvertrag: 207001", "type": "headline"}, {"id": -2, "title": "Bei Möbellieferung / Montage", "type": "headline"}, {"id": -3, "parentId": -2, "title": "Alle Teile sind vollständig und in Waage montiert.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -4, "parentId": -2, "title": "Die Funktionen der beweglichen Teile wurden vorgeführt und erklärt.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -5, "parentId": -2, "title": "Die Oberflächen sind einwandfrei.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -6, "parentId": -2, "title": "Die Ware wurde fachgerecht ausgerichtet", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -7, "parentId": -2, "title": "Die Polster / Bezüge sind in Ordnung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -8, "parentId": -2, "title": "Die Elektroinstallation / Beleuchtung ist funktionstüchtig.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -9, "parentId": -2, "title": "<PERSON><PERSON> Foto<PERSON> von der Mont<PERSON> gemacht?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -10, "title": "Zusätzlich bei Küchenmontage", "type": "headline"}, {"id": -39, "parentId": -10, "title": "Einbaugeräte sind einwandfrei und wurden auf Funktion geprüft und erklärt.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -11, "parentId": -10, "title": "<PERSON><PERSON> Foto<PERSON> von der Mont<PERSON> gemacht?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -12, "parentId": -10, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> und Schübe sind funktionstüchtig und sauber ausgerichtet.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -13, "parentId": -10, "title": "Die Wasserinstallation wurde auf Dichtigkeit geprüft.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -14, "parentId": -10, "title": "Pflegehinweise für die Arbeitsplatten wurden gegeben.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -15, "parentId": -10, "title": "Auf die Feuchtigkeitsproblematik im Spülmaschinenbereich wurde hingewiesen.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -16, "parentId": -10, "title": "Die Betriebsanleitungen für die Geräte sind vorhanden.", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -17, "parentId": -10, "title": "Lässt sich der Geschirrspüler vollständig öffnen?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -18, "parentId": -1, "title": "Wurden bei E-Geräten E- und FD-Nr. aufgenommen?", "description": "Kunde: Kamil Sertbolat LS-Nr.: 52302416 Kaufvertrag: 207001", "type": "headline"}, {"id": -19, "parentId": -18, "title": "FD-Nr", "type": "string"}, {"id": -20, "parentId": -18, "title": "FD-Nr", "type": "string"}, {"id": -21, "parentId": -18, "title": "FD-Nr", "type": "string"}, {"id": -22, "parentId": -18, "title": "FD-Nr", "type": "string"}, {"id": -23, "parentId": -18, "title": "FD-Nr", "type": "string"}, {"id": -24, "parentId": -18, "title": "E-Nr", "type": "string"}, {"id": -25, "parentId": -18, "title": "E-Nr", "type": "string"}, {"id": -26, "parentId": -18, "title": "E-Nr", "type": "string"}, {"id": -27, "parentId": -18, "title": "E-Nr", "type": "string"}, {"id": -28, "parentId": -18, "title": "E-Nr", "type": "string"}, {"id": -29, "title": "Haben Sie Grund zur Beanstandung ?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -30, "parentId": -18, "title": "G<PERSON>d der Beanstandung", "type": "string"}, {"id": -47, "title": "G<PERSON>d der Beanstandung", "type": "string"}, {"id": -31, "title": "Anmerkungen", "type": "string"}, {"id": -32, "title": "<PERSON><PERSON>", "type": "EMPLOYEESELECTOR"}, {"id": -35, "title": "Nachmontage", "type": "string"}, {"id": -37, "title": "Zeit", "type": "time"}, {"id": -40, "title": "Allgemeiner Fragebogen", "description": "Kunde: Kamil Sertbolat LS-Nr.: 52302416 Kaufvertrag: 207001", "type": "headline"}, {"id": -41, "parentId": -40, "title": "Wurde Kundeneigentum beschädigt (Wände, Böden, Einrichtung etc.)?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -42, "parentId": -40, "title": "Evtl. Fehlteile oder Beschädigungen wurden auf dem Lieferschein vermerkt", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -43, "parentId": -40, "title": "Die Verpackung wurde zurückgenommen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -44, "parentId": -40, "title": "Der Arbeitsplatz wurde sauber verlassen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -45, "parentId": -40, "title": "Der Fragebogen „Service-Check“ wurde übergeben", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -46, "parentId": -40, "title": "Haben Sie Grund zur Beanstandung?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -36, "title": "<PERSON><PERSON>", "type": "EMPLOYEESELECTOR"}, {"id": -33, "title": "Unterschrift Monteur", "type": "signatureField"}, {"id": -34, "title": "Unterschrift Kunde", "type": "signatureField"}]}