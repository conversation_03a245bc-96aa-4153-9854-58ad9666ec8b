<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "schemaId", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentId", required: true);
$config->addParameter(RouteConfigParameterType::int, "personalNo", required: true);
$config->addParameter(RouteConfigParameterType::date, "from", required: true);
$config->addParameter(RouteConfigParameterType::date, "to", required: true);
$config->addParameter(RouteConfigParameterType::ktrAanr, "ktrAanr", required: false);
return $config;