<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Stundenzettel())->getData(
        $_GET['schemaId'], $_GET['documentId'], $_GET['personalNo'], $_GET['from'], $_GET['to'], $_GET['ktrAanr']);
}

$hoursData = $data['hours_data'];
$intervalTo = DateTime::createFromFormat('Y-m-d', $hoursData[0]['date']);
$intervalFrom = DateTime::createFromFormat('Y-m-d', $hoursData[count($hoursData) - 1]['date']);
$sum = $data['annual_hours'][$intervalFrom->format('Y')];

$endday = $intervalTo->format('t');
$days = array_combine(range(1, $endday), range(1, $endday));
$hoursDataByDay = array();

foreach ($hoursData as $hoursDatum) {
    $hoursDateTime = DateTime::createFromFormat('Y-m-d', $hoursDatum['date']);
    $hoursDay = $hoursDateTime->format('j');
    $recordedOnString = explode('@ ', $hoursDatum['comment'])[1];
    $recordedOnDateTime = DateTime::createFromFormat('Y-m-d H:i:s', $recordedOnString);
    // formatting of pause
    if (in_array($hoursDatum['pause'], ['.00', '0.00'])) {
        $hoursDatum['pause'] = '';
    } elseif (substr($hoursDatum['pause'], 1) != '.') {
        $hoursDatum['pause'] = '0' . $hoursDatum['pause'];
    }
    $hoursReformat = array(
        'workStart' => $hoursDatum['start'],
        'workEnd' => $hoursDatum['end'],
        'breaks' => $hoursDatum['pause'],
        'actualWorkHours' => $hoursDatum['hours'],
        'waKug' => $hoursDatum['name'] == 'KUG' ? 'x' : '',
        'sick' => $hoursDatum['name'] == 'Krank' ? 'x' : '',
        'vacation' => '',
        'holiday' => $hoursDatum['task_name'] == 'Urlaub' ? 'x' : '',
        'recordedOn' => $recordedOnDateTime ? $recordedOnDateTime->format('d.m.Y') : '',
        'remark' => $hoursDatum['kname'],
    );
    $hoursDataByDay[$hoursDay][] = $hoursReformat;
}

$listKeys = ["day", "workStart", "workEnd", "breaks", "actualWorkHours", "waKug", "sick", "vacation", "holiday", "recordedOn", "remark"];
?>

<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Stundenzettel</title>
</head>
<body>
<main>
    <h2 class="text-align-center"><?php echo $data['companyDetails']['companyName']; ?></h2>

    <div class="header">
        <div class="header-left">
            <p>Stundenzettel</p>
            <p>Zeitraum:
                <span><?php echo $intervalFrom->format('d.m.Y') . ' - ' . $intervalTo->format('d.m.Y'); ?></span></p>
            <p>Name: <span><?php echo $data['print_name']; ?></span></p>
        </div>

        <div class="header-right">
            <?= "<img width='100px' height='auto' src='" . $data['logo'] . "' alt=''>" ?>
        </div>
    </div>

    <table class="main-table">
        <thead>
        <tr>
            <th>Tag</th>
            <th>Arbeits-<br>beginn</th>
            <th>Arbeits-<br>ende</th>
            <th>Pausen</th>
            <th>Arbeits-<br>stunden</th>
            <th>WA/KUG</th>
            <th>KRANK</th>
            <th>Urlaub</th>
            <th>Feiertag</th>
            <th>aufgezeichnet am:</th>
            <th>Bemerkung</th>
        </tr>
        </thead>
        <tbody>
        <?php
        foreach ($days as $day) {
            // also create a row, if there is no data available for this day
            if (!isset($hoursDataByDay[$day])) {
                echo '<tr>';
                echo "<td>" . $day . "</td>";
                for ($i = 0; $i <= 9; $i++) {
                    echo "<td class='right-align'></td>";
                }
                echo "</tr>";
            } else {
                if (isset($hoursDay)) {
                    usort($hoursDataByDay[$hoursDay], function (array $a, array $b): int {
                        return $a['workStart'] <=> $b['workStart'];
                    });
                }
                foreach ($hoursDataByDay[$day] as $key => $hoursEntry) {
                    echo '<tr>';

                    if ($key === 0) {
                        echo "<td class='days' rowspan='" . count($hoursDataByDay[$day]) . "'>" . $day . "</td>";
                    }
                    foreach ($hoursEntry as $value) {
                        echo "<td class='right-align'>" . $value . "</td>";
                    }
                    echo "</tr>";

                    $dailySum = floatval($hoursEntry['actualWorkHours']) + floatval($hoursEntry['breaks']);
                    echo "<tr class='sum'>";
                    echo "<td></td>";
                    echo "<td colspan='9' style='text-align: right'>Summe</td>";
                    echo "<td>" . number_format($dailySum, 2, '.', '');
                    echo "</tr>";
                }
            }
        }
        ?>
        <tr>
            <td class="sum text-align-center" colspan="4">Summe</td>
            <td class="text-align-right sum"><?= $sum ?></td>
            <td colspan="6">
        </tr>
        <tr>
            <td class="sum text-align-center" colspan="4">Kosten Arbeitskleidung</td>
            <td class="text-align-right sum"><?= $data['Kosten Arbeitskleidung'] ?></td>
            <td colspan="6">
        </tr>
        </tbody>
    </table>

    <div class="signature">
<!--        <img src='--><?php //= $signatureAssignee ?><!--' id='signature' alt='Foto' width='150px' height='auto'>-->
        <p>Unterschrift Arbeitnehmer</p>
    </div>
</main>
</body>
</html>