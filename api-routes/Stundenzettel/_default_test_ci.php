<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/SampleDocumentationCreator.php';

$creator = new SampleDocumentationCreator(__DIR__, forceDocumentationCreation: false);
$docs = $creator->createDocumentations([
    'schema.json',
]);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::Stundenzettel,
//    fileType: PrintoutFileType::Html,
    params: [
        "schemaId" => $docs[0]['schemaId'],
        "documentId" => $docs[0]['documentId'],
        "personalNo" => "15",
        "from" => "2024-01-01",
        "to" => "2024-01-31",
		],
    cleanupCallback: function () use ($creator, $docs) {
        $creator->deleteSchemasByIds([$docs[0]['schemaId']]);
    }
);
