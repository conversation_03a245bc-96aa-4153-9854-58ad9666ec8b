<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */

require_once __DIR__ . '/../../printout.helper.php';

class C_Kolonneneinteilung
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $workingHoursDate): array
    {
        $templateData = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        list($employees, $resources, $workingOrders, $settings) = $this->downloadData($curl, $workingHoursDate);

        $sortedEmployees = [];
        foreach ($employees['response'] as $val) {
            $sortedEmployees[$val['pnr']] = $val['displayName'];
        }

        $sortedResources = [];
        foreach ($resources['response'] as $res) {
            $sortedResources[$res['rnr']] = $res;
        }

        $templateData['wos'] = [];
        $projectIds = [];
        foreach ($workingOrders['response'] as $v) {
            foreach ($v['staffPreplanned'] as $i => $staff) {
                // resolve from cached array ($sortedEmployees)
                $v['staffPreplanned'][$staff] = $sortedEmployees[$staff] ?? '';
                // drop processed entry
                unset($v['staffPreplanned'][$i]);
            }

            foreach ($v['resourcesNonHr'] as $index => $resource) {
                unset($v['resourcesNonHr'][$index]);
                $v['resourcesNonHr'][$sortedResources[$resource]['gruppe']][$resource]['name'] =
                    $sortedResources[$resource]['kurzname'];
            }

            $projectNo = explode('-', $v['uniqueKey'])[0];
            $projectIds[] = $projectNo;

            if (isset($v['teamIdentification']['displayText']))
                $templateData['wos'][$v['teamIdentification']['displayText']][] = $v;
        }

        foreach ($templateData['wos'] as $kk => $vv) {
            $a = array_column($vv, "plannedStartTime");
            array_multisort($a, SORT_ASC, $templateData['wos'][$kk]);
        }

        $projectManagers = [];
        if (!empty($projectIds)) {
            $projectManagers = $this->getProject($projectIds, $curl);
        }

        //add project manager to wos to avoid changing template
        foreach ($templateData['wos'] as $kk => $vv) {
            foreach ($projectManagers as $projectManager) {
                if ($projectManager['projectNo'] == $vv[0]['projectNo']) {
                    $templateData['wos'][$kk][0]['projectManager'] = $projectManager['technicalContactDisplayName'];
                    break;
                }
            }
        }

        $templateData['reportDate'] = date('d.m.Y', strtotime($workingHoursDate));
        $templateData['settingsInfo'] = $settings['response'];

        return $templateData;
    }

    /**
     * @return array<mixed>
     */
    private function downloadData(PrintoutCurl $curl, string $workingHoursDate): array
    {
        $partial = "teamIdentification,staffPreplanned,resourcesNonHr,projectManager,projectName,shortDescription,uniqueKey" .
            "plannedDate,plannedStartTime,customerName,customerSalutation,uniqueKey,projectNo";
        $lteDate = date('Y-m-d', strtotime($workingHoursDate));

        $employeeUrl = "v3/employees?showRelatedProject=false&type=active&partial=pnr,displayName";
        $resourceUrl = "v1/vehicles/select/all";
        $workingOrderUrl = "v3/workingorders?partial=$partial&filter[plannedDate][gte]=$workingHoursDate" .
            "&filter[plannedDate][lte]=$lteDate&filter[status][lt]=5";
        $settingsUrl = "v1/settings/info";

        return $curl->_multi_call('get',
            [$employeeUrl, $resourceUrl, $workingOrderUrl, $settingsUrl],
            [], PrintoutHelper::getHeadersForApiCalls());
    }

    /**
     * @param array<string> $projectIds
     * @return array<mixed>
     */
    private function getProject(array $projectIds, PrintoutCurl $curl): array
    {
        $projectIds = implode(",", $projectIds);
        return json_decode($curl->_simple_call('get',
            "v3/projects?filter[projectNo][in]=$projectIds&partial=technicalContactDisplayName,projectNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}
