<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Kolonneneinteilung())->getData($_GET['workingHoursDate']);
}
?>

<!doctype html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Kolonneneinteilung</title>
</head>
<body>
<div align="left" class="title"><b><?= $data['settingsInfo']['companyName']; ?></b></div>
<div align="left" class="title"><u><b><?= $data['settingsInfo']['contactInfo']; ?></b></u></div>
<br>
<div align="left" class="title"><b>Tageszusammenfassung der Kolonnen vom <?= $data['reportDate']; ?></b></div>
<br><br>

<?php foreach ($data['wos'] as $k => $v) { ?>
    <?php $counter = 1; ?>
    <?php foreach ($v as $kk => $vv) { ?>
        <?php $elemCount = count($data['wos'][$k]); ?>
        <?php $class = $kk > 0 ? 'hide' : 'default'; ?>
        <div class="generalWrapper">
            <div class="underlined <?= $class; ?>">
                <table class="table">
                    <tr>
                        <td style="width: 7%" align="left" valign="top" class="bigFont"><b>Kolonne:</b></td>
                        <td style="width: 5%" align="left" valign="top"></td>
                        <td style="width: 88%" align="left" valign="top" class="bigFont"><?= $k; ?></td>
                    </tr>
                </table>
            </div>
            <div class="<?= $class; ?>">
                <table class="table personal">
                    <tr class="personal">
                        <td align="left" valign="top" class="smallFont" style="width: 29%"><b>Personal</b></td>
                        <td align="left" valign="top" class="smallFont underlined" style="width: 71%">
                            <b>
                                <?php
                                $names = [];
                                foreach ($vv['staffPreplanned'] as $key => $val) {
                                    $names[] = $val;
                                }
                                echo implode("&nbsp;&nbsp;&nbsp;&nbsp;", $names)
                                ?>
                            </b>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="<?= $class; ?>">
                <table class="table resources">
                    <?php
                    $resTotal = count($vv['resourcesNonHr']);
                    $resCounter = 1;
                    $resources = "";
                    foreach ($vv['resourcesNonHr'] as $group => $res) {
                        $underlineLastRes = ($resTotal == $resCounter) ? 'underlinedRes' : ''; ?>
                        <tr>
                            <td class="smallFont" align="left" valign="top" style="width: 15%">
                                <b><?php echo $resCounter == 1 ? 'Resourcen ' : ' '; ?></b></td>
                            <td align="left" valign="top" class="<?= $underlineLastRes . ' smallFont' ?>"
                                style="width: 10%"><b><?= $group; ?></b></td>
                            <td align="left" valign="top" class="smallFont underlined"
                                style="width: 4%"><?= str_repeat('&nbsp;', 1); ?></td>
                            <?php foreach ($res as $rnr => $name) {
                                $resources .= $name['name'] . ', ';
                            } ?>
                            <td align="left" valign="top" class="<?= $underlineLastRes . ' smallFont' ?>"
                                style="width: 71%"><?php
                                echo rtrim($resources, ',; ');
                                $resources = ''; ?></td>
                        </tr>
                        <?php $resCounter++;
                    } ?>
                </table>
            </div>
            <div style="width:100%">
                <table class="table">
                    <?php $underlineLast = ($elemCount == $counter) ? 'underlined' : ''; ?>
                    <tr class="<?= $underlineLast; ?>">
                        <td class="smallFont" style="width: 15%" align="left" valign="top">
                            <b><?= $vv['plannedStartTime']; ?></b></td>
                        <td class="smallFont " style="width: 10%" align="left" valign="top">
                            <b><?= $counter . '. ' . $vv['shortDescription']; ?></b></td>
                        <?php $separator = empty($vv['projectName']) ? '' : ', '; ?>
                        <td align="left" valign="top" class="smallFont"
                            style="width: 4%"><?= str_repeat('&nbsp;', 1); ?></td>
                        <?php $projectManager = ($vv['projectManager']) ? $separator . ' ' . $vv['projectManager'] : ''; ?>
                        <td class="smallFont" style="width: 71%" align="left"
                            valign="top"><?= $vv['projectName'] . $projectManager . ', ' . $vv['customerSalutation'] . ' ' . $vv['customerName']; ?></td>
                    </tr>
                </table>
            </div>
        </div>
        <?php if ($kk == count($v) - 1) {
            echo '</br></br>';
        }; ?>
        <?php $counter++; ?>
    <?php } ?>
<?php } ?>
</body>
</html>

