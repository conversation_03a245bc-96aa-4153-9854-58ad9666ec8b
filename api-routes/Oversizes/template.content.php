<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Oversizes())->getData($_GET['billingServiceSheetNo'], $_GET['billingServiceSheetVersionNo']);
}

function displayDate(string $date): string
{
    if (empty($date) || !strtotime($date)) {
        return '';
    }
    return PrintoutHelper::dateConverter($date, 'd.m.Y');
}

function formatNumberOrEmpty(string|float|int $value, int $decimals = 0): string
{
    if (empty($value) || $value == 0) {
        return '';
    }
    return PrintoutHelper::formatTimeNumberWithThousands($value, decimals: $decimals);
}

?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Oversizes</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<?php foreach ($data['oversizeItems'] as $index => $oversize) { ?>

    <?php if (!empty($oversize['subItems'])) { ?>
        <table class="main-table">
            <tr>
                <td colspan="2" class="bold-text"><?= $oversize['itemNo'] ?? '' ?></td> <!-- Gerüst-Pos -->
                <td class="bold-text"><?= $oversize['itemNoRent'] ?? '' ?></td>
                <td colspan="11" class="bold-text"><?= $oversize['description'] ?? '' ?></td>
                <td>&nbsp;</td>
            </tr>
            <tr>
                <td colspan="2" class="bold-text">
                    Grundvorh. <?= ($oversize['includedRentalTime'] ?? '') . ' ' . ($oversize['includedRentalTimeUnit'] ?? '') ?></td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td><?= displayDate($oversize['rentalStartDate'] ?? '') ?></td> <!-- Vorhaltebeginn -->
                <td><?= displayDate($oversize['includedRentalTimeEndDate'] ?? '') ?></td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td class="text-right bold-text"><?= PrintoutHelper::formatTimeNumberWithThousands($oversize['quantityRent'] ?? '') ?></td>
                <td class="bold-text"><?= $oversize['measuringUnitRental'] ?? '' ?></td>
            </tr>
            <tr class="bold-text">
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <?php if (isset($oversize['quantityDismantling'])) { ?>
                    <td colspan="2"><?= $oversize['quantityDismantling'] != 0 ? 'montiert & demontiert:' : '' ?></td>
                <?php } else { ?>
                    <td colspan="2"></td>
                <?php } ?>
                <td colspan="8"><?= PrintoutHelper::formatTimeNumberWithThousands($oversize['quantity'] ?? '') ?></td>
            </tr>
        </table>
        <table class="main-table" style="margin-top: 10px;">
            <?php foreach ($oversize['subItems'] as $subItem) { ?>
                <tr class="dark-row">
                    <!--15 cols-->
                    <td><?= $subItem['itemSortId'] ?? '' ?></td>
                    <td class="bold-text"><?= $subItem['objectDescription'] ?? '' ?></td>
                    <td class="bold-text"><?= $subItem['objectAxis'] ?? '' ?></td>
                    <td class="bold-text"></td> <!-- TODO what to display here? -->
                    <td class="text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['quantityCount'] ?? '') ?></td>
                    <td class="text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['quantityLength'] ?? '') ?></td>
                    <td class="text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['quantityDepth'] ?? '') ?></td>
                    <td class="bold-text text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['quantityHeight'] ?? '') ?></td>
                    <!-- Hohe -->
                    <td class="text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['quantity'] ?? '') ?></td>
                    <td><?= displayDate($subItem['mountingDate']) ?></td>
                    <td><?= displayDate($subItem['includedRentalTimeEndDate']) ?></td>
                    <td><?= displayDate($subItem['preliminaryRentalEndDate']) ?></td>
                    <td><?= displayDate($subItem['rentalEndDate']) ?></td>
                    <td class="text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['rentalPeriod'] ?? '') ?></td>
                    <td class="bold-text text-right"><?= PrintoutHelper::formatTimeNumberWithThousands($subItem['quantityRent'] ?? '') ?></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td colspan="14"><?= nl2br($subItem['detailDescription'] ?? '') ?></td>
                </tr>
            <?php } ?>
        </table>
    <?php } ?>
    <?php if ($index < count($data) - 1) { ?>
        <div class="break-before"></div>
    <?php } ?>
<?php } ?>
</body>
</html>
