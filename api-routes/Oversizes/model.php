<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Oversizes
{
    /** @var array<mixed> */
    private array $subItems = [];

    /**
     * @return array<mixed>
     */
    public function getData(string $billingServiceSheetNo, string $billingServiceSheetVersionNo): array
    {
        $data = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());

        $oversizes = $this->downloadOversizes($billingServiceSheetNo, $billingServiceSheetVersionNo, $curl);
        $allOversizes = $this->downloadAllOversizes($billingServiceSheetNo, $billingServiceSheetVersionNo, $curl);

        $data['oversizeItems'] = [];
        foreach ($oversizes as $oversize) {
            if ($oversize['subItemsAvailableInformation'] > 0) {
                $id = $billingServiceSheetNo . '-' . $billingServiceSheetVersionNo . '-' . $oversize['billingServiceSheetItemId'];

                if (!isset($this->subItems[$id])) {
                    $this->subItems[$id] = $this->downloadSubItems(
                        $billingServiceSheetNo,
                        $billingServiceSheetVersionNo,
                        $oversize['billingServiceSheetItemId'],
                        $curl
                    );
                }

                $oversize['subItems'] = $this->subItems[$id];
            }
            $data['oversizeItems'][] = $oversize;
        }
        $data['oversizes'] = $allOversizes[0];
        $projectNo = $data['oversizes']['projectNo'];
        $data['project'] = PrintoutHelper::downloadProject($projectNo, 'customerNo,customerName,projectNo,projectName', $curl);
        $data['settings'] = PrintoutHelper::downloadInfoSettings($curl);

        return $data;
    }

    /**
     * @return array<mixed>
     */
    private function downloadAllOversizes(string $bssNo, string $bssvNo, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get',
            "v1/oversizes/$bssNo/$bssvNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    /**
     * @return array<mixed>
     */
    private function downloadOversizes(string $bssNo, string $bssvNo, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get',
            "v1/oversizes/items/$bssNo/$bssvNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    /**
     * @return array<mixed>
     */
    private function downloadSubItems(string $bssNo, string $bssvNo, string $bssiNo, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('get',
            "v1/oversizes/subitems/$bssNo/$bssvNo/$bssiNo",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }
}