body {
    border: 0;
    margin: 0;
    padding: 0;
    color: #000000;
    font-family: Arial, sans-serif;
}

.page {
    max-height: 200mm;
    min-height: 200mm;
    min-width: 297mm;
    max-width: 297mm;
    box-sizing: border-box;
    page-break-before: always;
}

.text-gray {
    color: #666666;
}

.heading-1 {
    font-size: 130%;
}

.heading-2 {
    font-size: 120%;
}

.header-table {
    width: 100%;
    padding: 0;
    margin-bottom: 10px;
    border-collapse: collapse;
}

.header-table tr {
    text-align: center;
}

.header-table td img {
    max-height: 60px;
}

.header-table td:first-child {
    text-align: left;
}

.header-table td:last-child {
    text-align: right;
}

.header-table td:first-child img {
    margin-left: 5mm;
}

.header-table td:last-child img {
    margin-right: 5mm;
}

.header-table .triangle {
    width: 0;
    height: 0;
    display: inline-block;
    border-top: 90px solid black;
    border-right: 90px solid transparent;
    border-bottom: 0 solid transparent;
    border-left: 90px solid transparent;
}

.left-content {
    float: left;
    width: 46%;
    margin-left: 5mm;
}

.right-content {
    float: right;
    width: 46%;
    margin-right: 5mm;
}

.left-table {
    font-size: 90%;
    margin-top: 10px;
    border-collapse: collapse;
}

.left-table td {
    padding: 5px 7px;
    border: 2px solid black;
    width: 50%;
}

.duties-table-header {
    margin-top: 25px;
    text-align: center;
    width: 100%;
    background-color: #C0C0C0;
    padding-bottom: 4px;
}

.duties-table {
    width: 100%;
    font-size: 90%;
}

.checkbox-size {
    font-size: 250%;
    padding-top: 3px;
}

.duty-padding-top {
    padding-top: 10px;
}

.right-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 90%;
}

.right-table td {
    height: 42px;
    padding: 4px;
    border: 2px solid black;
}

.right-table tr:first-child td {
    width: 50%;
    text-align: center;
}