<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "projectNo", required: true);
$config->addParameter(RouteConfigParameterType::int, "internalNo", required: true);
$config->wkhtmltopdfArguments = [
    "--disable-smart-shrinking",
    "--no-pdf-compression",
    "--page-size", "a4",
    "--orientation", "Landscape",
    "--margin-bottom", "0",
    "--margin-top", "0",
    "--margin-left", "0",
    "--margin-right", "0",
    "--dpi", "300",
];
return $config;