<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/CiEntityCreator.php';

$project = CiEntityCreator::createProject($customerNo = 3379);
$projectNo = $project['projectNo'];
$scaffold = CiEntityCreator::createScaffold($projectNo);
$internalNo = $scaffold['intnr'];

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::GreenTag,
    params: [
        "projectNo" => $projectNo,
        "internalNo" => $internalNo
    ]
); 