<?php

use MediaPrintV2\HardCopyUpload;

require_once __DIR__ . '/HardCopyUpload.php';

$config = new RouteConfig();
$config->usePlaywright(new PlaywrightConfig(
    marginTopMm: 30,
    marginBottomMm: 10,
    marginLeftMm: 10,
    marginRightMm: 10
));
$config->addParameter(RouteConfigParameterType::int, "projectNo", required: true);
$config->addParameter(RouteConfigParameterType::bool, "saveHardCopy", required: false);
$config->addParameter(RouteConfigParameterType::bool, "hidePageNumbers", required: false);
$config->afterPdfGenerationCallback = function (string $pdfPath) {
    if ((Request::$query['saveHardCopy'] ?? "false") === "true") {
        HardCopyUpload::uploadHardCopy($pdfPath, "MediaPrintV2");
    }
};
return $config;