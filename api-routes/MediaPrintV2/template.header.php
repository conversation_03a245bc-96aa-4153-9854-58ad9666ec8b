<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_MediaPrintV2())->getData(
        projectNo: $_GET['projectNo'] ?? null,
        // pass workingOrderNo for MediaWorkingOrderV2
        workingOrderNo: $_GET['workingOrderNo'] ?? null,
        saveHardCopy: $_GET['saveHardCopy'] ?? null,
        hidePageNumbers: $_GET['hidePageNumbers'] ?? null
    );
}

$project = $data['project'] ?? '';
$projectDate = empty($project['projectValidStartDate']) ? '' : date('d.m.Y', strtotime($project['projectValidStartDate']));
?>

<style>
    header {
        padding: 0 0 0 10mm;
        font-family: Arial, sans-serif;

        .gray {
            color: #ccc;
        }

        table {
            font-size: 12px;
        }
    }
</style>

<header style="display: flex; width: 100%; align-items: center; gap: 10mm">
    <div style="max-width: 40%;">
        <img src="<?= PrintoutHelper::getImageBase64String($data['logo']) ?>"
             style="max-height: 20mm; max-width: 100%; object-fit: contain;"
             alt="">
    </div>
    <table style="width: 60%;">
        <tr>
            <td class="gray" style="width: 13%; padding-right: 5mm">Kundenname</td>
            <td><?= $project['customerName'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="gray">Datum</td>
            <td><?= $projectDate ?></td>
        </tr>
        <tr>
            <td class="gray">Projekttitel</td>
            <td><?= $project['projectName'] ?? '' ?></td>
        </tr>
    </table>
</header>