<?php

namespace MediaPrintV2;

use CURLFile;
use PrintoutCurl;
use PrintoutHelper;
use Request;

class HardCopyUpload
{
    private static function upload(string $pdfPath, string $relType, string $relKey): void
    {
        $headers = PrintoutHelper::getHeadersForApiCalls();
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $file = new CURLFile($pdfPath, "application/pdf", basename($pdfPath));
        // note that on any error uploadFile() aborts the entire request
        $curl->uploadFile([
            "rel_type" => $relType,
            "rel_key" => $relKey,
            "filename" => basename($pdfPath),
            "category" => "",
            "description" => "",
            "file-form-data" => $file,
        ],
            [
                "Accept" => "*/*",
                // set principal header
                "Principal" => str_replace("Principal: ", "", $headers['httpheader'][0]),
                // set Authorization header
                "Authorization" => str_replace("Authorization: ", "", $headers['httpheader'][1]),
            ]
        );
    }

    public static function uploadHardCopy(string $pdfPath, string $route): void
    {
        if ($route == "MediaPrintV2" || $route == "MediaPrintV3") {
            $project = Request::$query['projectNo'];
            HardCopyUpload::upload($pdfPath, "ktr", $project);
        } else if ($route == "MediaWorkingOrderV2") {
            HardCopyUpload::upload($pdfPath, "ktr-aanr", Request::$query['projectNo'] . "-" . Request::$query['workingOrderNo']);
        } else if ($route == "MediaProjectV2") {
            HardCopyUpload::upload($pdfPath, "ktr", Request::$query['projectNo']);
        } else {
            die_with_response_code(message: "Unknown route: $route");
        }
    }
}