<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_MediaPrintV2())->getData(
        projectNo: $_GET['projectNo'] ?? null,
        // pass workingOrderNo for MediaWorkingOrderV2
        workingOrderNo: $_GET['workingOrderNo'] ?? null,
        saveHardCopy: $_GET['saveHardCopy'] ?? null,
        hidePageNumbers: $_GET['hidePageNumbers'] ?? null
    );
}
if ($data['hidePageNumbers']) {
    // display any HTML to not have the default Playwright footer
    ?>
    <div></div>
<?php } else { ?>
    <style>
        footer {
            font-family: Arial, sans-serif;
            font-size: 12px;
            width: 100%;
            display: flex;
            padding-right: 10mm;
            justify-content: end;

            table tr td {
                padding: 5px 5px 20px 5px;
            }

            table tr td:first-child {
                font-size: 130%;
                font-weight: bold;
                border-right: 2px solid gray;
            }

            table tr td:last-child {
                color: gray;
            }
        }
    </style>

    <footer>
        <table>
            <tr>
                <td class="pageNumber"></td>
                <td class="totalPages"></td>
            </tr>
        </table>
    </footer>
<?php } ?>