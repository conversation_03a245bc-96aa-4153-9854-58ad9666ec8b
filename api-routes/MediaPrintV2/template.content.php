<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_MediaPrintV2())->getData(
        projectNo: $_GET['projectNo'] ?? null,
        // pass workingOrderNo for MediaWorkingOrderV2
        workingOrderNo: $_GET['workingOrderNo'] ?? null,
        saveHardCopy: $_GET['saveHardCopy'] ?? null,
        hidePageNumbers: $_GET['hidePageNumbers'] ?? null
    );
}

function convertUtcToBerlinTime(string $utcTime): string
{
    /** @noinspection PhpUnhandledExceptionInspection */
    $datetime = new DateTime($utcTime, new DateTimeZone('UTC'));
    $datetime->setTimezone(new DateTimeZone('Europe/Berlin'));

    return $datetime->format('d.m.Y H:i:s');
}

?>

<!doctype html>
<html lang="de">
<head>
    <title>Media Print V3</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("assets/css/emoji.css") ?>">
    <script src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("assets/js/emoji.js") ?>"></script>
    <style>
        body {
            width: 190mm;
        }
    </style>
</head>
<body>
<?php
$i = 0;
$totalFiles = 0;
foreach ($data['files'] as $files) {
    $totalFiles += count($files);
}
foreach ($data['files'] as $files) {
    foreach ($files as $file) {
        $i++;
        ?>
        <div class="container">
            <div class="left-content">
                <div style="position: relative">
                    <?php if ($file['isVideo'] && $file['size']) { ?>
                        <div style="position: absolute; width: 100%; display: flex; justify-content: end;">
                            <p style="background: rgba(0,0,0,0.47); color: white; border-radius: 6px; margin-right: 3mm; margin-top: 3mm; padding: 2mm">
                                <?= $file['size'] ?>
                            </p>
                        </div>
                    <?php } ?>
                    <?php if ($file['isVideo']) { ?>
                        <div style="position: absolute; width: 100%; display: flex; justify-content: center; bottom: 0">
                            <div style="background: rgba(255,255,255,0.7); border-radius: 20px; padding: 5mm; margin-bottom: 5mm">
                                <p style="text-align: center; width: 100%; margin: 0 0 4mm 0">Anschauen</p>
                                <div style="display: flex">
                                    <div style="width: 50%">
                                        <img src="<?= $file['qrString'] ?>"
                                             style="width: 120px; height: 120px"
                                             alt="">
                                    </div>
                                    <a style="width: 120px; display: flex; align-self: center; justify-content: center"
                                       href="<?= $file['filepath'] ?>">
                                        <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/play-icon.svg") ?>"
                                             style="width: 72px; height: 72px"
                                             alt="">
                                    </a>
                                </div>
                                <div style="display: flex">
                                    <div style="width: 50%">
                                        <b style="width: 100%; text-align: center; display: inline-block">Scannen</b>
                                    </div>
                                    <div style="width: 50%">
                                        <a href="<?= $file['filepath'] ?>"
                                           style="text-decoration: none; font-weight: bold; width: 100%; text-align: center; display: inline-block; color: black">Klicken</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php } ?>
                    <?php if ($file['isVideo']) { ?>
                        <img src="<?= $file['previewUrl'] ?>"
                             style="min-height: 500px; max-height: 650px; width: 100%; object-fit: cover"
                             alt="">
                    <?php } else { ?>
                        <img src="<?= $file['previewUrl'] ?>"
                             style="max-height: 650px; max-width: 100%; object-fit: contain"
                             alt="">
                    <?php } ?>
                </div>
                <table class="meta-data-table">
                    <tr>
                        <td>ID</td>
                        <td>&nbsp;</td>
                        <td><?= $file['fid'] ?></td>
                    </tr>
                    <?php if (!empty($file['showUploader'])) { ?>
                        <tr>
                            <td>Autor</td>
                            <td>&nbsp;</td>
                            <td>
                                <div style="display: flex; justify-content: end; align-items: center">
                                    <?php
                                    $profilePic = $data['employees'][$file['uploader']]['profilePictureUrl'] ?? '';
                                    if (!empty($profilePic)) { ?>
                                        <img src="<?= $profilePic ?>" alt=""
                                             style="object-fit: cover; height: 30px; width: 30px; border-radius: 50%; margin-left: auto; margin-right: 5mm">
                                    <?php }
                                    echo $data['employees'][$file['uploader']]['name'] ?? '';
                                    ?>
                                </div>
                            </td>
                        </tr>
                    <?php } ?>
                    <?php if (!empty($file['showCreationDate'])) { ?>
                        <tr>
                            <td>Erstellt am</td>
                            <td>&nbsp;</td>
                            <td><?= convertUtcToBerlinTime($file['creation_timeUTC']) ?></td>
                        </tr>
                    <?php } ?>
                    <?php if (!empty($file['showUploadDate'])) { ?>
                        <tr>
                            <td>Upload-Zeit</td>
                            <td>&nbsp;</td>
                            <td><?= convertUtcToBerlinTime($file['upload_timeUTC']) ?></td>
                        </tr>
                    <?php } ?>
                    <?php if (!empty($file['showWorkingOrderNumber'])) { ?>
                        <tr>
                            <td>Arbeitsauftrag</td>
                            <td>&nbsp;</td>
                            <td>
                                <?= $file['rel_key'] ?>
                            </td>
                        </tr>
                    <?php } ?>
                    <?php if (!empty($data['scaffoldListByRel'][$file['rel_key']])) { ?>
                        <tr>
                            <td>Gerüstlistennummer</td>
                            <td>&nbsp;</td>
                            <td>
                                <?= $data['scaffoldListByRel'][$file['rel_key']] ?>
                            </td>
                        </tr>
                    <?php } ?>
                </table>
            </div>
            <div class="right-content" style="max-height: 1100px">
                <?php if (($file['description'] ?? "") !== "") { ?>
                    <h2 style="word-break: break-word"><?= $file['description'] ?></h2>
                <?php }
                if (!empty($file['showComments'])) {
                    $comments = $data['comments'][$file['fid']] ?? [];
                    foreach ($comments as $comment) { ?>
                        <table class="comment-table">
                            <tr>
                                <td rowspan="3" style="width: 70px">
                                    <div class="comment-profile"
                                         style="background-image: url(<?= $data['employees'][$comment['createdBy']]['profilePictureUrl'] ?? '' ?>)"></div>
                                </td>
                                <td><?= $data['employees'][$comment['createdBy']]['name'] ?? '' ?></td>
                            </tr>
                            <tr>
                                <td>
                                    <b class="comment-text" style="word-break: break-word">
                                        <?= nl2br($comment['commentText']) ?>
                                    </b>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-secondary">
                                    <?= convertUtcToBerlinTime($comment['createdOn']) ?>
                            </tr>
                        </table>
                        <hr>
                    <?php } ?>
                <?php } ?>
            </div>
        </div>
        <?php if ($i !== $totalFiles) { ?>
            <div class="break-before"></div>
        <?php } ?>
    <?php } ?>
<?php } ?>
</body>
<!--suppress ES6ConvertVarToLetConst, JSUnresolvedReference -->
<script>
    var emoji = new EmojiConvertor();
    emoji.replace_mode = 'css';
    // GitHub Pages deployment of https://github.com/VERO-Digital-Solutions/emoji-data
    emoji.img_sets.apple.path = 'https://vero-digital-solutions.github.io/emoji-data/img-apple-64/';
    var elements = document.getElementsByClassName('comment-text')
    for (var i = 0; i < elements.length; i++) {
        var element = elements[i];
        element.innerHTML = emoji.replace_unified(element.innerHTML);
    }
</script>
</html>