body {
    border: 0;
    margin: 0;
    padding: 0;
    width: 100%;
    font-family: Arial, sans-serif;
}

h2 {
    margin-top: 0;
    border-bottom: 2px solid #e42427;
    padding-bottom: 25px;
    margin-bottom: 25px;
}

.text-secondary {
    color: gray;
}

.container {
    overflow: hidden;
    max-height: 1200px;
    display: -webkit-box;
}

.left-content {
    width: 60%;
    padding-right: 25px;
}

.break-before {
    page-break-before: always;
}

.media {
    width: 100%;
    height: 680px;
}

.meta-data-table {
    width: 100%;
    margin-top: 20px;
    border-collapse: separate;
    border-spacing: 0 10px;
}

.meta-data-table td {
    padding: 10px 5px;
}

.meta-data-table td:first-child {
    text-align: left;
}

.meta-data-table td:nth-child(2) {
    text-align: right;
}

.meta-data-table td:last-child {
    text-align: right;
    font-weight: bold;
}

.meta-data-table tr:nth-child(odd) td {
    background: #f8f8f8;
}

.meta-data-table tr:nth-child(even) td {
    background-color: #e7e7e7;
}

.right-content {
    width: 35%;
}

.right-content hr {
    width: 100%;
    margin: 25px 0;
    background: #cccccc;
}

.comment-table {
    width: 100%;
}

.comment-table tr td {
    padding: 5px;
    vertical-align: top;
}

.comment-profile {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: inline-block;
    background-color: gray;
    background-position: center;
    background-size: cover;
}