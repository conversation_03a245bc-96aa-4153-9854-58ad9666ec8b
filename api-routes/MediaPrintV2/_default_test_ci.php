<?php
require_once __DIR__ . '/../../scripts/CiEntityCreator.php';
require_once __DIR__ . '/../../PrintoutCompiler.php';

$project = CiEntityCreator::createProject($customerNo = 3379);
$projectNo = $project['projectNo'];

$imageFilePaths = [
    __DIR__ . '/../../tests/images/190x50.png',
    __DIR__ . '/../../tests/images/300x50.png',
];
$uploads = CiEntityCreator::uploadImagesToProject($projectNo, $imageFilePaths);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::MediaPrintV2,
    params: [
        "projectNo" => $projectNo,
        "hidePageNumbers" => "true"
    ]
);