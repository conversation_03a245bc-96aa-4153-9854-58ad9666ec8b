<?php
require_once __DIR__ . '/../../scripts/CiEntityCreator.php';
require_once __DIR__ . '/../../PrintoutCompiler.php';

$project = CiEntityCreator::createProject($customerNo = 3379);
$projectNo = $project['projectNo'];
$scaffold = CiEntityCreator::createScaffoldListItem($projectNo);
$ktr = $scaffold['ktr'];
$intnr = $scaffold['intnr'];

$workingOrder = CiEntityCreator::createWorkingOrderFromScaffoldListItem($ktr, $intnr);
$workingOrderNo = $workingOrder['aanr'];

$imageFilePaths = [
    __DIR__ . '/../../tests/images/190x50.png',
    __DIR__ . '/../../tests/images/300x50.png',
];
$uploads = CiEntityCreator::uploadImagesToWorkingOrder($projectNo, $workingOrderNo, $imageFilePaths);
printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::MediaPrintV2,
//    fileType: PrintoutFileType::Html,
    params: [
        "projectNo" => "$projectNo",
        "hidePageNumbers" => "true"
    ]
);
