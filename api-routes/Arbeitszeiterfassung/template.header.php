<?php
/** @noinspection DuplicatedCode */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Arbeitszeiterfassung())->getData($_GET['projectId'], $_GET['workingOrderId'] ?? 0, $_GET['hideBesonderheiten'] ?? false);
}
$isProject = $data['isProject'];
?>

<!doctype html>
<html lang="de">
<head>
    <title>Arbeitszeiterfassung</title>
    <meta charset="UTF-8">
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
        }

        table {
            width: 100%;
            margin-top: 30px;
            border-collapse: collapse;
        }

        table tr td {
            padding: 3px 6px;
            font-weight: 700;
            border: 1px solid black;
        }

    </style>

</head>
<body onload="subst()">

<table id="table">
    <tr>
        <td style="width: 10%">Name</td>
        <td style="width: 10%">Vorname</td>
        <?php if ($isProject) { ?>
            <td style="width: 12%">Funktion</td>
            <td style="width: 4%">Nr.</td>
            <td style="width: 20%">Tätigkeit des Mitarbeiters</td>
            <td style="width: 8%">Datum</td>
            <td style="width: 5%">Beginn</td>
            <td style="width: 5%">Ende</td>
            <td style="width: 8%">Arbeit (min)</td>
            <td style="width: 9%">Pause (min)</td>
            <td style="width: 10%">Dauer (min)</td>
        <?php } else { ?>
            <td style="width: 10%">Funktion</td>
            <td style="width: 5%">Nr.</td>
            <td style="width: 25%">Tätigkeit des Mitarbeiters</td>
            <td style="width: 5%">Beginn</td>
            <td style="width: 5%">Ende</td>
            <td style="width: 10%">Arbeit (min)</td>
            <td style="width: 10%">Pause (min)</td>
            <td style="width: 10%">Dauer (min)</td>
        <?php } ?>
    </tr>
</table>

</body>

<!--suppress ES6ConvertVarToLetConst, JSUnresolvedReference -->
<script>
    function subst() {
        var query_strings_from_url = document.location.search.substring(1).split('&');
        for (var query_string in query_strings_from_url) {
            if (query_strings_from_url.hasOwnProperty(query_string)) {
                var parts = query_strings_from_url[query_string].split('=', 2);
                var value = parts[1]
                if (parts[0] === "section") {
                    if (value === "" || value === "header-start" || value === "besonderheiten") {
                        document.getElementById('table').style.display = 'none'
                    }
                }
            }
        }
    }
</script>

</html>