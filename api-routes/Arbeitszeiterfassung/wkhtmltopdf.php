<?php

require_once '../../Pdf.php';

Pdf::$additionalArguments = [
    "--no-pdf-compression",
    "--page-size", "a4",
    "--margin-bottom", "10",
    "--margin-top", "10",
    "--margin-left", "10",
    "--margin-right", "10",
    "--dpi", "300",
    '--header-html',
    'ArbeitszeiterfassungHeader.template.php',
    '--footer-html',
    'ArbeitszeiterfassungFooter.template.php'
];

Pdf::$landscapeMode = true;

$projectId = 2308026;
$workingOrderId = 113;
Pdf::$outputFile = "output-" . Pdf::$principal . '-' . $projectId. '-' . $workingOrderId . '.pdf';
Pdf::compile(__DIR__, [
    'projectId' => $projectId,
    'workingOrderId' => $workingOrderId
]);

$projectId = 2308026;
$workingOrderId = 112;
Pdf::$outputFile = "output-" . Pdf::$principal . '-' . $projectId. '-' . $workingOrderId . '.pdf';
Pdf::compile(__DIR__, [
    'projectId' => $projectId,
    'workingOrderId' => $workingOrderId
]);
