<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "projectId", required: true);
$config->addParameter(RouteConfigParameterType::int, "workingOrderId", required: false);
$config->addParameter(RouteConfigParameterType::bool, "hideBesonderheiten", required: false);
// add only for afterPdfGenerationCallback
$config->addParameter(RouteConfigParameterType::bool, "appendMediaPrint", required: false);

$config->afterPdfGenerationCallback = function (string $pdfPath) {
    if (!isset($_GET['appendMediaPrint']) || $_GET['appendMediaPrint'] != "true") {
        return;
    }

    $isArbeitszeiterfassungRequestedForProject = ($_GET['workingOrderId'] ?? 0) == 0;
    $outputPath = tempnam(sys_get_temp_dir(), 'printouts-') . '.pdf';
    $params = [
        "projectNo" => $_GET['projectId']
    ];

    // set route, so $file['showWorkingOrderNumber'] and others are filled
    if ($isArbeitszeiterfassungRequestedForProject) {
        Request::$config = require_once __DIR__ . '/../MediaPrintV2/config.php';
        Request::setRawRoute("MediaPrintV2");
    } else {
        Request::$config = require_once __DIR__ . '/../MediaWorkingOrderV2/config.php';
        Request::setRawRoute("MediaWorkingOrderV2");
        $params['workingOrderNo'] = $_GET['workingOrderId'];
    }

    $requestBody = constructPlaywrightServerRequestBody(Request::$config, $outputPath, "MediaPrintV2", $params);
    callPlaywrightServerToGeneratePdf($requestBody);
    $mergedPdfPath = tempnam(sys_get_temp_dir(), 'printouts-') . '.pdf';
    $mergedPdfPath = mergePdfs([$pdfPath, $outputPath], $mergedPdfPath);
    // override the first generated PDF $pdfPath with the merged PDF
    $success = rename($mergedPdfPath, $pdfPath);
    if (!$success) {
        die_with_response_code(message: "Merging PDFs failed?");
    }
};

$config->wkhtmltopdfArguments = array_merge($config->wkhtmltopdfArguments ?? [], [
    "--no-pdf-compression",
    "--page-size", "a4",
    "--margin-bottom", "12mm",
    "--margin-top", "10mm",
    "--margin-left", "10mm",
    "--margin-right", "10mm",
    "--orientation", "Landscape",
    "--dpi", "300",
]);
return $config;