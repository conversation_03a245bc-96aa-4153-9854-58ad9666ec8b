body {
    margin: 0;
    width: 100%;
    font-family: Arial, sans-serif;
}

.tr-bold td {
    font-weight: 700;
}

h1 {
    font-size: 0;
    max-height: 0;
    color: transparent;
}

table.bordered, .bordered th, .bordered td {
    border: 1px solid black;
    border-collapse: collapse;
}

.align-top {
    vertical-align: top;
}

td {
    padding: 3px 6px;
}

.font-bold {
    font-weight: 700;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.bg-blue {
    background: rgb(217, 225, 242);
}

.pl-left-td {
    padding-left: 12px;
}

.w-full {
    width: 100%;
}

.nested-table {
    width: 100%;
}

.nested-table td {
    border: 0;
    padding: 0;
}

.border-none {
    /* use !important to unset the .bordered style */
    border: none !important;
}

.main-table tr.besonderheiten td {
    height: 30mm;
    vertical-align: top;
    border-bottom: none
}

.main-table tr.unterschrift td:first-child {
    border-top: none;
}

.main-table tr.unterschrift td:last-child {
    width: 150px;
    border-top: 3px solid black;
}
