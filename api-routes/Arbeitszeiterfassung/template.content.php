<?php
/** @noinspection DuplicatedCode */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Arbeitszeiterfassung())->getData($_GET['projectId'], $_GET['workingOrderId'] ?? 0, $_GET['hideBesonderheiten'] ?? false);
}
$isProject = $data['isProject'];

function formatDate(string $date): string
{
    // 2021-01-01 => 01.01.2021
    return substr($date, 8, 2) . "." . substr($date, 5, 2) . "." . substr($date, 0, 4);
}

/**
 * @return int 0 on any errors
 */
function calculateMinutes(string $startTime, string $endTime): int
{
    if ($startTime === '' || $endTime === '') {
        return 0;
    }
    try {
        $start = new DateTime($startTime);
        $end = new DateTime($endTime);
    } catch (Exception) {
        return 0;
    }
    $interval = $start->diff($end);
    return ($interval->h * 60) + $interval->i;
}

?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Arbeitszeiterfassung</title>
</head>

<body>
<table style="width: 100%">
    <tr>
        <td style="font-size: 30px; text-align: center; width: 70%">
            Arbeitszeiterfassung // Stundennachweis
        </td>
        <td style="width: 30%; height: 60px; text-align: right">
            <?php if (isset($data['logo'])) { ?>
                <img
                        style="max-height: 60px; max-width: 100%;"
                        alt=""
                        src="<?= $data['logo'] ?>"/>
            <?php } ?>
        </td>
    </tr>
</table>

<?php if ($isProject) { ?>
    <table class="w-full bordered" style="border-top: none; border-bottom: none; border-left: none">
        <tbody>
        <tr>
            <td colspan="2" class="text-center font-bold bg-blue">Projektdaten</td>
        </tr>
        <tr>
            <td class="bg-blue font-bold" style="width: 200px">Kundenname</td>
            <td><?= $data['project']['customerName'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="bg-blue font-bold">Projektnummer</td>
            <td><?= $data['projectId'] ?></td>
        </tr>
        <tr>
            <td class="bg-blue font-bold">Projektbezeichnung</td>
            <td><?= $data['project']['projectName'] ?></td>
        </tr>
        <tr>
            <td class="bg-blue font-bold">Adresse</td>
            <td>
                <?= PrintoutHelper::formatAddress(
                    $data['project']['projectSiteAddress'] ?? '',
                    $data['project']['projectSiteZipCode'] ?? '',
                    $data['project']['projectSiteCity'] ?? '');
                ?>
            </td>
        </tr>
        <tr>
            <td class="bg-blue font-bold" style="width: 45mm">zuständiger Bauleiter</td>
            <td>
                <?= $data['bauleiter'] ?? '';
                if (isset($data['bauleiter-email']) && $data['bauleiter-email'] != "") { ?>
                    <svg style="overflow: visible; color: currentcolor; margin-bottom: -3px; margin-left: 9px"
                         fill="currentColor" stroke-width="0" xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 1024 1024" height="1em" width="1em">
                        <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232 512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0 0 68.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"></path>
                    </svg>
                    <?= $data['bauleiter-email'];
                }
                if (isset($data['bauleiter-handy']) && $data['bauleiter-handy'] != "") { ?>
                    <svg fill="currentColor" stroke-width="0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"
                         style="overflow: visible; color: currentcolor; margin-bottom: -3px; margin-left: 9px"
                         height="1em"
                         width="1em">
                        <path d="M885.6 230.2 779.1 123.8a80.83 80.83 0 0 0-57.3-23.8c-21.7 0-42.1 8.5-57.4 23.8L549.8 238.4a80.83 80.83 0 0 0-23.8 57.3c0 21.7 8.5 42.1 23.8 57.4l83.8 83.8A393.82 393.82 0 0 1 553.1 553 395.34 395.34 0 0 1 437 633.8L353.2 550a80.83 80.83 0 0 0-57.3-23.8c-21.7 0-42.1 8.5-57.4 23.8L123.8 664.5a80.89 80.89 0 0 0-23.8 57.4c0 21.7 8.5 42.1 23.8 57.4l106.3 106.3c24.4 24.5 58.1 38.4 92.7 38.4 7.3 0 14.3-.6 21.2-1.8 134.8-22.2 268.5-93.9 376.4-201.7C828.2 612.8 899.8 479.2 922.3 344c6.8-41.3-6.9-83.8-36.7-113.8z"></path>
                    </svg>
                    <?= $data['bauleiter-handy'];
                }
                ?>
            </td>
        </tr>
        </tbody>
    </table>
<?php } else { ?>
    <table class="w-full bordered" style="border-top: none; border-bottom: none; border-left: none">
        <tbody>
        <tr>
            <td colspan="2" class="text-center font-bold bg-blue">Projektdaten</td>
            <td style="width: 10mm; border-bottom: none; border-top: none">&nbsp;</td>
            <td colspan="2" class="text-center font-bold bg-blue">Arbeitsauftragsdaten</td>
        </tr>
        <tr>
            <td class="bg-blue font-bold" style="width: 200px">Kundenname</td>
            <td><?= $data['project']['customerName'] ?? '' ?></td>
            <td style="width: 10mm; border-bottom: none; border-top: none">&nbsp;</td>
            <td class="bg-blue font-bold">Ausführungsdatum</td>
            <td class="text-right">
                <?= PrintoutHelper::dateConverter($data['wo']['plannedDate'], 'd.m.Y') ?>
            </td>
        </tr>
        <tr>
            <td class="bg-blue font-bold">Projektnummer</td>
            <td><?= $data['projectId'] ?></td>
            <td style="width: 10mm; border-bottom: none; border-top: none">&nbsp;</td>
            <td class="font-bold bg-blue">Wochentag</td>
            <td class="text-right">
                <?= PrintoutHelper::getWeekDayName(intval(date('N', strtotime($data['wo']['plannedDate']))), "de") ?>
            </td>
        </tr>
        <tr>
            <td class="bg-blue font-bold">Projektbezeichnung</td>
            <td><?= $data['project']['projectName'] ?></td>
            <td style="width: 10mm; border-bottom: none; border-top: none">&nbsp;</td>
            <td class="font-bold bg-blue">geplante Tätigkeiten</td>
            <td class="text-right"><?= $data['wo']['taskName'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="bg-blue font-bold">Adresse</td>
            <td>
                <?= PrintoutHelper::formatAddress(
                    $data['project']['projectSiteAddress'] ?? '',
                    $data['project']['projectSiteZipCode'] ?? '',
                    $data['project']['projectSiteCity'] ?? '');
                ?>
            </td>
            <td style="width: 10mm; border-bottom: none; border-top: none">&nbsp;</td>
            <td class="font-bold bg-blue">geplante Fahrzeuge</td>
            <td class="text-right">
                <?= implode(", ", $data['resources']) ?>
            </td>
        </tr>
        <tr>
            <td class="bg-blue font-bold" style="width: 45mm">zuständiger Bauleiter</td>
            <td>
                <?= $data['bauleiter'] ?? '';
                if (isset($data['bauleiter-email']) && $data['bauleiter-email'] != "") { ?>
                    <svg style="overflow: visible; color: currentcolor; margin-bottom: -3px; margin-left: 9px"
                         fill="currentColor" stroke-width="0" xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 1024 1024" height="1em" width="1em">
                        <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232 512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0 0 68.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"></path>
                    </svg>
                    <?= $data['bauleiter-email'];
                }
                if (isset($data['bauleiter-handy']) && $data['bauleiter-handy'] != "") { ?>
                    <svg fill="currentColor" stroke-width="0" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"
                         style="overflow: visible; color: currentcolor; margin-bottom: -3px; margin-left: 9px"
                         height="1em"
                         width="1em">
                        <path d="M885.6 230.2 779.1 123.8a80.83 80.83 0 0 0-57.3-23.8c-21.7 0-42.1 8.5-57.4 23.8L549.8 238.4a80.83 80.83 0 0 0-23.8 57.3c0 21.7 8.5 42.1 23.8 57.4l83.8 83.8A393.82 393.82 0 0 1 553.1 553 395.34 395.34 0 0 1 437 633.8L353.2 550a80.83 80.83 0 0 0-57.3-23.8c-21.7 0-42.1 8.5-57.4 23.8L123.8 664.5a80.89 80.89 0 0 0-23.8 57.4c0 21.7 8.5 42.1 23.8 57.4l106.3 106.3c24.4 24.5 58.1 38.4 92.7 38.4 7.3 0 14.3-.6 21.2-1.8 134.8-22.2 268.5-93.9 376.4-201.7C828.2 612.8 899.8 479.2 922.3 344c6.8-41.3-6.9-83.8-36.7-113.8z"></path>
                    </svg>
                    <?= $data['bauleiter-handy'];
                }
                ?>
            </td>
            <td style="width: 10mm; border-bottom: none !important; border-top: none !important;">&nbsp;</td>
            <td class="font-bold bg-blue">Arbeitsauftragsnummer</td>
            <td style="width: 50mm"
                class="text-right pl-left-td"><?= $data['projectId'] . "-" . $data['workingOrderId'] ?></td>
        </tr>
        </tbody>
    </table>
<?php } ?>

<?php if (!$isProject) { ?>
    <table class="w-full bordered" style="margin-top: 20px">
        <tbody>
        <tr>
            <td colspan="2" class="text-center font-bold bg-blue">
                Beschreibung des Arbeitsauftrages der planmäßig zu erledigenden Arbeiten
            </td>
        </tr>
        <tr>
            <td style="width: 200px" class="font-bold bg-blue">Kurztext</td>
            <td><?= $data['wo']['shortDescription'] ?? '' ?></td>
        </tr>
        <tr>
            <td class="font-bold bg-blue align-top">Langtext</td>
            <td class="align-top"><?= nl2br($data['wo']['longDescription'] ?? '') ?></td>
        </tr>
        </tbody>
    </table>
<?php } ?>

<table class="w-full bordered" style="margin-top: 20px">
    <tbody>
    <tr>
        <td colspan="2" class="text-center font-bold bg-blue">Zugehörige Dokumente</td>
    </tr>
    <tr>
        <td style="width: 200px" class="font-bold bg-blue">Bestellung vom Kunden</td>
        <td></td>
    </tr>
    <tr>
        <td class="font-bold bg-blue">Ladeliste</td>
        <td></td>
    </tr>
    <tr>
        <td class="font-bold bg-blue align-top">Fotos</td>
        <td>
            <?php if ($isProject) {
                echo $data['fileCount'] . " insgesamt";
            } else {
                echo implode(", ", $data['fileIds']);
            } ?>
        </td>
    </tr>
    </tbody>
</table>

<table style="margin-top: 30px" class="main-table w-full bordered">
    <tbody>
    <tr class="tr-bold">
        <td style="width: 10%">
            Name
            <h1>header-start</h1>
        </td>
        <td style="width: 10%">Vorname</td>
        <?php if ($isProject) { ?>
            <td style="width: 12%">Funktion</td>
            <td style="width: 4%">Nr.</td>
            <td style="width: 20%">Tätigkeit des Mitarbeiters</td>
            <td style="width: 8%">Datum</td>
            <td style="width: 5%">Beginn</td>
            <td style="width: 5%">Ende</td>
            <td style="width: 8%">Arbeit (min)</td>
            <td style="width: 9%">Pause (min)</td>
            <td style="width: 10%">Dauer (min)</td>
        <?php } else { ?>
            <td style="width: 10%">Funktion</td>
            <td style="width: 5%">Nr.</td>
            <td style="width: 25%">Tätigkeit des Mitarbeiters</td>
            <td style="width: 5%">Beginn</td>
            <td style="width: 5%">Ende</td>
            <td style="width: 10%">Arbeit (min)</td>
            <td style="width: 10%">Pause (min)</td>
            <td style="width: 10%">Dauer (min)</td>
        <?php } ?>
    </tr>

    <?php

    function emptyStringOnNull(int $number): string
    {
        if ($number === 0) {
            return "";
        }
        return strval($number);
    }

    $globalTotalSumWork = 0;
    $globalTotalSumBreak = 0;
    $globalTotalSumDrive = 0;
    $globalTotalSumDriveFrom = 0;
    $globalTotalSums = 0;
    $i = 1;
    foreach ($data['hours'] as $pnr => $employeeHours) {
        $employeeHourCount = 0;
        $totalSumWork = 0;
        $totalSumWorkAndDrive = 0;
        $totalSumBreak = 0;
        $totalSumDrive = 0;
        $totalSumDriveFrom = 0;
        $totalTotals = 0;
        foreach ($employeeHours as $employeeHour) {
            // note that the break minutes are subtracted here
            $sumWork = calculateMinutes($employeeHour['start'], $employeeHour['end']);
            $sumDriveFrom = calculateMinutes($employeeHour['startDriveFrom'], $employeeHour['endDriveFrom']);
            $sumBreak = 0;
            foreach ($employeeHour['breaks'] ?? [] as $break) {
                $sumBreak += calculateMinutes($break['start'], $break['end']);
            }
            $sumWork -= $sumBreak;
            $sumDrive = calculateMinutes($employeeHour['startDriveFrom'], $employeeHour['endDriveFrom']) +
                calculateMinutes($employeeHour['startDriveTo'], $employeeHour['endDriveTo']);
            $totals = $sumWork + $sumDrive + $sumBreak;
            $totalSumWork += $sumWork;
            $totalSumWorkAndDrive += $sumWork + $sumDrive;
            $totalSumBreak += $sumBreak;
            $totalSumDrive += $sumDrive;
            $totalSumDriveFrom += $sumDriveFrom;
            $totalTotals += $totals;
            ?>
            <tr>
                <?php if ($employeeHourCount === 0) { ?>
                    <td>
                        <h1>header-end</h1>
                        <?= $employeeHour['lastName']; ?> </td>
                    <td> <?= $employeeHour['firstName']; ?> </td>
                    <td> <?= $employeeHour['profession']; ?> </td>
                <?php } else { ?>
                    <td colspan="3" style="border-bottom: none; border-top: none">
                        <h1>header-end</h1>
                    </td>
                <?php } ?>
                <td class="text-right"><?= $i ?></td>
                <td><?= $employeeHour['task_name'] ?></td>
                <?php if ($isProject) { ?>
                    <td><?= formatDate($employeeHour['date']) ?></td>
                <?php } ?>
                <td class="text-center align-top">
                    <?= $employeeHour['start'] ?>
                </td>
                <td class="text-center align-top">
                    <?= $employeeHour['end'] ?>
                </td>
                <td class="align-top text-right">
                    <?= emptyStringOnNull($sumWork) ?>
                </td>
                <td class="align-top text-right">
                    <?= emptyStringOnNull($sumBreak) ?>
                </td>
                <td class="align-top text-right">
                    <?= emptyStringOnNull($sumWork + $sumBreak) ?>
                </td>
            </tr>

            <?php if ((!empty($employeeHour['startDriveFrom']) && !empty($employeeHour['endDriveFrom']))
                || (!empty($employeeHour['startDriveTo']) && !empty($employeeHour['endDriveTo']))) { ?>
                <tr>
                    <td colspan="3" style="border-bottom: none; border-top: none"></td>
                    <td class="text-right" style="border-bottom: none; border-top: none"><?= ++$i ?></td>
                    <td>Fahrzeit</td>
                    <?php if ($isProject) { ?>
                        <td><?= formatDate($employeeHour['date']) ?></td>
                    <?php } ?>
                    <td class="text-center align-top">
                        <?php if (!empty($employeeHour['startDriveFrom']) && !empty($employeeHour['endDriveFrom'])) {
                            echo $employeeHour['startDriveFrom'];
                        } else {
                            echo $employeeHour['startDriveTo'];
                        } ?>
                    </td>
                    <td class="text-center align-top">
                        <?php if (!empty($employeeHour['startDriveFrom']) && !empty($employeeHour['endDriveFrom'])) {
                            echo $employeeHour['endDriveFrom'];
                        } else {
                            echo $employeeHour['endDriveTo'];
                        } ?>
                    </td>
                    <td class="align-top text-right"><?= $sumDrive > 0 ? $sumDrive : '' ?></td>
                    <td>&nbsp;</td>
                    <td class="text-right"><?= $sumDrive > 0 ? $sumDrive : '' ?></td>
                </tr>
            <?php } ?>

            <?php
            $i++;
            $employeeHourCount++;
        }
        $globalTotalSumWork += $totalSumWork;
        $globalTotalSumBreak += $totalSumBreak;
        $globalTotalSumDrive += $totalSumDrive;
        $globalTotalSumDriveFrom += $totalSumDriveFrom;
        $globalTotalSums += $totalTotals;
        ?>
        <tr>
            <td colspan="3" style="border-top: none"></td>
            <td colspan="<?= $isProject ? 5 : 4 ?>" class="font-bold bg-blue">Summe</td>
            <td class="align-top font-bold bg-blue text-right">
                <?= emptyStringOnNull($totalSumWorkAndDrive) ?>
            </td>
            <td class="align-top font-bold bg-blue text-right">
                <?= emptyStringOnNull($totalSumBreak) ?>
            </td>
            <td class="align-top font-bold bg-blue text-right">
                <?= emptyStringOnNull($totalSumWorkAndDrive + $totalSumBreak) ?>
            </td>
        </tr>
        <?php
    } ?>

    <tr>
        <td colspan="3"></td>
        <td colspan="4" class="font-bold bg-blue text-right">Gesamtsumme</td>
        <td colspan="4" class="text-center align-top">
            <table class="nested-table">
                <tr>
                    <td>
                        <div>Arbeit</div>
                        <div><?= emptyStringOnNull($globalTotalSumWork) ?></div>
                    </td>
                    <td>
                        <div>Fahrt</div>
                        <div><?= emptyStringOnNull($globalTotalSumDrive) ?></div>
                    </td>
                    <td>
                        <div>Pause</div>
                        <div><?= emptyStringOnNull($globalTotalSumBreak) ?></div>
                    </td>
                    <td>
                        <div>Gesamt</div>
                        <div><?= emptyStringOnNull($globalTotalSums) ?></div>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <?php if (!$data['hideBesonderheiten']) { ?>
        <tr class="besonderheiten">
            <td colspan="<?= $isProject ? 11 : 10 ?>">
                <h1>besonderheiten</h1>
                Besonderheiten:
            </td>
        </tr>
        <tr class="unterschrift">
            <td colspan="<?= $isProject ? 9 : 8 ?>"></td>
            <td colspan="2">Unterschrift Kunde</td>
        </tr>
    <?php } ?>
    </tbody>
</table>
</body>
</html>