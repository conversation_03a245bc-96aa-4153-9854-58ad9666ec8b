@page {
    size: 297mm 210mm;
}

* {
    font-family: Arial, sans-serif;
    font-size: 14px;
}

body {
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

.page {
    max-height: 200mm;
    min-height: 200mm;
    min-width: 297mm;
    max-width: 297mm;
    box-sizing: border-box;
    page-break-before: always;
}

.main-table {
    width: 98%;
    border-collapse: collapse;
    border: 1px solid black;
}

.main-table td {
    border-bottom: 1px solid black;
    border-right: 1px solid black;
    padding: 2px;
    height: 20px;
}

.main-table td:first-child {
    border-bottom: unset !important;
}

.empty-cell {
    border-bottom: 1px solid black;
    border-top: 1px solid black;
    border-right: unset !important;
}

.empty-cell-first-row {
    border-top: 1px solid black;
}

.signature {
    height: 55px;
    max-height: 65px;
    object-fit: contain;
    width: 85px;
    max-width: 100%;
}

.geda-section {
    width: 100%;
    text-align: left;
    page-break-before: always;
}

.geda-header {
    display: flex;
    justify-content: space-between;
    border: 1px solid #000;
    padding: 2px;
    height: 100px;
}

.company-details {
    font-size: 14px;
}

.logo-geda img {
    max-width: 100%;
    height: 100px;
    border: 1px solid #ccc;
}

.geda-content h2 {
    font-size: 20px;
}

.geda-content p {
    margin-bottom: 12px;
}

.geda-qualification {
    page-break-before: always;
    text-align: left;
}

#geda-qualification .title {
    font-size: 18px !important;
    margin-bottom: 16px;
}

#geda-qualification .section-title {
    font-weight: bold;
    text-decoration: underline;
    margin-top: 24px;
    margin-bottom: 8px;
}

#geda-qualification ul {
    margin: 0 0 16px 24px;
    padding: 0;
    list-style-type: disc;
}

#geda-qualification ul ul {
    list-style-type: circle;
    margin-left: 24px;
}

#geda-qualification ul ul ul {
    list-style-type: square;
}

#geda-qualification p {
    margin: 8px 0;
}

.vertical-cell-first-row {
    writing-mode: vertical-rl;
    text-orientation: sideways;
    white-space: nowrap;
    vertical-align: middle;
    text-align: center;
}

.last-section {
    width: 100%;
    text-align: left;
    page-break-before: always;
}

.checkbox-size {
    font-size: 25px;
}

u {
    text-underline-offset: 2px;
}

.break-before {
    page-break-before: always;
}

.stamp {
    max-height: 200px;
    object-fit: contain;
    max-width: 90%;
}

.vertical-cell-first-row span {
    display: inline-block;
    transform: rotate(180deg);
    transform-origin: center center;
    white-space: nowrap;
}

.img-cell {
    page-break-inside: avoid;
}

.img-cell img {
    width: auto;
    max-height: 180mm;
    object-fit: contain;
    page-break-inside: avoid;
}