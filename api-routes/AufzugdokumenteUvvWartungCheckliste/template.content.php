<?php
namespace AufzugdokumenteUvvWartungCheckliste;

use C_AufzugdokumenteUvvWartungCheckliste;
use PrintoutHelper;

/** @noinspection HtmlDeprecatedAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_AufzugdokumenteUvvWartungCheckliste())->getData($_GET['schemaId'], $_GET['documentId']);
}

/**
 * @param array<string, mixed> $data
 */
function renderCheckbox(array $data, string $key, string $value): void
{
    echo '<span class="checkbox-size">';
    echo ($data[$key] ?? '') === $value ? '&#9745;' : '&#9744;';
    echo "</span>";
}

/**
 * @param array<string, mixed> $data
 */
function renderCheckboxText(array $data, string $key, string $value): void
{
    echo '<span>';
    echo ($data[$key] ?? '') === $value ? $value : '';
    echo "</span>";
}

/**
 * @param array<string, mixed> $schema
 */
function renderComment(string $title, string $key, array $schema): void
{
    $text = trim($schema[$key] ?? '');
    if ($text !== '') {
        echo '<p><strong>' . $title . ':</strong> ' . nl2br($text) . "</p>\n";
    }
}

/**
 * @param array<string, mixed> $data
 */
function displayImages(array $data, string $schemaKey): void
{
    $images = $data['schema'][$schemaKey] ?? [];
    if (!is_array($images) || empty($images)) {
        return;
    }
    foreach ($images as $img) {
        $src = $img['filePath'] ?? $img['thumbPath'] ?? '';
        if (!$src) {
            continue;
        }
        echo "
            <div class='img-cell'>
                <img class='img' src='$src' alt='photo'>
            </div><br>
        ";
    }
}

$commentPrefix = 'Aufzugdokumente UVV Wartung Checkliste Kommentar ';
$fotoPrefix = 'Aufzugdokumente UVV Wartung Checkliste Foto ';

$sections = [
    'Ausrichtungstabelle' => [
        'Sichtprüfung: Typenschild, Fabrikationsnummer' => $commentPrefix . 'Sichtprüfung: Typenschild, Fabrikationsnummer',
        'Sichtprüfung: UVV Plakette' => $commentPrefix . 'Sichtprüfung: UVV Plakette',
        'Sichtprüfung: Schaltplan' => $commentPrefix . 'Sichtprüfung: Schaltplan',
        'Sichtprüfung: Bedienungsanleitung' => $commentPrefix . 'Sichtprüfung: Bedienungsanleitung',
        'Sichtprüfung: Nachweis UVV Prüfung' => $commentPrefix . 'Sichtprüfung: Nachweis UVV Prüfung',
        'Sichtprüfung: Aufkleber' => $commentPrefix . 'Sichtprüfung: Aufkleber',
    ],
    'Funktionen' => [
        'Probelauf: Antrieb Hubbewegung' => $commentPrefix . 'Probelauf: Antrieb Hubbewegung',
        'Probelauf: Betriebsbremse' => $commentPrefix . 'Probelauf: Betriebsbremse',
        'Test: Handführung der Betriebsbremse' => $commentPrefix . 'Test: Handführung der Betriebsbremse',
        'Test: Fangvorrichtung' => $commentPrefix . 'Test: Fangvorrichtung',
        'Sichtprüfung: Schleppleitung' => $commentPrefix . 'Sichtprüfung: Schleppleitung',
        'Test: Überlastabschaltung' => $commentPrefix . 'Test: Überlastabschaltung',
        'Probelauf: Fahrbahn, Maste' => $commentPrefix . 'Probelauf: Fahrbahn, Maste',
        'Test: Endschalter sämtliche' => $commentPrefix . 'Test: Endschalter sämtliche',
        'Test: Induktivschalter' => $commentPrefix . 'Test: Induktivschalter',
    ],
    'Elektrische Anlagen' => [
        'Messung: Isolation' => $commentPrefix . 'Messung: Isolation',
        'Messung: Schutzleiterwiderstand' => $commentPrefix . 'Messung: Schutzleiterwiderstand',
        'Sichtprüfung: Isolation der Leitungen' => $commentPrefix . 'Sichtprüfung: Isolation der Leitungen',
        'Sichtprüfung: Leitungsverlegung' => $commentPrefix . 'Sichtprüfung: Leitungsverlegung',
        'Sichtprüfung: Schleppleitung Isolation' => $commentPrefix . 'Sichtprüfung: Schleppleitung Isolation',
        'Sichtprüfung: Dichtigkeit der Schaltkästen' => $commentPrefix . 'Sichtprüfung: Dichtigkeit der Schaltkästen',
        'Sichtprüfung: Feuchtigkeit' => $commentPrefix . 'Sichtprüfung: Feuchtigkeit',
        'Sichtprüfung, Test: Bedienelemente, Taster, Schalter' => $commentPrefix . 'Sichtprüfung, Test: Bedienelemente, Taster, Schalter',
        'Sichtprüfung, Test: Kontrollelemente, Leuchten' => $commentPrefix . 'Sichtprüfung, Test: Kontrollelemente, Leuchten',
        'Sichtprüfung: Sicherungen' => $commentPrefix . 'Sichtprüfung: Sicherungen',
        'Test: Einstellungen Zeitglieder' => $commentPrefix . 'Test: Einstellungen Zeitglieder',
        'Sichtprüfung: Steckverbindungen' => $commentPrefix . 'Sichtprüfung: Steckverbindungen',
        'Sichtprüfung, Test: Verschraubungen' => $commentPrefix . 'Sichtprüfung, Test: Verschraubungen',
        'Messung: Erdung der Steuer-spannung-Masse' => $commentPrefix . 'Messung: Erdung der Steuer-spannung-Masse',
    ],
    'Mechanik' => [
        'Test, Sichtprüfung: Allgemein bewegliche Teile' => $commentPrefix . 'Test, Sichtprüfung: Allgemein bewegliche Teile',
        'Sichtprüfung: Allgemein Beschädigung' => $commentPrefix . 'Sichtprüfung: Allgemein Beschädigung',
        'Sichtprüfung: Grundmast' => $commentPrefix . 'Sichtprüfung: Grundmast',
        'Sichtprüfung: Maste' => $commentPrefix . 'Sichtprüfung: Maste',
        'Test: Mastverbindung' => $commentPrefix . 'Test: Mastverbindung',
        'Sichtprüfung: Tragende Teile, Bühne, Schlitten' => $commentPrefix . 'Sichtprüfung: Tragende Teile, Bühne, Schlitten',
        'Sichtprüfung: Bühnenboden' => $commentPrefix . 'Sichtprüfung: Bühnenboden',
        'Sichtprüfung: Antrieb Dichtigkeit' => $commentPrefix . 'Sichtprüfung: Antrieb Dichtigkeit',
        'Test, Austausch: Getriebeöl' => $commentPrefix . 'Test, Austausch: Getriebeöl',
        'Bremse reinigen und einstellen' => $commentPrefix . 'Bremse reinigen und einstellen',
        'Sichtprüfung: Anfahrkurven für Endschalter' => $commentPrefix . 'Sichtprüfung: Anfahrkurven für Endschalter',
        'Sichtprüfung: Puffer, Pufferaufnahme' => $commentPrefix . 'Sichtprüfung: Puffer, Pufferaufnahme',
        'Sichtprüfung: Zahnrad, Zahnstangen' => $commentPrefix . 'Sichtprüfung: Zahnrad, Zahnstangen',
        'Sichtprüfung: Verschleißzahnstange' => $commentPrefix . 'Sichtprüfung: Verschleißzahnstange',
        'Sichtprüfung: Schmierung von Zahnrad, -stangen' => $commentPrefix . 'Sichtprüfung: Schmierung von Zahnrad, -stangen',
        'Sichtprüfung: Laufrollen' => $commentPrefix . 'Sichtprüfung: Laufrollen',
        'Sichtprüfung: Fangvorrichtung' => $commentPrefix . 'Sichtprüfung: Fangvorrichtung',
        'Sichtprüfung: Fanghaken (falls vorhanden)' => $commentPrefix . 'Sichtprüfung: Fanghaken (falls vorhanden)',
        'Sichtprüfung: Klappen, Türen' => $commentPrefix . 'Sichtprüfung: Klappen, Türen',
        'Sichtprüfung: Lagerung' => $commentPrefix . 'Sichtprüfung: Lagerung',
        'Test: Schwenkmechanik' => $commentPrefix . 'Test: Schwenkmechanik',
        'Sichtprüfung: Sicherheitsbauteile' => $commentPrefix . 'Sichtprüfung: Sicherheitsbauteile',
        'Sichtprüfung: Zubehör' => $commentPrefix . 'Sichtprüfung: Zubehör',
        'Sichtprüfung, Test: Etagentore' => $commentPrefix . 'Sichtprüfung, Test: Etagentore',
        'Sichtprüfung: Umwehrung' => $commentPrefix . 'Sichtprüfung: Umwehrung',
        'Sichtprüfung, Test: Montagesteg' => $commentPrefix . 'Sichtprüfung, Test: Montagesteg',
    ],
    'Aufbau' => [
        'Sichtprüfung: Unterbau Grundmast' => $commentPrefix . 'Sichtprüfung: Unterbau Grundmast',
        'Sichtprüfung: Standfestigkeit' => $commentPrefix . 'Sichtprüfung: Standfestigkeit',
        'Sichtprüfung: Verankerung' => $commentPrefix . 'Sichtprüfung: Verankerung',
        'Sichtprüfung: Mastüberstand' => $commentPrefix . 'Sichtprüfung: Mastüberstand',
        'Sichtprüfung: Schleppkabeleinführungen' => $commentPrefix . 'Sichtprüfung: Schleppkabeleinführungen',
        'Sichtprüfung: Abstände, Sicherheit, Schwenken' => $commentPrefix . 'Sichtprüfung: Abstände, Sicherheit, Schwenken',
        'Sichtprüfung: Übergänge' => $commentPrefix . 'Sichtprüfung: Übergänge',
    ],
];

$filtered = [];
foreach ($sections as $heading => $items) {
    foreach ($items as $title => $commentKey) {
        $photoKey = str_replace($commentPrefix, $fotoPrefix, $commentKey);
        $hasComment = isset($data['schema'][$commentKey])
            && trim($data['schema'][$commentKey]) !== '';
        $hasPhotos = !empty($data['schema'][$photoKey])
            && is_array($data['schema'][$photoKey]);
        if ($hasComment || $hasPhotos) {
            $filtered[$heading][$title] = $commentKey;
        }
    }
}

?>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Aufzugdokumente: UVV Wartung Checkliste</title>
</head>
<body>
<div class="page">
    <p style="text-align: left; padding-bottom: 20px">
        <strong>
            <u style="font-size: 24px!important;">Checkliste für UVV & Wartung</u>
        </strong>
    </p>
    <table class="main-table">
        <tr>
            <td></td>
            <td><strong>Maschinenteil</strong></td>
            <td><strong>Wie zu prüfen</strong></td>
            <td><strong>Vorgabe</strong></td>
            <td><strong>Ergebnis</strong></td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
        <tr>
            <td class="empty-cell-first-row"></td>
            <td>Typenschild, Fabrikationsnummer</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein, Lesbarkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Allgemein Sichtprüfung: Typenschild, Fabrikationsnummer', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="vertical-cell-first-row">
                <strong><span>Allgemein</span></strong>
            </td>
            <td>UVV Plakette</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein, Fälligkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Allgemein Sichtprüfung: UVV Plakette', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schaltplan</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Allgemein Sichtprüfung: Schaltplan', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Bedienungsanleitung</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Allgemein Sichtprüfung: Bedienungsanleitung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Nachweis UVV Prüfung</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Allgemein Sichtprüfung: Nachweis UVV Prüfung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Aufkleber</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein, Lesbarkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Allgemein Sichtprüfung: Aufkleber', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
        <tr>
            <td class="empty-cell-first-row"></td>
            <td>Antrieb Hubbewegung</td>
            <td>Probelauf</td>
            <td>Laufgeräusche, Geschwindigkeit, Zugkraft</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Probelauf: Antrieb Hubbewegung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Betriebsbremse</td>
            <td>Probelauf</td>
            <td>Anhalten, Nachlaufweg Bremse</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Probelauf: Betriebsbremse', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Handlüftung der Betriebsbremse</td>
            <td>Test</td>
            <td>Gängigkeit, Spiel vor Aktivwerden</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Test: Handlüftung der Betriebsbremse', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="vertical-cell-first-row">
                <strong><span>Funktion</span></strong>
            </td>
            <td>Fangvorrichtung</td>
            <td>Test</td>
            <td>Auslösen, Bremsweg</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Test: Fangvorrichtung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schleppleitung</td>
            <td>Sichtprüfung</td>
            <td>Spulverhalten</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Sichtprüfung: Schleppleitung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Überlastabschaltung</td>
            <td>Test</td>
            <td>Abschaltung bei 115 % Last</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Test: Überlastabschaltung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Fahrbahn, Maste</td>
            <td>Probelauf</td>
            <td>Laufgeräusche, Übergänge</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Probelauf: Fahrbahn, Maste', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Endschalter sämtliche</td>
            <td>Test</td>
            <td>Abschaltung der jeweiligen Funktion</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Test: Endschalter sämtliche', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Induktivschalter</td>
            <td>Test</td>
            <td>Abschaltung der Funktion</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Funktionen Test: Induktivschalter', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
    </table>
    <table class="main-table" style="page-break-before: always">
        <tr>
            <td></td>
            <td><strong>Maschinenteil</strong></td>
            <td><strong>Wie zu prüfen</strong></td>
            <td><strong>Vorgabe</strong></td>
            <td><strong>Ergebnis</strong></td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
        <tr>
            <td class="empty-cell-first-row"></td>
            <td>Isolation</td>
            <td>Messung</td>
            <td>&gt;0,5 MΩ für Schutzklasse 1</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Messung: Isolation', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schutzleiterwiderstand</td>
            <td>Messung</td>
            <td>&lt;0,3 Ω für Schutzklasse 1</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Messung: Schutzleiterwiderstand', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Isolation der Leitungen</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Isolation der Leitungen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="vertical-cell-first-row">
                <strong><span>Elektrische Anlagen</span></strong>
            </td>
            <td>Leitungsverlegung</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Leitungsverlegung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schleppleitung Isolation</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Schleppleitung Isolation', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Dichtigkeit der Schaltkästen</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Dichtigkeit der Schaltkästen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Feuchtigkeit</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Feuchtigkeit', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Bedienelemente, Taster, Schalter</td>
            <td>Sichtprüfung, Test</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung, Test: Bedienelemente, Taster, Schalter', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Kontrollelemente, Leuchten</td>
            <td>Sichtprüfung, Test</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung, Test: Kontrollelemente, Leuchten', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Sicherungen</td>
            <td>Sichtprüfung</td>
            <td>Werte der Sicherungseinsätze</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Sicherungen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Einstellungen Zeitglieder</td>
            <td>Test</td>
            <td>3 sec. für 2 Meter Stop; 1 sec. für Umschaltung I/II</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Test: Einstellungen Zeitglieder', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Steckverbindungen</td>
            <td>Sichtprüfung</td>
            <td>Korrosion, Verriegelung, Dichtigkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung: Steckverbindungen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Verschraubungen</td>
            <td>Sichtprüfung, Test</td>
            <td>Dichtigkeit, Zugentlastung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Sichtprüfung, Test: Verschraubungen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Erdung der Steuerspannung-Masse</td>
            <td>Messung</td>
            <td>&lt;0,3 Ω</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Elektrische Anlagen Messung: Erdung der Steuerspannung-Masse', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
    </table>
    <table class="main-table" style="page-break-before: always; page-break-inside: avoid">
        <tr>
            <td></td>
            <td><strong>Maschinenteil</strong></td>
            <td><strong>Wie zu prüfen</strong></td>
            <td><strong>Vorgabe</strong></td>
            <td><strong>Ergebnis</strong></td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
        <tr>
            <td class="empty-cell-first-row"></td>
            <td>Allgemein bewegliche Teile</td>
            <td>Test, Sichtprüfung</td>
            <td>Schmierung, Gangbarkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Test, Sichtprüfung: Allgemein bewegliche Teile', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Allgemeine Beschädigung</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Allgemein Beschädigung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Grundmast</td>
            <td>Sichtprüfung</td>
            <td>Schweißnähte, Rissbildung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Grundmast', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Maste</td>
            <td>Sichtprüfung</td>
            <td>Schweißnähte, Rissbildung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Maste', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Mastverbindung</td>
            <td>Test</td>
            <td>Festigkeit, Anzugsmoment (90 Nm Alu, 150 Nm Stahl)</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Test: Mastverbindung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Tragende Teile, Bühne, Schlitten</td>
            <td>Sichtprüfung</td>
            <td>Korrosion, Schweißnähte, Schraubverbindungen</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Tragende Teile, Bühne, Schlitten', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Bühnenboden</td>
            <td>Sichtprüfung</td>
            <td>Rissbildung, Befestigung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Bühnenboden', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Antrieb Dichtigkeit</td>
            <td>Sichtprüfung</td>
            <td>Getriebedichtungen</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Antrieb Dichtigkeit', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Getriebeöl</td>
            <td>Test, Austausch</td>
            <td>Füllmenge, Austausch</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Test, Austausch: Getriebeöl', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="vertical-cell-first-row">
                <strong><span>Mechanik</span></strong>
            </td>
            <td>Bremse reinigen und einstellen</td>
            <td></td>
            <td>Einstelldaten Antriebshersteller</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Bremse reinigen und einstellen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Anfahrkurven für Endschalter</td>
            <td>Sichtprüfung</td>
            <td>Vorhandensein, Einstellung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Anfahrkurven für Endschalter', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Puffer, Pufferaufnahme</td>
            <td>Sichtprüfung</td>
            <td>Beschädigung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Puffer, Pufferaufnahme', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Zahnrad, Zahnstangen</td>
            <td>Sichtprüfung</td>
            <td>Beschädigung, Verschleiß</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Zahnrad, Zahnstangen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Verschleißzahnstange</td>
            <td>Sichtprüfung</td>
            <td>Beschädigung, Vorhandensein</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Verschleißzahnstange', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schmierung von Zahnrad, -stangen</td>
            <td>Sichtprüfung</td>
            <td>Schmieren</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Schmierung von Zahnrad, -stangen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Laufrollen</td>
            <td>Sichtprüfung</td>
            <td>Verschleiß, Spiel, Lagerzustand, Sicherungsringe</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Laufrollen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Fangvorrichtung</td>
            <td>Sichtprüfung</td>
            <td>Interne Beschädigungen</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Fangvorrichtung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Fanghaken (falls vorhanden)</td>
            <td>Sichtprüfung</td>
            <td>Funktion, Beschädigung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Fanghaken (falls vorhanden)', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Klappen, Türen</td>
            <td>Sichtprüfung</td>
            <td>Gangbarkeit, Verriegelung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Klappen, Türen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Lagerung</td>
            <td>Sichtprüfung</td>
            <td>Schwenklagerung Bühne, Überlastlagerung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Lagerung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schwenkmechanik</td>
            <td>Test</td>
            <td>Beschädigung, Funktion</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Test: Schwenkmechanik', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Sicherheitsbauteile</td>
            <td>Sichtprüfung</td>
            <td>Schutzbleche, Verkleidungen, Verschlüsse</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Sicherheitsbauteile', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Zubehör:</td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td>Etagentore</td>
            <td>Sichtprüfung, Test</td>
            <td>Abstand, Öffnungsweite, Gangbarkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung, Test: Etagentore', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Umwehrung</td>
            <td>Sichtprüfung</td>
            <td>Abstand, Elektrik</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung: Umwehrung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Montagesteg</td>
            <td>Sichtprüfung, Test</td>
            <td>Befestigung, Funktion, Schutzgeländer</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Mechanik Sichtprüfung, Test: Montagesteg', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
    </table>
    <table class="main-table" style="page-break-before: always; page-break-inside: avoid">
        <tr>
            <td></td>
            <td><strong>Maschinenteil</strong></td>
            <td><strong>Wie zu prüfen</strong></td>
            <td><strong>Vorgabe</strong></td>
            <td><strong>Ergebnis</strong></td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
        <tr>
            <td class="empty-cell-first-row"></td>
            <td>Unterbau Grundmast</td>
            <td>Sichtprüfung</td>
            <td>Tragfähigkeit</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Unterbau Grundmast', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Standfestigkeit</td>
            <td>Sichtprüfung</td>
            <td></td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Standfestigkeit', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="vertical-cell-first-row">
                <strong><span>Aufbau</span></strong>
            </td>
            <td>Verankerung</td>
            <td>Sichtprüfung</td>
            <td>Festigkeit, Abstand</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Verankerung', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Mastüberstand</td>
            <td>Sichtprüfung</td>
            <td>Maximal zulässiger Überstand</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Mastüberstand', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Schleppkabelführungen</td>
            <td>Sichtprüfung</td>
            <td>Abstand, Vorhandensein</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Schleppkabelführungen', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Abstände, Sicherheit, Schwenken</td>
            <td>Sichtprüfung</td>
            <td>Bedienungsanleitung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Abstände, Sicherheit, Schwenken', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>Übergänge</td>
            <td>Sichtprüfung</td>
            <td>Bedienungsanleitung</td>
            <td>
                <?php renderCheckboxText($data['schema'], 'Aufbau Sichtprüfung: Übergänge', 'In Ordnung'); ?>
            </td>
        </tr>
        <tr>
            <td class="empty-cell"></td>
            <td colspan="4"></td>
        </tr>
    </table>
    <div class="geda-section">
        <div class="geda-header">
            <div class="company-details">
                <span style="font-size: 16px!important">
                    GEDA-Dechentreiter GmbH & Co. KG<br>
                    Mertinger Str. 60<br>
                    D-86663 Asbach-Bäumenheim<br>
                </span>
                <span style="width: 30px; display: inline-block">Tel.</span> 09 06/98 09-0<br>
                <span style="width: 30px; display: inline-block">Fax.</span> 09 06/98 09-50<br>
                Kundendienst: 0906/98 09-60
            </div>
            <div class="logo-geda">
                <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/image/geda-logo.png') ?>"
                     alt="GEDA">
            </div>
        </div>
        <div class="geda-content">
            <h2>Sachkundigenprüfung an GEDA-Baumaschinen</h2>
            <p>Alle GEDA Bauaufzüge sind nach einschlägigen Normen hergestellt (z. B. EG-Maschinenrichtlinie
                98/37/EG; EN12158-1; EN12158-2; EN12159) so weit es sich um Geräte handelt, die zum Heben von
                Personen eingesetzt werden und nach der EG-Maschinenrichtlinie 98/37/EG gebaut sind, sind sie auch
                baumustergeprüft.<br>
                Alle Bauaufzüge sind nach der Betriebssicherheitsverordnung in regelmäßigen Abständen zu prüfen.
                Baumaterialaufzüge sind in Anlehnung an die BGV D7 § 45 (bisher: VBG 35 § 45) mindestens einmal im
                Jahr durch einen Sachkundigen zu prüfen.<br>
                Die Ergebnisse der Prüfung müssen durch den Sachkundigen im Prüfbuch bzw. im Anhang der
                Betriebsanleitung schriftlich festgehalten werden. Das Ergebnis kann auch auf einem formlosen Blatt
                DinA4 dokumentiert werden, wenn kein Prüfbuch zur Verfügung steht. Dieser Prüfnachweis ist dem
                Eigentümer auszuhändigen und den Anlagedokumenten beizufügen.<br>
                In der Betriebsanleitung ist die EG-Konformitätserklärung abgedruckt, die Auskunft darüber gibt, nach
                welchen Normen die Maschine hergestellt ist.</p>
            <h2>Anforderungen an den Sachkundigen (Nach ZH 1/22,2.2):</h2>
            <p>Sachkundige sind Personen (Betriebsingenieure, Maschinenmeister, besonders ausgebildetes
                Fachpersonal, Kundendienstmonteure der Hersteller), die aufgrund ihrer fachlichen Ausbildung und
                Erfahrung ausreichende Kenntnisse auf dem Gebiet der Bauaufzüge haben und mit den einschlägigen
                staatlichen Arbeitsschutzvorschriften, Unfallverhütungsvorschriften, Richtlinien und allgemein
                anerkannten Regeln der Technik (z. B. VDE-Bestimmungen, DIN-Normen wie DIN 15020, VBG-Bestimmungen
                insbesondere VBG 35, VBG 8 und VBG 9a) so weit vertraut sind, dass sie den arbeitssicheren Zustand
                von Bauaufzügen beurteilen können.</p>
            <h2>Befähigte Person (Nach Betriebssicherheitsverordnung – BetrSichV)</h2>
            <p>Befähigte Person im Sinne dieser Verordnung ist eine Person, die durch ihre Berufsausbildung, ihre
                Berufserfahrung und ihre zeitnahe berufliche Tätigkeit über die erforderlichen Fachkenntnisse zur
                Prüfung der Arbeitsmittel verfügt (§2 (7)).</p>
        </div>
    </div>
    <div class="geda-qualification">
        <p>
            <span style="font-size: 18px!important">
                Die Sachkundigenprüfung umfasst im Wesentlichen 3 Teile:
            </span>
        </p>
        <p class="section-title">
            <u><strong>1. Sichtprüfung der elektrischen und mechanischen Teile der Anlage:</strong></u>
        </p>
        <ul>
            <li>
                Kontrolle bezüglich Beschädigung, Verschleiß, Dichtigkeit und Vorhandensein
                von relevanten Anlageteilen
            </li>
        </ul>

        <p class="section-title">
            <u><strong>2. Funktionsprüfung:</strong></u>
        </p>
        <ul>
            <li>
                Die Funktionen der elektrischen Ausrüstung, insbesondere solcher, die sich
                auf Sicherheit und Schutzmaßnahmen beziehen, müssen geprüft werden.
                <ul>
                    <li>Anzeigen von gefährlichen Zuständen</li>
                    <li>
                        Nachlauf der Betriebsbremse unter Vollast gilt allgemein maximal 10 cm
                        (eventuell Verschleiß und Luftspalt prüfen)
                    </li>
                    <li>
                        Prüfen der Betriebsendschalter durch Probefahrt bzw. Simulation (Auf- und Abbewegung des
                        Aufzuges)
                        <br>
                        Dazu gehören unter anderem:
                        <ul>
                            <li>Not-Aus-Funktion der Handsteuerung überprüfen</li>
                            <li>Verriegelungen von Schutzeinrichtungen</li>
                        </ul>
                    </li>
                </ul>
            </li>
        </ul>

        <p class="section-title">
            <u><strong>3. Elektrische Prüfung:</strong></u>
        </p>
        <p>
            Die elektrischen Beanstandungen müssen durch eine Fachkraft behoben werden.
            Nach jeder Reparatur sind mindestens folgende Messungen notwendig:
        </p>
        <ul>
            <li>Messung nach DIN VDE 0701-260; VDE 0113; VDE 0701-240</li>
            <li>
                Gegebenenfalls Isolationsprüfung (auf spannungsempfindliche Teile achten)
                – Isolationswiderstand mit 500 V, mind. 1 MΩ
            </li>
            <li>
                Prüfung von Restspannung. Restspannungen müssen sich nach Ausschalten der
                Versorgungsspannung von selbst abbauen (nach Austausch von Kondensatoren)
            </li>
            <li>Schutzleiterwiderstand – Prüfstrom mit 10 A</li>
        </ul>
    </div>
    <div class="last-section">
        <p>
            <u style="font-size: 18px!important">Bestätigung über die Durchführung der wiederkehrenden Prüfung nach BGR
                500
                <br>
                Teil 2 Kapitel 2.30 Unfallverhütungsvorschrift
            </u>
        </p>
        <p>
            Diese Bestätigung ist dem Prüfbuch bzw. der Bedienungsanleitung beizulegen und mindestens für ein Jahr bzw.
            bis zur nächsten
            Prüfung aufzubewahren.
        </p>
        <p>
            Bei der Maschine GEDA
        </p>
        <div>
            <table style="width: 100%">
                <tr>
                    <td style="border-bottom: 1px solid black;width: 20%">
                        <?= $data['schema']['Aufzugdokumente UVV Wartung Checkliste Typ'] ?? '' ?>
                    </td>
                    <td></td>
                    <td style="border-bottom: 1px solid black; width: 20%">
                        <?php
                        $rawDate = $data['schema']['Aufzugdokumente UVV Wartung Checkliste Prüfdatum'] ?? '';
                        if ($rawDate && ($ts = strtotime($rawDate)) !== false) {
                            echo date('d.m.Y', $ts);
                        }
                        ?>
                    </td>
                    <td></td>
                    <td style="border-bottom: 1px solid black; width: 20%">
                        <?= $data['schema']['Aufzugdokumente UVV Wartung Checkliste Fabrikationsnummer'] ?? '' ?>
                    </td>
                </tr>
                <tr>
                    <td style="width: 20%">
                        Typ
                    </td>
                    <td></td>
                    <td style="width: 20%">
                        Baujahr
                    </td>
                    <td></td>
                    <td style="width: 20%">
                        Fabrikationsnummer
                    </td>
                </tr>
                <tr>
                    <td style="border-bottom: 1px solid black; padding-top: 20px; width: 20%">
                        <?= $data['schema']['Aufzugdokumente UVV Wartung Checkliste Reparaturvorgangsnummer'] ?? '' ?>
                    </td>
                </tr>
                <tr>
                    <td>
                        Reparaturvorgangsnummer
                    </td>
                </tr>
            </table>
        </div>
        <p>
            wurde die wiederkehrende Prüfung nach BGR 500 Teil 2 Kapitel 2.30 durchgeführt.
        </p>
        <p>
            <u style="font-size: 22px!important">Prüfbefund:</u>
        </p>
        <div style="font-size: 18px!important">
            <?php renderCheckbox($data['schema'], 'Aufzugdokumente UVV Wartung Checkliste Gerät / Maschine ist', 'In Ordnung'); ?>
            <span class="checkbox-size">Gerät / Maschine ist in Ordnung</span>
            <br>
            <?php renderCheckbox($data['schema'], 'Aufzugdokumente UVV Wartung Checkliste Gerät / Maschine ist', 'Nicht in Ordnung'); ?>
            <span class="checkbox-size">Gerät / Maschine ist nicht in Ordnung, da:</span>
            <br>
            <div style="width: 100%">
                <?php if (($data['schema']['Aufzugdokumente UVV Wartung Checkliste Gerät / Maschine ist'] ?? null) == 'Nicht in Ordnung') { ?>
                    <br>
                    <div style="border-bottom: 1px solid black; height: 25px">
                        <?= $data['schema']['Aufzugdokumente UVV Wartung Checkliste Nicht in Ordnung, da:'] ?? '' ?>
                    </div>
                <?php } ?>
                <div style="border-bottom: 1px solid black; height: 25px"></div>
                <div style="border-bottom: 1px solid black; height: 25px"></div>
                <div style="border-bottom: 1px solid black; height: 25px"></div>
            </div>
        </div>
        <p>
            Diese Prüfung bezieht sich nur auf das entsprechende Grundgerät und das zum Prüfzeitpunkt vorgefundene
            Zubehör.
        </p>
        <p>
            Diese Bestätigung ist nur gültig in Verbindung mit Firmenstempel, Prüfdatum und Unterschrift der befähigten Person.
        </p>
        <table style="width: 100%; padding-top: 40px">
            <tr>
                <td style="border-bottom: 1px solid black;width: 20%">
                    <?php if (!empty($data['schema']['Aufzugdokumente UVV Wartung Checkliste Firmenstempel'])) {
                        $img = $data['schema']['Aufzugdokumente UVV Wartung Checkliste Firmenstempel'][0];
                        $src = $img['filePath'] ?? $img['thumbPath'] ?? '';
                        ?>
                        <img src="<?= $src ?>"
                             alt="stamp" class="stamp">
                    <?php } ?>
                </td>
                <td></td>
                <td style="border-bottom: 1px solid black;width: 20%">
                    <?php
                    $rawDate = $data['schema']['Aufzugdokumente UVV Wartung Checkliste Prüfdatum'] ?? '';
                    if (!empty($rawDate) && ($ts = strtotime($rawDate)) !== false) {
                        echo date('d.m.Y', $ts);
                    }
                    ?>
                </td>
                <td></td>
                <td style="border-bottom: 1px solid black;width: 20%">
                    <?= $data['schema']['Aufzugdokumente UVV Wartung Checkliste Befähigte Person / Sachkundiger'] ?? '' ?>
                </td>
            </tr>
            <tr>
                <td style="width: `">
                    Firmenstempel
                </td>
                <td></td>
                <td style="width: 20%">
                    Prüfdatum
                </td>
                <td></td>
                <td style="width: 20%">
                    Befähigte Person /
                    Sachkundiger
                </td>
            </tr>
        </table>
    </div>
    <?php if (!empty($filtered)) { ?>
        <div class="comments-photos-section" style="page-break-before: always; text-align: left">
            <div class="comments">
                <h1 style='font-size:24px'>Kommentare </h1>
                <?php
                $firstSection = true;
                foreach ($filtered as $heading => $items) {
                    $style = $firstSection ? '' : 'page-break-before:always;';
                    $firstSection = false;

                    echo "<h2 style=\"$style font-size:20px\">$heading</h2>";
                    foreach ($items as $title => $commentKey) {
                        renderComment($title, $commentKey, $data['schema']);
                        $photoKey = str_replace($commentPrefix, $fotoPrefix, $commentKey);
                        displayImages($data, $photoKey);
                    }
                }
                ?>
            </div>
        </div>
    <?php } ?>
</div>
</body>
</html>