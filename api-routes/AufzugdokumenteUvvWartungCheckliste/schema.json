{"name": "Aufzugdokumente UVV Wartung Checkliste", "status": "active", "createdBy": "364", "createdOn": "2025-05-09T00:00:00Z", "positions": [{"id": 1, "title": "Allgemein", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Sichtprüfung: Typenschild, Fabrikationsnummer", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 3, "displayInside": 2, "title": "Foto Sichtprüfung: Typenschild, Fabrikationsnummer", "type": "photo"}, {"id": 4, "displayInside": 2, "title": "Kommentar Sichtprüfung: Typenschild, Fabrikationsnummer", "type": "string"}, {"id": 5, "parentId": 1, "title": "Sichtprüfung: UVV Plakette", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 6, "displayInside": 5, "title": "Foto Sichtprüfung: UVV Plakette", "type": "photo"}, {"id": 7, "displayInside": 5, "title": "Kommentar Sichtprüfung: UVV Plakette", "type": "string"}, {"id": 8, "parentId": 1, "title": "Sichtprüfung: Schaltplan", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 9, "displayInside": 8, "title": "Foto Sichtprüfung: Schaltplan", "type": "photo"}, {"id": 10, "displayInside": 8, "title": "Kommentar Sichtprüfung: Schaltplan", "type": "string"}, {"id": 11, "parentId": 1, "title": "Sichtprüfung: Bedienungsanleitung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 12, "displayInside": 11, "title": "Foto Sichtprüfung: Bedienungsanleitung", "type": "photo"}, {"id": 13, "displayInside": 11, "title": "Kommentar Sichtprüfung: Bedienungsanleitung", "type": "string"}, {"id": 14, "parentId": 1, "title": "Sichtprüfung: Nachweis UVV Prüfung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 15, "displayInside": 14, "title": "Foto Sichtprüfung: Nachweis UVV Prüfung", "type": "photo"}, {"id": 16, "displayInside": 14, "title": "Kommentar Sichtprüfung: Nachweis UVV Prüfung", "type": "string"}, {"id": 17, "parentId": 1, "title": "Sichtprüfung: <PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 18, "displayInside": 17, "title": "Foto Sichtprüfung: Au<PERSON>kleber", "type": "photo"}, {"id": 19, "displayInside": 17, "title": "Kommentar Sichtprüfung: Aufkleber", "type": "string"}, {"id": 20, "title": "Funktionen", "type": "headline"}, {"id": 21, "parentId": 20, "title": "Probelauf: <PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 22, "displayInside": 21, "title": "Foto Probelauf: <PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 23, "displayInside": 21, "title": "Kommentar Probelauf: <PERSON><PERSON><PERSON>", "type": "string"}, {"id": 24, "parentId": 20, "title": "Probelauf: Betriebsbremse", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 25, "displayInside": 24, "title": "Foto Probelauf: Betriebsbremse", "type": "photo"}, {"id": 26, "displayInside": 24, "title": "Kommentar Probelauf: Betriebsbremse", "type": "string"}, {"id": 27, "parentId": 20, "title": "Test: Handlüftung der Betriebsbremse", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 28, "displayInside": 27, "title": "Foto Test: Handlüftung der Betriebsbremse", "type": "photo"}, {"id": 29, "displayInside": 27, "title": "Kommentar Test: Handlüftung der Betriebsbremse", "type": "string"}, {"id": 30, "parentId": 20, "title": "Test: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 31, "displayInside": 30, "title": "Foto Test: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 32, "displayInside": 30, "title": "Kommentar Test: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 33, "parentId": 20, "title": "Sichtprüfung: Schleppleitung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 34, "displayInside": 33, "title": "Foto Sichtprüfung: Schleppleitung", "type": "photo"}, {"id": 35, "displayInside": 33, "title": "Kommentar Sichtprüfung: Schleppleitung", "type": "string"}, {"id": 36, "parentId": 20, "title": "Test: Überlastabschaltung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 37, "displayInside": 36, "title": "Foto Test: Überlastabschaltung", "type": "photo"}, {"id": 38, "displayInside": 36, "title": "Kommentar Test: Überlastabschaltung", "type": "string"}, {"id": 39, "parentId": 20, "title": "Probelauf: <PERSON><PERSON><PERSON><PERSON>, Maste", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 40, "displayInside": 39, "title": "Foto Probelauf: <PERSON><PERSON><PERSON><PERSON>, Maste", "type": "photo"}, {"id": 41, "displayInside": 39, "title": "Kommentar Probelauf: <PERSON><PERSON><PERSON><PERSON>, Maste", "type": "string"}, {"id": 42, "parentId": 20, "title": "Test: <PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 43, "displayInside": 42, "title": "Foto Test: <PERSON><PERSON><PERSON><PERSON> sä<PERSON>tliche", "type": "photo"}, {"id": 44, "displayInside": 42, "title": "Kommentar Test: <PERSON><PERSON><PERSON><PERSON> sämtliche", "type": "string"}, {"id": 45, "parentId": 20, "title": "Test: Induktiv<PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 46, "displayInside": 45, "title": "Foto Test: Induktivschalter", "type": "photo"}, {"id": 47, "displayInside": 45, "title": "Kommentar Test: Induktivschalter", "type": "string"}, {"id": 48, "title": "Elektrische Anlagen", "type": "headline"}, {"id": 49, "parentId": 48, "title": "Messung: Isolation", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 50, "displayInside": 49, "title": "Foto Messung: Isolation", "type": "photo"}, {"id": 51, "displayInside": 49, "title": "Kommentar Messung: Isolation", "type": "string"}, {"id": 52, "parentId": 48, "title": "Messung: Schutzleiterwiderstand", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 53, "displayInside": 52, "title": "Foto Messung: Schutzleiterwiderstand", "type": "photo"}, {"id": 54, "displayInside": 52, "title": "Kommentar Messung: Schutzleiterwiderstand", "type": "string"}, {"id": 55, "parentId": 48, "title": "Sichtprüfung: Isolation der Leitungen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 56, "displayInside": 55, "title": "Foto Sichtprüfung: Isolation der Leitungen", "type": "photo"}, {"id": 57, "displayInside": 55, "title": "Kommentar Sichtprüfung: Isolation der Leitungen", "type": "string"}, {"id": 58, "parentId": 48, "title": "Sichtprüfung: Leitungsverlegung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 59, "displayInside": 58, "title": "Foto Sichtprüfung: Leitungsverlegung", "type": "photo"}, {"id": 60, "displayInside": 58, "title": "Kommentar Sichtprüfung: Leitungsverlegung", "type": "string"}, {"id": 61, "parentId": 48, "title": "Sichtprüfung: Schleppleitung Isolation", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 62, "displayInside": 61, "title": "Foto Sichtprüfung: Schleppleitung Isolation", "type": "photo"}, {"id": 63, "displayInside": 61, "title": "Kommentar Sichtprüfung: Schleppleitung Isolation", "type": "string"}, {"id": 64, "parentId": 48, "title": "Sichtprüfung: Dichtigkeit der Schaltkästen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 65, "displayInside": 64, "title": "Foto Sichtprüfung: Dichtigkeit der Schaltkästen", "type": "photo"}, {"id": 66, "displayInside": 64, "title": "Kommentar Sichtprüfung: Dichtigkeit der Schaltkästen", "type": "string"}, {"id": 67, "parentId": 48, "title": "Sichtprüfung: Feuchtigkeit", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 68, "displayInside": 67, "title": "Foto Sichtprüfung: Feuchtigkeit", "type": "photo"}, {"id": 69, "displayInside": 67, "title": "Kommentar Sichtprüfung: Feuchtigkeit", "type": "string"}, {"id": 70, "parentId": 48, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Test: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 71, "displayInside": 70, "title": "Foto Sichtprüfung, Test: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 72, "displayInside": 70, "title": "<PERSON><PERSON><PERSON><PERSON>, Test: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "string"}, {"id": 73, "parentId": 48, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Test: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 74, "displayInside": 73, "title": "Foto Sichtprüfu<PERSON>, Test: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 75, "displayInside": 73, "title": "<PERSON><PERSON><PERSON><PERSON>, Test: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "string"}, {"id": 76, "parentId": 48, "title": "Sichtprüfung: Sicherungen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 77, "displayInside": 76, "title": "Foto Sichtprüfung: Sicherungen", "type": "photo"}, {"id": 78, "displayInside": 76, "title": "Kommentar Sichtprüfung: Sicherungen", "type": "string"}, {"id": 79, "parentId": 48, "title": "Test: Einstellungen Zeitglieder", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 80, "displayInside": 79, "title": "Foto Test: Einstellungen Zeitglieder", "type": "photo"}, {"id": 81, "displayInside": 79, "title": "Kommentar Test: Einstellungen Zeitglieder", "type": "string"}, {"id": 82, "parentId": 48, "title": "Sichtprüfung: Steckverbindungen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 83, "displayInside": 82, "title": "Foto Sichtprüfung: Steckverbindungen", "type": "photo"}, {"id": 84, "displayInside": 82, "title": "Kommentar Sichtprüfung: Steckverbindungen", "type": "string"}, {"id": 85, "parentId": 48, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Test: Verschraubungen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 86, "displayInside": 85, "title": "Foto Sichtprüfung, Test: Verschraubungen", "type": "photo"}, {"id": 87, "displayInside": 85, "title": "<PERSON><PERSON><PERSON><PERSON>, Test: Verschraubungen", "type": "string"}, {"id": 88, "parentId": 48, "title": "Messung: Erdung der Steuerspannung-Masse", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 89, "displayInside": 88, "title": "Foto Messung: Erdung der Steuerspannung-Masse", "type": "photo"}, {"id": 90, "displayInside": 88, "title": "Kommentar Messung: Erdung der Steuerspannung-Masse", "type": "string"}, {"id": 91, "title": "<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 92, "parentId": 91, "title": "Test, Sichtprüfung: Allgemein bewegliche Teile", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 93, "displayInside": 92, "title": "Foto Test, Sichtprüfung: Allgemein bewegliche Teile", "type": "photo"}, {"id": 94, "displayInside": 92, "title": "Kommentar Test, Sichtprüfung: Allgemein bewegliche Teile", "type": "string"}, {"id": 95, "parentId": 91, "title": "Sichtprüfung: Allgemein Beschädigung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 96, "displayInside": 95, "title": "Foto Sichtprüfung: Allgemein Beschädigung", "type": "photo"}, {"id": 97, "displayInside": 95, "title": "Kommentar Sichtprüfung: Allgemein Beschädigung", "type": "string"}, {"id": 98, "parentId": 91, "title": "Sichtprüfung: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 99, "displayInside": 98, "title": "Foto Sichtprüfung: Grundmast", "type": "photo"}, {"id": 100, "displayInside": 98, "title": "Kommentar Sichtprüfung: Grundmast", "type": "string"}, {"id": 101, "parentId": 91, "title": "Sichtprüfung: <PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 102, "displayInside": 101, "title": "Foto Sichtprüfung: <PERSON><PERSON>", "type": "photo"}, {"id": 103, "displayInside": 101, "title": "Kommentar Sichtprüfung: <PERSON><PERSON>", "type": "string"}, {"id": 104, "parentId": 91, "title": "Test: Mastverbindung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 105, "displayInside": 104, "title": "Foto Test: Mastverbindung", "type": "photo"}, {"id": 106, "displayInside": 104, "title": "Kommentar Test: Mastverbindun", "type": "string"}, {"id": 107, "parentId": 91, "title": "Sichtprüfung: Tragende Teile, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 108, "displayInside": 107, "title": "Foto Sichtprüfung: Tragende Teile, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 109, "displayInside": 107, "title": "Kommentar Sichtprüfung: Tragende Teile, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 110, "parentId": 91, "title": "Sichtprüfung: Bühnenboden", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 111, "displayInside": 110, "title": "Foto Sichtprüfung: Bühnenboden", "type": "photo"}, {"id": 112, "displayInside": 110, "title": "Kommentar Sichtprüfung: Bühnenboden", "type": "string"}, {"id": 113, "parentId": 91, "title": "Sichtprüfung: Antrieb Dichtigkeit", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 114, "displayInside": 113, "title": "Foto Sichtprüfung: Antrieb Dichtigkeit", "type": "photo"}, {"id": 115, "displayInside": 113, "title": "Kommentar Sichtprüfung: Antrieb Dichtigkeit", "type": "string"}, {"id": 116, "parentId": 91, "title": "Test, Austausch: <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 117, "displayInside": 116, "title": "Foto Test, Austausch: Getriebeöl", "type": "photo"}, {"id": 118, "displayInside": 116, "title": "Kommentar Test, Austausch: Getriebeöl", "type": "string"}, {"id": 119, "parentId": 91, "title": "Bremse reinigen und einstellen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 120, "displayInside": 119, "title": "Foto Bremse reinigen und einstellen", "type": "photo"}, {"id": 121, "displayInside": 119, "title": "Kommentar Bremse reinigen und einstellen", "type": "string"}, {"id": 122, "parentId": 91, "title": "Sichtprüfung: Anfahrkurven für Endschalter", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 123, "displayInside": 122, "title": "Foto Sichtprüfung: Anfahrkurven für Endschalter", "type": "photo"}, {"id": 124, "displayInside": 122, "title": "Kommentar Sichtprüfung: Anfahrkurven für Endschalter", "type": "string"}, {"id": 125, "parentId": 91, "title": "Sichtprüfung: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 126, "displayInside": 125, "title": "Foto Sichtprüfung: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 127, "displayInside": 125, "title": "Kommentar Sichtprüfung: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 128, "parentId": 91, "title": "Sichtprüfung: Zahnrad, Zahnstangen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 129, "displayInside": 128, "title": "Foto Sichtprüfung: Zahnrad, Zahnstangen", "type": "photo"}, {"id": 130, "displayInside": 128, "title": "Kommentar Sichtprüfung: Zahnrad, Zahnstangen", "type": "string"}, {"id": 131, "parentId": 91, "title": "Sichtprüfung: Verschleißzahnstange", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 132, "displayInside": 131, "title": "Foto Sichtprüfung: Verschleißzahnstange", "type": "photo"}, {"id": 133, "displayInside": 131, "title": "Kommentar Sichtprüfung: Verschleißzahnstange", "type": "string"}, {"id": 134, "parentId": 91, "title": "Sichtprüfung: Schm<PERSON><PERSON> von Zahnrad, -stangen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 135, "displayInside": 134, "title": "Foto Sichtprüfung: Schmier<PERSON> von Zahnrad, -stangen", "type": "photo"}, {"id": 136, "displayInside": 134, "title": "Kommentar Sichtprüfung: Schmier<PERSON> von Zahnrad, -stangen", "type": "string"}, {"id": 137, "parentId": 91, "title": "Sichtprüfung: Laufrollen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 138, "displayInside": 137, "title": "Foto Sichtprüfung: Laufrollen", "type": "photo"}, {"id": 139, "displayInside": 137, "title": "Kommentar Sichtprüfung: Laufrollen", "type": "string"}, {"id": 140, "parentId": 91, "title": "Sichtprüfung: Fang<PERSON>richtung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 141, "displayInside": 140, "title": "Foto Sichtprüfung: Fang<PERSON>richtung", "type": "photo"}, {"id": 142, "displayInside": 140, "title": "Kommentar Sichtprüfung: Fangvorrichtung", "type": "string"}, {"id": 143, "parentId": 91, "title": "Sichtprüfung: <PERSON><PERSON><PERSON> (falls vorhanden)", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 144, "displayInside": 143, "title": "Foto Sichtprüfung: <PERSON><PERSON><PERSON> (falls vorhanden)", "type": "photo"}, {"id": 145, "displayInside": 143, "title": "Kommentar Sichtprüfung: <PERSON><PERSON><PERSON> (falls vorhanden)", "type": "string"}, {"id": 146, "parentId": 91, "title": "Sichtprüfung: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 147, "displayInside": 146, "title": "Foto Sichtprüfung: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 148, "displayInside": 146, "title": "Kommentar Sichtprüfung: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "string"}, {"id": 149, "parentId": 91, "title": "Sichtprüfung: Lagerung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 150, "displayInside": 149, "title": "Foto Sichtprüfung: Lagerung", "type": "photo"}, {"id": 151, "displayInside": 149, "title": "Kommentar Sichtprüfung: Lagerung", "type": "string"}, {"id": 152, "parentId": 91, "title": "Test: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 153, "displayInside": 152, "title": "Foto Test: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "photo"}, {"id": 154, "displayInside": 152, "title": "Kommentar Test: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 155, "parentId": 91, "title": "Sichtprüfung: Sicherheitsbauteile", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 156, "displayInside": 155, "title": "Foto Sichtprüfung: Sicherheitsbauteile", "type": "photo"}, {"id": 157, "displayInside": 155, "title": "Kommentar Sichtprüfung: Sicherheitsbauteile", "type": "string"}, {"id": 158, "parentId": 91, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Test: <PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 159, "displayInside": 158, "title": "Foto Sichtprüfung, Test: Etage<PERSON><PERSON>", "type": "photo"}, {"id": 160, "displayInside": 158, "title": "<PERSON><PERSON><PERSON><PERSON>, Test: E<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 161, "parentId": 91, "title": "Sichtprüfung: Umwehrung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 162, "displayInside": 161, "title": "Foto Sichtprüfung: Umwehrung", "type": "photo"}, {"id": 163, "displayInside": 161, "title": "Kommentar Sichtprüfung: Umwehrung", "type": "string"}, {"id": 164, "parentId": 91, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Test: Montagesteg", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 165, "displayInside": 164, "title": "Foto Sichtprüfung, Test: Montagesteg", "type": "photo"}, {"id": 166, "displayInside": 164, "title": "<PERSON><PERSON><PERSON><PERSON>, Test: Montagesteg", "type": "string"}, {"id": 167, "title": "Aufbau", "type": "headline"}, {"id": 168, "parentId": 167, "title": "Sichtprüfung: Unterbau Grundmast", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 169, "displayInside": 168, "title": "Foto Sichtprüfung: Unterbau Grundmast", "type": "photo"}, {"id": 170, "displayInside": 168, "title": "Kommentar Sichtprüfung: Unterbau Grundmast", "type": "string"}, {"id": 171, "parentId": 167, "title": "Sichtprüfung: Standfestigkeit", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 172, "displayInside": 171, "title": "Foto Sichtprüfung: Standfestigkeit", "type": "photo"}, {"id": 173, "displayInside": 171, "title": "Kommentar Sichtprüfung: Standfestigkeit", "type": "string"}, {"id": 174, "parentId": 167, "title": "Sichtprüfung: Verankerung", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 175, "displayInside": 174, "title": "Foto Sichtprüfung: Verankerung", "type": "photo"}, {"id": 176, "displayInside": 174, "title": "Kommentar Sichtprüfung: Verankerung", "type": "string"}, {"id": 177, "parentId": 167, "title": "Sichtprüfung: Mastüberstand", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 178, "displayInside": 177, "title": "Foto Sichtprüfung: Mastüberstand", "type": "photo"}, {"id": 179, "displayInside": 177, "title": "Kommentar Sichtprüfung: Mastüberstand", "type": "string"}, {"id": 180, "parentId": 167, "title": "Sichtprüfung: Schleppkabelführungen", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 181, "displayInside": 180, "title": "Foto Sichtprüfung: Schleppkabelführungen", "type": "photo"}, {"id": 182, "displayInside": 180, "title": "Kommentar Sichtprüfung: Schleppkabelführungen", "type": "string"}, {"id": 183, "parentId": 167, "title": "Sichtprüfung: Abstände, Sicherheit, Schwenken", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 184, "displayInside": 183, "title": "Foto Sichtprüfung: Abstände, Sicherheit, Schwenken", "type": "photo"}, {"id": 185, "displayInside": 183, "title": "Kommentar Sichtprüfung: Abstände, Sicherheit, Schwenken", "type": "string"}, {"id": 186, "parentId": 167, "title": "Sichtprüfung: Übergänge", "type": "combobox", "value": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 187, "displayInside": 186, "title": "Foto Sichtprüfung: Übergänge", "type": "photo"}, {"id": 188, "displayInside": 186, "title": "Kommentar Sichtprüfung: Übergänge", "type": "string"}, {"id": 189, "title": "<PERSON><PERSON>", "type": "string"}, {"id": 190, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date"}, {"id": 191, "title": "Fabrikationsnummer", "type": "int"}, {"id": 192, "title": "Reparaturvorgangsnummer", "type": "int"}, {"id": 193, "title": "Gerät / Maschine ist", "type": "combobox", "values": ["In Ordnung", "Nicht in Ordnung"]}, {"id": 194, "title": "<PERSON>cht in Ordnung, da:", "displayInside": 193, "type": "string"}, {"id": 195, "title": "Firmenstempel", "type": "photo"}, {"id": 196, "title": "Prüfdatum", "type": "date"}, {"id": 197, "title": "Befähigte Person / Sachkundiger", "type": "string"}]}