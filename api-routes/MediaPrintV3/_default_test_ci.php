<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/CiEntityCreator.php';

// schema-without-photo.json is used because schema.json contains a type=photo
// and since our v3 code ignores that position anyway, we changed it to type=string to not slow down CI
// by avoiding one POST files API call
$result = CiEntityCreator::createSchemaAndReturnId(__DIR__ . '/schema-without-photo.json');
$schemaId = $result['schemaId'];
$positionIds = $result['positionIds'];
$workingOrderData = CiEntityCreator::downloadAnyWorkingOrderAndGetId();
$projectNo = $workingOrderData['projectNo'];
$workingOrderNo = $workingOrderData['workingOrderNo'];

$imageFilePaths = [
    __DIR__ . '/../../tests/images/190x50.png',
    __DIR__ . '/../../tests/images/300x50.png',
];
$uploads = CiEntityCreator::uploadImagesToWorkingOrder($projectNo, $workingOrderNo, $imageFilePaths);

$files = [
    [
        'rel_key1' => $uploads[0]['fid'],
        'reportedValues' => [
            ['id' => $positionIds[2], 'value' => 'Filename 1'],
            ['id' => $positionIds[3], 'value' => true],          // Show Working Order
            ['id' => $positionIds[4], 'value' => false],         // Show Uploader
            ['id' => $positionIds[5], 'value' => true],          // Show Comments
            ['id' => $positionIds[6], 'value' => true],          // Show Creation Date
            ['id' => $positionIds[7], 'value' => false],         // Show Upload Date
        ],
    ],
    [
        'rel_key1' => $uploads[1]['fid'],
        'reportedValues' => [
            ['id' => $positionIds[2], 'value' => 'Filename 2'],
            ['id' => $positionIds[3], 'value' => true],          // Show Working Order
            ['id' => $positionIds[4], 'value' => true],         // Show Uploader
            ['id' => $positionIds[5], 'value' => true],          // Show Comments
            ['id' => $positionIds[6], 'value' => true],          // Show Creation Date
            ['id' => $positionIds[7], 'value' => true],         // Show Upload Date
        ],
    ],
    [
        'rel_key1' => $uploads[0]['fid'],
        'reportedValues' => [
            ['id' => $positionIds[2], 'value' => 'Filename 3'],
            ['id' => $positionIds[3], 'value' => false],          // Show Working Order
            ['id' => $positionIds[4], 'value' => false],         // Show Uploader
            ['id' => $positionIds[5], 'value' => false],          // Show Comments
            ['id' => $positionIds[6], 'value' => false],          // Show Creation Date
            ['id' => $positionIds[7], 'value' => false],         // Show Upload Date
        ],
    ],
    [
        'rel_key1' => $uploads[0]['fid'],
    ],
];

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::MediaPrintV3,
    apiRouteSuffix: "/$schemaId",
    method: RequestMethod::POST,
    requestBodyForPost: json_encode($files),
    cleanupCallback: function () use ($result) {
        require_once __DIR__ . '/../../scripts/SampleDocumentationCreator.php';
        $creator = new SampleDocumentationCreator(__DIR__);
        $creator->deleteSchemasByIds([$result['schemaId']]);
    }
);