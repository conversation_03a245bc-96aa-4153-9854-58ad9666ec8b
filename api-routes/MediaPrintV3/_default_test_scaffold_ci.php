<?php
require_once __DIR__ . '/../../scripts/CiEntityCreator.php';
require_once __DIR__ . '/../../PrintoutCompiler.php';

$project = CiEntityCreator::createProject($customerNo = 3379);
$projectNo = $project['projectNo'];
$scaffold = CiEntityCreator::createScaffoldListItem($projectNo);
$ktr = $scaffold['ktr'];
$intnr = $scaffold['intnr'];

$workingOrder = CiEntityCreator::createWorkingOrderFromScaffoldListItem($ktr, $intnr);
$workingOrderNo = $workingOrder['aanr'];

$imageFilePaths = [
    __DIR__ . '/../../tests/images/190x50.png',
    __DIR__ . '/../../tests/images/300x50.png',
];
$uploads = CiEntityCreator::uploadImagesToWorkingOrder($projectNo, $workingOrderNo, $imageFilePaths);

// schema-without-photo.json is used because schema.json contains a type=photo
// and since our v3 code ignores that position anyway, we changed it to type=string to not slow down CI
// by avoiding one POST files API call
$result = CiEntityCreator::createSchemaAndReturnId(__DIR__ . '/schema-without-photo.json');
$schemaId = $result['schemaId'];
$positionIds = $result['positionIds'];

$files = [
    [
        'rel_key1' => $uploads[0]['fid'],
        'reportedValues' => [
            ['id' => $positionIds[2], 'value' => 'Scaffold Test File 1'],
            ['id' => $positionIds[3], 'value' => true],          // Show Working Order
            ['id' => $positionIds[4], 'value' => true],          // Show Uploader
            ['id' => $positionIds[5], 'value' => true],          // Show Comments
            ['id' => $positionIds[6], 'value' => true],          // Show Creation Date
            ['id' => $positionIds[7], 'value' => true],          // Show Upload Date
        ],
    ],
    [
        'rel_key1' => $uploads[1]['fid'],
        'reportedValues' => [
            ['id' => $positionIds[2], 'value' => 'Scaffold Test File 2'],
            ['id' => $positionIds[3], 'value' => true],          // Show Working Order
            ['id' => $positionIds[4], 'value' => true],          // Show Uploader
            ['id' => $positionIds[5], 'value' => true],          // Show Comments
            ['id' => $positionIds[6], 'value' => true],          // Show Creation Date
            ['id' => $positionIds[7], 'value' => true],          // Show Upload Date
        ],
    ],
];

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::MediaPrintV3,
    apiRouteSuffix: "/$schemaId",
    method: RequestMethod::POST,
    requestBodyForPost: json_encode($files),
); 