<?php
require_once __DIR__ . '/../MediaPrintV2/model.php';
require_once __DIR__ . '/../MediaPrint/MediaPrintCommon.php';

/** @noinspection PhpUnused C_MediaPrintV3 is called dynamically in index.php */

class C_MediaPrintV3 extends MediaPrintCommon
{
    /**
     * @return array<mixed>
     */
    public function getData(
        string|null $projectNo = null,
        string|null $workingOrderNo = null,
        string|null $hidePageNumbers = null,
        string|null $schemaId = null,
    ): array
    {
        $data = $this->getDataCommon(projectNo: $projectNo, workingOrderNo: $workingOrderNo, schemaId: $schemaId);

        self::processPreviewQrVideoFields($data['files']);

        return array_merge($data, [
            'hidePageNumbers' => $hidePageNumbers === "true" || $hidePageNumbers === "1"
        ]);
    }
}