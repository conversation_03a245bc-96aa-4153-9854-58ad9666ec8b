<?php
require_once __DIR__ . '/../MediaPrintV2/HardCopyUpload.php';

$config = new RouteConfig();
$config->usePlaywright(new PlaywrightConfig(
    marginTopMm: 30,
    marginBottomMm: 10,
    marginLeftMm: 10,
    marginRightMm: 10
));
$config->addRouteSegment(RouteConfigParameterType::int, "schemaId", required: true);
$config->addParameter(RouteConfigParameterType::bool, "hidePageNumbers", required: false);
$config->beforeRouteCallback = function (&$query) {
    $query['schemaId'] = Request::$query['schemaId'] ?? $query['schemaId'] ?? '';
    $_GET['schemaId'] = $query['schemaId'];
};

$config->requestMethod = PrintoutRequestMethod::POST;
$config->setTemplateDirCallback(function (TemplatePart $part): string {
    if ($part === TemplatePart::header) {
        return 'MediaPrintV3';
    }
    return 'MediaPrintV2';
});
return $config;