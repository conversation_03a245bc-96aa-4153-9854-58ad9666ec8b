<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Schadenmeldeformular())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Schadenmeldeformular</title>
    <meta name="description" content="Schadenmeldeformular Template">
    <meta name="author" content="<PERSON>vetlin I.">
    <style>
        body {
            padding: 5px;
        }

        * {
            font-family: Arial, sans-serif;
        }

        #main {
            width: 100%;
        }

        #main-table {
            width: 100%;
        }

        table, th, td {
            border: 1px solid #999;
            border-collapse: collapse;
        }

        table {
            table-layout: fixed;
            border-collapse: collapse;
        }

        table td {
            border-collapse: collapse;
            padding: 4px 10px;
        }

        .text-align-left {
            text-align: left;
        }

        .text-align-right {
            text-align: right;
        }

        .signatures {
            max-height: 90px;
        }

    </style>
</head>
<body>

<section id="main">
    <table id="main-table">
        <tr>
            <td colspan="5">
                <b style="text-decoration: underline">Schadenmeldeformular</b>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td colspan="5"><b style="font-style: italic">Schadenverursacher</b></td>
        </tr>
        <tr>
            <td></td>
            <td class="text-align-left" colspan="2">
                Name Gruppenleiter:
            </td>
            <td colspan="2">
                <?= $data['Name Gruppenleiter'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                Name Schadenverursacher:
            </td>
            <td colspan="2">
                <?= $data['mapped']['Schadenverursacher Name Schadensverursacher'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td colspan="5">
                Vertreten durch:
            </td>
        </tr>
        <tr>
            <td style="text-align: right">
                Name, Vorname:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Vertreten durch Name, Vorname'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Telefon-Nr.:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Vertreten durch Telefon-Nr.'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left">
                <b style="font-style: italic">Geschädigter</b>
            </td>
            <td colspan="4">
                <?= $data['mapped']['Geschädigter Name'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-left" colspan="5">
                Vertreten durch:
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Name, Vorname:
            </td>
            <td colspan="4">
                <?php
                $name = $data['mapped']['Vertreten durch Name'];
                if ($name) {
                    echo $name . ', ';
                }
                echo $data['mapped']['Vertreten durch Vorname'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Telefon-Nr.:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Vertreten durch Telefon-Nr.2'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                E-Mail:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Vertreten durch E-Mail'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left" colspan="5" style="font-style: italic">
                <b>Schadenereignis</b>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Schadendatum, Tag:
            </td>
            <td colspan="4">
                <?php
                $date = $data['mapped']['Schadenereignis Schadendatum, Tag'] ?? null;
                if ($date) {
                    echo date('d.m.Y', strtotime($date));
                }
                ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Schadenzeit:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Schadenereignis Schadenzeit'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Schadenort, Adresse:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Schadenereignis Schadenort, Adresse'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Hergang:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Schadenereignis Hergang'] ?? "" ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left" colspan="5">
                <b style="font-style: italic">Schadenobjekt</b>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Sachschaden:
            </td>
            <td style="text-align: right">
                Ja
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Sachschaden'] ?? "")
                === true ? "&#9745;" : "&#9744;"; ?>
            </td>
            <td style="text-align: right">
                Nein
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Sachschaden'] ?? "")
                === false ? "&#9745;" : "&#9744;"; ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Personenschaden:
            </td>
            <td style="text-align: right">
                Ja
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Personenschaden'] ?? "")
                === true ? "&#9745;" : "&#9744;"; ?>
            </td>
            <td style="text-align: right">
                Nein
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Personenschaden'] ?? "")
                === false ? "&#9745;" : "&#9744;"; ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Polizeirapport:
            </td>
            <td style="text-align: right">
                Ja
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Polizeirapport'] ?? "")
                === true ? "&#9745;" : "&#9744;"; ?>
            </td>
            <td style="text-align: right">
                Nein
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Polizeirapport'] ?? "")
                === false ? "&#9745;" : "&#9744;"; ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-right">
                Zeugen:
            </td>
            <td style="text-align: right">
                Ja
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Zeugen'] ?? "")
                === true ? "&#9745;" : "&#9744;"; ?>
            </td>
            <td style="text-align: right">
                Nein
            </td>
            <td>
                <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Zeugen'] ?? "")
                === false ? "&#9745;" : "&#9744;"; ?>
            </td>
        </tr>

        <tr>
            <td></td>
            <td colspan="2">
                Name, Adresse, Tel.:
            </td>
            <td colspan="2">
                <?php
                $parts = [
                    $data['mapped']['Zeugen Name'] ?? "",
                    $data['mapped']['Zeugen Adresse'] ?? "",
                    $data['mapped']['Zeugen Telefon'] ?? "",
                ];
                $parts = array_filter($parts, function ($x) {
                    return $x !== "";
                });
                echo implode(', ', $parts);
                ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td style="text-align: right">
                entstandener Schaden:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Schadenobjekt Entstandener Schaden'] ?? '' ?>
            </td>
        </tr>
        <tr>
            <td style="text-align: right">
                Schaden in CHF:
            </td>
            <td colspan="4">
                <?= $data['mapped']['Schadenobjekt Schaden in CHF'] ?? '' ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left">
                <b>Grobfahrlässigkeit:</b>
            </td>
            <td style="text-align: right">
                <b>Ja</b>
            </td>
            <td>
                <b>
                    <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Grobfahrlässigkeit (grobfahrlässig verursachte Schäden können dem Schadenverursacher am Lohn abgezogen werden!)'] ?? "")
                    === true ? "&#9745;" : "&#9744;"; ?>
                </b>
            </td>
            <td style="text-align: right">
                <b>Nein</b>
            </td>
            <td>
                <b>
                    <?= PrintoutHelper::isCheckboxTickedByReportedValue($data['mapped']['Schadenobjekt Grobfahrlässigkeit (grobfahrlässig verursachte Schäden können dem Schadenverursacher am Lohn abgezogen werden!)'] ?? "")
                    === false ? "&#9745;" : "&#9744;"; ?>
                </b>
            </td>
        </tr>
        <tr>
            <td colspan="5">
                <b>(grobfahrlässig verursachte Schäden können dem Schadenverursacher am Lohn abgezogen werden!)</b>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left">
                <b style="font-style: italic">Bemerkungen</b>
            </td>
            <td colspan="4">
                <?= $data['mapped']['Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07 Bemerkungen'] ?? '' ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left" colspan="5">
                Datum:
                <?php
                $date = $data['mapped']['Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07 Datum'] ?? null;
                if ($date) {
                    echo date('d.m.Y', strtotime($date));
                }
                ?>
            </td>
        </tr>
        <tr>
            <td colspan="5" style="color: white">_</td>
        </tr>
        <tr>
            <td class="text-align-left">
                Unterschrift:
            </td>
            <td colspan="4">
                Schadenverursacher
                <?php if (isset($data['mapped']['Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07 Unterschrift Schadenverursacher'])) { ?>
                    <img src="<?= $data['mapped']['Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07 Unterschrift Schadenverursacher'] ?>"
                         alt=""/>
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="4">
                Geschädigter
                <?php if (isset($data['mapped']['Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07 Unterschrift Geschädigter'])) { ?>
                    <img src="<?= $data['mapped']['Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07 Unterschrift Geschädigter'] ?>"
                         alt=""/>
                <?php } ?>
            </td>
        </tr>
    </table>
</section>
</body>
</html>
