{"name": "Schadenmeldeformular fix Vertr. durch & h Grobfahrl. v07", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2023-01-25T13:40:00Z", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["8"], "positions": [{"id": -1, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -2, "title": "Name Gruppenleiter", "type": "EMPLOYEESELECTOR", "collapsed": "0", "parentId": -1, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -3, "title": "Name <PERSON><PERSON><PERSON>", "type": "string", "collapsed": "0", "parentId": -1, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -4, "title": "Vertreten durch", "type": "headline", "collapsed": "0", "parentId": -1, "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -5, "title": "Name, <PERSON><PERSON><PERSON>", "type": "string", "collapsed": "0", "parentId": -4, "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -6, "title": "Telefon-Nr.", "type": "string", "collapsed": "0", "parentId": -4, "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -7, "title": "Geschädigter", "type": "headline", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -41, "title": "Name", "type": "string", "collapsed": "0", "parentId": -7, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -40, "title": "Vertreten durch", "type": "headline", "parentId": -7, "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -9, "title": "Name", "type": "string", "collapsed": "0", "parentId": -40, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -10, "title": "<PERSON><PERSON><PERSON>", "type": "string", "collapsed": "0", "parentId": -40, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -11, "title": "Telefon-Nr.", "type": "string", "collapsed": "0", "parentId": -40, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -12, "title": "E-Mail", "type": "string", "collapsed": "0", "parentId": -40, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -13, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -14, "title": "Schadendatum, Tag", "type": "date", "collapsed": "0", "parentId": -13, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -15, "title": "Schadenzeit", "type": "TIME", "collapsed": "0", "parentId": -13, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -16, "title": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "type": "string", "collapsed": "0", "parentId": -13, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -17, "title": "<PERSON><PERSON><PERSON>", "type": "string", "collapsed": "0", "parentId": -13, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -18, "title": "Schadenobjekt", "type": "headline", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -19, "title": "Sachschaden", "type": "checkbox", "collapsed": "0", "parentId": -18, "values": ["<PERSON>a", "<PERSON><PERSON>"], "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -20, "title": "Personenschaden", "type": "checkbox", "collapsed": "0", "parentId": -18, "values": ["<PERSON>a", "<PERSON><PERSON>"], "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -21, "title": "Polizeirapport", "type": "checkbox", "collapsed": "0", "parentId": -18, "values": ["<PERSON>a", "<PERSON><PERSON>"], "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -22, "title": "Zeugen", "type": "checkbox", "collapsed": "0", "parentId": -18, "values": ["<PERSON>a", "<PERSON><PERSON>"], "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -23, "title": "Name", "type": "string", "displayInside": -22, "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -24, "title": "<PERSON><PERSON><PERSON>", "type": "string", "displayInside": -22, "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -25, "title": "Telefon", "type": "string", "displayInside": -22, "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -26, "title": "Entstandener Schaden", "type": "string", "collapsed": "0", "parentId": -18, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -27, "title": "<PERSON><PERSON><PERSON> in CHF", "type": "string", "collapsed": "0", "parentId": -18, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -29, "title": "Grobfahrlässigkeit (grobfahrlässig verursachte Schäden können dem Schadenverursacher am Lohn abgezogen werden!)", "type": "checkbox", "collapsed": "0", "values": ["<PERSON>a", "<PERSON><PERSON>"], "parentId": -18, "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -30, "title": "Bemerkungen", "type": "string", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -31, "title": "Datum", "type": "date", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 1}, {"id": -32, "title": "Unterschrift Schadenverursacher", "type": "signatureField", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 2}, {"id": -33, "title": "Unterschrift Geschädigter", "type": "signatureField", "collapsed": "0", "isRequiredSignature": "0", "indentationLevel": 2}]}