{"name": "Aufzugdokumente Rückgabeprotokoll", "status": "active", "createdBy": "364", "createdOn": "2025-04-21T00:00:00Z", "positions": [{"id": 1, "title": "Rückgabeprotokoll Zahnstangenaufzüge", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Übergabeprotokoll Zahnstangenaufzüge", "type": "combobox-multi", "value": ["Aufbau", "Monatliche Kontrolle", "<PERSON><PERSON><PERSON>"]}, {"id": 3, "parentId": 1, "title": "Transportbühne wurde gemäß Stücklisten-Nr.:", "type": "int"}, {"id": 4, "title": "Sonstiges", "type": "string", "parentId": 1}, {"id": 5, "title": "Ort", "type": "string", "parentId": 1}, {"id": 6, "title": "Datum", "type": "date", "parentId": 1}, {"id": 7, "title": "Unterschrift", "type": "signatureField", "parentId": 1}, {"id": 8, "title": "Übernommen durch", "type": "headline"}, {"id": 9, "parentId": 8, "title": "Firma", "type": "string"}, {"id": 10, "parentId": 8, "title": "<PERSON>/<PERSON>", "type": "string"}, {"id": 11, "parentId": 8, "title": "Anschrift", "type": "string"}, {"id": 12, "parentId": 8, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 13, "parentId": 8, "title": "Fabrik-Nr.", "type": "string"}, {"id": 14, "title": "Typ: Transportbühne GEDA 500/1200/1500 ZP/ZZP", "type": "string", "displayInside": 13}, {"id": 15, "parentId": 8, "title": "Fabrik-Nr.", "type": "string"}, {"id": 16, "title": "Typ: Transportbühne ALIMAK", "type": "string", "displayInside": 15}, {"id": 17, "title": "Prüfprotokoll", "type": "headline"}, {"id": 18, "title": "Sichtprüfung Bühne", "type": "headline", "parentId": 17}, {"id": 19, "title": "Befund", "type": "string", "parentId": 18}, {"id": 20, "title": "Bemerkungen", "type": "string", "parentId": 18}, {"id": 21, "title": "Sichtprüfung <PERSON>", "type": "headline", "parentId": 17}, {"id": 22, "title": "Befund", "type": "string", "parentId": 21}, {"id": 23, "title": "Bemerkungen", "type": "string", "parentId": 21}, {"id": 24, "title": "Sichtprüfung Zahnstange", "type": "headline", "parentId": 17}, {"id": 25, "title": "Befund", "type": "string", "parentId": 24}, {"id": 26, "title": "Bemerkungen", "type": "string", "parentId": 24}, {"id": 27, "title": "Sichtprüfung Kabel/Elektrik", "type": "headline", "parentId": 17}, {"id": 28, "title": "Befund", "type": "string", "parentId": 27}, {"id": 29, "title": "Bemerkungen", "type": "string", "parentId": 27}, {"id": 30, "title": "Sichtprüfung Bedienung", "type": "headline", "parentId": 17}, {"id": 31, "title": "Befund", "type": "string", "parentId": 30}, {"id": 32, "title": "Bemerkungen", "type": "string", "parentId": 30}, {"id": 33, "title": "Funktionstest Bühne", "type": "headline", "parentId": 17}, {"id": 34, "title": "Befund", "type": "string", "parentId": 33}, {"id": 35, "title": "Bemerkungen", "type": "string", "parentId": 33}, {"id": 36, "title": "Funktionstest Etagentore", "type": "headline", "parentId": 17}, {"id": 37, "title": "Befund", "type": "string", "parentId": 36}, {"id": 38, "title": "Bemerkungen", "type": "string", "parentId": 36}, {"id": 39, "title": "Funktionstest Hupe", "type": "headline", "parentId": 17}, {"id": 40, "title": "Befund", "type": "string", "parentId": 39}, {"id": 41, "title": "Bemerkungen", "type": "string", "parentId": 39}, {"id": 42, "title": "Anleitung und Warnhinweise", "type": "headline", "parentId": 17}, {"id": 43, "title": "Befund", "type": "string", "parentId": 42}, {"id": 44, "title": "Bemerkungen", "type": "string", "parentId": 42}, {"id": 45, "title": "Prüfu<PERSON>", "type": "headline", "parentId": 17}, {"id": 46, "title": "Befund", "type": "string", "parentId": 45}, {"id": 47, "title": "Bemerkungen", "type": "string", "parentId": 45}]}