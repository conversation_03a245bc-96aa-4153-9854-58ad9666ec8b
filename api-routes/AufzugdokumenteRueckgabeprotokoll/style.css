@page {
    size: 210mm 297mm;
}

* {
    font-family: Arial, sans-serif;
    font-size: 15px;
}

body {
    width: 230mm;
    height: 100%;
    text-align: -webkit-center;
    margin: 0 auto;
    padding: 0;
}

.page {
    page-break-before: always;
    max-height: 297mm;
    min-height: 297mm;
    min-width: 210mm;
    max-width: 210mm;
    box-sizing: border-box;
}

.main-table-text {
    width: 100%;
    text-align: left
}

.first-header-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
}

.first-header-table td,
.main-table td {
    padding: 5px;
    border-right: 1px solid black;
}

.header-info-table {
    width: 100%;
    margin: 40px 0;
}

.main-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid black;
    margin-top: 5px;
}

.main-table td {
    border-bottom: 1px solid black;
}

.main-table td:last-child {
    height: 40px;
}

.last-table {
    width: 100%;
    page-break-inside: avoid;
    page-break-before: always;
}

u {
    text-underline-offset: 2px;
}

.checkbox-size {
    font-size: 24px;
    background-color: #3965af;
    color: white;
    margin-right: .5rem;
    display: inline-block;
    width: 40px;
    height: 30px;
    text-align: center;
}

.checkbox-size.unchecked {
    color: #3965af;
}

.last-section {
    vertical-align: bottom;
}

.signature {
    max-height: 200px;
    object-fit: contain;
    max-width: 50%;
}
