<?php
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . '/model.php';
    $data = (new C_AufzugdokumenteRueckgabeprotokoll())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!doctype html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Aufzugdokumente Rückgabeprotokoll</title>
    <style>
        @page {
            size: 210mm 297mm;
        }

        * {
            font-family: Arial, sans-serif;
            font-size: 14px;
        }

        body {
            width: 230mm;
            height: 100%;
            text-align: -webkit-center;
            margin: 0 auto;
            padding: 0;
        }

        .page {
            page-break-before: always;
            max-height: 297mm;
            min-height: 297mm;
            min-width: 210mm;
            max-width: 210mm;
            box-sizing: border-box;
            padding: 10px 35px;
        }

        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 5px;
        }

        .header-table td {
            vertical-align: top;
        }

        .logo {
            width: 200px;
            height: auto;
        }
    </style>
</head>
<body>
<div class="page">
    <!-- Header Table -->
    <table class="header-table">
        <tr>
            <td colspan="2">
                <div><strong>Verfasser</strong></div>
                <div>Scheler Maximilian</div>
                <div><strong>Veröffentlichungsdatum</strong></div>
                <div>20.08.2020</div>
            </td>
            <td colspan="2">
                <div><strong>Prozessverantwortlicher </strong></div>
                <div>Scheler Maximilian</div>
                <div><strong>Nachweis Ablage</strong></div>
                <div>Laufwerk U:</div>
            </td>
            <td colspan="2">
                <div><strong>Freigegeben durch</strong></div>
                <div>Scheler Maximilian</div>
                <div><strong>Revision</strong></div>
                <div>0001</div>
            </td>
            <td colspan="4" align="right">
                <img src="<?= PrintoutHelper::getImageBase64String(PrintoutHelper::translateLocalPathToServerPathFromRoot('img/schaferLogo.png')) ?>"
                     alt="SCHÄFER Logo" class="logo">
            </td>
        </tr>
    </table>
</div>
</body>
</html>