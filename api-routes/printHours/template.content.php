<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_PrintHours())->getData($_GET['personalNo'], $_GET['from'] ?? null, $_GET['to'] ?? null, $_GET['ktrAanr'] ?? null);
}

function timeCalculations(string $x, string $y): float|int
{
    //this will switch the end and the start if we have night shifts and the start is larger than the end
    if ($x && $y) {
        if ($x > $y) { //if start is larger than the end
            return 24 - ((strtotime($x) - strtotime($y)) / 3600);
        } else {
            return (strtotime($y) - strtotime($x)) / 3600;
        }
    } else {
        return 0;
    }
}

?>

<html lang="<?= PrintoutHelper::getLanguage() ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Print Hours</title>
    <!--suppress CssUnusedSymbol -->
    <style>

        body {
            font-family: Arial, sans-serif;
        }

        @page {
            size: A4;
        }

        .js-work-time,
        .js-break-time,
        .js-drive-to-time,
        .js-drive-from-time,
        .js-total-drive-time,
        .js-total-hours {
            text-align: center;
        }

        .date_td.print-lg-2 {
            width: 150px;
        }

        .wo_td_ht.print-lg-3 {
            width: 150px;
        }

        .text-right {
            text-align: right;
        }

        .carousel {
            position: relative;
        }

        .hideRow {
            display: none;
        }

        .ktr_row,
        .montly_row {
            background-color: #F6F6F6;
        }

        .annual_row {
            background-color: #E6E6E6;
        }

        .hourstip {
            padding: 5px;
            text-align: center;
            font-size: x-small;
            position: absolute;
            display: none;
            border: 1px solid lightslategrey;
            border-radius: 5px;
            background-color: white;
            filter: drop-shadow(1px 1px 1px lightslategrey);
            -webkit-filter: drop-shadow(1px 1px 1px lightslategrey);
            z-index: 99;
        }

        .hourstip table td {
            padding: 5px;
        }

        .hourstip table tr {
            border-bottom: 1px solid lightgray;
        }

        .hourstip table tr.ht_te {
            color: #333333;
            background-color: #CCCCCC;
        }

        .hourstip table tr:not(.ht_te) {
            background-color: whitesmoke;
        }

        .unapproved_te {
            color: #A3A3A3;
        }

        .visible-print-inline {
            display: inline !important;
        }

        body {
            -webkit-print-color-adjust: exact;
        }

        .table {
            width: 100%;
        }

        .table tr.bg-silver td {
            background-color: #DEDEDE !important;
        }

        .container-print {
            margin: 0;
        }

        .container-print .page-break {
            page-break-after: always;
            page-break-inside: avoid;
        }

        .table > thead > tr > th {
            word-break: keep-all;
        }

        .table > thead > tr > th,
        .table > tbody > tr > td,
        .table > tfoot > tr > td {
            border-bottom: 0 none;
            font-size: /*8*/ 12px;
        }

        body {
            margin-left: 5px;
            font-size: 12px;
        }

        .hidden-print {
            display: none;
        }

        .table > thead > tr > th {
            vertical-align: top;
            border-bottom: none;
        }

        .dateTdCol {
            display: inline-block;
            width: 36px;
        }

        #page-wrapper .sol-selected-display-item {
            width: 100%;
            margin: 0;
            border: none;
        }

        @media (min-width: 768px) {
            .container {
                width: 750px;
            }
        }

        @media (min-width: 992px) {
            .container {
                width: 970px;
            }
        }

        @media (min-width: 1200px) {
            .container {
                width: 1170px;
            }
        }

        .col-xs-12 {
            width: 100%;
            min-height: 1px;
            padding-right: 15px;
            padding-left: 15px;
        }

        .montly_row > td {
            font-size: 14px !important;
        }

        .annual_row > td {
            font-size: 16px !important;
        }

        td {
            color: black !important;
        }
    </style>
</head>
<body>
<?php
$counter = 0;
foreach ($data['hours'] as $pnr => $hours) {
    $employee = $data['employees'][$pnr];
    ?>
    <?php if ($counter != 0) { ?>
        <div style="page-break-before: always"></div>
    <?php } ?>
    <h2 style="font-size: 30px; text-align: center">
        <?= PrintoutHelper::lang('hours') ?>
        <span class="visible-print-inline">
        <?= ($employee['employeeNo'] ?? '') . '-' . ($employee['displayName'] ?? '') ?>
    </span>
        <span class="visible-print-inline">
        <?= empty($employee['birthDate']) ? '' : "({$employee['birthDate']})" ?>
    </span>
    </h2>
    <table class="table">
        <thead>
        <tr class="headerRow">
            <th class="date_td print-lg-2 dateSelector"></th>
            <th class="wo_td print-lg-3"><?= PrintoutHelper::lang('working_order') ?></th>
            <th class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui"><?= PrintoutHelper::lang('drive_to_time') ?></th>
            <th class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui"><?= PrintoutHelper::lang('working_time') ?></th>
            <th class="print-lg-1 js-regular-ui"><?= PrintoutHelper::lang('break_time') ?></th>
            <th class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui"><?= PrintoutHelper::lang('drive_from_time') ?></th>
            <th class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui"><?= PrintoutHelper::lang('total_drive_time'); ?>
                <br/><?= PrintoutHelper::lang('total_printable') ?></th>
            <th class="print-lg-1 js-regular-ui"><?= PrintoutHelper::lang('total_working_time'); ?>
                <br/><?= PrintoutHelper::lang('total_printable') ?></th>
        </tr>
        </thead>
        <tbody>
        <?php $drive_time_to = $drive_time_from = $travel_sum = $approved_sum = $total_drive_time = 0;
        $the_month = "";
        $the_year = "";
        $month_class_identifier = "row_month_";
        $year_class_identifier = "row_year_";
        $ktr_class_identifier = "head_ktr_row_";
        $ktr_rows = array();

        foreach ($hours['hours_data'] as $row) {
            list($y, $m, $d) = explode('-', $row['date']);
            $drive_time_from = timeCalculations($row['startDriveFrom'], $row['endDriveFrom']);
            $drive_time_to = timeCalculations($row['startDriveTo'], $row['endDriveTo']);
            $total_drive_time = $drive_time_from + $drive_time_to;
            $total_break_time = timeCalculations($row['startBreak'], $row['endBreak']);
            $totalHours = $row['std'];

            if (!array_key_exists($row['ktr'], $ktr_rows)) {
                $ktr_rows[$row['ktr']] = $row['name'];
            }

            if ($row['origin'] === 'hours' && ($row['wageTypeExternal'] ?? '') != 'B' &&
                !strpos($row['ltext'], "via Expense Allowance @")) {

                $approved_sum += $totalHours;
                $travel_sum += (float)$total_drive_time;
            }

            $wo_task = $row['taskDisplayName'] ?: '';
            if ($the_year !== $y) {
                $the_year = $y; ?>
                <tr class="annual_row <?= $hours['annual_bookings'][$y] ? '' : ' hideRow' ?>"
                    id="<?= $year_class_identifier . $y ?>">
                    <td><?= $y ?></td>
                    <td><?= PrintoutHelper::lang('bookings') ?>
                        : <?= $hours['annual_bookings'][$y] ?></td>
                    <td>
                        <span><?= PrintoutHelper::lang('hours') ?>: <?= $hours['annual_hours'][$y] ?></span>
                    </td>
                    <td><?= PrintoutHelper::lang('days') ?>: <?= $hours['annual_days'][$y] ?></td>
                </tr>
            <?php }
            if ($the_month !== $m) {
                $the_month = $m; ?>
                <tr id="<?= $month_class_identifier . $m ?>"
                    class="<?= $year_class_identifier . $y ?> montly_row bg-silver<?= $hours['monthly_bookings'][$y][$m] ? '' : ' hideRow' ?>">
                    <td>
                        <?= PrintoutHelper::getMonth(PrintoutHelper::getLanguage(), (int)$m) ?>
                    </td>
                    <td><?= PrintoutHelper::lang('bookings') ?>
                        : <?= $hours['monthly_bookings'][$y][$m] ?></td>
                    <td><?= PrintoutHelper::lang('hours') ?>
                        : <?= $hours['monthly_hours'][$y][$m] ?></td>
                    <td><?= PrintoutHelper::lang('days') ?>
                        : <?= $hours['monthly_days'][$y][$m] ?></td>
                </tr>
                <tr id="<?= $month_class_identifier . $m ?>"
                    class="<?= $year_class_identifier . $y ?> monthlyBonusRow bg-silver js-bonus-ui<?= ($data['monthlyBonusBookings'][$y][$m] ?? null) ? '' : ' hideRow' ?>">
                    <td class="only_desktop_visibility print_visibility"
                        colspan="5"></td>
                    <td>
                        <?php if ($hours['monthlyBonusAmount']) { ?>
                            <?php foreach ($hours['monthlyBonusAmount'][$y][$m] as $unit => $amount) { ?>
                                <?= PrintoutHelper::lang('sum') ?>(<?= $unit ?>): <?= $amount ?>
                                <br>
                            <?php } ?>
                        <?php } ?>
                    </td>
                    <td class="only_desktop_visibility print_visibility"></td>
                    <td class="only_desktop_visibility print_visibility"></td>
                </tr>
            <?php } ?>
            <tr id="<?= $y . $m . $d . $row['ktr'] . $row['aanr'] . $row['pnr'] . $row['uid'] ?>"
                class="<?= $month_class_identifier . $m ?> <?= $year_class_identifier . $y ?>
										<?= $ktr_class_identifier . $row['ktr'] ?>
										<?= ((($row['wageTypeExternal'] ?? '') == 'B' && !($row['origin'] == 'TimeTrackEvent')) || strpos($row['ltext'], "via Expense Allowance @")) ? 'bonusRow' : 'hoursRow' ?>"
            >
                <td class="date_td print-lg-2">
										<span class="text-info small dateTdCol">
											<?= substr(PrintoutHelper::getWeekDayName((int)date('N', mktime(0, 0, 0, (int)$m, (int)$d, (int)$y))), 0, 3) ?>
											&nbsp;
										</span>
                    <span class="js-date-col"><?= "$d.$m.$y" ?></span>
                </td>
                <td class="wo_td_ht print-lg-3" data-hourstip="ht_data">
                    <span><?= $ktr_rows[$row['ktr']] ?></span>
                    <span class="small"><?= $wo_task ?></span>
                </td>

                <td class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui js-drive-to-time">
                    <?php if (!empty($row['startDriveTo'])) { ?>
                        <?= $row['startDriveTo'] . ' h' . ' - ' . $row['endDriveTo'] . ' h' ?>
                    <?php } ?>
                </td>
                <td class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui js-work-time">
                    <?php if (!empty($row['start'])) { ?>
                        <?= $row['start'] . ' h' . ' - ' . $row['end'] . ' h' ?>
                    <?php } ?>
                </td>
                <td class="print-lg-1 js-regular-ui js-break-time">
                    <?php if (!empty($row['startBreak'])) { ?>
                        <?= $row['startBreak'] . ' h' . ' - ' . $row['endBreak'] . ' h' ?>
                    <?php } ?>
                </td>
                <td class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui js-drive-from-time">
                    <?php if (!empty($row['startDriveFrom'])) { ?>
                        <?= $row['startDriveFrom'] . ' h' . ' - ' . $row['endDriveFrom'] . ' h' ?>
                    <?php } ?>
                </td>
                <td class="only_desktop_visibility print_visibility print-lg-1 js-regular-ui js-total-drive-time">
                    <?php if ($total_drive_time) { ?>
                        <?= number_format($total_drive_time, 2, '.', '') . " h" ?>
                    <?php } ?>
                </td>
                <td class="print-lg-1 js-regular-ui js-total-hours bbb">

                    <!-- total hours or expense allowance -->
                    <?php if (($row['wageTypeExternal'] ?? '') == 'B' &&
                        !($row['origin'] == 'TimeTrackEvent') ||
                        strpos($row['ltext'] ?? '', "via Expense Allowance @")) {

                        // it's expense allowance so euro symbol will be used in this case
                        $symbol = " \xE2\x82\xAc";
                    } else {
                        $symbol = ' h';
                    }
                    if ($totalHours) {
                    echo number_format($totalHours, 2, '.', '') . $symbol; ?>
                </td>
                <?php } ?>
                <td class="hidden-print"></td>
            </tr>
            <?php if (isset($row['expenseAllowance'])) { ?>
                <tr>
                    <td colspan="7"></td>
                    <td>
                        <b><?= $row['expenseAllowance'][0]['value'] . ' ' . $row['expenseAllowance'][0]['wageTypeUnit']; ?></b>
                    </td>
                </tr>
            <?php }
        }
        foreach ($ktr_rows as $ktr_row_index => $ktr_row_value) { ?>
            <tr id="<?= $ktr_class_identifier . $ktr_row_index ?>" class="ktr_row hidden">
                <td style="width: 150px"><?= $ktr_row_value ?></td>
                <td class="print-lg-1" style="width: 150px"><?= PrintoutHelper::lang('bookings') ?>
                    : <?= $hours['ktr_bookings'][$ktr_row_index] ?></td>
                <td class="print-lg-1"><?= PrintoutHelper::lang('hours') ?>
                    : <?= $hours['ktr_hours'][$ktr_row_index] ?></td>
                <td class="print-lg-1"><?= PrintoutHelper::lang('days') ?>
                    : <?= $hours['ktr_days'][$ktr_row_index] ?></td>
            </tr>
        <?php } ?>
        </tbody>
        <tfoot>
        <tr>
            <td class="print-lg-10 text-right"
                colspan="3"><?= PrintoutHelper::lang('approved_total') ?>:
            </td>
            <td class="print-lg-2">
                <span><?= number_format((float)$approved_sum, 2, '.', '') ?></span> <?= PrintoutHelper::lang('x_hours') ?>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="print-lg-10 text-right"
                colspan="3"><?= PrintoutHelper::lang('total_drive_time') ?>:
            </td>
            <td class="print-lg-2">
                <span><?= number_format((float)$travel_sum, 2, '.', '') ?></span> <?= PrintoutHelper::lang('x_hours') ?>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="print-lg-10 text-right"
                colspan="3"><?= PrintoutHelper::lang('hours_total') ?>:
            </td>
            <td class="print-lg-2">
									<span>
										<?= number_format($approved_sum + $travel_sum, 2, '.', '') ?>
									</span> <?= PrintoutHelper::lang('x_hours') ?>
            </td>
        </tr>
        </tfoot>
    </table>

    <?php $counter++;
} ?>
</body>
</html>

