body {
    font-family: "Arial", sans-serif;
    width: 190mm;
    height: 100%;
    margin: 0 auto;
    padding: 0;
}

* {
    box-sizing: border-box;
}

.header {
    display: flex;
    align-items: center;
    border-bottom: 2px solid #000;
}

.header-logo-text-container {
    display: flex;
}

.right-container {
    display: inline-table;
}

.right-container .no-border,
.right-container .no-border th,
.right-container .no-border td {
    border: none;
    font-size: 12px;
    padding: 2px
}

.center-container {
    text-align: center;
}

.center-container h1 {
    font-size: 20px;
}

.left-container img,
.right-container img {
    width: 100%;
    display: block;
    margin-bottom: 8px;
}

.left-container p, .right-container p {
    margin: 0;
    font-size: 14px
}

.signatures {
    max-height: 90px;
    max-width: 100%;
    object-fit: contain;
}

.main-content {
    padding-bottom: 20px;
}

.two-columns {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.column {
    width: 48%;
    box-sizing: border-box;
    text-align: left;
}

.column h3 {
    margin: 0 0 10px;
    font-weight: 100;
}

.column p {
    margin: 0;
    font-weight: bold;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

/* Basic border styling for all table cells */
table,
th,
td {
    border-left: 1px solid #000;
    border-right: 1px solid #000;
}

th,
td {
    padding: 4px;
    vertical-align: top;
    height: 10px;
}

th {
    text-align: left;
}

.small-text {
    font-size: 0.9rem;
}

.border-bottom {
    border-bottom: 1px solid #000;
}

.border-top {
    border-top: 1px solid #000;
}

.border-none {
    border: none;
}