<?php
const AUSGEFUEHRTE_ARBEIT_DUPLICATION_COUNT = 20;

/**
 * Generates the PaderTor Servicebericht schema.
 *
 * @return array<mixed> The schema as a PHP array.
 */
function generatePaderTorSchema(): array
{
    $schema = [
        "name" => "PaderTor: Servicebericht",
        "status" => "active",
        "createdBy" => "4",
        "createdOn" => "2025-03-04T10:00:00Z",
        "positions" => []
    ];

    // Initialize current ID counter
    // The first static item is ID 1.
    $currentId = 1;

    // 1. Ausgeführte Arbeiten (Headline)
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Ausgeführte Arbeiten",
        "type" => "headline"
    ];

    // Variables for handling linked list type structure of 'Ausgeführte Arbeit' headlines
    $prevHeadlineId = null;
    $headlineBlockSize = 4; // Each 'Ausgeführte Arbeit' block consists of 1 headline + 3 child fields

    // 2. Dynamic 'Ausgeführte Arbeit' blocks
    for ($i = 0; $i < AUSGEFUEHRTE_ARBEIT_DUPLICATION_COUNT; $i++) {
        $headlineId = $currentId;
        $nextHeadlineId = ($i < AUSGEFUEHRTE_ARBEIT_DUPLICATION_COUNT - 1) ? ($currentId + $headlineBlockSize) : null;

        // Add the main 'Ausgeführte Arbeit' headline
        $schema['positions'][] = [
            "id" => $headlineId,
            "title" => "Ausgeführte Arbeit",
            "type" => "headline",
            "prevSiblingId" => $prevHeadlineId,
            "nextSiblingId" => $nextHeadlineId
        ];
        $currentId++; // Increment ID for the next item

        // Add child fields for this 'Ausgeführte Arbeit' block
        $schema['positions'][] = [
            "id" => $currentId++,
            "parentId" => $headlineId,
            "title" => "Beschreibung",
            "type" => "string"
        ];
        $schema['positions'][] = [
            "id" => $currentId++,
            "parentId" => $headlineId,
            "title" => "von (in Vierteltakt)",
            "type" => "time"
        ];
        $schema['positions'][] = [
            "id" => $currentId++,
            "parentId" => $headlineId,
            "title" => "bis (in Vierteltakt)",
            "type" => "time"
        ];

        // Update prevHeadlineId for the next iteration
        $prevHeadlineId = $headlineId;
    }

    // 3. Static trailing items (starting from ID 10 in the original schema if 2 duplications)
    // The $currentId correctly points to the next available ID after the loop.
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Verwendete Materialien/Ersatzteile",
        "type" => "MEASUREMENT"
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Offene Restarbeiten",
        "type" => "string",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Ankunftszeit (nächster Vierteltakt)",
        "type" => "time",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Abfahrtszeit (nächster Vierteltakt)",
        "type" => "time",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Anfahrt (Anzahl)",
        "type" => "int",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Einsatz Scherenbühne",
        "type" => "checkbox"
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Techniker (Initialen)",
        "type" => "string",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Unterschrift Techniker (inkl. Name in Druckbuchstaben)",
        "type" => "signatureField",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Ort",
        "type" => "string",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId++,
        "title" => "Datum",
        "type" => "date",
        "required" => true
    ];
    $schema['positions'][] = [
        "id" => $currentId,
        "title" => "Unterschrift Kunde (inkl. Name in Druckbuchstaben)",
        "type" => "signatureField",
        "required" => true
    ];
    return $schema;
}

$json = json_encode(generatePaderTorSchema(), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
file_put_contents(__DIR__ . '/schema.json', $json);
