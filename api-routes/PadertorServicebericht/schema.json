{"name": "Servicebericht", "description": null, "status": "active", "createdBy": "4", "printText": null, "createdOn": "2025-03-04T10:00:00Z", "editedBy": null, "editedOn": "2025-08-08T16:29:04Z", "applicableEntities": [], "printOptions": ["73"], "positions": [{"id": -1, "title": "Ausgeführte Arbeiten", "type": "headline", "sort": "20", "visible": true}, {"id": -2, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "30", "nextSiblingId": "4200", "visible": true}, {"id": -3, "parentId": -2, "title": "Beschreibung", "type": "string", "sort": "40", "visible": true}, {"id": -4, "parentId": -2, "title": "von (in Vierteltakt)", "type": "time", "sort": "50", "visible": true}, {"id": -5, "parentId": -2, "title": "bis (in Vierteltakt)", "type": "time", "sort": "60", "visible": true}, {"id": -6, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "70", "nextSiblingId": "4204", "prevSiblingId": "4196", "visible": false}, {"id": -7, "parentId": -6, "title": "Beschreibung", "type": "string", "sort": "80", "visible": false}, {"id": -8, "parentId": -6, "title": "von (in Vierteltakt)", "type": "time", "sort": "90", "visible": false}, {"id": -9, "parentId": -6, "title": "bis (in Vierteltakt)", "type": "time", "sort": "100", "visible": false}, {"id": -10, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "110", "nextSiblingId": "4208", "prevSiblingId": "4200", "visible": false}, {"id": -11, "parentId": -10, "title": "Beschreibung", "type": "string", "sort": "120", "visible": false}, {"id": -12, "parentId": -10, "title": "von (in Vierteltakt)", "type": "time", "sort": "130", "visible": false}, {"id": -13, "parentId": -10, "title": "bis (in Vierteltakt)", "type": "time", "sort": "140", "visible": false}, {"id": -14, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "150", "nextSiblingId": "4212", "prevSiblingId": "4204", "visible": false}, {"id": -15, "parentId": -14, "title": "Beschreibung", "type": "string", "sort": "160", "visible": false}, {"id": -16, "parentId": -14, "title": "von (in Vierteltakt)", "type": "time", "sort": "170", "visible": false}, {"id": -17, "parentId": -14, "title": "bis (in Vierteltakt)", "type": "time", "sort": "180", "visible": false}, {"id": -18, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "190", "nextSiblingId": "4216", "prevSiblingId": "4208", "visible": false}, {"id": -19, "parentId": -18, "title": "Beschreibung", "type": "string", "sort": "200", "visible": false}, {"id": -20, "parentId": -18, "title": "von (in Vierteltakt)", "type": "time", "sort": "210", "visible": false}, {"id": -21, "parentId": -18, "title": "bis (in Vierteltakt)", "type": "time", "sort": "220", "visible": false}, {"id": -22, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "230", "nextSiblingId": "4220", "prevSiblingId": "4212", "visible": false}, {"id": -23, "parentId": -22, "title": "Beschreibung", "type": "string", "sort": "240", "visible": false}, {"id": -24, "parentId": -22, "title": "von (in Vierteltakt)", "type": "time", "sort": "250", "visible": false}, {"id": -25, "parentId": -22, "title": "bis (in Vierteltakt)", "type": "time", "sort": "260", "visible": false}, {"id": -26, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "270", "nextSiblingId": "4224", "prevSiblingId": "4216", "visible": false}, {"id": -27, "parentId": -26, "title": "Beschreibung", "type": "string", "sort": "280", "visible": false}, {"id": -28, "parentId": -26, "title": "von (in Vierteltakt)", "type": "time", "sort": "290", "visible": false}, {"id": -29, "parentId": -26, "title": "bis (in Vierteltakt)", "type": "time", "sort": "300", "visible": false}, {"id": -30, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "310", "nextSiblingId": "4228", "prevSiblingId": "4220", "visible": false}, {"id": -31, "parentId": -30, "title": "Beschreibung", "type": "string", "sort": "320", "visible": false}, {"id": -32, "parentId": -30, "title": "von (in Vierteltakt)", "type": "time", "sort": "330", "visible": false}, {"id": -33, "parentId": -30, "title": "bis (in Vierteltakt)", "type": "time", "sort": "340", "visible": false}, {"id": -34, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "350", "nextSiblingId": "4232", "prevSiblingId": "4224", "visible": false}, {"id": -35, "parentId": -34, "title": "Beschreibung", "type": "string", "sort": "360", "visible": false}, {"id": -36, "parentId": -34, "title": "von (in Vierteltakt)", "type": "time", "sort": "370", "visible": false}, {"id": -37, "parentId": -34, "title": "bis (in Vierteltakt)", "type": "time", "sort": "380", "visible": false}, {"id": -38, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "390", "nextSiblingId": "4236", "prevSiblingId": "4228", "visible": false}, {"id": -39, "parentId": -38, "title": "Beschreibung", "type": "string", "sort": "400", "visible": false}, {"id": -40, "parentId": -38, "title": "von (in Vierteltakt)", "type": "time", "sort": "410", "visible": false}, {"id": -41, "parentId": -38, "title": "bis (in Vierteltakt)", "type": "time", "sort": "420", "visible": false}, {"id": -42, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "430", "nextSiblingId": "4240", "prevSiblingId": "4232", "visible": false}, {"id": -43, "parentId": -42, "title": "Beschreibung", "type": "string", "sort": "440", "visible": false}, {"id": -44, "parentId": -42, "title": "von (in Vierteltakt)", "type": "time", "sort": "450", "visible": false}, {"id": -45, "parentId": -42, "title": "bis (in Vierteltakt)", "type": "time", "sort": "460", "visible": false}, {"id": -46, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "470", "nextSiblingId": "4244", "prevSiblingId": "4236", "visible": false}, {"id": -47, "parentId": -46, "title": "Beschreibung", "type": "string", "sort": "480", "visible": false}, {"id": -48, "parentId": -46, "title": "von (in Vierteltakt)", "type": "time", "sort": "490", "visible": false}, {"id": -49, "parentId": -46, "title": "bis (in Vierteltakt)", "type": "time", "sort": "500", "visible": false}, {"id": -50, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "510", "nextSiblingId": "4248", "prevSiblingId": "4240", "visible": false}, {"id": -51, "parentId": -50, "title": "Beschreibung", "type": "string", "sort": "520", "visible": false}, {"id": -52, "parentId": -50, "title": "von (in Vierteltakt)", "type": "time", "sort": "530", "visible": false}, {"id": -53, "parentId": -50, "title": "bis (in Vierteltakt)", "type": "time", "sort": "540", "visible": false}, {"id": -54, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "550", "nextSiblingId": "4252", "prevSiblingId": "4244", "visible": false}, {"id": -55, "parentId": -54, "title": "Beschreibung", "type": "string", "sort": "560", "visible": false}, {"id": -56, "parentId": -54, "title": "von (in Vierteltakt)", "type": "time", "sort": "570", "visible": false}, {"id": -57, "parentId": -54, "title": "bis (in Vierteltakt)", "type": "time", "sort": "580", "visible": false}, {"id": -58, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "590", "nextSiblingId": "4256", "prevSiblingId": "4248", "visible": false}, {"id": -59, "parentId": -58, "title": "Beschreibung", "type": "string", "sort": "600", "visible": false}, {"id": -60, "parentId": -58, "title": "von (in Vierteltakt)", "type": "time", "sort": "610", "visible": false}, {"id": -61, "parentId": -58, "title": "bis (in Vierteltakt)", "type": "time", "sort": "620", "visible": false}, {"id": -62, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "630", "nextSiblingId": "4260", "prevSiblingId": "4252", "visible": false}, {"id": -63, "parentId": -62, "title": "Beschreibung", "type": "string", "sort": "640", "visible": false}, {"id": -64, "parentId": -62, "title": "von (in Vierteltakt)", "type": "time", "sort": "650", "visible": false}, {"id": -65, "parentId": -62, "title": "bis (in Vierteltakt)", "type": "time", "sort": "660", "visible": false}, {"id": -66, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "670", "nextSiblingId": "4264", "prevSiblingId": "4256", "visible": false}, {"id": -67, "parentId": -66, "title": "Beschreibung", "type": "string", "sort": "680", "visible": false}, {"id": -68, "parentId": -66, "title": "von (in Vierteltakt)", "type": "time", "sort": "690", "visible": false}, {"id": -69, "parentId": -66, "title": "bis (in Vierteltakt)", "type": "time", "sort": "700", "visible": false}, {"id": -70, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "710", "nextSiblingId": "4268", "prevSiblingId": "4260", "visible": false}, {"id": -71, "parentId": -70, "title": "Beschreibung", "type": "string", "sort": "720", "visible": false}, {"id": -72, "parentId": -70, "title": "von (in Vierteltakt)", "type": "time", "sort": "730", "visible": false}, {"id": -73, "parentId": -70, "title": "bis (in Vierteltakt)", "type": "time", "sort": "740", "visible": false}, {"id": -74, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "750", "nextSiblingId": "4272", "prevSiblingId": "4264", "visible": false}, {"id": -75, "parentId": -74, "title": "Beschreibung", "type": "string", "sort": "760", "visible": false}, {"id": -76, "parentId": -74, "title": "von (in Vierteltakt)", "type": "time", "sort": "770", "visible": false}, {"id": -77, "parentId": -74, "title": "bis (in Vierteltakt)", "type": "time", "sort": "780", "visible": false}, {"id": -78, "title": "Ausgeführte Arbeit", "type": "headline", "sort": "790", "prevSiblingId": "4268", "visible": false}, {"id": -79, "parentId": -78, "title": "Beschreibung", "type": "string", "sort": "800", "visible": false}, {"id": -80, "parentId": -78, "title": "von (in Vierteltakt)", "type": "time", "sort": "810", "visible": false}, {"id": -81, "parentId": -78, "title": "bis (in Vierteltakt)", "type": "time", "sort": "820", "visible": false}, {"id": -82, "title": "Verwendete Materialien/Ersatzteile", "type": "MEASUREMENT", "sort": "830", "visible": true}, {"id": -83, "title": "<PERSON><PERSON>", "type": "string", "required": "true", "sort": "840", "visible": true}, {"id": -84, "title": "Ankunftszeit (nächster Vierteltakt)", "type": "time", "required": "true", "sort": "850", "visible": true}, {"id": -85, "title": "Abfahrtszeit (nächster Vierteltakt)", "type": "time", "required": "true", "sort": "860", "visible": true}, {"id": -86, "title": "Anfahrt (Anzahl)", "type": "int", "required": "true", "sort": "870", "visible": true}, {"id": -87, "title": "Einsatz Scherenbühne", "type": "checkbox", "sort": "880", "visible": true}, {"id": -88, "title": "Techniker (Initialen)", "type": "string", "required": "true", "sort": "890", "visible": true}, {"id": -89, "title": "Name Kunde", "type": "string", "required": "true", "sort": "895", "multipliable": "0", "visible": true}, {"id": -90, "title": "Ort", "type": "string", "required": "true", "sort": "910", "visible": true}, {"id": -91, "title": "Datum", "type": "date", "required": "true", "sort": "920", "visible": true}, {"id": -92, "title": "Unterschrift Techniker (inkl. Name in Druckbuchstaben)", "type": "signatureField", "required": "true", "sort": "921", "visible": true}, {"id": -93, "title": "Unterschrift Kunde (inkl. Name in Druckbuchstaben)", "type": "signatureField", "sort": "930", "visible": true}]}