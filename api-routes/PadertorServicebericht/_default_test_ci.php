<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/SampleDocumentationCreator.php';

// this API route is only allowed to a working order, see
// https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/merge_requests/340#note_688236
$creator = new SampleDocumentationCreator(__DIR__, forceDocumentationCreation: false);
$docs = $creator->createDocumentations([
    'schema.json',
], RelType::WorkingOrder);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::PadertorServicebericht,
//      fileType: PrintoutFileType::Html,
    params: [
        "schemaId" => $docs[0]['schemaId'],
        "documentId" => $docs[0]['documentId'],
		],
    cleanupCallback: function () use ($creator, $docs) {
        $creator->deleteSchemasByIds([$docs[0]['schemaId']]);
    }
);
