<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_PadertorServicebericht
{
    private PrintoutCurl $curl;
    private string $baseUrl;

    public function __construct()
    {
        $this->baseUrl = PrintoutHelper::getApiBaseUrl();
        $this->curl = new PrintoutCurl($this->baseUrl);
    }

    /**
     * @return array<mixed>
     */
    public function getData(string|null $schemaId, string|null $documentId): array
    {
        $document = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $this->curl)['fullDocument'];
        if ($document['documentRelType'] !== "workingOrder") {
            die_with_response_code(Response::BAD_REQUEST,
                "The document is only allowed to be related to a working order, but it is related to " . $document['documentRelType']);
        }
        $projectNo = $document['documentRelKey1'];
        $workingOrderNo = $document['documentRelKey2'];
        $projectData = PrintoutHelper::downloadProject($projectNo,
            "customerNo,externalProjectNo,projectName,projectSiteAddress,projectSiteZipCode,projectSiteCity,partners",
            $this->curl);

        $workingOrderData = PrintoutHelper::downloadWorkingOrder(
            $projectNo, $workingOrderNo, "shortDescription,longDescription,plannedDate,staffPreplanned,resourcesNonHr", $this->curl);

        $hourJsonResponse = $this->curl->_simple_call('get', "v1/hours/all?ktr=$projectNo&aanr=$workingOrderNo&type=approved",
            [], PrintoutHelper::getHeadersForApiCalls());
        $hoursData = json_decode($hourJsonResponse, associative: true);
        $breakStart = null;
        $breakEnd = null;
        // the spec lists that we take the break for any employee and display for all, since they should be the same
        // https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/merge_requests/340#note_693074
        if (count($hoursData) >= 1) {
            // format: 09:45
            $breakStart = $hoursData[0]['startBreak'];
            $breakEnd = $hoursData[0]['endBreak'];
        }

        $employeeNames = $this->downloadEmployeeNames($workingOrderData['staffPreplanned']);
        $resources = $this->downloadResourceNames($workingOrderData['resourcesNonHr']);
        $employeeResourceOverview = [
            'names' => implode('; ', array_merge($employeeNames, $resources)),
            'date' => $this::formatDate($workingOrderData['plannedDate']),
            'breakStart' => $breakStart,
            'breakEnd' => $breakEnd,
        ];

        $mappedDocument = PrintoutHelper::mapDocumentChildrenToValues($document);
        $measurements = $this->downloadMeasurement($mappedDocument);
        return [
            'customerData' => $this->downloadCustomer($projectData['customerNo']),
            'document' => $mappedDocument,
            'measurements' => $measurements,
            'projectData' => $projectData,
            'documentTasks' => $this->extractCarriedOutWork($mappedDocument),
            'employeeResourceOverview' => $employeeResourceOverview,
            'firstProjectPartner' => $this->downloadFirstProjectsPartner($projectData),
            'workingOrderData' => $workingOrderData
        ];
    }

    /**
     * @param array<mixed> $mappedDocument
     * @return array<mixed>
     */
    private function downloadMeasurement(array $mappedDocument): array
    {
        $result = [];
        $oversizeItemIds = $mappedDocument['Verwendete Materialien/Ersatzteile'] ?? [];
        if (empty($oversizeItemIds)) {
            return $result;
        }
        $base = PrintoutHelper::getApiBaseUrl();
        $urls = [];
        foreach ($oversizeItemIds as $oversizeItemId) {
            $oversizeIdParts = explode('-', $oversizeItemId);
            $billingServiceSheetNo = $oversizeIdParts[0];
            $billingServiceSheetVersionNo = $oversizeIdParts[1];
            $billingServiceSheetItemNo = $oversizeIdParts[2];
            $urls[] = "$base/v1/oversizes/items/$billingServiceSheetNo/$billingServiceSheetVersionNo/$billingServiceSheetItemNo";
        }
        $responses = $this->curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
        foreach ($responses as $i => $resp) {
            PrintoutHelper::ensureStatusCode($resp['statusCode'], "GET $urls[$i]", $resp['response']);
            if (!empty($resp['response'])) {
                $result[] = $resp['response'];
            }
        }
        return array_map(function ($item) {
            return [
                "description" => $item['description'],
                "quantity" => PrintoutHelper::formatNumberLocale($item['quantity'], 2, NumberLocale::DE)
            ];
        }, $result);
    }

    /**
     * For the "Ausgeführte Arbeiten" in the schema.
     * @param array<mixed> $mappedDocument
     * @return array<mixed> empty when none are set
     */
    private function extractCarriedOutWork(array $mappedDocument): array
    {
        $tasks = [];
        for ($i = 1; $i <= 20; $i++) {
            $suffix = $i === 1 ? "" : "$i";
            $desc = null;
            $from = null;
            $to = null;
            if (isset($mappedDocument['Beschreibung' . $suffix])) {
                $desc = $mappedDocument['Beschreibung' . $suffix];
            }
            if (isset($mappedDocument['von (in Vierteltakt)' . $suffix])) {
                $from = $mappedDocument['von (in Vierteltakt)' . $suffix];
            }
            if (isset($mappedDocument['bis (in Vierteltakt)' . $suffix])) {
                $to = $mappedDocument['bis (in Vierteltakt)' . $suffix];
            }
            if ($desc !== null || $from !== null || $to !== null) {
                $tasks[] = [
                    "name" => $desc,
                    "from" => $from,
                    "to" => $to
                ];
            }
        }
        return $tasks;
    }

    /**
     * @param array<mixed> $projectData
     * @return string|null the string to display in the template, or null if not set
     */
    private function downloadFirstProjectsPartner(array $projectData): string|null
    {
        $firstProjectPartner = null;
        if (count($projectData['partners']) >= 1) {
            $firstPartnerId = $projectData['partners'][0];
            $base = PrintoutHelper::getApiBaseUrl();
            // the API returns an array, we take the first one always
            $firstProjectPartnerResult = json_decode($this->curl->_simple_call('get', "$base/v1/partners?virtualNo=$firstPartnerId",
                [], PrintoutHelper::getHeadersForApiCalls()), true)[0];
            // trim because the lastName can be empty
            $firstProjectPartner = trim($firstProjectPartnerResult['personalTitle'] . ' ' . $firstProjectPartnerResult['lastName']);
            $phones = [];
            if (!empty($firstProjectPartnerResult['phone'])) {
                $phones[] = $firstProjectPartnerResult['phone'];
            }
            if (!empty($firstProjectPartnerResult['cell'])) {
                $phones[] = $firstProjectPartnerResult['cell'];
            }
            if (count($phones) >= 1) {
                $firstProjectPartner .= " (" . implode(', ', $phones) . ")";
            }
        }
        return $firstProjectPartner;
    }

    /**
     * @return array<mixed>
     */
    private function downloadCustomer(string $customerNo): array
    {
        return empty($customerNo) ? [] : PrintoutHelper::downloadCustomer((int)$customerNo, $this->curl);
    }

    /**
     * @param array<mixed> $resourcesNonHr
     * @return array<mixed> of kurzname of the resources
     */
    private function downloadResourceNames(array $resourcesNonHr): array
    {
        if (empty($resourcesNonHr)) {
            return [];
        }
        $base = PrintoutHelper::getApiBaseUrl();
        $urls = array_map(fn($rnr) => "$base/v1/vehicles/select_resource/$rnr", $resourcesNonHr);
        $responses = $this->curl->_multi_call('get', $urls, [], PrintoutHelper::getHeadersForApiCalls());
        $names = [];
        foreach ($responses as $i => $resp) {
            PrintoutHelper::ensureStatusCode($resp['statusCode'], "GET $urls[$i]", $resp['response']);
            $names[] = $resp['response']['kurzname'];
        }
        return $names;
    }

    /**
     * @param array<mixed> $staffPreplanned
     * @return array<mixed>  of displayName of the employees
     */
    private function downloadEmployeeNames(array $staffPreplanned): array
    {
        if (empty($staffPreplanned)) {
            return [];
        }
        $personalNumbers = implode(',', $staffPreplanned);
        $employees = json_decode(
            $this->curl->_simple_call('get', "v3/employees?employeeNumbers=$personalNumbers", [], PrintoutHelper::getHeadersForApiCalls()),
            true
        );
        return array_map(
            function ($employee) {
                $role = trim($employee['profession']);
                if ($role !== "") {
                    return trim($employee['displayName']) . " ($role)";
                }
                return trim($employee['displayName']);
            },
            $employees
        );
    }

    /**
     * @param array<mixed> $data
     */
    public static function displayField(array $data, string $field, string $prefix = '', string $suffix = ''): string
    {
        if (empty($data[$field])) {
            return '';
        }
        $value = $data[$field];
        if (is_array($value)) {
            $value = reset($value);
        }
        return $prefix . $value . $suffix;
    }

    /**
     * Convert from 2021-01-01 to 01.01.2021
     */
    public static function formatDate(string $date): string
    {
        return substr($date, 8, 2) . "." . substr($date, 5, 2) . "." . substr($date, 0, 4);
    }

}