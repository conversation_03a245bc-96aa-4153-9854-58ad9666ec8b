<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_PadertorServicebericht())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<html lang="de">
<head>
    <title>PaderTor: Servicebericht</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<div>
    <!-- Header with three containers: left, center, and right -->
    <div class="header">
        <div class="left-container" style="flex: 1">
            <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("api-routes/PaderTor/img/houses.png") ?>"
                 alt="">
            <p>
                <?= C_PadertorServicebericht::displayField($data['projectData'], 'externalProjectNo', '(', ')') ?>
                <?= C_PadertorServicebericht::displayField($data['projectData'], 'projectName') ?>
            </p>
        </div>
        <div class="center-container" style="flex: 1">
            <h1 style="font-weight: normal; margin: 0 10px">Montage-/Reparatur-/<br>Service-Bericht</h1>
        </div>
        <div class="right-container" style="flex: 1">
            <img src="<?= PrintoutHelper::translateLocalPathToServerPathFromRoot("api-routes/PaderTor/img/logo.png") ?>"
                 alt="">
            <table class="no-border">
                <tr>
                    <td>PaderTor GmbH</td>
                    <td>|</td>
                    <td>Tel.: 0 52 51 / 688 11-0</td>
                </tr>
                <tr>
                    <td>Navarrastraße 12</td>
                    <td>|</td>
                    <td>Fax: 0 52 51 / 688 11-99</td>
                </tr>
                <tr>
                    <td>33106 Paderborn</td>
                    <td>|</td>
                    <td>Mail: <EMAIL></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="main-content">
        <div class="two-columns">
            <div class="column">
                <h3>Auftraggeber:</h3>
                <p>
                    <?= C_PadertorServicebericht::displayField($data['customerData'], 'name') ?><br>
                    <?= C_PadertorServicebericht::displayField($data['customerData'], 'address') ?><br>
                    <?php
                    $customerZipCity = PrintoutHelper::formatAddress(
                        address: null,
                        postalCode: $data['customerData']['postcode'],
                        city: $data['customerData']['city']
                    );
                    if ($customerZipCity !== "") {
                        echo $customerZipCity;
                    }
                    ?>
                    <br><br>
                    - <?= C_PadertorServicebericht::displayField($data['customerData'], 'name') . ' ' ?>
                    <?= C_PadertorServicebericht::displayField($data['customerData'], 'phoneNumber', '(', ')') ?>
                </p>
            </div>
            <div class="column">
                <h3>Serviceobjekt/Lieferanschrift:</h3>
                <p>
                    <?= C_PadertorServicebericht::displayField($data['customerData'], 'displayName') ?><br>
                    <?= C_PadertorServicebericht::displayField($data['projectData'], 'projectSiteAddress') ?><br>
                    <?php
                    $zipCity = PrintoutHelper::formatAddress(
                        address: null,
                        postalCode: $data['projectData']['projectSiteZipCode'],
                        city: $data['projectData']['projectSiteCity']);
                    if ($zipCity !== "") {
                        echo $zipCity;
                    }
                    if ($data['firstProjectPartner'] !== null) {
                        echo "<br><br>- " . $data['firstProjectPartner'] . "<br>";
                    }
                    ?>
                </p>
            </div>
        </div>
    </div>

    <table>
        <tr class="border-top">
            <th colspan="8">Aufgabenbeschreibung / Ausgangssituation</th>
        </tr>
        <tr class="border-bottom">
            <td colspan="8">
                <?php
                $parts = [];
                if (!empty($data['workingOrderData']['shortDescription'])) {
                    $parts[] = $data['workingOrderData']['shortDescription'];
                }
                if (!empty($data['workingOrderData']['longDescription'])) {
                    $parts[] = nl2br($data['workingOrderData']['longDescription']);
                }
                echo implode("<br><br>", $parts);
                ?>
            </td>
        </tr>

        <tr>
            <th colspan="4" class="border-none">Techniker:</th>
            <th colspan="2" class="border-none">Tag:</th>
            <th colspan="2" class="border-none">Pause:</th>
        </tr>
        <tr class="border-bottom">
            <td colspan="4" class="border-none"><?= $data['employeeResourceOverview']['names'] ?></td>
            <td colspan="2" class="border-none"><?= $data['employeeResourceOverview']['date'] ?></td>
            <td colspan="1" class="border-none">von <?= $data['employeeResourceOverview']['breakStart'] ?></td>
            <td colspan="1" class="border-none">bis <?= $data['employeeResourceOverview']['breakEnd'] ?></td>
        </tr>
        <tr>
            <th colspan="6" style="border:none;">Ausgeführte Arbeiten:</th>
            <th colspan="2" class="border-none">Zeit (in Vierteltakt):</th>
        </tr>
        <tr class="border-bottom">
            <td colspan="6"></td>
            <td colspan="1">von</td>
            <td colspan="1">bis</td>
        </tr>
        <?php
        if (empty($data['documentTasks'])) { ?>
            <tr class="border-bottom">
                <td colspan="8">&nbsp;</td>
            </tr>
        <?php } else {
            foreach ($data['documentTasks'] as $task) { ?>
                <tr class="border-bottom">
                    <td colspan="6"><?= $task['name'] ?></td>
                    <td colspan="1"><?= $task['from'] ?></td>
                    <td colspan="1"><?= $task['to'] ?></td>
                </tr>
            <?php }
        } ?>
        <tr>
            <th colspan="8">Verwendete Materialien / Ersatzteile:</th>
        </tr>
        <tr class="border-bottom">
            <td colspan="1">Menge</td>
            <td colspan="3">Bezeichnung</td>
            <td colspan="1">Menge</td>
            <td colspan="3">Bezeichnung</td>
        </tr>
        <?php
        if (empty($data['measurements'])) { ?>
            <tr class="border-bottom">
                <td colspan="1">&nbsp;</td>
                <td colspan="3">&nbsp;</td>
                <td colspan="1">&nbsp;</td>
                <td colspan="3">&nbsp;</td>
            </tr>
        <?php } else {
            // render measurements in two columns (left and right) directly in this table
            $half = (int)ceil(count($data['measurements']) / 2);
            $left = array_slice($data['measurements'], 0, $half);
            $right = array_slice($data['measurements'], $half);
            $rows = max(count($left), count($right));
            for ($i = 0; $i < $rows; $i++) { ?>
                <tr class="border-bottom">
                    <td colspan="1"><?= $left[$i]['quantity'] ?></td>
                    <td colspan="3"><?= htmlspecialchars($left[$i]['description']) ?></td>
                    <td colspan="1"><?= $right[$i]['quantity'] ?? '&nbsp;' ?></td>
                    <td colspan="3"><?= htmlspecialchars($right[$i]['description'] ?? '') ?></td>
                </tr>
            <?php }
        } ?>
        <tr>
            <th colspan="8">Offene Restarbeiten:</th>
        </tr>
        <tr class="border-bottom">
            <td colspan="8">
                <?= $data['document']['Offene Restarbeiten'] ?? '<br>' ?>
            </td>
        </tr>
        <tr class="border-bottom">
            <td colspan="4">
                <b>Ankunftszeit (nächster Vierteltakt):</b>
                <?= C_PadertorServicebericht::displayField($data['document'], 'Ankunftszeit (nächster Vierteltakt)') ?>
            </td>
            <?php // set width so the time is always displayed without wrapping to next row ?>
            <td colspan="4" style="min-width: 90mm">
                <b>Abfahrzeit (nächster Vierteltakt):</b>
                <?= C_PadertorServicebericht::displayField($data['document'], 'Abfahrtszeit (nächster Vierteltakt)') ?>
            </td>
        </tr>

        <tr class="border-bottom">
            <?php // set width so the time is always displayed without wrapping to next row ?>
            <td colspan="4" style="min-width: 90mm">
                <b>Anfahrt (Anzahl):</b>
                <?= C_PadertorServicebericht::displayField($data['document'], 'Anfahrt (Anzahl)') ?>
            </td>
            <td colspan="4">
                <b>Einsatz / Scherenbühne:</b>
                <?php
                $isCheckboxTicked = $data['document']['Einsatz Scherenbühne'] ?? false;
                echo $isCheckboxTicked ? '&#9744 Ja / &#9745 Nein' : '&#9745 Ja / &#9744 Nein';
                ?>
            </td>
        </tr>
        <tr class="border-bottom">
            <td colspan="4">
                <b>Techniker (Initialen):</b>
                <?= C_PadertorServicebericht::displayField($data['document'], 'Techniker (Initialen)') ?>
            </td>
            <td colspan="4">
                <b>Unterschrift Techniker:<br>(inkl. Name in Druckbuchstaben)<br></b>
                <?php if (empty($data['document']['Unterschrift Techniker (inkl. Name in Druckbuchstaben)'])) { ?>
                    <br>
                <?php } else { ?>
                    <img src="<?= C_PadertorServicebericht::displayField($data['document'],
                        'Unterschrift Techniker (inkl. Name in Druckbuchstaben)') ?>"
                         alt="" class="signatures">
                <?php } ?>
            </td>
        </tr>
        <tr class="border-bottom">
            <td colspan="4">
                <b>Ort/Datum:</b>
                <?php
                $parts = [];
                if (!empty($data['document']['Ort'])) {
                    $parts[] = $data['document']['Ort'];
                }
                if (!empty($data['document']['Datum'])) {
                    $parts[] = C_PadertorServicebericht::formatDate($data['document']['Datum']);
                }
                echo implode(' ', $parts);
                ?>
            </td>
            <td colspan="4">
                <b>Unterschrift Kunde:<br>(inkl. Name in Druckbuchstaben)<br></b>
                <?php
                $hasSignature = !empty($data['document']['Unterschrift Kunde (inkl. Name in Druckbuchstaben)']);
                $hasName = !empty($data['document']['Name Kunde']);
                $hasNameOrSignature = $hasSignature || $hasName;
                if ($hasNameOrSignature) {
                    if ($hasName) { ?>
                        <p style="margin: 0"><?= $data['document']['Name Kunde'] ?></p>
                    <?php }
                    if ($hasSignature) { ?>
                        <img src="<?= C_PadertorServicebericht::displayField($data['document'],
                            'Unterschrift Kunde (inkl. Name in Druckbuchstaben)') ?>"
                             alt="" class="signatures">
                    <?php }
                } else {
                    echo "<br>";
                } ?>
            </td>
        </tr>
    </table>
</div>
</body>
</html>
