{"name": "Bautagesbericht", "description": "", "status": "active", "createdBy": "365", "printText": null, "createdOn": "2020-01-23T10:00:00Z", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["42"], "positions": [{"id": -1, "title": "Wetter", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "Temperatur (in °C)", "type": "float", "virtualAssistantText": "<PERSON><PERSON>st gibst du nun die Außentemperatur an.", "virtualAssistantImage": "TOP_RIGHT", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -3, "parentId": -1, "title": "Verhältnis", "type": "combobox-multi", "virtualAssistantText": "Ist es auf der Baustelle trocken, verregnet oder sogar schneebed<PERSON>t?", "virtualAssistantImage": "TOP_RIGHT", "isRequiredSignature": "0", "value": ["trocken", "bedeckt", "<PERSON><PERSON><PERSON>", "Regen", "<PERSON><PERSON><PERSON>", "Wind", "Sc<PERSON><PERSON>"], "values": [{"value": "trocken", "colorCode": null}, {"value": "bedeckt", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Regen", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Wind", "colorCode": null}, {"value": "Sc<PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -4, "title": "Anwesende Firmen / Personen", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -5, "parentId": -4, "title": "Anwesende", "type": "string", "virtualAssistantText": "Wer ist mit dir auf der Baustelle?", "virtualAssistantImage": "THOUGHTFUL", "isRequiredSignature": "0", "visible": true}, {"id": -6, "title": "Feststellungen / Leistungsstand", "type": "headline", "virtualAssistantText": "Wie weit bin ich bereits mit den Aufgaben vorangekommen?", "virtualAssistantImage": "BOTTOM_LEFT", "isRequiredSignature": "0", "visible": true}, {"id": -7, "parentId": -6, "title": "Feststellungen", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -6, "title": "Leistungsstand", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -9, "title": "Fotos / Sonstiges", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -10, "parentId": -9, "title": "Fotos", "type": "photo", "virtualAssistantText": "Lade doch direkt ein Foto hoch, damit du den Stand oder Probleme an der Baustelle festhalten kannst.", "virtualAssistantImage": "BOTTOM_LEFT", "isRequiredSignature": "0", "visible": true}, {"id": -11, "parentId": -9, "title": "Sonstiges", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -12, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -13, "parentId": -12, "title": "Datum des Besuchs", "type": "date", "virtualAssistantText": "<PERSON>es Datum ist heute oder warst du an einem anderen Tag auf der Baustelle?", "virtualAssistantImage": "THOUGHTFUL", "isRequiredSignature": "0", "visible": true}, {"id": -14, "parentId": -12, "title": "Name <PERSON><PERSON><PERSON><PERSON>", "type": "string", "virtualAssistantText": "Wer ist der Bauherr?", "virtualAssistantImage": "BOTTOM_LEFT", "isRequiredSignature": "0", "visible": true}, {"id": -15, "parentId": -12, "title": "Unterschrift Bauherr", "type": "signatureField", "virtualAssistantText": "Lass ihn doch direkt unterschreiben, dann hast du dies direkt erledigt.", "virtualAssistantImage": "BOTTOM_LEFT", "isRequiredSignature": "0", "visible": true}, {"id": -16, "parentId": -12, "title": "Name Handwerker", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "parentId": -12, "title": "Unterschrift Handwerker", "type": "signatureField", "isRequiredSignature": "0", "visible": true}, {"id": -18, "parentId": -12, "title": "Name <PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "parentId": -12, "title": "Unterschrift Bauleiter", "type": "signatureField", "isRequiredSignature": "0", "visible": true}]}