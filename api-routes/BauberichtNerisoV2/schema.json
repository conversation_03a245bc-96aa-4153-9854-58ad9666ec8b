{"name": "Baubericht", "status": "active", "createdBy": "289", "createdOn": "2023-05-04T09:56:23Z", "positions": [{"id": 1, "title": "Wetter", "type": "headline"}, {"id": 2, "title": "Temperatur (in °C)", "type": "float", "parentId": 1}, {"id": 3, "title": "Verhältnis", "type": "combobox-multi", "values": ["trocken", "bedeckt", "<PERSON><PERSON><PERSON>", "Regen", "<PERSON><PERSON><PERSON>", "Wind", "Sc<PERSON><PERSON>"], "parentId": 1}, {"id": 4, "title": "Wetterbedingte Einschränkungen:", "type": "string", "parentId": 1}, {"id": 5, "title": "Anwesende Firmen / Personen", "type": "string"}, {"id": 6, "title": "Feststellungen / Leistungsstand", "type": "string"}, {"id": 7, "title": "Foto", "type": "photo"}, {"id": 8, "title": "Sonstiges", "type": "string"}, {"id": 9, "title": "Datum des Besuchs", "type": "date"}, {"id": 10, "title": "Name <PERSON><PERSON><PERSON><PERSON>", "type": "string"}, {"id": 11, "title": "Unterschrift Bauherr", "type": "signatureField"}, {"id": 12, "title": "Name Handwerker", "type": "string"}, {"id": 13, "title": "Unterschrift Handwerker", "type": "signatureField"}, {"id": 14, "title": "Name <PERSON><PERSON>", "type": "string"}, {"id": 15, "title": "Unterschrift Bauleiter", "type": "signatureField"}]}