<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_BauberichtNerisoV2
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument']);

        // handle difference between schema.json and schema_heinen.json, otherwise 'Wetterbedingte Einschränkungen:' is always
        // incorrect shown for the heinen version
        $data['hasWetterbedingteEinschränkungen'] = false;
        $schema = PrintoutHelper::downloadSchema($schemaId, $curl);

        foreach ($schema['children'] as $child) {
            if ($child['title'] === "Wetterbedingte Einschränkungen:") {
                $data['hasWetterbedingteEinschränkungen'] = true;
                break;
            }
        }

        if (isset($data['Datum des Besuchs']) && $data['Datum des Besuchs']) {
            $data['dateOfVisit'] = date("d.m.Y", strtotime($data['Datum des Besuchs']));
        } else {
            /** @noinspection PhpUnhandledExceptionInspection */
            $utcDateTime = new DateTime($doc['fullDocument']['documentCreatedOn'], new DateTimeZone('UTC'));
            $berlinDateTimeZone = new DateTimeZone('Europe/Berlin');
            $utcDateTime->setTimezone($berlinDateTimeZone);
            $data['dateOfVisit'] = $utcDateTime->format('d.m.Y H:i');
        }

        $project = PrintoutHelper::downloadProject((int)$doc['documentRelKey1'], "projectName,customerName", $curl);
        $data['projectName'] = $project['projectName'];
        $data['customerName'] = $project['customerName'];
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'] ?? '';
        $data['documentId'] = $documentId;

        if (isset($data['Verhältnis']) && count($data['Verhältnis']) >= 1) {
            $data['Verhältnis'] = implode(', ', $data['Verhältnis']);
        }

        return $data;
    }
}
