<!DOCTYPE html>
<html lang="de">
<head>
    <title></title>
    <style>
        .footer {
            width: 100%;
            font-size: small !important;
            border-collapse: collapse;
            mso-cellspacing: 0;
            color: black;
            height: 20mm;
        }

        .rightAlign {
            text-align: right;
            font-size: medium;
            font-family: Arial, sans-serif;
        }
    </style>

    <!--suppress ES6ConvertVarToLetConst -->
    <script>
        function subst() {
            var vars = {};
            var query_strings_from_url = document.location.search.substring(1).split('&');
            for (var query_string in query_strings_from_url) {
                if (query_strings_from_url.hasOwnProperty(query_string)) {
                    var temp_var = query_strings_from_url[query_string].split('=', 2);
                    vars[temp_var[0]] = decodeURI(temp_var[1]);
                }
            }
            var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
            for (var css_class in css_selector_classes) {
                if (css_selector_classes.hasOwnProperty(css_class)) {
                    var element = document.getElementsByClassName(css_selector_classes[css_class]);
                    for (var j = 0; j < element.length; ++j) {
                        element[j].textContent = vars[css_selector_classes[css_class]];
                    }
                }
            }
        }
    </script>
</head>

<body onload="subst()">

<table class="footer">
    <tr>
        <td class="rightAlign">Seite
            <span class="page"></span> / <span class="topage"></span>
        </td>
    </tr>
</table>

</body>
</html>
