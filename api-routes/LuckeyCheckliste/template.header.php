<?php /** @noinspection HtmlDeprecatedAttribute */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_LuckeyCheckliste())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!DOCTYPE html>
<html lang="de">
<head>
    <title></title>
    <style>
        .headerTable {
            width: 100%;
        }

        .font {
            font-size: 22px;
        }

        body {
            font-family: Arial, sans-serif;
        }

        .underlined {
            border-bottom: 2px solid black;
        }
    </style>

</head>

<body>
<table class="headerTable">
    <tbody>
    <tr>
        <td width="60%" class="font" valign="top">
            <b>
                <?= trim(str_replace('v2', '', $data['schemaTitle'])); ?> <?= $data['woOrProjectData']['externalWorkingOrderNo'] ?>
            </b>
        </td>
        <td rowspan="2" width="40%" valign="top">
            <img src="<?= $data['logo'] ?>" alt="companyLogo" width="99%" height="auto">
        </td>
    </tr>
    <tr>
        <td></td>
        <td></td>
    </tr>
    <tr>
        <!-- external project number eventually -->
        <td colspan="2" align="left">
            zu Projekt <?= $data['woOrProjectData']['externalProjectNo'] ?>
        </td>
    </tr>
    </tbody>
</table>
<div class="underlined"></div>
<br>
</body>

</html>
