body {
    font-family: Arial, sans-serif;
    margin-top: 10px !important;
    width: 100%;
}

.logo {
    top: 10px;
    width: 100%;
    height: 100%;
}

.container-logo {
    width: 100%;
    padding-top: 10px;
    height: 320px
}

.text-container {
    width: 50%;
    display: table-cell;
    vertical-align: bottom;
    padding: 5px;
}

.container-header {
    width: 100%;
    display: table;
}

header {
    padding-bottom: 20px;
}

.image-container {
    width: 50%;
    display: table-cell;
    vertical-align: middle;
    text-align: right;
}

.text-blue {
    font-weight: bold;
    color: #005988;
    font-size: large
}

.text-left {
    text-align: left;
}

.header-table, .sub-header-table, .positions {
    width: 100%;
    font-size: large !important;
}

.positions {
    border-collapse: collapse !important;
    width: 100% !important;
}

.positions tr td {
    padding: 7px;
    vertical-align: top;
}

.positions td:first-child {
    width: 15%;
}

.positions td:nth-child(2) {
    width: 40%;
}

.positions td:nth-child(3) {
    width: 10%;
}

.positions td:last-child {
    width: 35%;
}

.sub-header-table tr td {
    vertical-align: top;
    text-align: left;
    width: 20%;
}

.sub-header-table tr td:first-child {
    width: 40%;
}

.footer {
    width: 100%;
}

.footer-image {
    width: 100%;
    height: auto;
}