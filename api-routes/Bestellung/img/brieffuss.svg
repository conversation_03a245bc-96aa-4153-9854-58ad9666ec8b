<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="2481px" height="190px" viewBox="140 33 2081 130" version="1.1" xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:serif="http://www.serif.com/"
     style="fill-rule:evenodd;clip-rule:evenodd;">
    <g id="Ebene-1" serif:id="Ebene 1" transform="matrix(4.16667,0,0,4.16667,0,0)">
        <g transform="matrix(1,0,0,1,38.4868,27.3275)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.151,6.949C-0.252,7.049 -0.382,7.1 -0.542,7.1L-2.549,7.1C-2.708,7.1 -2.838,7.049 -2.939,6.949C-3.041,6.848 -3.091,6.717 -3.091,6.558L-3.091,0C-3.091,-0.159 -3.041,-0.29 -2.939,-0.39C-2.838,-0.491 -2.708,-0.542 -2.549,-0.542L-0.542,-0.542C-0.382,-0.542 -0.252,-0.491 -0.151,-0.39C-0.05,-0.29 0,-0.159 0,0L0,1.778L-0.684,1.778L-0.684,0.059L-2.412,0.059L-2.412,6.499L-0.684,6.499L-0.684,3.858L-1.499,3.858L-1.499,3.272L0,3.272L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M41.646,11.681L40.25,11.681L40.25,13.112L41.646,13.112L41.646,11.681ZM42.276,15.338C42.276,15.706 42.094,15.89 41.729,15.89L40.167,15.89C39.802,15.89 39.62,15.706 39.62,15.338L39.62,11.691C39.62,11.323 39.802,11.139 40.167,11.139L41.729,11.139C42.094,11.139 42.276,11.323 42.276,11.691L42.276,13.4L42.056,13.629L40.25,13.629L40.25,15.348L41.646,15.348L41.646,14.479L42.276,14.479L42.276,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,45.9087,30.2287)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.501,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.369 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.194,-0.552 -0.01,-0.369 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.531 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,49.5757,30.2287)">
            <path d="M0,3.647C0,4.015 -0.182,4.199 -0.547,4.199L-2.09,4.199C-2.455,4.199 -2.637,4.015 -2.637,3.647L-2.637,-0C-2.637,-0.369 -2.455,-0.552 -2.09,-0.552L-0.547,-0.552C-0.182,-0.552 0,-0.369 0,-0L0,0.947L-0.64,0.947L-0.64,0.019L-1.987,0.019L-1.987,3.627L-0.64,3.627L-0.64,2.568L0,2.568L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,53.3599,26.7855)">
            <path d="M0,7.642L-0.649,7.642L-0.649,3.472L-2.08,3.511L-2.08,7.642L-2.729,7.642L-2.729,0L-2.08,0L-2.08,3.023C-1.823,2.991 -1.566,2.957 -1.309,2.925C-0.983,2.883 -0.728,2.862 -0.542,2.862C-0.181,2.862 0,3.039 0,3.394L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,17.4095)">
            <path d="M56.758,11.847L56.04,11.847L56.04,11.11L56.758,11.11L56.758,11.847ZM56.465,16.427L56.465,14.747L55.015,14.747L55.015,16.456L56.465,16.427ZM55.499,11.847L54.781,11.847L54.781,11.11L55.499,11.11L55.499,11.847ZM57.095,17.018L56.456,17.018L56.456,16.886C56.198,16.922 55.941,16.956 55.684,16.989C55.365,17.028 55.109,17.047 54.918,17.047C54.563,17.047 54.385,16.871 54.385,16.52L54.385,14.787C54.385,14.422 54.569,14.24 54.937,14.24L56.465,14.24L56.465,12.809L55.069,12.809L55.069,13.62L54.439,13.62L54.439,12.819C54.439,12.451 54.621,12.267 54.986,12.267L56.548,12.267C56.913,12.267 57.095,12.451 57.095,12.819L57.095,17.018Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,59.9566,33.8562)">
            <path d="M0,-6.499L-0.918,-6.499L-0.918,-4.18L-0.107,-4.18L-0.107,-3.608L-0.918,-3.608L-0.918,0.571L-1.558,0.571L-1.558,-3.608L-2.109,-3.608L-2.109,-4.18L-1.558,-4.18L-1.558,-6.519C-1.558,-6.887 -1.375,-7.07 -1.011,-7.07L0,-7.07L0,-6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,62.2466,28.1975)">
            <path d="M0,6.23L-0.923,6.23C-1.288,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,65.6353,30.2287)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.501,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.369 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.194,-0.552 -0.01,-0.369 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.531 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,68.4624,33.8562)">
            <path d="M0,-6.499L-0.918,-6.499L-0.918,-4.18L-0.107,-4.18L-0.107,-3.608L-0.918,-3.608L-0.918,0.571L-1.558,0.571L-1.558,-3.608L-2.109,-3.608L-2.109,-4.18L-1.558,-4.18L-1.558,-6.519C-1.558,-6.887 -1.375,-7.07 -1.011,-7.07L0,-7.07L0,-6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,17.4095)">
            <path d="M71.392,11.847L70.674,11.847L70.674,11.11L71.392,11.11L71.392,11.847ZM70.132,11.847L69.414,11.847L69.414,11.11L70.132,11.11L70.132,11.847ZM71.763,17.018L71.124,17.018L71.124,16.886C70.863,16.922 70.605,16.956 70.347,16.989C70.035,17.028 69.776,17.047 69.571,17.047C69.219,17.047 69.043,16.871 69.043,16.52L69.043,12.267L69.683,12.267L69.683,16.437L71.124,16.398L71.124,12.267L71.763,12.267L71.763,17.018Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,75.5523,26.7855)">
            <path d="M0,7.642L-0.649,7.642L-0.649,3.472L-2.08,3.511L-2.08,7.642L-2.729,7.642L-2.729,0L-2.08,0L-2.08,3.023C-1.823,2.991 -1.566,2.957 -1.309,2.925C-0.983,2.883 -0.728,2.862 -0.542,2.862C-0.181,2.862 0,3.039 0,3.394L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,79.0874,32.835)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.405,-3.093 -1.187,-3.129C-0.926,-3.168 -0.709,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M81.939,11.681L80.543,11.681L80.543,13.112L81.939,13.112L81.939,11.681ZM82.569,15.338C82.569,15.706 82.387,15.89 82.022,15.89L80.46,15.89C80.095,15.89 79.913,15.706 79.913,15.338L79.913,11.691C79.913,11.323 80.095,11.139 80.46,11.139L82.022,11.139C82.387,11.139 82.569,11.323 82.569,11.691L82.569,13.4L82.349,13.629L80.543,13.629L80.543,15.348L81.939,15.348L81.939,14.479L82.569,14.479L82.569,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,86.1284,32.835)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.405,-3.093 -1.187,-3.129C-0.926,-3.168 -0.709,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,19.1285)">
            <path d="M87.623,15.299L86.881,15.299L86.881,14.44L87.623,14.44L87.623,15.299ZM87.623,11.998L86.881,11.998L86.881,11.139L87.623,11.139L87.623,11.998Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,94.4292,26.7855)">
            <path d="M0,7.642L-0.64,7.642L-0.64,2.803C-0.64,2.735 -0.617,2.535 -0.571,2.203L-1.821,6.924L-1.958,6.924L-3.208,2.203C-3.163,2.538 -3.14,2.738 -3.14,2.803L-3.14,7.642L-3.779,7.642L-3.779,0L-3.149,0L-1.909,4.971C-1.903,4.997 -1.896,5.071 -1.89,5.191C-1.89,5.142 -1.883,5.069 -1.87,4.971L-0.63,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,16.6775)">
            <path d="M96.251,17.75L95.611,17.75L95.611,12.999L96.251,12.999L96.251,17.75ZM96.26,11.959L95.601,11.959L95.601,11.139L96.26,11.139L96.26,11.959Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M99.434,11.71L98.003,11.71L98.003,15.318L99.434,15.318L99.434,11.71ZM100.084,15.338C100.084,15.706 99.899,15.89 99.532,15.89L97.901,15.89C97.536,15.89 97.354,15.706 97.354,15.338L97.354,11.691C97.354,11.323 97.536,11.139 97.901,11.139L99.532,11.139C99.899,11.139 100.084,11.323 100.084,11.691L100.084,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,103.199,27.4057)">
            <path d="M0,6.431L0,2.842L-0.898,2.842C-1.256,2.842 -1.436,3.026 -1.436,3.394L-1.436,5.933C-1.436,6.297 -1.256,6.48 -0.898,6.48C-0.814,6.48 -0.514,6.463 0,6.431M0.645,7.022L0,7.022L0,6.89C-0.697,6.997 -1.083,7.051 -1.157,7.051C-1.434,7.051 -1.657,6.957 -1.826,6.77C-1.996,6.583 -2.08,6.35 -2.08,6.07L-2.08,3.291C-2.08,2.998 -1.984,2.755 -1.792,2.561C-1.6,2.368 -1.359,2.271 -1.069,2.271L0,2.271L0,-0.62L0.645,-0.62L0.645,7.022Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,107.398,32.835)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.405,-3.093 -1.187,-3.129C-0.926,-3.168 -0.709,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,110.279,30.2967)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.1,4.141 -1.356,4.16 -1.548,4.16C-1.903,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.437 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.448,-0.62 0.63,-0.437 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,114.029,32.0202)">
            <path d="M0,1.836L0,-1.753C-0.514,-1.785 -0.814,-1.802 -0.898,-1.802C-1.256,-1.802 -1.436,-1.619 -1.436,-1.255L-1.436,1.289C-1.436,1.653 -1.256,1.836 -0.898,1.836L0,1.836ZM0.645,3.657C0.645,4.025 0.46,4.209 0.093,4.209L-1.45,4.209C-1.825,4.209 -2.012,3.977 -2.012,3.511C-2.012,3.455 -2.009,3.373 -2.004,3.265C-2,3.155 -1.997,3.076 -1.997,3.027L-1.357,3.027L-1.357,3.657L0,3.657L0,2.407L-1.069,2.407C-1.359,2.407 -1.6,2.311 -1.792,2.116C-1.984,1.923 -2.08,1.68 -2.08,1.387L-2.08,-1.392C-2.08,-1.672 -1.996,-1.905 -1.826,-2.093C-1.657,-2.279 -1.434,-2.373 -1.157,-2.373C-1.083,-2.373 -0.697,-2.319 0,-2.212L0,-2.344L0.645,-2.344L0.645,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,120.967,26.7855)">
            <path d="M0,7.642L-0.747,7.642L-2.51,3.97L-2.51,7.642L-3.188,7.642L-3.188,0L-2.51,0L-2.51,3.433L-0.908,0L-0.171,0L-0.171,0.044L-1.899,3.682L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,124.278,29.6475)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.129 -1.571,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M127.354,11.681L125.958,11.681L125.958,13.112L127.354,13.112L127.354,11.681ZM127.984,15.338C127.984,15.706 127.802,15.89 127.437,15.89L125.875,15.89C125.51,15.89 125.328,15.706 125.328,15.338L125.328,11.691C125.328,11.323 125.51,11.139 125.875,11.139L127.437,11.139C127.802,11.139 127.984,11.323 127.984,11.691L127.984,13.4L127.764,13.629L125.958,13.629L125.958,15.348L127.354,15.348L127.354,14.479L127.984,14.479L127.984,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,131.333,29.6765)">
            <path d="M0,4.751L-2.5,4.751L-2.5,4.453L-0.767,0.581L-2.319,0.581L-2.319,0L0,0L0,0.303L-1.738,4.17L0,4.17L0,4.751Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M134.283,11.681L132.887,11.681L132.887,13.112L134.283,13.112L134.283,11.681ZM134.913,15.338C134.913,15.706 134.73,15.89 134.366,15.89L132.803,15.89C132.439,15.89 132.256,15.706 132.256,15.338L132.256,11.691C132.256,11.323 132.439,11.139 132.803,11.139L134.366,11.139C134.73,11.139 134.913,11.323 134.913,11.691L134.913,13.4L134.693,13.629L132.886,13.629L132.886,15.348L134.283,15.348L134.283,14.479L134.913,14.479L134.913,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,138.663,34.4375)">
            <path d="M0,-4.761L-1.177,0L-1.768,0L-2.949,-4.761L-2.28,-4.761L-1.47,-1.021L-0.649,-4.761L0,-4.761Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,16.6775)">
            <path d="M140.137,17.75L139.497,17.75L139.497,12.999L140.137,12.999L140.137,17.75ZM140.147,11.959L139.488,11.959L139.488,11.139L140.147,11.139L140.147,11.959Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,143.877,30.2287)">
            <path d="M0,3.647C0,4.015 -0.182,4.199 -0.547,4.199L-2.09,4.199C-2.455,4.199 -2.637,4.015 -2.637,3.647L-2.637,-0C-2.637,-0.369 -2.455,-0.552 -2.09,-0.552L-0.547,-0.552C-0.182,-0.552 0,-0.369 0,-0L0,0.947L-0.64,0.947L-0.64,0.019L-1.987,0.019L-1.987,3.627L-0.64,3.627L-0.64,2.568L0,2.568L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,294.689,15.6465)">
            <rect x="147.005" y="11.139" width="0.679" height="7.642" style="fill:rgb(0,140,79);"/>
        </g>
        <g transform="matrix(1,0,0,1,156.07,26.7855)">
            <path d="M0,7.642L-0.679,7.642L-0.679,3.97L-2.52,3.97L-2.52,7.642L-3.198,7.642L-3.198,0L-2.52,0L-2.52,3.36L-0.679,3.36L-0.679,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,160.543,26.7855)">
            <path d="M0,7.642L-0.713,7.642L-2.134,3.55L-2.041,3.413L-0.83,3.413L-0.83,0.601L-2.573,0.601L-2.573,7.642L-3.252,7.642L-3.252,0L-0.693,0C-0.534,0 -0.404,0.051 -0.303,0.152C-0.202,0.252 -0.151,0.383 -0.151,0.542L-0.151,3.443C-0.151,3.811 -0.399,3.995 -0.894,3.995C-0.942,3.995 -1.014,3.992 -1.108,3.988C-1.203,3.982 -1.271,3.98 -1.313,3.98C-0.874,5.198 -0.436,6.418 0,7.642" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,163.937,27.3869)">
            <path d="M0,6.44L0,3.798L-0.688,3.349L-1.699,3.349L-1.699,6.44L0,6.44ZM-0.02,2.299L-0.02,0L-1.699,0L-1.699,2.758L-0.698,2.758L-0.02,2.299ZM0.684,6.499C0.684,6.658 0.633,6.789 0.532,6.889C0.431,6.99 0.301,7.041 0.142,7.041L-2.378,7.041L-2.378,-0.601L0.122,-0.601C0.282,-0.601 0.412,-0.55 0.513,-0.45C0.614,-0.349 0.664,-0.218 0.664,-0.059L0.664,2.211C0.664,2.414 0.573,2.583 0.391,2.719C0.211,2.827 0.033,2.934 -0.146,3.042L0.43,3.413C0.599,3.517 0.684,3.679 0.684,3.901L0.684,6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M170.084,11.74L168.414,11.74L168.414,14.762L170.084,14.762L170.084,11.74ZM170.763,18.239C170.763,18.398 170.713,18.529 170.614,18.63C170.515,18.73 170.385,18.781 170.226,18.781L168.316,18.781C168.157,18.781 168.025,18.73 167.921,18.63C167.817,18.529 167.765,18.398 167.765,18.239L167.765,16.55L168.443,16.55L168.443,18.18L170.084,18.18L170.084,15.353L168.272,15.353C168.113,15.353 167.983,15.302 167.884,15.202C167.785,15.101 167.735,14.97 167.735,14.811L167.735,11.681C167.735,11.522 167.785,11.391 167.884,11.291C167.983,11.19 168.113,11.139 168.272,11.139L170.226,11.139C170.385,11.139 170.515,11.19 170.614,11.291C170.713,11.391 170.763,11.522 170.763,11.681L170.763,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,174.982,28.7783)">
            <path d="M0,3.657L-0.581,3.657L-0.581,5.649L-1.221,5.649L-1.221,3.657L-3.379,3.657L-3.379,3.31L-1.528,-1.992L-0.889,-1.992L-0.889,-1.963L-2.646,3.081L-1.221,3.081L-1.221,0.307L-0.581,0.307L-0.581,3.081L0,3.081L0,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,178.209,31.6348)">
            <path d="M0,-2.056L0,-4.248L-1.699,-4.248L-1.699,-2.056L-0.85,-1.49L0,-2.056ZM0.02,2.192L0.02,-0.337L-0.85,-0.909L-1.719,-0.337L-1.719,2.192L0.02,2.192ZM0.703,2.251C0.703,2.41 0.651,2.541 0.547,2.641C0.443,2.742 0.311,2.793 0.151,2.793L-1.86,2.793C-2.02,2.793 -2.149,2.742 -2.249,2.641C-2.348,2.541 -2.397,2.41 -2.397,2.251L-2.397,-0.24C-2.397,-0.457 -2.314,-0.62 -2.148,-0.728L-1.406,-1.206L-2.129,-1.656C-2.295,-1.76 -2.378,-1.924 -2.378,-2.149L-2.378,-4.307C-2.378,-4.466 -2.328,-4.597 -2.227,-4.698C-2.125,-4.798 -1.996,-4.849 -1.836,-4.849L0.142,-4.849C0.301,-4.849 0.431,-4.798 0.532,-4.698C0.633,-4.597 0.684,-4.466 0.684,-4.307L0.684,-2.149C0.684,-1.924 0.599,-1.76 0.43,-1.656L-0.288,-1.206L0.439,-0.728C0.615,-0.614 0.703,-0.452 0.703,-0.24L0.703,2.251Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M182.491,15.163L180.821,15.163L180.821,18.181L182.491,18.181L182.491,15.163ZM183.17,18.239C183.17,18.398 183.12,18.529 183.021,18.63C182.922,18.73 182.793,18.781 182.633,18.781L180.685,18.781C180.525,18.781 180.395,18.73 180.294,18.63C180.193,18.529 180.143,18.398 180.143,18.239L180.143,11.681C180.143,11.522 180.193,11.391 180.294,11.291C180.395,11.19 180.525,11.139 180.685,11.139L182.613,11.139C182.773,11.139 182.902,11.19 183.001,11.291C183.101,11.391 183.15,11.522 183.15,11.681L183.15,13.371L182.472,13.371L182.472,11.74L180.821,11.74L180.821,14.572L182.633,14.572C182.793,14.572 182.922,14.622 183.021,14.72C183.12,14.82 183.17,14.95 183.17,15.109L183.17,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,377.349,15.6465)">
            <rect x="188.335" y="11.139" width="0.679" height="7.642" style="fill:rgb(0,140,79);"/>
        </g>
        <g transform="matrix(1,0,0,1,197.371,27.3275)">
            <path d="M0,6.558C0,6.717 -0.051,6.848 -0.154,6.949C-0.256,7.049 -0.387,7.1 -0.547,7.1L-2.627,7.1C-2.787,7.1 -2.917,7.049 -3.018,6.949C-3.119,6.848 -3.169,6.717 -3.169,6.558L-3.169,-0.542L-2.49,-0.542L-2.49,6.499L-0.679,6.499L-0.679,-0.542L0,-0.542L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,201.57,27.3275)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.149,6.949C-0.248,7.049 -0.377,7.1 -0.537,7.1L-2.461,7.1C-2.621,7.1 -2.751,7.049 -2.854,6.949C-2.957,6.848 -3.008,6.717 -3.008,6.558L-3.008,4.62L-2.329,4.62L-2.329,6.499L-0.679,6.499L-0.679,4.678L-2.788,2.129C-2.935,1.954 -3.008,1.76 -3.008,1.548L-3.008,0C-3.008,-0.159 -2.957,-0.29 -2.854,-0.39C-2.751,-0.491 -2.621,-0.542 -2.461,-0.542L-0.537,-0.542C-0.377,-0.542 -0.248,-0.491 -0.149,-0.39C-0.05,-0.29 0,-0.159 0,0L0,1.778L-0.679,1.778L-0.679,0.059L-2.329,0.059L-2.329,1.67L-0.21,4.219C-0.07,4.388 0,4.579 0,4.79L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,204.402,28.1975)">
            <path d="M0,6.23L-0.923,6.23C-1.288,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,411.06,22.4295)">
            <rect x="205.159" y="11.139" width="0.742" height="0.859" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(-1,0,0,1,415.67,17.4595)">
            <rect x="206.829" y="13.751" width="2.012" height="0.605" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(-1,0,0,1,420.597,15.6465)">
            <rect x="209.959" y="11.139" width="0.679" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,213.89,27.4057)">
            <path d="M0,6.431L0,2.842L-0.898,2.842C-1.256,2.842 -1.436,3.026 -1.436,3.394L-1.436,5.933C-1.436,6.297 -1.256,6.48 -0.898,6.48C-0.814,6.48 -0.514,6.463 0,6.431M0.645,7.022L0,7.022L0,6.89C-0.697,6.997 -1.083,7.051 -1.157,7.051C-1.434,7.051 -1.657,6.957 -1.826,6.77C-1.996,6.583 -2.08,6.35 -2.08,6.07L-2.08,3.291C-2.08,2.998 -1.984,2.755 -1.792,2.561C-1.6,2.368 -1.359,2.271 -1.069,2.271L0,2.271L0,-0.62L0.645,-0.62L0.645,7.022Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,218.831,26.7855)">
            <path d="M0,7.642L-0.469,7.642L-2.51,2.022L-2.51,7.642L-3.149,7.642L-3.149,0L-2.642,0L-0.64,5.523L-0.64,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,222.459,32.835)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.405,-3.093 -1.187,-3.129C-0.926,-3.168 -0.709,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,447.164,22.4295)">
            <rect x="223.211" y="11.139" width="0.742" height="0.859" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,19.1285)">
            <path d="M225.613,15.299L224.871,15.299L224.871,14.44L225.613,14.44L225.613,15.299ZM225.613,11.998L224.871,11.998L224.871,11.139L225.613,11.139L225.613,11.998Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,231.111,28.2363)">
            <path d="M0,4.741L0,-0.01C0,-0.261 -0.079,-0.463 -0.237,-0.617C-0.395,-0.773 -0.599,-0.85 -0.85,-0.85L-1.792,-0.85L-1.792,5.591L-0.889,5.591C-0.625,5.591 -0.411,5.515 -0.247,5.363C-0.082,5.213 0,5.005 0,4.741M0.679,4.809C0.679,5.213 0.546,5.545 0.281,5.803C0.016,6.062 -0.319,6.191 -0.723,6.191L-2.471,6.191L-2.471,-1.45L-0.723,-1.45C-0.316,-1.45 0.02,-1.321 0.283,-1.063C0.547,-0.803 0.679,-0.472 0.679,-0.069L0.679,4.809Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,235.491,26.7855)">
            <path d="M0,7.642L-2.51,7.642L-2.51,0L-0.029,0L-0.029,0.601L-1.831,0.601L-1.831,3.453L-0.19,3.453L-0.19,4.063L-1.831,4.063L-1.831,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,241.351,26.7855)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.252 -2.783,0.152C-2.682,0.051 -2.552,0 -2.393,0L-0.552,0C-0.392,0 -0.262,0.051 -0.161,0.152C-0.06,0.252 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.581 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M244.73,11.74L243.06,11.74L243.06,14.762L244.73,14.762L244.73,11.74ZM245.408,18.239C245.408,18.398 245.358,18.529 245.259,18.63C245.16,18.73 245.031,18.781 244.871,18.781L242.962,18.781C242.802,18.781 242.67,18.73 242.566,18.63C242.462,18.529 242.41,18.398 242.41,18.239L242.41,16.55L243.089,16.55L243.089,18.18L244.73,18.18L244.73,15.353L242.918,15.353C242.758,15.353 242.629,15.302 242.53,15.202C242.431,15.101 242.381,14.97 242.381,14.811L242.381,11.681C242.381,11.522 242.431,11.391 242.53,11.291C242.629,11.19 242.758,11.139 242.918,11.139L244.871,11.139C245.031,11.139 245.16,11.19 245.259,11.291C245.358,11.391 245.408,11.522 245.408,11.681L245.408,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M249.09,11.74L247.327,11.74L247.327,18.18L249.09,18.18L249.09,11.74ZM249.769,18.239C249.769,18.398 249.716,18.529 249.612,18.63C249.508,18.73 249.376,18.781 249.217,18.781L247.19,18.781C247.031,18.781 246.901,18.73 246.8,18.63C246.699,18.529 246.648,18.398 246.648,18.239L246.648,11.681C246.648,11.522 246.699,11.391 246.8,11.291C246.901,11.19 247.031,11.139 247.19,11.139L249.217,11.139C249.376,11.139 249.508,11.19 249.612,11.291C249.716,11.391 249.769,11.522 249.769,11.681L249.769,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,253.107,26.7855)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M257.927,11.74L256.257,11.74L256.257,14.762L257.927,14.762L257.927,11.74ZM258.605,18.239C258.605,18.398 258.556,18.529 258.457,18.63C258.357,18.73 258.228,18.781 258.068,18.781L256.159,18.781C256,18.781 255.868,18.73 255.764,18.63C255.66,18.529 255.607,18.398 255.607,18.239L255.607,16.55L256.286,16.55L256.286,18.18L257.927,18.18L257.927,15.353L256.115,15.353C255.956,15.353 255.826,15.302 255.727,15.202C255.628,15.101 255.578,14.97 255.578,14.811L255.578,11.681C255.578,11.522 255.628,11.391 255.727,11.291C255.826,11.19 255.956,11.139 256.115,11.139L258.068,11.139C258.228,11.139 258.357,11.19 258.457,11.291C258.556,11.391 258.605,11.522 258.605,11.681L258.605,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,260.974,26.7855)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,266.841,31.6348)">
            <path d="M0,-2.056L0,-4.248L-1.699,-4.248L-1.699,-2.056L-0.85,-1.49L0,-2.056ZM0.02,2.192L0.02,-0.337L-0.85,-0.909L-1.719,-0.337L-1.719,2.192L0.02,2.192ZM0.703,2.251C0.703,2.41 0.651,2.541 0.547,2.641C0.443,2.742 0.311,2.793 0.151,2.793L-1.86,2.793C-2.02,2.793 -2.149,2.742 -2.249,2.641C-2.348,2.541 -2.397,2.41 -2.397,2.251L-2.397,-0.24C-2.397,-0.457 -2.314,-0.62 -2.148,-0.728L-1.406,-1.206L-2.129,-1.656C-2.295,-1.76 -2.378,-1.924 -2.378,-2.149L-2.378,-4.307C-2.378,-4.466 -2.328,-4.597 -2.227,-4.698C-2.125,-4.798 -1.996,-4.849 -1.836,-4.849L0.142,-4.849C0.301,-4.849 0.431,-4.798 0.532,-4.698C0.633,-4.597 0.684,-4.466 0.684,-4.307L0.684,-2.149C0.684,-1.924 0.599,-1.76 0.43,-1.656L-0.288,-1.206L0.439,-0.728C0.615,-0.614 0.703,-0.452 0.703,-0.24L0.703,2.251Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,271.153,31.6348)">
            <path d="M0,-2.056L0,-4.248L-1.699,-4.248L-1.699,-2.056L-0.85,-1.49L0,-2.056ZM0.02,2.192L0.02,-0.337L-0.85,-0.909L-1.719,-0.337L-1.719,2.192L0.02,2.192ZM0.703,2.251C0.703,2.41 0.651,2.541 0.547,2.641C0.443,2.742 0.311,2.793 0.151,2.793L-1.86,2.793C-2.02,2.793 -2.149,2.742 -2.249,2.641C-2.348,2.541 -2.397,2.41 -2.397,2.251L-2.397,-0.24C-2.397,-0.457 -2.314,-0.62 -2.148,-0.728L-1.406,-1.206L-2.129,-1.656C-2.295,-1.76 -2.378,-1.924 -2.378,-2.149L-2.378,-4.307C-2.378,-4.466 -2.328,-4.597 -2.227,-4.698C-2.125,-4.798 -1.996,-4.849 -1.836,-4.849L0.142,-4.849C0.301,-4.849 0.431,-4.798 0.532,-4.698C0.633,-4.597 0.684,-4.466 0.684,-4.307L0.684,-2.149C0.684,-1.924 0.599,-1.76 0.43,-1.656L-0.288,-1.206L0.439,-0.728C0.615,-0.614 0.703,-0.452 0.703,-0.24L0.703,2.251Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,276.026,27.3275)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.151,6.949C-0.252,7.049 -0.382,7.1 -0.542,7.1L-2.422,7.1C-2.582,7.1 -2.711,7.049 -2.81,6.949C-2.909,6.848 -2.959,6.717 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.382,2.3 -0.252,2.351 -0.151,2.452C-0.05,2.552 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,563.023,15.6465)">
            <rect x="281.172" y="11.139" width="0.679" height="7.642" style="fill:rgb(0,140,79);"/>
        </g>
        <g transform="matrix(1,0,0,1,290.021,27.3275)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.149,6.949C-0.248,7.049 -0.377,7.1 -0.537,7.1L-2.461,7.1C-2.621,7.1 -2.751,7.049 -2.854,6.949C-2.957,6.848 -3.008,6.717 -3.008,6.558L-3.008,4.62L-2.329,4.62L-2.329,6.499L-0.679,6.499L-0.679,4.678L-2.788,2.129C-2.935,1.954 -3.008,1.76 -3.008,1.548L-3.008,0C-3.008,-0.159 -2.957,-0.29 -2.854,-0.39C-2.751,-0.491 -2.621,-0.542 -2.461,-0.542L-0.537,-0.542C-0.377,-0.542 -0.248,-0.491 -0.149,-0.39C-0.05,-0.29 0,-0.159 0,0L0,1.778L-0.679,1.778L-0.679,0.059L-2.329,0.059L-2.329,1.67L-0.21,4.219C-0.07,4.388 0,4.579 0,4.79L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,292.853,28.1975)">
            <path d="M0,6.23L-0.923,6.23C-1.288,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M295.71,11.681L294.314,11.681L294.314,13.112L295.71,13.112L295.71,11.681ZM296.34,15.338C296.34,15.706 296.158,15.89 295.793,15.89L294.23,15.89C293.866,15.89 293.684,15.706 293.684,15.338L293.684,11.691C293.684,11.323 293.866,11.139 294.23,11.139L295.793,11.139C296.158,11.139 296.34,11.323 296.34,11.691L296.34,13.4L296.12,13.629L294.314,13.629L294.314,15.348L295.71,15.348L295.71,14.479L296.34,14.479L296.34,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,300.133,29.7055)">
            <path d="M0,4.722L-0.64,4.722L-0.64,4.59C-0.9,4.626 -1.159,4.66 -1.416,4.693C-1.729,4.732 -1.987,4.751 -2.192,4.751C-2.544,4.751 -2.72,4.576 -2.72,4.224L-2.72,-0.029L-2.08,-0.029L-2.08,4.141L-0.64,4.102L-0.64,-0.029L0,-0.029L0,4.722Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M303.219,11.681L301.823,11.681L301.823,13.112L303.219,13.112L303.219,11.681ZM303.849,15.338C303.849,15.706 303.667,15.89 303.302,15.89L301.74,15.89C301.376,15.89 301.193,15.706 301.193,15.338L301.193,11.691C301.193,11.323 301.376,11.139 301.74,11.139L303.302,11.139C303.667,11.139 303.849,11.323 303.849,11.691L303.849,13.4L303.629,13.629L301.823,13.629L301.823,15.348L303.219,15.348L303.219,14.479L303.849,14.479L303.849,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,307.409,32.835)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.093 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,310.963,29.6475)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.129 -1.57,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,314.733,29.7055)">
            <path d="M0,4.722L-0.64,4.722L-0.64,4.59C-0.9,4.626 -1.159,4.66 -1.416,4.693C-1.729,4.732 -1.987,4.751 -2.192,4.751C-2.544,4.751 -2.72,4.576 -2.72,4.224L-2.72,-0.029L-2.08,-0.029L-2.08,4.141L-0.64,4.102L-0.64,-0.029L0,-0.029L0,4.722Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,320.568,29.6475)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.065,0.649L-2.065,4.78L-2.71,4.78L-2.71,0.61L-4.126,0.649L-4.126,4.78L-4.775,4.78L-4.775,0.029L-4.126,0.029L-4.126,0.161C-3.872,0.129 -3.618,0.094 -3.364,0.058C-3.055,0.019 -2.8,0 -2.598,0C-2.399,0 -2.253,0.06 -2.158,0.18C-1.885,0.144 -1.613,0.107 -1.343,0.068C-0.988,0.022 -0.72,0 -0.537,0C-0.179,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,326.398,29.6475)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.065,0.649L-2.065,4.78L-2.71,4.78L-2.71,0.61L-4.126,0.649L-4.126,4.78L-4.775,4.78L-4.775,0.029L-4.126,0.029L-4.126,0.161C-3.872,0.129 -3.618,0.094 -3.364,0.058C-3.055,0.019 -2.8,0 -2.598,0C-2.399,0 -2.253,0.06 -2.158,0.18C-1.885,0.144 -1.613,0.107 -1.343,0.068C-0.988,0.022 -0.72,0 -0.537,0C-0.179,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,18.5375)">
            <path d="M329.479,11.681L328.083,11.681L328.083,13.112L329.479,13.112L329.479,11.681ZM330.109,15.338C330.109,15.706 329.926,15.89 329.562,15.89L328,15.89C327.635,15.89 327.453,15.706 327.453,15.338L327.453,11.691C327.453,11.323 327.635,11.139 328,11.139L329.562,11.139C329.926,11.139 330.109,11.323 330.109,11.691L330.109,13.4L329.889,13.629L328.083,13.629L328.083,15.348L329.479,15.348L329.479,14.479L330.109,14.479L330.109,15.338Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,333.668,32.835)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.093 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,339.583,27.3275)">
            <path d="M0,6.558C0,6.717 -0.051,6.848 -0.151,6.949C-0.252,7.049 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.049 -2.9,6.949C-3.001,6.848 -3.052,6.717 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.29 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.29 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,343.689,27.3275)">
            <path d="M0,6.558C0,6.717 -0.051,6.848 -0.151,6.949C-0.252,7.049 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.049 -2.81,6.949C-2.909,6.848 -2.959,6.717 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.351 -0.151,2.452C-0.051,2.552 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,345.623,26.7855)">
            <path d="M0,7.642L-0.684,7.642L-0.684,2.344L-0.981,2.344L-0.981,2.32L-0.552,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,351.373,34.3838)">
            <path d="M0,-7.554L-3.232,0.044L-3.882,0.044L-3.882,0.005L-0.649,-7.598L0,-7.598L0,-7.554Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,356.132,27.3275)">
            <path d="M0,6.558C0,6.717 -0.051,6.848 -0.151,6.949C-0.252,7.049 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.049 -2.81,6.949C-2.909,6.848 -2.959,6.717 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.351 -0.151,2.452C-0.051,2.552 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,358.432,26.7855)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,363.984,27.3275)">
            <path d="M0,6.558C0,6.717 -0.051,6.848 -0.151,6.949C-0.252,7.049 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.049 -2.9,6.949C-3.001,6.848 -3.052,6.717 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.29 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.29 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M367.592,11.74L365.829,11.74L365.829,18.18L367.592,18.18L367.592,11.74ZM368.271,18.239C368.271,18.398 368.219,18.529 368.115,18.63C368.01,18.73 367.878,18.781 367.719,18.781L365.693,18.781C365.534,18.781 365.403,18.73 365.302,18.63C365.202,18.529 365.151,18.398 365.151,18.239L365.151,11.681C365.151,11.522 365.202,11.391 365.302,11.291C365.403,11.19 365.534,11.139 365.693,11.139L367.719,11.139C367.878,11.139 368.01,11.19 368.115,11.291C368.219,11.391 368.271,11.522 368.271,11.681L368.271,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,373.973,34.3838)">
            <path d="M0,-7.554L-3.232,0.044L-3.882,0.044L-3.882,0.005L-0.649,-7.598L0,-7.598L0,-7.554Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,378.635,26.7855)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.252 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.252 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.581 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,15.6465)">
            <path d="M382.136,11.74L380.373,11.74L380.373,18.18L382.136,18.18L382.136,11.74ZM382.815,18.239C382.815,18.398 382.763,18.529 382.659,18.63C382.554,18.73 382.422,18.781 382.263,18.781L380.237,18.781C380.078,18.781 379.947,18.73 379.846,18.63C379.746,18.529 379.695,18.398 379.695,18.239L379.695,11.681C379.695,11.522 379.746,11.391 379.846,11.291C379.947,11.19 380.078,11.139 380.237,11.139L382.263,11.139C382.422,11.139 382.554,11.19 382.659,11.291C382.763,11.391 382.815,11.522 382.815,11.681L382.815,18.239Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,386.414,31.6348)">
            <path d="M0,-2.056L0,-4.248L-1.699,-4.248L-1.699,-2.056L-0.85,-1.49L0,-2.056ZM0.02,2.192L0.02,-0.337L-0.85,-0.909L-1.719,-0.337L-1.719,2.192L0.02,2.192ZM0.703,2.251C0.703,2.41 0.651,2.541 0.547,2.641C0.442,2.742 0.311,2.793 0.151,2.793L-1.86,2.793C-2.02,2.793 -2.149,2.742 -2.248,2.641C-2.348,2.541 -2.397,2.41 -2.397,2.251L-2.397,-0.24C-2.397,-0.457 -2.314,-0.62 -2.148,-0.728L-1.406,-1.206L-2.129,-1.656C-2.295,-1.76 -2.378,-1.924 -2.378,-2.149L-2.378,-4.307C-2.378,-4.466 -2.327,-4.597 -2.227,-4.698C-2.126,-4.798 -1.995,-4.849 -1.836,-4.849L0.142,-4.849C0.301,-4.849 0.432,-4.798 0.532,-4.698C0.633,-4.597 0.684,-4.466 0.684,-4.307L0.684,-2.149C0.684,-1.924 0.599,-1.76 0.43,-1.656L-0.288,-1.206L0.439,-0.728C0.615,-0.614 0.703,-0.452 0.703,-0.24L0.703,2.251Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,391.287,27.3275)">
            <path d="M0,6.558C0,6.717 -0.051,6.848 -0.151,6.949C-0.252,7.049 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.049 -2.81,6.949C-2.909,6.848 -2.959,6.717 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.351 -0.151,2.452C-0.051,2.552 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,38.3648,14.9671)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.149,6.949C-0.248,7.049 -0.377,7.1 -0.537,7.1L-2.461,7.1C-2.621,7.1 -2.751,7.049 -2.854,6.949C-2.957,6.848 -3.008,6.717 -3.008,6.558L-3.008,4.62L-2.329,4.62L-2.329,6.499L-0.679,6.499L-0.679,4.678L-2.788,2.129C-2.935,1.954 -3.008,1.76 -3.008,1.548L-3.008,0C-3.008,-0.159 -2.957,-0.29 -2.854,-0.39C-2.751,-0.491 -2.621,-0.542 -2.461,-0.542L-0.537,-0.542C-0.377,-0.542 -0.248,-0.491 -0.149,-0.39C-0.05,-0.29 0,-0.159 0,0L0,1.778L-0.679,1.778L-0.679,0.059L-2.329,0.059L-2.329,1.67L-0.21,4.219C-0.07,4.388 0,4.579 0,4.79L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,41.5679,20.207)">
            <path d="M0,0.742L0,-2.31L-1.431,-2.271L-1.431,1.289L-0.532,1.289C-0.177,1.289 0,1.106 0,0.742M0.649,0.84C0.649,1.133 0.553,1.376 0.361,1.569C0.169,1.763 -0.072,1.86 -0.361,1.86L-1.431,1.86L-1.431,3.662L-2.08,3.662L-2.08,-2.891L-1.431,-2.891L-1.431,-2.759C-1.173,-2.791 -0.917,-2.824 -0.659,-2.857C-0.333,-2.899 -0.078,-2.92 0.107,-2.92C0.469,-2.92 0.649,-2.742 0.649,-2.388L0.649,0.84Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,45.313,17.9363)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.1,4.141 -1.356,4.16 -1.548,4.16C-1.903,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.437 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.448,-0.62 0.63,-0.437 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,49.4927,20.4746)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.405,-3.093 -1.187,-3.129C-0.926,-3.168 -0.709,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,52.9839,14.4251)">
            <path d="M0,7.642L-0.708,7.642L-2.017,5.22L-2.017,7.642L-2.666,7.642L-2.666,0L-2.017,0L-2.017,4.854L-0.796,2.891L-0.098,2.891L-0.098,2.911L-1.479,5.01L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,55.7232,17.9363)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.1,4.141 -1.356,4.16 -1.548,4.16C-1.903,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.437 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.448,-0.62 0.63,-0.437 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,59.9761,17.8684)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.501,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.369 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.194,-0.552 -0.01,-0.369 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.531 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,63.565,17.8684)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.501,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.369 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.194,-0.552 -0.01,-0.369 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.531 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-6.1825)">
            <path d="M66.622,24.041L65.226,24.041L65.226,25.472L66.622,25.472L66.622,24.041ZM67.251,27.698C67.251,28.066 67.069,28.25 66.705,28.25L65.142,28.25C64.777,28.25 64.595,28.066 64.595,27.698L64.595,24.05C64.595,23.682 64.777,23.499 65.142,23.499L66.705,23.499C67.069,23.499 67.251,23.682 67.251,24.05L67.251,25.759L67.032,25.989L65.225,25.989L65.225,27.708L66.622,27.708L66.622,26.838L67.251,26.838L67.251,27.698Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,73.4527,14.9671)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.151,6.949C-0.252,7.049 -0.382,7.1 -0.542,7.1L-2.549,7.1C-2.708,7.1 -2.838,7.049 -2.939,6.949C-3.041,6.848 -3.091,6.717 -3.091,6.558L-3.091,0C-3.091,-0.159 -3.041,-0.29 -2.939,-0.39C-2.838,-0.491 -2.708,-0.542 -2.549,-0.542L-0.542,-0.542C-0.382,-0.542 -0.252,-0.491 -0.151,-0.39C-0.05,-0.29 0,-0.159 0,0L0,1.778L-0.684,1.778L-0.684,0.059L-2.412,0.059L-2.412,6.499L-0.684,6.499L-0.684,3.858L-1.499,3.858L-1.499,3.272L0,3.272L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-7.3105)">
            <path d="M76.934,24.207L76.216,24.207L76.216,23.47L76.934,23.47L76.934,24.207ZM75.674,24.207L74.956,24.207L74.956,23.47L75.674,23.47L75.674,24.207ZM77.305,29.378L76.666,29.378L76.666,29.246C76.405,29.282 76.147,29.316 75.889,29.348C75.577,29.387 75.318,29.407 75.113,29.407C74.761,29.407 74.585,29.231 74.585,28.88L74.585,24.627L75.225,24.627L75.225,28.797L76.666,28.758L76.666,24.627L77.305,24.627L77.305,29.378Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,80.0738,15.8371)">
            <path d="M0,6.23L-0.923,6.23C-1.288,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-6.1825)">
            <path d="M82.93,24.041L81.534,24.041L81.534,25.472L82.93,25.472L82.93,24.041ZM83.56,27.698C83.56,28.066 83.378,28.25 83.013,28.25L81.451,28.25C81.086,28.25 80.904,28.066 80.904,27.698L80.904,24.05C80.904,23.682 81.086,23.499 81.451,23.499L83.013,23.499C83.378,23.499 83.56,23.682 83.56,24.05L83.56,25.759L83.34,25.989L81.534,25.989L81.534,27.708L82.93,27.708L82.93,26.838L83.56,26.838L83.56,27.698Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,87.1197,20.4746)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.405,-3.093 -1.187,-3.129C-0.926,-3.168 -0.709,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.01 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,90.5034,17.8684)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.501,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.369 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.194,-0.552 -0.01,-0.369 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.531 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,183.784,-9.0735)">
            <rect x="91.572" y="23.499" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-6.1825)">
            <path d="M95.396,24.07L93.965,24.07L93.965,27.678L95.396,27.678L95.396,24.07ZM96.045,27.698C96.045,28.066 95.861,28.25 95.494,28.25L93.863,28.25C93.498,28.25 93.316,28.066 93.316,27.698L93.316,24.05C93.316,23.682 93.498,23.499 93.863,23.499L95.494,23.499C95.861,23.499 96.045,23.682 96.045,24.05L96.045,27.698Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,99.8345,14.4251)">
            <path d="M0,7.642L-0.649,7.642L-0.649,3.472L-2.08,3.511L-2.08,7.642L-2.729,7.642L-2.729,0L-2.08,0L-2.08,3.023C-1.823,2.991 -1.566,2.957 -1.309,2.925C-0.983,2.883 -0.728,2.862 -0.542,2.862C-0.181,2.862 0,3.039 0,3.394L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,210.509,-9.0735)">
            <rect x="104.915" y="23.499" width="0.679" height="7.642" style="fill:rgb(0,140,79);"/>
        </g>
        <g transform="matrix(-1,0,0,1,222.329,-9.0735)">
            <rect x="110.825" y="23.499" width="0.679" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,115.156,15.0265)">
            <path d="M0,6.44L0,3.798L-0.688,3.349L-1.699,3.349L-1.699,6.44L0,6.44ZM-0.02,2.299L-0.02,0L-1.699,0L-1.699,2.758L-0.698,2.758L-0.02,2.299ZM0.684,6.499C0.684,6.658 0.633,6.789 0.532,6.889C0.431,6.99 0.301,7.041 0.142,7.041L-2.378,7.041L-2.378,-0.601L0.122,-0.601C0.282,-0.601 0.412,-0.55 0.513,-0.45C0.614,-0.349 0.664,-0.218 0.664,-0.059L0.664,2.211C0.664,2.414 0.573,2.583 0.391,2.719C0.211,2.827 0.033,2.934 -0.146,3.042L0.43,3.413C0.599,3.517 0.684,3.679 0.684,3.901L0.684,6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,119.077,16.7063)">
            <path d="M0,3.061L-0.669,-1.08L-1.343,3.061L0,3.061ZM1.03,5.361L0.356,5.361L0.088,3.662L-1.431,3.662L-1.699,5.361L-2.354,5.361L-2.354,5.341L-0.991,-2.3L-0.322,-2.3L1.03,5.361Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,124.175,14.4251)">
            <path d="M0,7.642L-0.469,7.642L-2.51,2.022L-2.51,7.642L-3.149,7.642L-3.149,0L-2.642,0L-0.64,5.523L-0.64,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-5.5915)">
            <path d="M125.986,27.659L125.244,27.659L125.244,26.8L125.986,26.8L125.986,27.659ZM125.986,24.358L125.244,24.358L125.244,23.499L125.986,23.499L125.986,24.358Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,131.484,15.8759)">
            <path d="M0,4.741L0,-0.01C0,-0.261 -0.079,-0.463 -0.237,-0.617C-0.395,-0.773 -0.599,-0.85 -0.85,-0.85L-1.792,-0.85L-1.792,5.591L-0.889,5.591C-0.625,5.591 -0.411,5.515 -0.247,5.363C-0.082,5.213 0,5.005 0,4.741M0.679,4.809C0.679,5.213 0.546,5.545 0.281,5.803C0.016,6.062 -0.319,6.191 -0.723,6.191L-2.471,6.191L-2.471,-1.45L-0.723,-1.45C-0.316,-1.45 0.02,-1.321 0.283,-1.063C0.547,-0.803 0.679,-0.472 0.679,-0.069L0.679,4.809Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,135.864,14.4251)">
            <path d="M0,7.642L-2.51,7.642L-2.51,0L-0.029,0L-0.029,0.601L-1.831,0.601L-1.831,3.453L-0.19,3.453L-0.19,4.063L-1.831,4.063L-1.831,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,138.052,14.4251)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,143.53,16.4179)">
            <path d="M0,3.657L-0.581,3.657L-0.581,5.649L-1.221,5.649L-1.221,3.657L-3.379,3.657L-3.379,3.31L-1.528,-1.992L-0.889,-1.992L-0.889,-1.963L-2.646,3.081L-1.221,3.081L-1.221,0.307L-0.581,0.307L-0.581,3.081L0,3.081L0,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,149.316,16.4179)">
            <path d="M0,3.657L-0.581,3.657L-0.581,5.649L-1.221,5.649L-1.221,3.657L-3.379,3.657L-3.379,3.31L-1.528,-1.992L-0.889,-1.992L-0.889,-1.963L-2.646,3.081L-1.221,3.081L-1.221,0.307L-0.581,0.307L-0.581,3.081L0,3.081L0,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,151.294,14.4251)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,156.172,19.2745)">
            <path d="M0,-2.056L0,-4.248L-1.699,-4.248L-1.699,-2.056L-0.85,-1.49L0,-2.056ZM0.02,2.192L0.02,-0.337L-0.85,-0.909L-1.719,-0.337L-1.719,2.192L0.02,2.192ZM0.703,2.251C0.703,2.41 0.651,2.541 0.547,2.641C0.443,2.742 0.311,2.793 0.151,2.793L-1.86,2.793C-2.02,2.793 -2.149,2.742 -2.249,2.641C-2.348,2.541 -2.397,2.41 -2.397,2.251L-2.397,-0.24C-2.397,-0.457 -2.314,-0.62 -2.148,-0.728L-1.406,-1.206L-2.129,-1.656C-2.295,-1.76 -2.378,-1.924 -2.378,-2.149L-2.378,-4.307C-2.378,-4.466 -2.328,-4.597 -2.227,-4.698C-2.125,-4.798 -1.996,-4.849 -1.836,-4.849L0.142,-4.849C0.301,-4.849 0.431,-4.798 0.532,-4.698C0.633,-4.597 0.684,-4.466 0.684,-4.307L0.684,-2.149C0.684,-1.924 0.599,-1.76 0.43,-1.656L-0.288,-1.206L0.439,-0.728C0.615,-0.614 0.703,-0.452 0.703,-0.24L0.703,2.251Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,161.045,14.9671)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.151,6.949C-0.252,7.049 -0.382,7.1 -0.542,7.1L-2.422,7.1C-2.582,7.1 -2.711,7.049 -2.81,6.949C-2.909,6.848 -2.959,6.717 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.382,2.3 -0.252,2.351 -0.151,2.452C-0.05,2.552 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M166.616,24.1L164.853,24.1L164.853,30.54L166.616,30.54L166.616,24.1ZM167.295,30.599C167.295,30.758 167.243,30.889 167.139,30.989C167.035,31.09 166.903,31.141 166.743,31.141L164.717,31.141C164.557,31.141 164.427,31.09 164.326,30.989C164.225,30.889 164.175,30.758 164.175,30.599L164.175,24.041C164.175,23.882 164.225,23.751 164.326,23.65C164.427,23.55 164.557,23.499 164.717,23.499L166.743,23.499C166.903,23.499 167.035,23.55 167.139,23.65C167.243,23.751 167.295,23.882 167.295,24.041L167.295,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M170.957,24.1L169.194,24.1L169.194,30.54L170.957,30.54L170.957,24.1ZM171.636,30.599C171.636,30.758 171.584,30.889 171.48,30.989C171.376,31.09 171.244,31.141 171.084,31.141L169.058,31.141C168.898,31.141 168.768,31.09 168.667,30.989C168.566,30.889 168.516,30.758 168.516,30.599L168.516,24.041C168.516,23.882 168.566,23.751 168.667,23.65C168.768,23.55 168.898,23.499 169.058,23.499L171.084,23.499C171.244,23.499 171.376,23.55 171.48,23.65C171.584,23.751 171.636,23.882 171.636,24.041L171.636,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M175.205,27.522L173.535,27.522L173.535,30.54L175.205,30.54L175.205,27.522ZM175.884,30.599C175.884,30.758 175.834,30.889 175.735,30.989C175.636,31.09 175.506,31.141 175.347,31.141L173.398,31.141C173.239,31.141 173.109,31.09 173.008,30.989C172.907,30.889 172.856,30.758 172.856,30.599L172.856,24.041C172.856,23.882 172.907,23.751 173.008,23.65C173.109,23.55 173.239,23.499 173.398,23.499L175.327,23.499C175.487,23.499 175.616,23.55 175.715,23.65C175.814,23.751 175.864,23.882 175.864,24.041L175.864,25.73L175.186,25.73L175.186,24.1L173.535,24.1L173.535,26.932L175.347,26.932C175.506,26.932 175.636,26.981 175.735,27.08C175.834,27.18 175.884,27.31 175.884,27.469L175.884,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,180.034,14.9671)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.151,6.949C-0.252,7.049 -0.382,7.1 -0.542,7.1L-2.422,7.1C-2.582,7.1 -2.711,7.049 -2.81,6.949C-2.909,6.848 -2.959,6.717 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.382,2.3 -0.252,2.351 -0.151,2.452C-0.05,2.552 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M185.605,24.1L183.842,24.1L183.842,30.54L185.605,30.54L185.605,24.1ZM186.284,30.599C186.284,30.758 186.232,30.889 186.128,30.989C186.024,31.09 185.892,31.141 185.732,31.141L183.706,31.141C183.546,31.141 183.417,31.09 183.315,30.989C183.214,30.889 183.164,30.758 183.164,30.599L183.164,24.041C183.164,23.882 183.214,23.751 183.315,23.65C183.417,23.55 183.546,23.499 183.706,23.499L185.732,23.499C185.892,23.499 186.024,23.55 186.128,23.65C186.232,23.751 186.284,23.882 186.284,24.041L186.284,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M189.946,24.1L188.183,24.1L188.183,30.54L189.946,30.54L189.946,24.1ZM190.625,30.599C190.625,30.758 190.573,30.889 190.469,30.989C190.365,31.09 190.233,31.141 190.073,31.141L188.047,31.141C187.887,31.141 187.757,31.09 187.656,30.989C187.555,30.889 187.505,30.758 187.505,30.599L187.505,24.041C187.505,23.882 187.555,23.751 187.656,23.65C187.757,23.55 187.887,23.499 188.047,23.499L190.073,23.499C190.233,23.499 190.365,23.55 190.469,23.65C190.573,23.751 190.625,23.882 190.625,24.041L190.625,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M194.287,24.1L192.524,24.1L192.524,30.54L194.287,30.54L194.287,24.1ZM194.966,30.599C194.966,30.758 194.914,30.889 194.81,30.989C194.706,31.09 194.574,31.141 194.414,31.141L192.388,31.141C192.228,31.141 192.098,31.09 191.997,30.989C191.896,30.889 191.846,30.758 191.846,30.599L191.846,24.041C191.846,23.882 191.896,23.751 191.997,23.65C192.098,23.55 192.228,23.499 192.388,23.499L194.414,23.499C194.574,23.499 194.706,23.55 194.81,23.65C194.914,23.751 194.966,23.882 194.966,24.041L194.966,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M198.628,24.1L196.865,24.1L196.865,30.54L198.628,30.54L198.628,24.1ZM199.307,30.599C199.307,30.758 199.254,30.889 199.15,30.989C199.046,31.09 198.915,31.141 198.755,31.141L196.729,31.141C196.569,31.141 196.439,31.09 196.338,30.989C196.237,30.889 196.187,30.758 196.187,30.599L196.187,24.041C196.187,23.882 196.237,23.751 196.338,23.65C196.439,23.55 196.569,23.499 196.729,23.499L198.755,23.499C198.915,23.499 199.046,23.55 199.15,23.65C199.254,23.751 199.307,23.882 199.307,24.041L199.307,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M204.927,24.1L203.164,24.1L203.164,30.54L204.927,30.54L204.927,24.1ZM205.605,30.599C205.605,30.758 205.553,30.889 205.449,30.989C205.345,31.09 205.213,31.141 205.054,31.141L203.027,31.141C202.868,31.141 202.738,31.09 202.637,30.989C202.536,30.889 202.485,30.758 202.485,30.599L202.485,24.041C202.485,23.882 202.536,23.751 202.637,23.65C202.738,23.55 202.868,23.499 203.027,23.499L205.054,23.499C205.213,23.499 205.345,23.55 205.449,23.65C205.553,23.751 205.605,23.882 205.605,24.041L205.605,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,207.588,14.4251)">
            <path d="M0,7.642L-0.684,7.642L-0.684,2.344L-0.981,2.344L-0.981,2.32L-0.552,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M211.177,24.1L209.507,24.1L209.507,27.122L211.177,27.122L211.177,24.1ZM211.855,30.599C211.855,30.758 211.806,30.889 211.707,30.989C211.607,31.09 211.478,31.141 211.318,31.141L209.409,31.141C209.25,31.141 209.118,31.09 209.014,30.989C208.91,30.889 208.857,30.758 208.857,30.599L208.857,28.909L209.536,28.909L209.536,30.54L211.177,30.54L211.177,27.713L209.365,27.713C209.206,27.713 209.076,27.662 208.977,27.562C208.878,27.461 208.828,27.33 208.828,27.171L208.828,24.041C208.828,23.882 208.878,23.751 208.977,23.65C209.076,23.55 209.206,23.499 209.365,23.499L211.318,23.499C211.478,23.499 211.607,23.55 211.707,23.65C211.806,23.751 211.855,23.882 211.855,24.041L211.855,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,215.938,14.4251)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.252 -2.783,0.152C-2.682,0.051 -2.552,0 -2.393,0L-0.552,0C-0.392,0 -0.262,0.051 -0.161,0.152C-0.06,0.252 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.581 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-9.0735)">
            <path d="M221.304,27.522L219.634,27.522L219.634,30.54L221.304,30.54L221.304,27.522ZM221.982,30.599C221.982,30.758 221.933,30.889 221.834,30.989C221.734,31.09 221.605,31.141 221.445,31.141L219.497,31.141C219.337,31.141 219.208,31.09 219.106,30.989C219.005,30.889 218.955,30.758 218.955,30.599L218.955,24.041C218.955,23.882 219.005,23.751 219.106,23.65C219.208,23.55 219.337,23.499 219.497,23.499L221.426,23.499C221.585,23.499 221.715,23.55 221.814,23.65C221.913,23.751 221.963,23.882 221.963,24.041L221.963,25.73L221.284,25.73L221.284,24.1L219.634,24.1L219.634,26.932L221.445,26.932C221.605,26.932 221.734,26.981 221.834,27.08C221.933,27.18 221.982,27.31 221.982,27.469L221.982,30.599Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,224.321,14.4251)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,462.229,-9.0735)">
            <rect x="230.775" y="23.499" width="0.679" height="7.642" style="fill:rgb(0,140,79);"/>
        </g>
        <g transform="matrix(1,0,0,1,239.02,15.0265)">
            <path d="M0,6.44L0,3.798L-0.688,3.349L-1.699,3.349L-1.699,6.44L0,6.44ZM-0.02,2.299L-0.02,0L-1.699,0L-1.699,2.758L-0.698,2.758L-0.02,2.299ZM0.684,6.499C0.684,6.658 0.633,6.789 0.532,6.889C0.431,6.99 0.301,7.041 0.142,7.041L-2.378,7.041L-2.378,-0.601L0.122,-0.601C0.282,-0.601 0.412,-0.55 0.513,-0.45C0.614,-0.349 0.664,-0.218 0.664,-0.059L0.664,2.211C0.664,2.414 0.573,2.583 0.391,2.719C0.211,2.827 0.033,2.934 -0.146,3.042L0.43,3.413C0.599,3.517 0.684,3.679 0.684,3.901L0.684,6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,482.537,-9.0735)">
            <rect x="240.929" y="23.499" width="0.679" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,245.944,14.9671)">
            <path d="M0,6.558C0,6.717 -0.052,6.848 -0.156,6.949C-0.26,7.049 -0.392,7.1 -0.552,7.1L-2.52,7.1C-2.679,7.1 -2.809,7.049 -2.91,6.949C-3.011,6.848 -3.062,6.717 -3.062,6.558L-3.062,0C-3.062,-0.159 -3.011,-0.29 -2.91,-0.39C-2.809,-0.491 -2.679,-0.542 -2.52,-0.542L-0.552,-0.542C-0.392,-0.542 -0.26,-0.491 -0.156,-0.39C-0.052,-0.29 0,-0.159 0,0L0,1.778L-0.684,1.778L-0.684,0.059L-2.383,0.059L-2.383,6.499L-0.684,6.499L-0.684,4.62L0,4.62L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-5.5915)">
            <path d="M247.721,27.659L246.979,27.659L246.979,26.8L247.721,26.8L247.721,27.659ZM247.721,24.358L246.979,24.358L246.979,23.499L247.721,23.499L247.721,24.358Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,255.319,22.0865)">
            <path d="M0,-7.661L-1.04,0L-1.592,0L-2.422,-5.762C-2.425,-5.862 -2.432,-6.013 -2.441,-6.211C-2.441,-6.106 -2.448,-5.957 -2.461,-5.762L-3.291,0L-3.843,0L-4.883,-7.661L-4.209,-7.661L-3.56,-2.148C-3.556,-2.024 -3.55,-1.843 -3.54,-1.601C-3.543,-1.725 -3.537,-1.907 -3.521,-2.148L-2.729,-7.661L-2.148,-7.661L-1.362,-2.148C-1.356,-2.024 -1.349,-1.843 -1.343,-1.601C-1.343,-1.725 -1.334,-1.907 -1.318,-2.148L-0.669,-7.661L0,-7.661Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,258.737,14.4251)">
            <path d="M0,7.642L-2.51,7.642L-2.51,0L-0.029,0L-0.029,0.601L-1.831,0.601L-1.831,3.453L-0.19,3.453L-0.19,4.063L-1.831,4.063L-1.831,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,262.238,14.4251)">
            <path d="M0,7.642L-2.441,7.642L-2.441,0L-1.763,0L-1.763,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,265.216,16.7063)">
            <path d="M0,3.061L-0.669,-1.08L-1.343,3.061L0,3.061ZM1.03,5.361L0.356,5.361L0.088,3.662L-1.431,3.662L-1.699,5.361L-2.354,5.361L-2.354,5.341L-0.991,-2.3L-0.322,-2.3L1.03,5.361Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,269.635,15.8759)">
            <path d="M0,4.741L0,-0.01C0,-0.261 -0.079,-0.463 -0.237,-0.617C-0.395,-0.773 -0.599,-0.85 -0.85,-0.85L-1.792,-0.85L-1.792,5.591L-0.889,5.591C-0.625,5.591 -0.411,5.515 -0.247,5.363C-0.082,5.213 0,5.005 0,4.741M0.679,4.809C0.679,5.213 0.546,5.545 0.281,5.803C0.016,6.062 -0.319,6.191 -0.723,6.191L-2.471,6.191L-2.471,-1.45L-0.723,-1.45C-0.316,-1.45 0.02,-1.321 0.283,-1.063C0.547,-0.803 0.679,-0.472 0.679,-0.069L0.679,4.809Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,274.015,14.4251)">
            <path d="M0,7.642L-2.51,7.642L-2.51,0L-0.029,0L-0.029,0.601L-1.831,0.601L-1.831,3.453L-0.19,3.453L-0.19,4.063L-1.831,4.063L-1.831,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,277.545,15.8759)">
            <path d="M0,4.741L0,-0.01C0,-0.261 -0.079,-0.463 -0.237,-0.617C-0.395,-0.773 -0.599,-0.85 -0.85,-0.85L-1.792,-0.85L-1.792,5.591L-0.889,5.591C-0.625,5.591 -0.411,5.515 -0.247,5.363C-0.082,5.213 0,5.005 0,4.741M0.679,4.809C0.679,5.213 0.546,5.545 0.281,5.803C0.016,6.062 -0.319,6.191 -0.723,6.191L-2.471,6.191L-2.471,-1.45L-0.723,-1.45C-0.316,-1.45 0.02,-1.321 0.283,-1.063C0.547,-0.803 0.679,-0.472 0.679,-0.069L0.679,4.809Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,280.177,14.4251)">
            <path d="M0,7.642L-0.684,7.642L-0.684,2.344L-0.981,2.344L-0.981,2.32L-0.552,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,284.538,14.9671)">
            <path d="M0,6.558C0,6.717 -0.05,6.848 -0.151,6.949C-0.252,7.049 -0.382,7.1 -0.542,7.1L-2.549,7.1C-2.708,7.1 -2.838,7.049 -2.939,6.949C-3.041,6.848 -3.091,6.717 -3.091,6.558L-3.091,0C-3.091,-0.159 -3.041,-0.29 -2.939,-0.39C-2.838,-0.491 -2.708,-0.542 -2.549,-0.542L-0.542,-0.542C-0.382,-0.542 -0.252,-0.491 -0.151,-0.39C-0.05,-0.29 0,-0.159 0,0L0,1.778L-0.684,1.778L-0.684,0.059L-2.412,0.059L-2.412,6.499L-0.684,6.499L-0.684,3.858L-1.499,3.858L-1.499,3.272L0,3.272L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,288.38,21.4661)">
            <path d="M0,-6.44L-1.182,-6.44L-1.182,0.601L-1.86,0.601L-1.86,-6.44L-3.052,-6.44L-3.052,-7.041L0,-7.041L0,-6.44Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,291.618,14.4251)">
            <path d="M0,7.642L-2.441,7.642L-2.441,0L-1.763,0L-1.763,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,396.203,34.4206)">
            <path d="M0,0L161.86,0" style="fill:none;fill-rule:nonzero;stroke:rgb(0,140,79);stroke-width:0.66px;"/>
        </g>
    </g>
</svg>
