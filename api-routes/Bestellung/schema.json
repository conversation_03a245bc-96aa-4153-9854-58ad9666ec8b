{"name": "Bestellung", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2021-10-04T10:00:00Z", "editedBy": null, "editedOn": "2022-06-03T04:37:16Z", "applicableEntities": [], "printOptions": ["9"], "positions": [{"id": -1, "title": "Titel", "type": "string", "required": "true", "isSummary": "0", "isRequiredSignature": "0", "sort": "1"}, {"id": -2, "title": "Lieferant", "type": "SUPPLIERSELECTOR", "required": "true", "isSummary": "0", "isRequiredSignature": "0", "sort": "2"}, {"id": -3, "title": "Lieferantenangebot", "type": "string", "sort": "5"}, {"id": -4, "title": "Lagerort", "type": "string", "sort": "3"}, {"id": -5, "title": "Lieferort soll", "type": "combobox", "required": "true", "isSummary": "0", "isRequiredSignature": "0", "sort": "4", "value": ["<PERSON><PERSON><PERSON>", "Lager", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -6, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "required": "true", "isSummary": "0", "isRequiredSignature": "0", "sort": "6"}, {"id": -8, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "sort": "9"}, {"id": -9, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "10"}, {"id": -10, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "11"}, {"id": -11, "title": "AB-Nummer", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "12"}, {"id": -12, "title": "Auftragswert", "type": "float", "isSummary": "0", "isRequiredSignature": "0", "sort": "13", "format": {"hasThousandsSeparator": false}}, {"id": -13, "title": "Be<PERSON><PERSON><PERSON><PERSON> (extern)", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "14"}, {"id": -14, "title": "Position 1", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "15"}, {"id": -15, "parentId": -14, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "16"}, {"id": -16, "parentId": -14, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "17"}, {"id": -18, "parentId": -14, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "19"}, {"id": -19, "parentId": -14, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "20"}, {"id": -20, "parentId": -14, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "21"}, {"id": -21, "parentId": -14, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "22", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -22, "parentId": -14, "title": "Lagerort", "type": "string", "sort": "22"}, {"id": -23, "parentId": -14, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "23"}, {"id": -24, "title": "Position 2", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "24"}, {"id": -25, "parentId": -24, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "25"}, {"id": -26, "parentId": -24, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "26"}, {"id": -28, "parentId": -24, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "28"}, {"id": -29, "parentId": -24, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "29"}, {"id": -30, "parentId": -24, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "30"}, {"id": -31, "parentId": -24, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "31", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -32, "parentId": -24, "title": "Lagerort", "type": "string", "sort": "31"}, {"id": -33, "parentId": -24, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "32"}, {"id": -34, "title": "Position 3", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "33"}, {"id": -35, "parentId": -34, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "34"}, {"id": -36, "parentId": -34, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "35"}, {"id": -38, "parentId": -34, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "37"}, {"id": -39, "parentId": -34, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "38"}, {"id": -40, "parentId": -34, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "39"}, {"id": -41, "parentId": -34, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "40", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -42, "parentId": -34, "title": "Lagerort", "type": "string", "sort": "40"}, {"id": -43, "parentId": -34, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "41"}, {"id": -44, "title": "Position 4", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "42"}, {"id": -45, "parentId": -44, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "43"}, {"id": -46, "parentId": -44, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "44"}, {"id": -48, "parentId": -44, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "46"}, {"id": -49, "parentId": -44, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "47"}, {"id": -50, "parentId": -44, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "48"}, {"id": -51, "parentId": -44, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "49", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -52, "parentId": -44, "title": "Lagerort", "type": "string", "sort": "49"}, {"id": -53, "parentId": -44, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "50"}, {"id": -54, "title": "Position 5", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "51"}, {"id": -55, "parentId": -54, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "52"}, {"id": -56, "parentId": -54, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "53"}, {"id": -58, "parentId": -54, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "55"}, {"id": -59, "parentId": -54, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "56"}, {"id": -60, "parentId": -54, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "57"}, {"id": -61, "parentId": -54, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "58", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -62, "parentId": -54, "title": "Lagerort", "type": "string", "sort": "58"}, {"id": -63, "parentId": -54, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "59"}, {"id": -64, "title": "Position 6", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "60"}, {"id": -65, "parentId": -64, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "61"}, {"id": -66, "parentId": -64, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "62"}, {"id": -68, "parentId": -64, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "64"}, {"id": -69, "parentId": -64, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "65"}, {"id": -70, "parentId": -64, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "66"}, {"id": -71, "parentId": -64, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "67", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -72, "parentId": -64, "title": "Lagerort", "type": "string", "sort": "67"}, {"id": -73, "parentId": -64, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "68"}, {"id": -74, "title": "Position 7", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "69"}, {"id": -75, "parentId": -74, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "70"}, {"id": -76, "parentId": -74, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "71"}, {"id": -78, "parentId": -74, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "73"}, {"id": -79, "parentId": -74, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "74"}, {"id": -80, "parentId": -74, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "75"}, {"id": -81, "parentId": -74, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "76", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -82, "parentId": -74, "title": "Lagerort", "type": "string", "sort": "76"}, {"id": -83, "parentId": -74, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "77"}, {"id": -84, "title": "Position 8", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "78"}, {"id": -85, "parentId": -84, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "79"}, {"id": -86, "parentId": -84, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "80"}, {"id": -88, "parentId": -84, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "82"}, {"id": -89, "parentId": -84, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "83"}, {"id": -90, "parentId": -84, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "84"}, {"id": -91, "parentId": -84, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "85", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -92, "parentId": -84, "title": "Lagerort", "type": "string", "sort": "85"}, {"id": -93, "parentId": -84, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "86"}, {"id": -94, "title": "Position 9", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "87"}, {"id": -95, "parentId": -94, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "88"}, {"id": -96, "parentId": -94, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "89"}, {"id": -98, "parentId": -94, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "91"}, {"id": -99, "parentId": -94, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "92"}, {"id": -100, "parentId": -94, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "93"}, {"id": -101, "parentId": -94, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "94", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -102, "parentId": -94, "title": "Lagerort", "type": "string", "sort": "94"}, {"id": -103, "parentId": -94, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "95"}, {"id": -104, "title": "Position 10", "type": "headline", "isSummary": "0", "isRequiredSignature": "0", "sort": "96"}, {"id": -105, "parentId": -104, "title": "Titel", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "97"}, {"id": -106, "parentId": -104, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "98"}, {"id": -108, "parentId": -104, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "100"}, {"id": -109, "parentId": -104, "title": "<PERSON><PERSON>in<PERSON><PERSON>in", "type": "date", "isSummary": "0", "isRequiredSignature": "0", "sort": "101"}, {"id": -110, "parentId": -104, "title": "Vereinbarte Uhrzeit", "type": "string", "isSummary": "0", "isRequiredSignature": "0", "sort": "102"}, {"id": -111, "parentId": -104, "title": "Lieferort soll", "type": "combobox", "isSummary": "0", "isRequiredSignature": "0", "sort": "103", "value": ["Lager", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Lager", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}]}, {"id": -112, "parentId": -104, "title": "Lagerort", "type": "string", "sort": "103"}, {"id": -113, "parentId": -104, "title": "<PERSON><PERSON>", "type": "photo", "isSummary": "0", "isRequiredSignature": "0", "sort": "104"}]}