<?php
/** @noinspection PhpUnhandledExceptionInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_TeamPlanningReport())->getData($_GET['fromDate'], $_GET['toDate'], $_GET['employeeList']);
}
?>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Team Planning Report</title>
</head>
<body>
<div class="page">
    <table>
        <thead>
        <!-- Month Label Row -->
        <tr>
            <th class="team-col"></th>
            <?php foreach ($data['months'] as $monthName => $cells) { ?>
                <th colspan="<?= count($cells) ?>" class="month-label">
                    <?= $monthName ?>
                </th>
            <?php } ?>
        </tr>
        <!-- Week Titles Row -->
        <tr>
            <th class="team-col"></th>
            <?php foreach ($data['weekGroups'] as $group) { ?>
                <th colspan="<?= $group['colspan'] ?>" class="week-title">
                    <?php if (!empty($group['week'])) { ?>
                        <span class="week-name">WEEK</span>
                        <span class="week-number"><?= $group['week'] ?></span>
                    <?php } ?>
                </th>
            <?php } ?>
        </tr>
        <!-- Day Header Row -->
        <tr>
            <th class="team-col"></th>
            <?php foreach ($data['headerCells'] as $cell) { ?>
                <th class="<?= $cell['class'] ?>">
                    <span class="day-name"><?= $cell['dayName'] ?></span>
                    <span class="day-number"><?= $cell['dayNumber'] ?></span>
                </th>
            <?php } ?>
        </tr>
        </thead>
        <tbody>
        <?php if (empty($data['wos'])) { ?>
            <?php foreach ($data['teamsInfo'] as $team) { ?>
                <tr>
                    <td class="team-col">
                        <div class="team-info">
                            <img src="<?= ($team['profilePicture']
                                ? PrintoutHelper::translateLocalPathToServerPath($team['profilePicture'])
                                : PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/empty-user-img.png")); ?>"
                                 alt="Team Avatar">
                            <span><?= $team['name']; ?></span>
                        </div>
                    </td>
                    <?php foreach ($data['headerCells'] as $cell) { ?>
                        <td class="<?= $cell['class'] ?>"></td>
                    <?php } ?>
                </tr>
            <?php }
        } else {
            foreach ($data['wos'] as $wosName => $wosArray) { ?>
                <tr>
                    <!-- Team Column -->
                    <td class="team-col">
                        <div class="team-info">
                            <?php
                            foreach ($data['teamsInfo'] as $team) {
                                if ($wosArray[0]['teamIdentification']['id'] == $team['id']) { ?>
                                    <img src="<?= $team['profilePicture'] ?? PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/img/empty-user-img.png"); ?>"
                                         alt="Team Avatar">
                                    <span><?= $team['name']; ?></span>
                                <?php }
                            } ?>
                        </div>
                    </td>
                    <?php foreach ($data['headerCells'] as $cell) {
                        $matchedTasks = [];
                        foreach ($wosArray as $task) {
                            $taskDate = date('Y-m-d', strtotime($task['plannedDate']));
                            if ($taskDate === $cell['date']) {
                                $matchedTasks[] = $task;
                            }
                        }
                        ?>
                        <td class="<?= $cell['class'] ?>">
                            <?php if (!empty($matchedTasks)) {
                                foreach ($matchedTasks as $matchedTask) {
                                    $baseColor = $matchedTask['projectColor'] ?? '#ff6f00';
                                    $borderColor = ($baseColor === '#ffffff') ? '#000000' : $baseColor;
                                    $iconColor = $baseColor;
                                    $textColor = ColorHelper::projectColor($baseColor, 1);
                                    $backgroundColor = ColorHelper::projectColor($baseColor, 97);
                                    if (ColorHelper::contrastRatio($textColor, $backgroundColor) < 3) {
                                        $textColor = ColorHelper::getReadableTextColor($backgroundColor);
                                        $iconColor = ColorHelper::getReadableTextColor($backgroundColor);
                                    }
                                    ?>
                                    <div class="task-box" style="
                                            border-bottom: 1px solid <?= $borderColor ?>;
                                            border-left: 2px solid <?= $borderColor ?>;
                                            color: <?= $textColor ?>;
                                            background-color: <?= $backgroundColor ?>;
                                            padding: 5px;
                                            margin-bottom: 5px;
                                            ">
                                        <?php if (!empty($matchedTask['customerName'])) { ?>
                                            <div class="task-row">
                                            <span class="icon icon-pm">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 0 1812 1792">
                                                   <path fill="<?= $iconColor ?>"
                                                         d="M1280 1399q0 109 -62.5 187t-150.5 78h-854q-88 0 -150.5 -78t-62.5 -187q0 -85 8.5 -160.5t31.5 -152t58.5 -131t94 -89t134.5 -34.5q131 128 313 128t313 -128q76 0 134.5 34.5t94 89t58.5 131t31.5 152t8.5 160.5zM1024 512q0 159 -112.5 271.5t-271.5 112.5t-271.5 -112.5t-112.5 -271.5t112.5 -271.5t271.5 -112.5t271.5 112.5t112.5 271.5z"/>
                                                </svg>
                                            </span>
                                                <span class="text-pm">
                                                <?= $matchedTask['customerName'] ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['projectName'])) { ?>
                                            <div class="task-row">
                                            <span class="icon icon-briefcase">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                                                    <path fill="<?= $iconColor ?>"
                                                          d="M488.6 250.2L392 214V105.5c0-15-9.3-28.4-23.4-33.7l-100-37.5c-8.1-3.1-17.1-3.1-25.3 0l-100 37.5c-14.1 5.3-23.4 18.7-23.4 33.7V214l-96.6 36.2C9.3 255.5 0 268.9 0 283.9V394c0 13.6 7.7 26.1 19.9 32.2l100 50c10.1 5.1 22.1 5.1 32.2 0l103.9-52 103.9 52c10.1 5.1 22.1 5.1 32.2 0l100-50c12.2-6.1 19.9-18.6 19.9-32.2V283.9c0-15-9.3-28.4-23.4-33.7zM358 214.8l-85 31.9v-68.2l85-37v73.3zM154 104.1l102-38.2 102 38.2v.6l-102 41.4-102-41.4v-.6zm84 291.1l-85 42.5v-79.1l85-38.8v75.4zm0-112l-102 41.4-102-41.4v-.6l102-38.2 102 38.2v.6zm240 112l-85 42.5v-79.1l85-38.8v75.4zm0-112l-102 41.4-102-41.4v-.6l102-38.2 102 38.2v.6z"/>
                                                </svg>
                                            </span>
                                                <span class="text-briefcase">
                                                <?= $matchedTask['projectName'] ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['projectNo']) || !empty($matchedTask['workingOrderNo'])) { ?>
                                            <div class="task-row">
                                            <span class="text-number">
                                                <?= ($matchedTask['projectNo'] ?? '') . '-' . ($matchedTask['workingOrderNo'] ?? '');
                                                if (!empty($matchedTask['scaffoldItemId'])) { ?>
                                                    <span class="icon-inline">
                                                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                                                            <path fill="<?= $iconColor ?>"
                                                                  d="M0 488V171.3c0-26.2 15.9-49.7 40.2-59.4L308.1 4.8c7.6-3.1 16.1-3.1 23.8 0L599.8 111.9c24.3 9.7 40.2 33.3 40.2 59.4V488c0 13.3-10.7 24-24 24H568c-13.3 0-24-10.7-24-24V224c0-17.7-14.3-32-32-32H128c-17.7 0-32 14.3-32 32V488c0 13.3-10.7 24-24 24H24c-13.3 0-24-10.7-24-24zm488 24l-336 0c-13.3 0-24-10.7-24-24V432H512l0 56c0 13.3-10.7 24-24 24zM128 400V336H512v64H128zm0-96V224H512l0 80H128z"/>
                                                        </svg>
                                                    </span>
                                                    <?= $matchedTask['scaffoldItemId'];
                                                } ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['projectSiteAddress1']) || !empty($matchedTask['projectSiteAddress2']) || !empty($matchedTask['projectSiteZipcode']) || !empty($matchedTask['projectSiteCity'])) { ?>
                                            <div class="task-row">
                                            <span class="icon icon-location">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 0 1812 1792">
                                                   <path fill="<?= $iconColor ?>"
                                                         d="M1408 992v480q0 26 -19 45t-45 19h-384v-384h-256v384h-384q-26 0 -45 -19t-19 -45v-480q0 -1 0.5 -3t0.5 -3l575 -474l575 474q1 2 1 6zM1631 923l-62 74q-8 9 -21 11h-3q-13 0 -21 -7l-692 -577l-692 577q-12 8 -24 7q-13 -2 -21 -11l-62 -74q-8 -10 -7 -23.5t11 -21.5l719 -599q32 -26 76 -26t76 26l244 204v-195q0 -14 9 -23t23 -9h192q14 0 23 9t9 23v408l219 182q10 8 11 21.5t-7 23.5z"/>
                                                </svg>
                                            </span>
                                                <span class="text-location">
                                                <?= implode(', ', array_filter([
                                                    $matchedTask['projectSiteAddress1'] ?? '',
                                                    $matchedTask['projectSiteAddress2'] ?? '',
                                                    $matchedTask['projectSiteZipcode'] ?? '',
                                                    $matchedTask['projectSiteCity'] ?? ''
                                                ])) ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['taskName'])) { ?>
                                            <div class="task-row">
                                            <span class="icon icon-task">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 0 1812 1792">
                                                   <path fill="<?= $iconColor ?>"
                                                         d="M1024 1408h640v-128h-640v128zM640 896h1024v-128h-1024v128zM1280 384h384v-128h-384v128zM1792 1216v256q0 26 -19 45t-45 19h-1664q-26 0 -45 -19t-19 -45v-256q0 -26 19 -45t45 -19h1664q26 0 45 19t19 45zM1792 704v256q0 26 -19 45t-45 19h-1664q-26 0 -45 -19t-19 -45v-256q0 -26 19 -45t45 -19h1664q26 0 45 19t19 45zM1792 192v256q0 26 -19 45t-45 19h-1664q-26 0 -45 -19t-19 -45v-256q0 -26 19 -45t45 -19h1664q26 0 45 19t19 45z"/>
                                                </svg>
                                            </span>
                                                <span class="text-task">
                                                <?= $matchedTask['taskName'] ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['shortDescription'])) { ?>
                                            <div class="task-row">
                                            <span class="icon icon-title">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="-10 0 1812 1792">
                                                   <path fill="<?= $iconColor ?>"
                                                         d="M1024 1408h640v-128h-640v128zM640 896h1024v-128h-1024v128zM1280 384h384v-128h-384v128zM1792 1216v256q0 26 -19 45t-45 19h-1664q-26 0 -45 -19t-19 -45v-256q0 -26 19 -45t45 -19h1664q26 0 45 19t19 45zM1792 704v256q0 26 -19 45t-45 19h-1664q-26 0 -45 -19t-19 -45v-256q0 -26 19 -45t45 -19h1664q26 0 45 19t19 45zM1792 192v256q0 26 -19 45t-45 19h-1664q-26 0 -45 -19t-19 -45v-256q0 -26 19 -45t45 -19h1664q26 0 45 19t19 45z"/>
                                                </svg>
                                            </span>
                                                <span class="text-title">
                                                <?= $matchedTask['shortDescription'] ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['resourcesNonHr'])) { ?>
                                            <div class="task-row">
                                            <span class="icon icon-vehicle">
                                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 480 512">
                                                    <path fill="<?= $iconColor ?>"
                                                          d="M438.66 212.33l-11.24-28.1-19.93-49.83C390.38 91.63 349.57 64 303.5 64h-127c-46.06 0-86.88 27.63-103.99 70.4l-19.93 49.83-11.24 28.1C17.22 221.5 0 244.66 0 272v48c0 16.12 6.16 30.67 16 41.93V416c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32v-32h256v32c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32v-54.07c9.84-11.25 16-25.8 16-41.93v-48c0-27.34-17.22-50.5-41.34-59.67zm-306.73-54.16c7.29-18.22 24.94-30.17 44.57-30.17h127c19.63 0 37.28 11.95 44.57 30.17L368 208H112l19.93-49.83zM80 319.8c-19.2 0-32-12.76-32-31.9S60.8 256 80 256s48 28.71 48 47.85-28.8 15.95-48 15.95zm320 0c-19.2 0-48 3.19-48-15.95S380.8 256 400 256s32 12.76 32 31.9-12.8 31.9-32 31.9z"/>
                                                </svg>
                                            </span>
                                                <span class="text-vehicle">
                                                <?php
                                                $names = array_map(function ($vehicle) {
                                                    return $vehicle['kurzname'];
                                                }, $matchedTask['resolvedVehicles']);
                                                echo implode(", ", $names);
                                                ?>
                                            </span>
                                            </div>
                                        <?php }
                                        if (!empty($matchedTask['resolvedLabels'])) { ?>
                                            <div class="labels">
                                                <?php foreach ($matchedTask['resolvedLabels'] as $label) { ?>
                                                    <span class="label">
                                                    <span class="label-circle"
                                                          style="background-color: <?= $label['colorCode'] ?>;"></span>
                                                    <?= $label['displayText']; ?>
                                                </span>
                                                <?php } ?>
                                            </div>
                                        <?php } ?>
                                    </div>
                                <?php }
                            } ?>
                        </td>
                    <?php } ?>
                </tr>
            <?php }
        } ?>
        </tbody>
    </table>
</div>
</body>
</html>
