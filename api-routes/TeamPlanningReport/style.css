@page {
    size: 1189mm 841mm;
}

body {
    margin: 20px;
    font-family: Arial, sans-serif;
    font-size: xx-small;
}

.page {
    page-break-before: always;
    max-height: 841mm;
    max-width: 1189mm;
    padding: 5mm;
    box-sizing: border-box;
}

table {
    width: 100%;
    table-layout: fixed;
}

th, td {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 5px;
    border: 1px solid #f4f4f4;
    padding: 8px;
    vertical-align: top;
    text-align: left;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.week-title {
    text-align: center;
    border-top: none;
}

.week-name {
    font-weight: normal;
    font-size: 14px;
}

.week-number {
    font-weight: bold;
    font-size: 16px;
    margin-left: 3px;
}

.day-name {
    font-weight: normal;
    font-size: 10px;
    color: #494848;
}

.day-number {
    font-weight: bold;
    font-size: 12px;
    margin-left: 2px;
}

.month-label {
    font-size: 32px;
    font-weight: bold;
    color: #cacccf;
    text-align: center;
    border-top: none;
}

thead tr:nth-child(2) th {
    text-align: center;
}

.team-col {
    width: 50mm;
    text-align: center;
    vertical-align: middle;
}

th.team-col {
    border: none;
}

.weekday-col {
    width: 50mm;
    text-align: center;
}

.weekday-col th {
    display: block;
}

.weekend-col {
    width: 16mm;
    background-color: #fafafa;
    text-align: center;
}

.task-box {
    font-size: 8px;
    border-radius: 4px;
    padding: 4px;
    margin-bottom: 4px;
    white-space: normal;
    overflow-wrap: break-word;
    break-inside: avoid-page;
    page-break-inside: avoid;
    display: block;
}

.task-row {
    display: flex;
    align-items: center;
    margin: 4px 0;
}

.icon {
    display: block;
    width: 10px;
    height: 12px;
    margin-right: 6px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

.icon-inline {
    width: 10px;
    height: 12px;
    margin-left: 6px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    display: inline-flex;
    align-items: center;
}

.task-box p,
.task-box-blue p {
    margin: 4px 0;
}

.labels {
    margin-top: 8px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.label {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 8px;
}

.label-circle {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

.team-info {
    font-size: xx-small;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.team-info img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-bottom: 8px;
    object-fit: cover;
}

.text-vehicle,
.text-title,
.text-task,
.text-location,
.text-number,
.text-briefcase,
.text-pm {
    text-align: left;
    flex: 1;
}
