[{"jsonKey": "Version", "type": "string", "title": "0.2. Version <D.2>", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Version\n\n2007/46/EC: 0.2.\n371/2010: 0.2.\n183/2011 IAC: 0.2.\n2002/24/EC: 0.2.\n901/2014: 0.2.2.\n2003/37/EC: 0.2.\n2015/504: 1.2.2. This field is mandatory if the TypeApprovalTypeCode contains the value: EC, KS, NKS.\n\nIf a variant is not applicable use the abbreviation: N/A", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "StageEcVersion", "type": "string", "title": "0.2.2. EC Version", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Ec version for the stage of completion. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "StageEcVariant", "type": "string", "title": "0.2.2. EC Variante", "maxLength": "25", "unit": "", "decimalPlaces": "", "tooltip": "<PERSON><PERSON> V<PERSON>t for the stage of completion. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "StageEcType", "type": "string", "title": "0.2.2. EC Typ", "maxLength": "50", "unit": "", "decimalPlaces": "", "tooltip": "Ec Type for the stage of completion ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "StageEcTypeApprovalNumber", "type": "string", "title": "0.2.2. EC Typgenehmigungsnummer einschließlich Erweiterungsnummer", "maxLength": "40", "unit": "", "decimalPlaces": "", "tooltip": "EC Type-approval number for the stage of completion.\nType approval number of the stage for every entry in the COC for multistage vehicles.\n\n2007/46/EC: NN\n2003/37/EC: NN\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3", "type": "string", "title": "0.2.3. <PERSON><PERSON><PERSON><PERSON> (gegebenenfalls)", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3", "type": "string", "title": "0.2.3. Wert zur Kennungen (gegebenenfalls)", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_1", "type": "string", "title": "*******. <PERSON><PERSON>ng der Interpolationsfamilie", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_1", "type": "string", "title": "*******. Wert zur Kennung der Interpolationsfamilie", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_2", "type": "string", "title": "*******. <PERSON><PERSON><PERSON> der ATCT-Familie", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_2", "type": "string", "title": "*******. Wert zur Kennung der ATCT-Familie", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_3", "type": "string", "title": "*******. <PERSON><PERSON><PERSON> der PEMS-Familie", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_3", "type": "string", "title": "*******. Wert zur Kennung der PEMS-Familie", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_4", "type": "string", "title": "*******. <PERSON><PERSON><PERSON> der Fahrwiderstandsfamilie", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_4", "type": "string", "title": "*******. Wert zur Kennung der Fahrwiderstandsfamilie", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_5", "type": "string", "title": "*******. Kennung der Fahrwiderstandsmatrik-Familie (ggf.)", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_5", "type": "string", "title": "*******. Wert zur Kennung der Fahrwiderstandsmatrik-Familie (ggf.)", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_6", "type": "string", "title": "*******. Kennung der Familie mit periodischer Regenerierung", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_6", "type": "string", "title": "*******. Wert zur Kennung der Familie mit periodischer Regenerierung", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierCode_0_2_3_7", "type": "string", "title": "*******. Kennung der Verdunstungsprüffamilie", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Unique identifier for a grouping of vehicles as defined in the guideline.\n \n2018/1832: *******. – *******.\n\nPossible values:\nIP\t= Interpolation family as defined in paragraph 5.6\nRL\t= Road load family as defined in paragraph 5.7\nRM\t= Road load matrix family as defined in paragraph 5.8\nPR\t= Periodically regenerating systems family as defined in  paragraph 5.9\nATCT\t= ATCT family as defined in paragraph 2. of Sub-Annex 6a.\nEV\t= Evaporative emissions family identifier\nPEMS\t= Portable emissions management system ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FamilyIdentifierValue_0_2_3_7", "type": "string", "title": "*******. Wert zur Kennung der Verdunstungsprüffamilie", "maxLength": "100", "unit": "", "decimalPlaces": "", "tooltip": "Value  for a unique identifier for grouping of vehicles as defined in the guideline.\n\n2018/1832: *******. – *******. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "", "type": "headline", "title": "Herstellerinformationen", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON>n zum Her<PERSON>ller", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "AddressTypeCode_0_5", "type": "string", "title": "0.5. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Designation of the responsible party whose address is shown.\n\nValues: \nAPP  Applicant\nBVM  BaseVehicleManufacturer\nCUS  Customer\nIAA    IndividualApprovalAuthority\nMRP  ManufacturersRepresentative\nPSM  PresentStageManufacturer\nSSM  SecondStageManufacturer How to use in case of multistage.\nIn case of a single stage (complete) vehicle add:\nIn case of a incomplete vehicle and other stages are not mentioned on CoC.\nStageManufacturerNumber = 1 and AddressTypeCode = PSM.\nIn case of a completed vehicle and other stages are mentioned on the CoC.\nAdd StageManufacturerNumber = 1 and AddressTypeCode = BVM for the first stage.\nAdd StageManufacturerNumber = 2 and AddressTypeCode = PSM for the last stage. (Always enter the PSM as the highest StageManufacturerNumber. In case of three stages this will be 3).\nIn case of three stages add StageManufacturerNumber = 2 and AddressTypeCode = SSM\nIn case of a completed vehicle and other stages are not mentioned on the CoC.\nAdd StageManufacturerNumber = 2 and AddressTypeCode = PSM for the first stage. Add the StageManufacturerNumber for the current stage (in case of second stage this will be 2, in case of third stage this will be 3 etc.).\nIn general: if the manufacturer’s representative is known also add AddressTypeCode = MRP for the relevant StageManufacturerNumber.", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "Name_0_5", "type": "string", "title": "0.5. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "80", "unit": "", "decimalPlaces": "", "tooltip": "Name of the specified authority (AddressTypeCode).\n\n2007/46/EC: 0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC: 0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC: 0.5.\n2015/504: 1.4. and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "AddressLine1_0_5", "type": "string", "title": "0.5. <PERSON><PERSON><PERSON>", "maxLength": "150", "unit": "", "decimalPlaces": "", "tooltip": "Address of the specified responsible party (AddressTypeCode) \n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "AddressLine2_0_5", "type": "string", "title": "0.5. <PERSON><PERSON><PERSON>", "maxLength": "150", "unit": "", "decimalPlaces": "", "tooltip": "PO box of the specified responsible party (AddressTypeCode) \n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "AddressLine3_0_5", "type": "string", "title": "0.5. PLZ", "maxLength": "150", "unit": "", "decimalPlaces": "", "tooltip": "Postalcode of the specified responsible party (AddressTypeCode) \n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "PlaceOfResidence_0_5", "type": "string", "title": "0.5. <PERSON><PERSON>", "maxLength": "80", "unit": "", "decimalPlaces": "", "tooltip": "Place of residence of the specified authority (AddressTypeCode.\n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "CountryOfResidence_0_5", "type": "string", "title": "0.5. Land", "maxLength": "80", "unit": "", "decimalPlaces": "", "tooltip": "Country of the specified authority (AddressTypeCode).\n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4. AND 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "PhoneNumber_0_5", "type": "string", "title": "0.5. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "20", "unit": "", "decimalPlaces": "", "tooltip": "Phone number of the specified authority (AddressTypeCode).\n\n183/2011 IAC: NN ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "EMailAddress_0_5", "type": "string", "title": "0.5. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "130", "unit": "", "decimalPlaces": "", "tooltip": "Email adress for the specified authority (AddressTypeCode).\n\n183/2011 IAC: NN ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Herstellerinformationen des Basisfahrzeugs/des Fahrzeugs der vorangegangenen Stufe(n)", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zum Hersteller des Basisfahrzeugs/des Fahrzeugs der vorangegangenen Stufe(n)", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LocOfTheStatutoryPlates23Wheel", "type": "string", "title": "0.5.1. Lage des Fabrikschilds", "maxLength": "50", "unit": "", "decimalPlaces": "", "tooltip": "Location of the statutory plates for two and three wheel vehicles (code EC 2002/24 and 901/2014).\n\n2002/24/EC: 0.6.\n901/2014: 0.5.1. ", "tabs": ["L"]}, {"jsonKey": "AddressTypeCode_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Designation of the responsible party whose address is shown.\n\nValues: \nAPP  Applicant\nBVM  BaseVehicleManufacturer\nCUS  Customer\nIAA    IndividualApprovalAuthority\nMRP  ManufacturersRepresentative\nPSM  PresentStageManufacturer\nSSM  SecondStageManufacturer How to use in case of multistage.\nIn case of a single stage (complete) vehicle add:\nIn case of a incomplete vehicle and other stages are not mentioned on CoC.\nStageManufacturerNumber = 1 and AddressTypeCode = PSM.\nIn case of a completed vehicle and other stages are mentioned on the CoC.\nAdd StageManufacturerNumber = 1 and AddressTypeCode = BVM for the first stage.\nAdd StageManufacturerNumber = 2 and AddressTypeCode = PSM for the last stage. (Always enter the PSM as the highest StageManufacturerNumber. In case of three stages this will be 3).\nIn case of three stages add StageManufacturerNumber = 2 and AddressTypeCode = SSM\nIn case of a completed vehicle and other stages are not mentioned on the CoC.\nAdd StageManufacturerNumber = 2 and AddressTypeCode = PSM for the first stage. Add the StageManufacturerNumber for the current stage (in case of second stage this will be 2, in case of third stage this will be 3 etc.).\nIn general: if the manufacturer’s representative is known also add AddressTypeCode = MRP for the relevant StageManufacturerNumber.", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "Name_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "80", "unit": "", "decimalPlaces": "", "tooltip": "Name of the specified authority (AddressTypeCode).\n\n2007/46/EC: 0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC: 0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC: 0.5.\n2015/504: 1.4. and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "AddressLine1_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON><PERSON>", "maxLength": "150", "unit": "", "decimalPlaces": "", "tooltip": "Address of the specified responsible party (AddressTypeCode) \n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "AddressLine2_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON><PERSON>", "maxLength": "150", "unit": "", "decimalPlaces": "", "tooltip": "PO box of the specified responsible party (AddressTypeCode) \n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "AddressLine3_0_5_1", "type": "string", "title": "0.5.1. PLZ", "maxLength": "150", "unit": "", "decimalPlaces": "", "tooltip": "Postalcode of the specified responsible party (AddressTypeCode) \n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "PlaceOfResidence_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON>", "maxLength": "80", "unit": "", "decimalPlaces": "", "tooltip": "Place of residence of the specified authority (AddressTypeCode.\n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4 and 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "CountryOfResidence_0_5_1", "type": "string", "title": "0.5.1. Land", "maxLength": "80", "unit": "", "decimalPlaces": "", "tooltip": "Country of the specified authority (AddressTypeCode).\n\n2007/46/EC:  0.5.\n371/2010: 0.5.\n183/2011 IAC: 0.5. \n2002/24/EC:  0.5.\n901/2014: 0.4. and 0.4.2.\n2003/37/EC:  0.5.\n2015/504: 1.4. AND 1.4.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "PhoneNumber_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "20", "unit": "", "decimalPlaces": "", "tooltip": "Phone number of the specified authority (AddressTypeCode).\n\n183/2011 IAC: NN ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "EMailAddress_0_5_1", "type": "string", "title": "0.5.1. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "130", "unit": "", "decimalPlaces": "", "tooltip": "Email adress for the specified authority (AddressTypeCode).\n\n183/2011 IAC: NN ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LocOfTheStatutoryPlatesCode", "type": "string", "title": "0.6. Anbringungsstelle der vorgeschriebenen Schilder", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Location of the statutory plates\n\n2007/46/EC: 0.6.\n371/2010: 0.6.\n183/2011 IAC: 0.6.\n2003/37/EC: 0.6.\n2015/504: 1.5.1.\n\n\nValues\n\nA0   In the engine compartment, right\nA1   In the engine compartment, left\nA2   In the engine compartment, front\nA3   In the engine compartment, rear\nA4   In the engine compartment, front or on the right B-pillar\nA5   In the engine compartment, rear or on the left B-pillar\nA6   In the engine compartment, rear or on the right B-pillar\nA7   In the engine compartment, rear or rear wheel arch left\nA8   In the luggage compartment on the right sidewall or closure pillar\nA9   On the right A-pillar\nB0   On the left A-pillar\nB1   On the right B-pillar\nB2   On the left B-pillar\nB3   On the right C-pillar\nB4   On the left C-pillar\nB5   On the side surface of the dashboard\nB6   In the engine compartment\nB7   In the luggage compartment\nB8   In the luggage compartment, front\nB9   On the left closure pillar\nC0   On the left or right B-pillar\nC1   On the B-pillar, driverside\nD4   Outside of the driver seatbox\nD5   Right hand B/C post\nD7   Left hand B/C post\nD8   On frame front right\nE1   On right side member\nE2   On left side member\nE3   Right door entrance\nE4   Left door entrance\nE5   On cabine below door entrance right\nE6   On cabine below door entrance left \nE7   At frontwall right \nE8   At frontwall left\nE9   On dashboard right\nF0   On dashboard left\nF1   At water compartment upper right\nF2   At water compartment upper left\nF3   Inside of the driver seatbox\nF4   Right cross member\nF5   Left cross member\nF6   At the right side of centre console\nF7   At the right side of the seat console passenger side\nF8   At the front of the seat console passenger side\nF9   On top of the seat console passenger side\nG1   On front of left B-pillar\nG2   Behind the driver seat\nG3   At the front support\nG4   Rightside of the driver seatbox\nG5   On the rear wall of the inside of the  driver seatbox\nG6   Right cross member front\nG7   Right cross member, in front of coupling point\nG8   Right cross member, at coupling point\nG9   Right cross member, behind coupling point\nH0   Right cross member, in front of axle group\nH1   Right cross member, at axle group\nH2   Right cross member, behind axle group\nH3   Right side member, front\nH4   Right side member, rear \nH5   Right side member, in front of coupling point\nH6   Right side member, at coupling point\nH7   Right side member, behind coupling point\nH8   Right side member, in front of axle group\nH9   Right side member, at axle group \nI0   Right side member, behind axle group \nI1   Right side member, in front of front axle\nI2   Right side member, at front axle\nI3   Right side member, behind front axle\nI4   Right side member, in front of rear axle\nI5   Right side member, at rear axle\nI6   Right side member, behind rear axle\nI7   Front at water gutter\nI8   Right at entrance\nI9   On the left D-pillar\nJ0   On the right D-pillar\nJ1   Right side front sideboard ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "MethodOfAttachmStatPlatesCode", "type": "string", "title": "0.6. Art der Befestigung der vorgeschriebenen Schilder", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Method of attachment of the statutory plates\n\n371/2010: 0.6.\n183/2011 IAC: 0.6.\n901/2014: 0.5.2.\n2015/504:  1.5.2.\n\nA1   glued or bonded\nA2   screwed\nA3   riveted\nA4   bolted ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LocationOfTheVinCode", "type": "string", "title": "0.6. Anbringungsstelle der Fahrzeug-Identifizierungsnummer", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Location of the vehicle identification number\n\n2007/46/EC: 0.6.\n371/2010: 0.6.\n183/2011 IAC: 0.6.\n2003/37/EC: 0.3.2.\n2015/504: 1.6.1.\n\nCode  Description\nA0      In the engine compartment, right\nA1      In the engine compartment, left\nA2      In the engine compartment, front\nA3      In the engine compartment, rear\nA4      In the rear luggage compartment on the bottom plate\nA5      On the right closure pillar\nA6      On the right A-pillar\nA7      On the right side member\n           Can also be used for: On the chassis frame\nA8      At the front entrance, right\n           Can also be used for: Front step panel\nA9      On the frame side member, front right\nB0      Between the right front seat and door sills\nB1      On the chassis behind the right front wheel\nB2      Under the right front seat at the right closure pillar\nB3      In the engine compartment\nB4      Inside interior, front\nB5      Inside interior, rear\nB6      In the luggage compartment\nB7      On the left A-pillar\nB8      In the vehicle floor, front right\nB9      In the front luggage compartment\nC0      In the front right wheel arch\nC1      Under the right rear seat\nC2      Under the right front seat\nC3      On the left B-pillar\nC4      On the right B-pillar\nC5      In vehicle floor front right or rear\nC6      In the engine compartment, rear or between the right front seat and door sills\nC7      At right side of the front wall\nC8      On the right hand of front cross member\nC9      RIGHT SIDE CROSSMEMBER              \nD0      RIGHT SIDE WINDSCREEN BASE/COWL LOUVRE\nD1      In the luggage or engine compartment\nD2      On frame front right\nD3      Right side windscreen base/cowl louvre\nD4      R. cross memb, in front of coupl. point\nD5      Right cross member, at coupling point\nD6      R. cross member, behind coupling point\nD7      R. cross member, in front of axle group\nD8      Right cross member, at axle group \nD9      Right cross member, behind axle group\nE1      Right side member, front\nE2      Right side member, rear\nE3      R. side member, in front of coupl.point\nE4      Right side member, at coupling point\nE5      R. side member, behind coupling point\nE6      R. side member, in front of axle group\nE7      Right side member, at axle group \nE8      Right side member, behind axle group\nE9      R. side member, in front of front axle\nF1      Right side member, at front axle\nF2      Right side member, behind front axle\nF3      R. side member, in front of rear axle\nF4      Right side member, at rear axle\nF5      Right side member, behind rear axle\nF6      front at water gutter\nF7      On the left hand of front cross member\nF8      On the frame side member, front left\nF9      On left side member\nG1      Left side cross member\nG2      Drawbar\nG3      Forked drawbar\nG4      Right cross member, in front of front axle\nG5      Right side member, behind second axle\nG6      At the front support\nG7      At inside roofpillar rightside\nG8      At the back of the ladder frame on the right\nG9      On the right C-pillar\nH0      On the right D-pillar ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "LocationOfTheVinCode23Wheel", "type": "string", "title": "0.6. Anbringungsstelle der FIN", "maxLength": "50", "unit": "", "decimalPlaces": "", "tooltip": "Location of the vehicle identification number (code 2002/24/EC and 901/2014)\n\n2002/24/EC: 0.7.\n901/2014: 0.6. ", "tabs": ["L"]}, {"jsonKey": "TypeApprovalNumber", "type": "string", "title": "0.6. EG-Typgenehmigungsnummer einschließlich Erweiterungsnummer <K>", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Type approval number\n\n2007/46/EC: NN\n371/2010: NN\n2002/24/EC: NN \n901/2014: NN \n2003/37/EC: NN\n2015/504: NN This field is mandatory if the Type ApprovalTypeCode contains the value: EC, KS, NKS or NAT", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TypeApprovalDateOfIssue", "type": "date", "title": "0.6. <PERSON><PERSON> EG-Typgenehmigung <6>", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Type approval date of issue.\nThis is the date the type approval (00-extension) has been approved by the type approval authority. For extensions (01+ extensions) the date of issue for the extension has to be used (see also : 0.10 of the COC 2007/46).\n\n2007/46/EC: NN\n371/2010: NN\n2002/24/EC: NN \n901/2014: NN\n2003/37/EC: NN\n2015/504: NN\n\nFormat: CCYY-MM-DD ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "DescriptionOfCompletion", "type": "string", "title": "0.10. a) wie folgt vervollständigt und geändert ( 1 ) worden ist", "maxLength": "500", "unit": "", "decimalPlaces": "", "tooltip": "Description of complete/altered\n\n371/2010: NN\n901/2014: NN\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Allgemeine Baumerkmale / General construction characteristics", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON> zu Baumerkmalen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "NumberOfAxles", "type": "float", "title": "1. <PERSON><PERSON><PERSON> Achsen <L>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Number of axles\n\n2007/46/EC: 1.\n371/2010: 1.\n183/2011 IAC: 1.\n2002/24/EC: 1. \n901/2014: 1.3.\n2003/37/EC: 1.1.\n2015/504: 3.3.1. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "NumberOfWheels", "type": "float", "title": "1. <PERSON><PERSON><PERSON> Räder <L>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Number of wheels\n\n2007/46/EC: 1.\n371/2010: 1.\n183/2011 IAC: 1.\n2002/24/EC: 1.1.\n901/2014: 1.3.\n2003/37/EC: 1.1.\n2015/504: 3.3.1. Twin wheels have to be indicated as two wheels.", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "NumberOfAxlesWithTwinWheels", "type": "float", "title": "1.1. <PERSON><PERSON><PERSON> <PERSON> Achsen mit Zwillingsbereifung", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Number of axles with twin wheels\n\n371/2010: 1.1.\n183/2011 IAC: 1.1. \n2015/504: 3.3.2. ", "tabs": ["N1", "N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "TwinWheelsAxleInd", "type": "string", "title": "1.1. <PERSON><PERSON> von Achsen mit Zwillingsbereifung", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Twin wheel axle indicator\n\n371/2010: 1.1.\n183/2011 IAC: 1.1. \n901/2014: 1.3.1.\n2015/504: 3.3.2.\n\nValues: \nY =  Yes\nN = No ", "tabs": ["N1", "N2", "N3", "O34", "L", "M2", "M3"]}, {"jsonKey": "PowerMassRatio", "type": "float", "title": "1.10. Verh.Nennleistg./Masse fahrbereit", "maxLength": "3", "unit": "kW/kg", "decimalPlaces": 2, "tooltip": "Power mass ratio of the vehicle in running order (kW/kg)\n\n2002/24/EC: 26.1.\n901/2014: 1.10. ", "tabs": ["L"]}, {"jsonKey": "NumberOfSteeredAxles", "type": "float", "title": "2. <PERSON><PERSON><PERSON><PERSON> (Anzahl)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Number of Steered axles\n\n371/2010: 2.\n2015/504: 3.3.3. ", "tabs": ["N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "SteeredAxleInd", "type": "string", "title": "2. <PERSON><PERSON><PERSON><PERSON> (Lage)", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Steered axle indicator. \nThis entry is used to indicate if an axle is steered by a steering-device. A self-steering axle is not a steered axle.\n\n371/2010: 2.\n2015/504: 3.3.3.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TechPermMaxTowMassBrakedTrail", "type": "float", "title": "2.1.7. <PERSON><PERSON><PERSON><PERSON><PERSON> ungebr. <O.2>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum towable mass of Braked trailer.\nExcluding TechnPermMaxStatVertLoadCouplPt.\n\n2007/46/EC: 17.\n2002/24/EC: 17.\n901/2014: 2.1.7. In case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value", "tabs": ["L"]}, {"jsonKey": "GroundClearanceBetweenAxles", "type": "float", "title": "********. Bodenfreiheit zwischen den Achsen", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Ground clearance between the axles.\nThe ground clearance from the indicated axle to the next axle.\nThe last axle can not have a clearance.\n\n901/2014: ********. ", "tabs": ["L"]}, {"jsonKey": "WheelBaseGroundRatio", "type": "float", "title": "2.2.15. Verhältnis Radstand zu Bodenfreiheit", "maxLength": "3", "unit": "", "decimalPlaces": 2, "tooltip": "Wheelbase to ground clearance ratio\n\n901/2014: 2.2.15. ", "tabs": ["L"]}, {"jsonKey": "SeatingHeight", "type": "float", "title": "2.2.17. <PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Height of the seats\n\n901/2014: 2.2.17. ", "tabs": ["L"]}, {"jsonKey": "WheelBaseSideCar", "type": "float", "title": "*******. Radstand Beiwagen", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Wheelbase measured from the front wheel to the centre of the sidecar wheel.\n\n901/2014: 2.2.4. ", "tabs": ["L"]}, {"jsonKey": "NumberOfPoweredAxles", "type": "float", "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Anzahl) <9>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Number of powered axles\n\n2007/46/EC: 2.\n371/2010: 3.\n183/2011 IAC: 3. \n2003/37/EC: 1.1.3.\n2015/504: 3.3.4. ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "PoweredAxleInd", "type": "string", "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Lage) <9>", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Powered axle indicator\n\n371/2010: 3.\n183/2011 IAC: 3. \n901/2014: 1.3.2.\n2003/37/EC: 1.1.3.\n2015/504: 3.3.4.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "InterconnOfPoweredAxles", "type": "string", "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (gegenseitige Verbindung) <9>", "maxLength": "40", "unit": "", "decimalPlaces": "", "tooltip": "Interconnection of powered axles\n\n371/2010: 3\n2003/37/EC: 1.1.3. ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "ManufacturerHybridApplication", "type": "string", "title": "*******. Herst<PERSON> An<PERSON>ebsmasch. Hybridfahrzeug", "maxLength": "52", "unit": "", "decimalPlaces": "", "tooltip": "Manufacturer of the hybrid application.\n\n901/2014: *******. ", "tabs": ["L"]}, {"jsonKey": "HybridApplicationCode", "type": "string", "title": "*******. Anwendungscode des Herstellers", "maxLength": "40", "unit": "", "decimalPlaces": "", "tooltip": "Manufacturer’s hybrid application code\n\n901/2014: *******. ", "tabs": ["L"]}, {"jsonKey": "TestprocType5CO", "type": "float", "title": "******** CO mg/km", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Testprocedure Type V: CO\n\n901/2014: ******** ", "tabs": ["L"]}, {"jsonKey": "TestprocType5THC", "type": "float", "title": "********  THC mg/km", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Testprocedure Type V: THC\n\n901/2014: ******** ", "tabs": ["L"]}, {"jsonKey": "TestprocType5NMHC", "type": "float", "title": "******** NMHC mg/km", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Testprocedure Type V: NMHC\n\n901/2014: ******** ", "tabs": ["L"]}, {"jsonKey": "TestprocType5NOx", "type": "float", "title": "******** NOx mg/km", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Testprocedure Type V: NOx\n\n901/2014: ******** ", "tabs": ["L"]}, {"jsonKey": "TestprocType5HC_NOx", "type": "float", "title": "******** HC+Nox mg/km", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Testprocedure Type V: HC+NOx\n\n901/2014: ******** ", "tabs": ["L"]}, {"jsonKey": "TestprocType5Particulates", "type": "float", "title": "******** PM mg/km", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Testprocedure Type V: PM\n\n901/2014: ******** ", "tabs": ["L"]}, {"jsonKey": "TestprocType2COAtNormIdleSp", "type": "float", "title": "********. CO % vol", "maxLength": "6", "unit": "vol %", "decimalPlaces": 4, "tooltip": "Testprocedure Type II - CO at normal idle speed  (vol %)\n\n2002/24/EC: 46.\n901/2014: ********. ", "tabs": ["L"]}, {"jsonKey": "TestprocType2COAtHighIdleSp", "type": "float", "title": "********. CO % vol", "maxLength": "6", "unit": "vol %", "decimalPlaces": 4, "tooltip": "Testprocedure Type II - CO at high idle speed (vol %)\n\n2002/24/EC: 46.\n901/2014: ********. ", "tabs": ["L"]}, {"jsonKey": "TestprocType2HCAtNormIdleSp", "type": "float", "title": "********. HC g/min", "maxLength": "9", "unit": "ppm", "decimalPlaces": 5, "tooltip": "********. Type II HC: …ppm at normal idle speed\n\n901/2014: ********. ", "tabs": ["L"]}, {"jsonKey": "TestprocType2HCAtHighIdleSp", "type": "float", "title": "********. HC g/min", "maxLength": "9", "unit": "ppm", "decimalPlaces": 5, "tooltip": "********. Type II HC: …ppm at high idle speed\n\n901/2014: ********. ", "tabs": ["L"]}, {"jsonKey": "MaxPercentBiofuelAcceptInFuel", "type": "float", "title": "*******.1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "%", "decimalPlaces": 2, "tooltip": "Maximum amount of biofuel acceptabel in fuel \n\n901/2014: *******.1. ", "tabs": ["L"]}, {"jsonKey": "ElectricVehicleConfigurCode", "type": "string", "title": "3.3.1. Konfiguration des Elektrofahrzeugs", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Electric vehicle configuration: pure electric/hybrid electric/manpower — electric\n\n901/2014: 3.3.1.\n\nValues: \nP = Pure electric\nH = Hybrid electric\nM = Manpower - electric ", "tabs": ["L"]}, {"jsonKey": "Max15MinutesPowerFuel", "type": "float", "title": "*******. 15-/30- <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "maxLength": "6", "unit": "kW", "decimalPlaces": 2, "tooltip": "The maximum netpower measured according to paragraph 5.3.1.,  electric propulsion engines can deliver with continues current for a period of 15 minutes.\n\n901/2014: *******. Please use this field in stead of: Maximum15MinutesPower\n\nThe field has been added on the wrong level in a earlier release (CoCDataGroup in stead of FuelGroup).", "tabs": ["L"]}, {"jsonKey": "OffVehicleChargingVehInd", "type": "string", "title": "*******. Art des Elektrohybridfahrzeugs", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Off vehicle charging indicator for vehicle\n\n901/2014: *******.\n\nValues:\nY = Yes\nN = No This field is used to identify Plug-in hybrides. In some memberstates the field is  relevant for taxation purposes of environmental policies.\n\nWith this field two other fields become obsolete:\n­\tOffVehicleChargingIndicator\n­\tCategoryOfHybridElectricVehInd", "tabs": ["L"]}, {"jsonKey": "OverallGearRatioHighGearVeh", "type": "float", "title": "*******. Gesamtübersetzungsverh.im höchsten Gang", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Overall gear ratio in highest gear.\n\n901/2014: *******. Please do not use OverallGearRatioHighestGear but use this field in stead.", "tabs": ["L"]}, {"jsonKey": "MaximumAssistanceFactor", "type": "float", "title": "3.9.2. Maximaler Unterstützungsfaktor", "maxLength": "3", "unit": "", "decimalPlaces": 2, "tooltip": "Maximum assistance factor  of the engine for cycles designed to pedal\n\n901/2014: 3.9.2. ", "tabs": ["L"]}, {"jsonKey": "MaximumAssistanceSpeed", "type": "float", "title": "3.9.3. <PERSON><PERSON>G<PERSON>windigk. die Elektrom.unterstützt", "maxLength": "5", "unit": "km/h", "decimalPlaces": 2, "tooltip": "Maximum vehicle speed for which the electric motor gives assistance (3q) : ............ km/h\n\n901/2014: 3.9.3. ", "tabs": ["L"]}, {"jsonKey": "", "type": "headline", "title": "Hauptabmessungen / Main dimensions", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON> zu den Hauptabmessungen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "Wheelbase", "type": "float", "title": "4. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Wheelbase\n\n2007/46/EC: 3.\n371/2010: 4.\n183/2011 IAC: 4. \n2002/24/EC: 3.\n901/2014: 2.2.4.\n2003/37/EC: 2.5.\n2015/504: *******. This is the total wheelbase:\nin case of the vehicle categories M,N,L,T and C this is the distance between the first and rear axle of the vehicle;\n\nin case drawbar trailer of the vehicle categories O, R and S this is the distance between centre of the first axle and the rear axle;\n\nin case semi trailers, and centre axle and rigid drawbar trailers of the vehicle categories O, R and S this is the distance between centre of the coupling device and the rear axle;\n\nSee also: DistanceCouplPointFirstAxle and AxleSpacing.\n", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "WheelbaseMinimum", "type": "float", "title": "4. <PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Wheelbase minimum\n\n2007/46/EC: 3.\n371/2010: 4.\n183/2011 IAC: 4. \n2002/24/EC: 3.\n2003/37/EC: 2.5.\n2015/504: *******. Only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "WheelbaseMaximum", "type": "float", "title": "4. <PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Wheelbase maximum\n\n2007/46/EC: 3.\n371/2010: 4.\n183/2011 IAC: 4. \n2002/24/EC: 3.\n2003/37/EC: 2.5.\n2015/504: *******. Only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "TestprocTypeVIIFuelConsumption", "type": "float", "title": "4.0.2. Kraftstoffverbrauch l/kg", "maxLength": "9", "unit": "l/100km", "decimalPlaces": 5, "tooltip": "*******.6. Fuelconsumption Result of type VII (TR TTVIIx) …g/km\n\n901/2014: *******..\n2016/1825: 4.0.2. ", "tabs": ["L"]}, {"jsonKey": "TestprocTypeVIICO2", "type": "float", "title": "4.0.3. CO2-Emissionen g/km", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "*******. CO2.\n*******.6. CO2 Result of type VII (TR TTVIIx) …g/km\n\n901/2014: *******.\n2016/1825: 4.0.3. ", "tabs": ["L"]}, {"jsonKey": "ElectricRangeExternChargeable", "type": "float", "title": "4.0.5. Elektrische Reichweite", "maxLength": "5", "unit": "km", "decimalPlaces": "", "tooltip": "Electric range externally chargeable hybrid vehicle.\n\n371/2010: 49.\n901/2014: *******.\n2016/1825: 4.0.5. Use this entry only for OVC hybrid vehicles.", "tabs": ["L"]}, {"jsonKey": "ExtSoundLevelNrBaseRegulAct", "type": "string", "title": "4.0.6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gemessen nach", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Exterior sound level number of the base regulatory act and latest amending regulatory act applicable, incl phase\n\n2007/46/EC: 45.\n2002/24/EC: 46.\n901/2014: 4.0.2.\n2016/1825: 4.0.6.\n2003/37/EC: 13.\n2015/504: NN ", "tabs": ["L"]}, {"jsonKey": "LimitValueSoundLUrban", "type": "float", "title": "*******. Grenzwert für L", "maxLength": "5", "unit": "dB(A)", "decimalPlaces": 2, "tooltip": "Limit value of the sound level L urban is calculated by the weighted combination of a constant speed test and a full throttle acceleration test. \n\n2016/1825/EC: *******. ", "tabs": ["L"]}, {"jsonKey": "AxleSpacing_4_1X1_2", "type": "float", "title": "4.1. Achsenabstand 1-2", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Axle spacing. Distance to the next axle.\n\n371/2010: 4.1.\n183/2011 IAC: 4.1. \n2015/504: *******. The axle with the maximum number cannot have an axle spacing. \n\nSee also: DistanceCouplPointFirstAxle.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "AxleSpacing_4_1X2_3", "type": "float", "title": "4.1. Achsenabstand 2-3", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Axle spacing. Distance to the next axle.\n\n371/2010: 4.1.\n183/2011 IAC: 4.1. \n2015/504: *******. The axle with the maximum number cannot have an axle spacing. \n\nSee also: DistanceCouplPointFirstAxle.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "AxleSpacing_4_1X3_4", "type": "float", "title": "4.1. Achsenabstand 3-4", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Axle spacing. Distance to the next axle.\n\n371/2010: 4.1.\n183/2011 IAC: 4.1. \n2015/504: *******. The axle with the maximum number cannot have an axle spacing. \n\nSee also: DistanceCouplPointFirstAxle.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "Length", "type": "float", "title": "5. <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Length\n\n2007/46/EC:  6.1.\n371/2010: 5.\n183/2011 IAC: 5. \n2002/24/EC: 6.1.\n901/2014: 2.2.1.\n2015/504: *******.1. If the measurement exceeds the European legal boundaries, the ExceedingDimensionsIndicator has to be filled with the value \"Y\".", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LengthMinimum", "type": "float", "title": "5. <PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Length mimimum\n\n2007/46/EC: 6.1.\n371/2010: 5.\n183/2011 IAC: 5. \n2002/24/EC: 6.1. \n901/2014: 2.2.1\n2003/37/EC: 2.7.1 and *******.\n2015/504: *******.1. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the range.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "LengthMaximum", "type": "float", "title": "5. <PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Length maximum\n\n2007/46/EC: 6.1.\n371/2010: 5.\n183/2011 IAC: 5. \n2002/24/EC: 6.1.\n901/2014: 2.2.1\n2015/504: *******.1. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the range.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "MaximumPermissibleLength", "type": "float", "title": "5. <PERSON><PERSON> <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "For incomplete vehicles: Maximum permissible length\n\n2007/46/EC: 6.2.\n371/2010: 5.1.\n2003/37/EC: *******.1.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "<PERSON><PERSON><PERSON>", "type": "float", "title": "6. <PERSON><PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Width\n\n2007/46/EC: 7.1.\n371/2010: 6. \n183/2011 IAC: 6. \n2002/24/EC: 7.1.\n901/2014: 2.2.2.\n2015/504: *******.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "WidthMinimum", "type": "float", "title": "6. <PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Width mimimum\n\n2007/46/EC: 7.1.\n371/2010: 6.\n183/2011 IAC: 6. \n2002/24/EC: 7.1.\n901/2014: 2.2.2.\n2003/37/EC: 2.7.2. and *******.\n2015/504: *******.2. Only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "WidthMaximum", "type": "float", "title": "6. <PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Width maximum\n\n2007/46/EC: 7.1.\n371/2010: 6.\n183/2011 IAC: 6. \n2002/24/EC: 7.1.\n901/2014: 2.2.2.\n2015/504: *******.2. Only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "MaximumPermissibleWidth", "type": "float", "title": "6. <PERSON><PERSON> <PERSON><PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "For incomplete vehicles: Maximum permissible width\n\n2007/46/EC: 7.2.\n371/2010: 6.1.\n2003/37/EC: *******.1.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "PositionOfSeats", "type": "string", "title": "********. Lage und Anordnung der Sitze", "maxLength": "40", "unit": "", "decimalPlaces": "", "tooltip": "Positions of seats\n\n2002/24/EC: 42.1\n901/2014: ********.\n\nValues: \nrx: row number\nR: right side of the vehicle\nC: centre of the vehicle\nL: left side of the vehicle ", "tabs": ["L"]}, {"jsonKey": "AdvancedBrakingSystemCode", "type": "string", "title": "6.2.4. Verbessertes Bremssystem", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Code to identify the type of advanced braking system\n\n901/2014: 6.2.4.\n\nValues:\nABS = Anti-lock braking system\nCBS = Combined braking system \nNON = No advanced braking system\nACB = Combined ABS/CBS braking system ", "tabs": ["L"]}, {"jsonKey": "Height", "type": "float", "title": "7. <PERSON><PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Height\n\n2007/46/EC: 8.\n371/2010: 7.\n183/2011 IAC: 7. \n2002/24/EC: 8.\n901/2014: 2.2.3.\n2015/504: *******. and *******.3 If the measurement exceeds the European legal boundaries, the ExceedingDimensionsIndicator has to be filled with the value \"Y\"", "tabs": ["N1", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "HeightMinimum", "type": "float", "title": "7. <PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Height mimimum\n\n2007/46/EC: 8.\n371/2010: 7.\n183/2011 IAC: 7.\n2002/24/EC: 8.\n901/2014: 2.2.3.\n2003/37/EC: 2.7.3.\n2015/504: *******.3\n2016/1789: *******. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the installation range.\nIf more then 4000 then ExceedingDimensionIndicator has to be present with the value \"Y\".", "tabs": ["N1", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "HeightMaximum", "type": "float", "title": "7. <PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Height maximum\n\n2007/46/EC: 8.\n371/2010: 7.\n183/2011 IAC: 7. \n2002/24/EC: 8.\n901/2014: 2.2.3.\n2015/504: *******.3\n2016/1789: *******. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the installation range.\nIf more then 4000 then ExceedingDimensionIndicator has to be present with the value \"Y\".", "tabs": ["N1", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "MaximumPermissibleHeight", "type": "float", "title": "7. <PERSON><PERSON> <PERSON><PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Maximum permissible height\n\n371/2010: 7.1. ", "tabs": ["N1", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "FifthWheelLead", "type": "float", "title": "8. Sattelvormaß bei Sattelzugmasch.", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Fifth wheel lead for semi-trailer towing vehicle, or distance from vertical plane passing through the axis of the rear axle.\n\n2007/46/EC: 4.1. and 4.2.\n371/2010: 8.\n2003/37/EC: *******.\n2015/504: 38.3. and 38.4. In case of a complete or completed vehicle without coupling with slider (adjustable) state only one value.\n\nIn case of a complete or completed vehicle with coupling with slider (adjustable) use the FifthWheelLeadMinimum and FifthWheelLeadMaximum.", "tabs": ["N1", "N2", "N3"]}, {"jsonKey": "FifthWheelLeadMinimum", "type": "float", "title": "8. Sattelvormaß bei Sattelzugmasch. (minimum)", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Fifth wheel lead for semi-trailer towing vehicle - minimum, or minimum distance from vertical plane passing through the axis of the rear axle.\n\n2007/46/EC: 4.1. and 4.2.\n371/2010: 8.\n2015/504: 38.3. and 38.4. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the installation range.\nDo not use this entry for possible values in case of complete or completed vehicle with a non-adjustable coupling.", "tabs": ["N1", "N2", "N3"]}, {"jsonKey": "FifthWheelLeadMaximum", "type": "float", "title": "8. Sattelvormaß bei Sattelzugmasch. (maximum)", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Fifth wheel lead for semi-trailer towing vehicle maximum, or maximum distance from vertical plane passing through the axis of the rear axle.\n\n2007/46/EC: 4.1. and 4.2.\n371/2010: 8.\n2015/504: 38.3. and 38.4. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the installation range.\n\nDo ", "tabs": ["N1", "N2", "N3"]}, {"jsonKey": "ConvertingPerformanceIndic", "type": "string", "title": "8.1. <PERSON><PERSON><PERSON><PERSON><PERSON> eignet sich für die Umwandlung der Leistungsstufe von (L3e/L4e)-A2 nach (L3e/L4e)-A3 und umgekehrt", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Vehicle appropriate for converting its performance level between subcategories (L3e/L4e)-A2 and (L3e/L4e)-A3 and vice versa: \n\n\n901/2014: 8.1.\n\nValues: \nY = Yes\nN = No ", "tabs": ["L"]}, {"jsonKey": "DistFrontVehCentreCouplDev", "type": "float", "title": "9. Abstand zwischen der Fahrzeugfront und dem Mittelpunkt der Anhängevorrichtung", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Distance between the front end of the vehicle and the centre of the coupling device\n\n2007/46/EC: 6.3.\n371/2010: 9. In case of a complete or completed vehicle with coupling with slider (adjustable) use DistFrontVehCentreCouplDevMin and DistFrontVehCentreCouplDevMax.", "tabs": ["N1", "N2", "N3", "M2", "M3"]}, {"jsonKey": "Exemptions", "type": "string", "title": "9.2. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "200", "unit": "", "decimalPlaces": "", "tooltip": "Exemptions\n\n2007/46/EC: 51.\n2002/24/EC: 51.\n901/2014: 9.2. ", "tabs": ["L"]}, {"jsonKey": "DistCentreCouplDevRearVeh", "type": "float", "title": "10. Abstand zwischen dem Mittelpunkt der Anhängevorrichtung und dem Fahrzeugheck", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Distance between the centre of the coupling device and the rear end of the vehicle\n\n2007/46/EC: 6.4.\n371/2010: 10.\n2003/37/EC: *******. In case of an adjustable vehicle use the DistCentreCouplDevRearVehMin and DistCentreCouplDevRear VehMax.", "tabs": ["O12", "O34"]}, {"jsonKey": "DistCentreCouplDevRearVehMin", "type": "float", "title": "10. Abstand zwischen dem Mittelpunkt der Anhängevorrichtung und dem Fahrzeugheck (minimum)", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Distance between the centre of the coupling device and the rear end of the vehicle - minimum\n\n2007/46/EC: 6.4.\n371/2010: 10.\n2003/37/EC: *******. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the installation range.\nAlso as As DistCentreCouplDevRearVeh.", "tabs": ["O12", "O34"]}, {"jsonKey": "DistCentreCouplDevRearVehMax", "type": "float", "title": "10. Abstand zwischen dem Mittelpunkt der Anhängevorrichtung und dem Fahrzeugheck (maximum)", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Distance between the centre of the coupling device and the rear end of the vehicle - maximum\n\n2007/46/EC: 6.4.\n371/2010: 10.\n2003/37/EC: *******. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the installation range.\nAlso as As DistCentreCouplDevRearVeh.", "tabs": ["O12", "O34"]}, {"jsonKey": "LengthOfTheLoadingArea", "type": "float", "title": "11. <PERSON><PERSON><PERSON> der Ladefläche", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Length of the loading area\n\n2007/46/EC: 6.5.\n371/2010: 11.\n2003/37/EC: *******.1. ", "tabs": ["N1", "N2", "N3", "O12", "O34"]}, {"jsonKey": "LengthOfTheLoadingAreaMinimum", "type": "float", "title": "11. <PERSON><PERSON><PERSON> der Ladefläche", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Length of the loading area - minimum\n\n2007/46/EC: 6.5.\n371/2010: 11. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the range.", "tabs": ["N1", "N2", "N3"]}, {"jsonKey": "LengthOfTheLoadingAreaMaximum", "type": "float", "title": "11. <PERSON><PERSON> der Ladefläche", "maxLength": "5", "unit": "mm", "decimalPlaces": "", "tooltip": "Length of the loading area - maximum\n\n2007/46/EC: 6.5.\n371/2010: 11. In case of complete vehicles only to be used when the vehicle is adjustable. It is not permitted to use this entry for a range of values as defined in the type approval.\nIn case of incomplete vehicles only to be used to define the range.", "tabs": ["N1", "N2", "N3"]}, {"jsonKey": "RearOverhang", "type": "float", "title": "12. <PERSON><PERSON><PERSON>", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Rear overhang\n\n2007/46/EC: 11.\n371/2010: 12. ", "tabs": ["N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Massen / Masses", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON> zu den Massen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "MassOfTheVehicleInRunningOrder", "type": "float", "title": "13. <PERSON><PERSON> in fahrbereitem Zustand <G>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Mass of the vehicle in running order\n\n2007/46/EC: 12.1 and 12.3\n371/2010: 13.\n183/2011 IAC: 13.  \n2002/24/EC: 12.1\n901/2014: 2.1.1.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "DistrOfMassRunningOrderAxle_13_1X1", "type": "float", "title": "13.1. Verteilung dieser Masse auf die 1. <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Distribution of the mass of the vehicle in running order amongst the axles.\n\n371/2010: 13.1 In case of a central axle trailer, rigid drawbar or semi-trailer, please use the entry DistrOfMassRunningOrderCoupl for the mass on the coupling point (axle 0).", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "DistrOfMassRunningOrderAxle_13_1X2", "type": "float", "title": "13.1. Vert<PERSON><PERSON> dieser Masse auf die 2. <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Distribution of the mass of the vehicle in running order amongst the axles.\n\n371/2010: 13.1 In case of a central axle trailer, rigid drawbar or semi-trailer, please use the entry DistrOfMassRunningOrderCoupl for the mass on the coupling point (axle 0).", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "DistrOfMassRunningOrderAxle_13_1X3", "type": "float", "title": "13.1. Verteilung dieser Masse auf die 3. <PERSON><PERSON><PERSON> etc.", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Distribution of the mass of the vehicle in running order amongst the axles.\n\n371/2010: 13.1 In case of a central axle trailer, rigid drawbar or semi-trailer, please use the entry DistrOfMassRunningOrderCoupl for the mass on the coupling point (axle 0).", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "ActualMassOfTheVehicle", "type": "float", "title": "13.2. Tatsächliche Masse des Fahrzeugs <G>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Actual mass of the vehicle\n\n1230/2012: 13.2 or 14.\n901/2014: 2.1.2. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "MassIncompleteVehRunningOrder", "type": "float", "title": "14.  Masse des Basisfahrzeugs in fahrbereitem Zustand", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Mass of the incomplete vehicle in running order\n\n371/2010: 14.\n901/2014: 2.1.1. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechnPermMaxLadenMass", "type": "float", "title": "16.1. Technisch zulässige maximale Gesamtmasse <F.1>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum laden mass\n\n2007/46/EC: 14.1.\n371/2010:  16.1.\n183/2011 IAC: 16.1.  \n2002/24/EC: 14.1.\n901/2014: 2.1.3.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechnicallyPermMassAxle_16_2X1", "type": "float", "title": "16.2. Tech<PERSON><PERSON> zulässige maximale Masse je Achse 1 <7.1>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible mass on each axle\n\n2007/46/EC: 14.3./14.6.\n371/2010: 16.2.\n183/2011 IAC: 16.2.\n2002/24/EC: 14.3.\n901/2014: *******., *******., *******., \n2015/504: *******.1. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechnicallyPermMassAxle_16_2X2", "type": "float", "title": "16.2. <PERSON><PERSON><PERSON> zulässige maximale Masse je Achse 2 <7.2>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible mass on each axle\n\n2007/46/EC: 14.3./14.6.\n371/2010: 16.2.\n183/2011 IAC: 16.2.\n2002/24/EC: 14.3.\n901/2014: *******., *******., *******., \n2015/504: *******.1. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechnicallyPermMassAxle_16_2X3", "type": "float", "title": "16.2. Tech<PERSON>ch zulässige maximale Masse je Achse 3 etc. <7.3>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible mass on each axle\n\n2007/46/EC: 14.3./14.6.\n371/2010: 16.2.\n183/2011 IAC: 16.2.\n2002/24/EC: 14.3.\n901/2014: *******., *******., *******., \n2015/504: *******.1. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechPermMassAxleGroup_16_3X1", "type": "float", "title": "16.3. Technisch zulässige maximale Masse je Achsgruppe 1.", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible mass on each axle group\n\n2007/46/EC: 14.4\n371/2010: 16.3 ", "tabs": ["N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "TechPermMassAxleGroup_16_3X2", "type": "float", "title": "16.3. Technisch zulässige maximale Masse je Achsgruppe 2.", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible mass on each axle group\n\n2007/46/EC: 14.4\n371/2010: 16.3 ", "tabs": ["N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "TechPermMassAxleGroup_16_3X3", "type": "float", "title": "16.3. Technisch zulässige maximale Masse je Achsgruppe 3. etc.", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible mass on each axle group\n\n2007/46/EC: 14.4\n371/2010: 16.3 ", "tabs": ["N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "TechnPermMaxMassCombination", "type": "float", "title": "16.4. Technisch zulässige Gesamtmasse der Fahrzeugkombination", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum mass of the combination\n\n2007/46/EC: 18.\n371/2010: 16.4.\n183/2011 IAC: 16.4.  \n901/2014: *******.\n2003/37/EC: 2.4.4. If the vehicle is not equiped with a coupling device the field has to be ignored.\nIn case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped state maximum value maximum value \"0\" or skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassNational", "type": "float", "title": "17.1. im Betrieb maximal zulässige Gesamtmasse <F.2>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass - national\n\n371/2010: 17.1. ", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassAxleNational_17_2X1", "type": "float", "title": "17.2. im Betrieb maximal zulässige Gesamtmasse je Achse 1 <8.1>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass on each axle - national\n\n371/2010: 17.2. The value has to be less then or equal to the TechnicallyPermMassAxle.", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassAxleNational_17_2X2", "type": "float", "title": "17.2. im Betrieb maximal zulässige Gesamtmasse je Achse 2 <8.2>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass on each axle - national\n\n371/2010: 17.2. The value has to be less then or equal to the TechnicallyPermMassAxle.", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassAxleNational_17_2X3", "type": "float", "title": "17.2. im Betrieb maximal zulässige Gesamtmasse je Achse 3 <8.3>", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass on each axle - national\n\n371/2010: 17.2. The value has to be less then or equal to the TechnicallyPermMassAxle.", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassAxleGrNat_17_3X1", "type": "float", "title": "17.3. im Betrieb maximal zulässige Gesamtmasse je Achsgruppe 1.", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass on each axle group - national\n\n371/2010: 17.3. ", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassAxleGrNat_17_3X2", "type": "float", "title": "17.3. im Betrieb maximal zulässige Gesamtmasse je Achsgruppe 2.", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass on each axle group - national\n\n371/2010: 17.3. ", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermLadenMassAxleGrNat_17_3X3", "type": "float", "title": "17.3. im Betrieb maximal zulässige Gesamtmasse je Achsgruppe 3.", "maxLength": "5", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible laden mass on each axle group - national\n\n371/2010: 17.3. ", "tabs": ["N2", "N3", "O34", "M2", "M3"]}, {"jsonKey": "MaxPermMassCombinationNational", "type": "float", "title": "17.4. im Betrieb maximal zulässige Gesamtmasse der Fahrzeugkombination (national)", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible mass of the combination - national\n\n371/2010: 17.4. In case of an incomplete, complete or completed vehicle and the vehicle is equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped with a coupling state maximum value “0” or skip this entry in the xml.", "tabs": ["N2", "N3", "M2", "M3"]}, {"jsonKey": "MaxPermMassCombinationInt", "type": "float", "title": "17.4. im Betrieb maximal zulässige Gesamtmasse der Fahrzeugkombination (international)", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Intended registration/in service maximum permissible mass of the combination - international\n\n371/2010: 17.4. In case of an incomplete, complete or completed vehicle and the vehicle is equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped with a coupling state maximum value “0” or skip this entry in the xml.", "tabs": ["N2", "N3", "M2", "M3"]}, {"jsonKey": "TechPermMaxTowMassDrawbarTrail", "type": "float", "title": "18.1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum towable mass of Drawbar trailer.\nExcluding TechnPermMaxStatVertLoadCouplPt.\n\n2007/46/EC: 17.1.\n371/2010: 18.1. \n183/2011 IAC: 18.1.  \n2003/37/EC: 2.4.1. If the vehicle is not equiped with a coupling device the field has to be ignored.\nIn case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped state maximum value maximum value “0” or skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TechPermMaxTowMassSemiTrailer", "type": "float", "title": "18.2. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum towable mass of Semi-trailer.\nExcluding TechnPermMaxStatVertLoadCouplPt.\n\n2007/46/EC: 17.2.\n371/2010: 18.2.\n183/2011 IAC: 18.2.  \n2003/37/EC: 2.4.2. If the vehicle is not equiped with a coupling device the field has to be ignored.\nIn case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped state maximum value maximum value “0” or skip this entry in the xml.", "tabs": ["N1", "N2", "N3"]}, {"jsonKey": "TechPermMaxTowMassCentAxTrail", "type": "float", "title": "18.3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <O.1>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum towable mass of Centre-axle trailer.\nExcluding TechnPermMaxStatVertLoadCouplPt.\n\n2007/46/EC: 17.3.\n371/2010: 18.3.\n183/2011 IAC: 18.3. \n2003/37/EC: 2.4.3. If the vehicle is not equiped with a coupling device the field has to be ignored.\nIn case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped state maximum value maximum value “0” or skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TechPermMaxTowMassUnbrTrailer", "type": "float", "title": "18.4. <PERSON><PERSON><PERSON><PERSON><PERSON> <O.2>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum towable mass of Unbraked trailer.\nExcluding TechnPermMaxStatVertLoadCouplPt.\n\n2007/46/EC: 17. and 17.4.\n371/2010: 18.4. \n183/2011 IAC: 18.4.  \n2002/24/EC: 17.\n901/2014: 2.1.7. If the vehicle is not equiped with a coupling device the field has to be ignored.\nIn case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped state maximum value maximum value “0” or skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechPermMaxStatVertMassCouplPt", "type": "float", "title": "19. Technisch zulässige maximale vertikale Stützlast am Kupplungspunkt <13>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum static vertical mass at the coupling point\n\n2007/46/EC: 19.1.\n371/2010: 19.\n183/2011 IAC: 19.  \n2002/24/EC: 19.1.\n901/2014: *******. In case of an incomplete, complete or completed vehicle, equiped with a coupling system state maximum value\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling state maximum value\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped state maximum value maximum value “0” or skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TechPermMaxStatMassCouplPoint", "type": "float", "title": "19. Technisch zulässige maximale Stützlast am Kupplungspunkt eines Sattelanhängers <13>", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Technically permissible maximum static mass on the coupling point of a semi-trailer or centre-axle trailer\n\n2007/46/EC: 14.6.\n371/2010: 19.1. (and 19.)\n2015/504: *******.2. ", "tabs": ["O12", "O34"]}, {"jsonKey": "", "type": "headline", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zur Antriebsmaschine", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "ManufacturerOfTheEngine", "type": "string", "title": "20. Her<PERSON><PERSON> der Antriebsmaschine", "maxLength": "52", "unit": "", "decimalPlaces": "", "tooltip": "Manufacturer of the engine\n\n2007/46/EC: 20.\n371/2010: 20.\n183/2011 IAC: 20.\n2002/24/EC: 20.\n901/2014: *******. and *******.\n2003/37/EC: 3.1.1.\n2015/504: 2.1. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "EngineCodeAsMarkedOnTheEngine", "type": "string", "title": "21. Baumusterbezeichnung gemäß Kennzeichnung am Motor", "maxLength": "40", "unit": "", "decimalPlaces": "", "tooltip": "The engine code is the code specifying the engine model as marked on the engine.\n\n2007/46/EC: 21.\n371/2010: 21.\n183/2011 IAC: 21.\n2002/24/EC: 21.\n901/2014: *******. and *******.\n2003/37/EC: *******.\n2015/504: 2.5.2. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "WorkingPrincipleCode", "type": "string", "title": "22. Arbeitsverfahren", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Working principle code.\n\n2007/46/EC: 22.\n371/2010: 22.\n183/2011 IAC: 22.\n2002/24/EC: 22.\n901/2014: *******.\n2003/37/EC: 3.1.6.\n2016/1789: 6.2.2.\n\nValues: \nC2  compression ignition, two stroke\nC4  compression ignition, four stroke\nCA  compressed air\nCI  compression ignition\nDF  dualfuel\nE2  positive ignition, two stroke\nE4  positive ignition, four stroke\nEC  external combustion engine compression ignition \nEE  electric engine\nEF  external combustion engine \nEP  external combustion engine positive ignition\nER  positive ignition, rotary\nIC  internal combustion engine compression ignition\nIE  internal combustion engine positive ignition\nPI  positive ignition\nTB  turbine ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "PureElectricVehIndicator", "type": "string", "title": "23. rein el<PERSON><PERSON><PERSON>", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Pure electric indicator\n\n371/2010: 23.\n183/2011 IAC: 23.\n\nValues:\nY = Yes\nN = No This field replaces PureElectricIndicator.", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "HybridVehIndicator", "type": "string", "title": "23.1. <PERSON> [elektro] Fahrzeug", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Hybrid indicator\n\nValues:\nY = Yes\nN = No\n\n371/2010: 23.1.\n183/2011 IAC: 23.1. This field replaces HybridIndicator.", "tabs": ["N3", "M3"]}, {"jsonKey": "ClassOfHybridVehicleCode", "type": "string", "title": "23.1 Art des (Elektro-)Hybridfahrzeugs", "maxLength": "15", "unit": "", "decimalPlaces": "", "tooltip": "Class of Hybrid [electric] vehicle\n\n2017/1151: 23.1.\n\nValues:\nOVC-HEV\t= Off vehicle-charging hybrid electric vehicle\nNOVC-HEV\t= Not off-vehicle charging hybrid electric vehicle\nOVC-FCHV\t= Off-vehicle charging fuel cell hybrid vehicle\nNOVC-FCHV\t= Not off-vehicle charging fuel cell hybrid vehicle ", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "NumberOfCylinders", "type": "float", "title": "24. <PERSON><PERSON><PERSON> <PERSON>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Number of cylinders\n\n2007/46/EC: 23.\n371/2010: 24.\n183/2011 IAC: 24. \n2002/24/EC: 23.\n901/2014: *******.1.\n2003/37/EC: *******.\n2015/504: 6.4.\n2018/986: *******. For rotary engines count the number of rotors, to determine the number of cylinders.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "ArrangementOfCylindersCode", "type": "string", "title": "24. <PERSON><PERSON><PERSON><PERSON> <PERSON>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Arrangement of cylinders\n\n371/2010: 24.\n183/2011 IAC: 24. \n2002/24/EC: 23.\n901/2014: *******.2.\n2015/504: 6.4.\n2018/986: *******.\n\nValues:\nLI = Line\nO = Boxer\nR = Wankel Rotary engine\nS = Single cylinder\nV = V-engine\nW= W-engine ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "FuelCodePowerSource", "type": "float", "title": "26. Kraftstoffcode: Diesel/Benzin/LPG/NG – Biomethane/Ethanol/Biodiesel/Wasserstoff <10>", "maxLength": "4", "unit": "", "decimalPlaces": "", "tooltip": "Fuelcode power source. Important for registration purposes in Germany.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "FuelTypeCode", "type": "string", "title": "26.1. Mono fuel/Bi fuel/Flex fuel", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Fueltype\n\n371/2010: 26.1.\n183/2011 IAC: 26.1.\n901/2014: *******.\n\nValues: \nB = Bifuel\nD = Dualfuel\nF = Flexfuel\nM = Monofuel\nT = Trifuel ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "DualFuelType_26_2X1A", "type": "string", "title": "26.2. (nur <PERSON>wei<PERSON>ff<PERSON>ren) Typ 1A", "maxLength": "15", "unit": "", "decimalPlaces": "", "tooltip": "The normal operating mode of a dual-fuel engine during which the engine simultaneously uses diesel fuel and a gaseous fuel at some engine operating conditions.\n\n133/2014: 26.2. \n\nValues:\nType 1A\nType 1B\nType 2A\nType 2B\nType 3B ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "DualFuelType_26_2X1B", "type": "string", "title": "26.2. (nur Zweistoffmotoren) Typ 1B", "maxLength": "15", "unit": "", "decimalPlaces": "", "tooltip": "The normal operating mode of a dual-fuel engine during which the engine simultaneously uses diesel fuel and a gaseous fuel at some engine operating conditions.\n\n133/2014: 26.2. \n\nValues:\nType 1A\nType 1B\nType 2A\nType 2B\nType 3B ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "DualFuelType_26_2X2A", "type": "string", "title": "26.2. (nur Zweistoffmotoren) Typ 2A", "maxLength": "15", "unit": "", "decimalPlaces": "", "tooltip": "The normal operating mode of a dual-fuel engine during which the engine simultaneously uses diesel fuel and a gaseous fuel at some engine operating conditions.\n\n133/2014: 26.2. \n\nValues:\nType 1A\nType 1B\nType 2A\nType 2B\nType 3B ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "DualFuelType_26_2X2B", "type": "string", "title": "26.2. (nur Zweistoffmotoren) Typ 2B", "maxLength": "15", "unit": "", "decimalPlaces": "", "tooltip": "The normal operating mode of a dual-fuel engine during which the engine simultaneously uses diesel fuel and a gaseous fuel at some engine operating conditions.\n\n133/2014: 26.2. \n\nValues:\nType 1A\nType 1B\nType 2A\nType 2B\nType 3B ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "DualFuelType_26_2X3B", "type": "string", "title": "26.2. (nur Zweistoffmotoren) Typ 3B", "maxLength": "15", "unit": "", "decimalPlaces": "", "tooltip": "The normal operating mode of a dual-fuel engine during which the engine simultaneously uses diesel fuel and a gaseous fuel at some engine operating conditions.\n\n133/2014: 26.2. \n\nValues:\nType 1A\nType 1B\nType 2A\nType 2B\nType 3B ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "MaximumNetPower", "type": "float", "title": "27.1. Höchste Nennleistung (g) (Verbrennungsmotor) (1) <P.2>", "maxLength": "6", "unit": "kW", "decimalPlaces": 2, "tooltip": "Maximum net power\n\n2007/46/EC: 26.\n371/2010: 27.\n136/2014: 27.1.\n183/2011 IAC: 27.\n2002/24/EC: 26.\n901/2014: 1.9.\n2003/37/EC: 3.6.\n2015/504: 5.3.\n2018/986: *******.2. The field has to be used for combustion engines only. It’s also allowed to add this field in case of a hybrid electrical vehicle.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "EngineSpeedMaximumNetPower", "type": "float", "title": "27.1. Motordrehzahl bei Nennleistung (g) (Verbrennungsmotor) (1) <P.2>", "maxLength": "5", "unit": "1/min", "decimalPlaces": "", "tooltip": "Engine speed maximum net power\n\n2007/46/EC: 26.\n371/2010: 27.\n136/2014: 27.1.\n183/2011 IAC: 27.\n2002/24/EC: 26.\n901/2014: 1.9.\n2003/37/EC: 3.6.\n2015/504: 5.3. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "EngineSpeedMaximumNetPowerMin", "type": "float", "title": "27.1. Motordrehzahl bei niedrigster Nennleistung (g) (Verbrennungsmotor) (1) <P.2>", "maxLength": "5", "unit": "1/min", "decimalPlaces": "", "tooltip": "Engine speed maximum net power minimum\n\n2007/46/EC: 26.\n371/2010: 27.\n136/2014: 27.1.\n183/2011 IAC: 27.\n2002/24/EC: 26.\n901/2014: 1.9.\n2003/37/EC: 3.6.\n2015/504: 5.3. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "EngineSpeedMaximumNetPowerMax", "type": "float", "title": "27.1. Motordrehzahl bei höchster Nennleistung (g) (Verbrennungsmotor) (1) <P.2>", "maxLength": "5", "unit": "1/min", "decimalPlaces": "", "tooltip": "Engine speed maximum net power maximum\n\n2007/46/EC: 26.\n371/2010: 27.\n136/2014: 27.1.\n183/2011 IAC: 27.\n2002/24/EC: 26.\n901/2014: 1.9.\n2003/37/EC: 3.6.\n2015/504: 5.3. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "MaxHourlyPowerFuel", "type": "float", "title": "27.2. Höchste Stundenleistung (Elektromotor) (1)", "maxLength": "6", "unit": "kW", "decimalPlaces": 2, "tooltip": "The maximum netpower measured according to paragraph 5.3.1., electric propulsion engines can deliver with continues current for a period of one hour.\n\n136/2014: 27.2. Please use this field in stead of: MaximumHourlyPower", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "MaxNetPowerElectricEngFuel", "type": "float", "title": "27.3. Höchste Nennleistung (Elektromotor) (1)", "maxLength": "6", "unit": "kW", "decimalPlaces": 2, "tooltip": "Maximum net power for electric engines.\n\n136/2014: 27.3. Please use this field in stead of: MaximumNetPowerElectricEngine", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "MaximumContinuousRatedPower", "type": "float", "title": "27.4. <PERSON><PERSON><PERSON><PERSON> 30-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Elektromotor) (1) <P.2>", "maxLength": "6", "unit": "kW", "decimalPlaces": 2, "tooltip": "Maximum continuous rated power/30 minutes power\n\n371/2010: 27.\n136/2014: 27.4.\n183/2011 IAC: 27.\n2002/24/EC: 26.\n901/2014: *******. The field has to be used for electric engines only. It’s also allowed to add this field in case of a hybrid electrical vehicle.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearboxTypeCode", "type": "string", "title": "28. <PERSON><PERSON><PERSON> (Typ)", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Gearbox (type)\n\n2007/46/EC: 28.\n371/2010: 28.\n2002/24/EC: 28.\n901/2014: *******.\n2016/1789: 11.2.8.\n\nValues: \nM= Manual\nA= Automatic\nC= CVT\nD = Double clutch (gear change)\nF = Fixed ratio\nG= Automised\nO = Other\nH = Hydrostatic\nS = Semi-automatic (gear change)\nW= Wheel hub ", "tabs": ["N1", "N2", "N3", "L", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X1", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> Z<PERSON> (1)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X1", "type": "float", "title": "28.1. Übersetzungsverhältnisse 1", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X2", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> <PERSON> (2)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X2", "type": "float", "title": "28.1. Übersetzungsverhältnisse 2", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X3", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> <PERSON> (3)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X3", "type": "float", "title": "28.1. Übersetzungsverhältnisse 3", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X4", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> <PERSON> (4)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X4", "type": "float", "title": "28.1. Übersetzungsverhältnisse 4", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X5", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> <PERSON> (5)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X5", "type": "float", "title": "28.1. Übersetzungsverhältnisse 5", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X6", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> (6)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X6", "type": "float", "title": "28.1. Übersetzungsverhältnisse 6", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X7", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> (7)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X7", "type": "float", "title": "28.1. Übersetzungsverhältnisse 7", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearNumber_28_1X8", "type": "float", "title": "28.1. <PERSON><PERSON><PERSON><PERSON> (8)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the Gear informationset. ", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "GearRatio_28_1X8", "type": "float", "title": "28.1. Übersetzungsverhältnisse 8", "maxLength": "10", "unit": "", "decimalPlaces": 6, "tooltip": "Gear ratios\n\n2007/46/EC: 29.\n2020/683: 28.1\n2002/24/EC: 29.\n901/2014: 3.5.4.\n2015/504: 11.5. 2002/24/EG In case of GearboxTypeCode = CVT state minimum ratio in first gear and maximum ratio in the second gear.\n901/2014: In case of GearboxTypeCode = CVT\n1: gear ratio at maximum design vehicle speed\n2: gear ratio at maximum peak power\n3: gear ration at maximum peak torque\nThe gear ratios shall include the gear ratio of the primary transmission ratio (if applicable)\n901/2014: In case of GearboxTypeCode = W\nFor wheel hub engines without gear drive indicate ‘1’ or skip entry (= n/a).", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatioVeh", "type": "float", "title": "28.1.1. Übersetzung des Achsgetriebes", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.1.\n901/2014: *******.\n2015/504: 11.5.1. Use this field in case of one (or more) final drives not related to the gear.\nExample: one gearbox and one final drive in the rear axle.", "tabs": ["N1", "N2", "L", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X1", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>mer des Achsgetriebes (1)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X1", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 1", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X2", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> des Achsgetriebes (2)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X2", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 2", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X3", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> des Achsgetriebes (3)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X3", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 3", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X4", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> des Achsgetriebes (4)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X4", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 4", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X5", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> des Achsgetriebes (5)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X5", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 5", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X6", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> Achsgetriebes (6)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X6", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 6", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X7", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> des Achsgetriebes (7)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X7", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 7", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveNumber_28_1_2X8", "type": "float", "title": "28.1.2. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> des Achsgetriebes (8)", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Unique sequential number in order to identify the final drives.\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveVehNumber.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "FinalDriveRatio_28_1_2X8", "type": "float", "title": "28.1.2. Übersetzung des Achsgetriebes 8", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "Final drive ratio\n\n2018/1832: 28.1.2. Use this field in case of multiple final drives on gear level. In other cases use FinalDriveRatioVeh.", "tabs": ["N1", "N2", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Höchstgeschwindigkeit / Maximum Speed", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON> zu den Geschwindigkeiten", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "MaximumSpeedNonPropulsion", "type": "float", "title": "29. Höchstgeschwindigkeit Fahrzeuge ohne Antrieb <T>", "maxLength": "5", "unit": "km/h", "decimalPlaces": 2, "tooltip": "Maximum vehicle speed for vehicle without propulsion. \n\n2007/46/EC: 44.\n371/2010: 29.|\n183/2011 IAC: 29. \n2002/24/EC: 44.\n2003/37/EC: 4.7.1\n2015/504: *******. ", "tabs": ["O12", "O34"]}, {"jsonKey": "MaximumSpeed", "type": "float", "title": "29. Höchstgeschwindigkeit in km/h <T>", "maxLength": "5", "unit": "km/h", "decimalPlaces": 2, "tooltip": "Maximum speed\n\n2007/46/EC: 44.\n371/2010: 29.\n183/2011 IAC: 29. \n2002/24/EC: 44.\n901/2014: 1.8.\n2003/37/EC: 4.7.1.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Achsen und Radaufhängung / Axles and suspension", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zu den Achsen und Radaufhängungen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "AxleTrack_30X1", "type": "float", "title": "30. <PERSON><PERSON><PERSON><PERSON> 1", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Axle track\n\n2007/46/EC: 5.\n371/2010: 30.\n183/2011 IAC: 30. \n901/2014: *******, ******* and *******.\n2015/504: *******. Also see: TrackOfEachSteeredAxle and TrackOfAllOtherAxles.", "tabs": ["N1", "L", "M1", "M2"]}, {"jsonKey": "AxleTrack_30X2", "type": "float", "title": "30. <PERSON><PERSON><PERSON><PERSON> 2", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Axle track\n\n2007/46/EC: 5.\n371/2010: 30.\n183/2011 IAC: 30. \n901/2014: *******, ******* and *******.\n2015/504: *******. Also see: TrackOfEachSteeredAxle and TrackOfAllOtherAxles.", "tabs": ["N1", "L", "M1", "M2"]}, {"jsonKey": "AxleTrack_30X3", "type": "float", "title": "30. <PERSON><PERSON><PERSON><PERSON> 3", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Axle track\n\n2007/46/EC: 5.\n371/2010: 30.\n183/2011 IAC: 30. \n901/2014: *******, ******* and *******.\n2015/504: *******. Also see: TrackOfEachSteeredAxle and TrackOfAllOtherAxles.", "tabs": ["N1", "L", "M1", "M2"]}, {"jsonKey": "TrackOfEachSteeredAxle", "type": "float", "title": "30.1 Spurweite der gelenkten Achsen", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Track of each steered axle\n\n371/2010:  30.1. Also see: AxleTrack and TrackOfAllOtherAxles.", "tabs": ["O12", "M3"]}, {"jsonKey": "TrackOfAllOtherAxles", "type": "float", "title": "30.2 Spurweite aller übrigen Achsen", "maxLength": "4", "unit": "mm", "decimalPlaces": "", "tooltip": "Track of all other axles\n\n371/2010: 30.2 Also see: AxleTrack and TrackOfEachSteeredAxle. This entry has to be added per axlenumber.", "tabs": ["O12", "M3"]}, {"jsonKey": "LiftAxleInd", "type": "string", "title": "31. <PERSON><PERSON> der Hubachse(n)", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Lift axle indicator\n\n371/2010: 31.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N2", "N3", "O12", "O34"]}, {"jsonKey": "LoadableAxleInd", "type": "string", "title": "32. Lage der belastbaren Achse(n)", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Loadable axle indicator\nA loadable axle is an axle which can be (temporary) extra loaded by deflation of the air-suspension of another axle.\n\n371/2010: 32.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N2", "N3", "O12", "O34", "M3"]}, {"jsonKey": "DriveAxleWithAirSuspOrEquivInd", "type": "string", "title": "33. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(n) mit Luftfederung oder gleichwertiger Aufhängung", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Drive axle(s) fitted with air suspension or equivalent indicator\n\n2007/46/EC: 33.1\n371/2010:  33.\n\nValues:\nY = Yes\nN = No Only use this entry in case of a powered axle.\nFor the vehicle categories O1, O2 O3 and O4, please use the entry AxleWithAirSuspOrEquivInd.", "tabs": ["N2", "N3", "M2", "M3"]}, {"jsonKey": "AxleWithAirSuspOrEquivInd", "type": "string", "title": "34. <PERSON><PERSON><PERSON>(n) mit Luftfederung oder gleichwertiger Aufhängung", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Axle fitted with air suspension or equivalent indicator.\n\n2007/46/EC: 33.2.\n371/2010: 34.\n\nValues:\nY = Yes\nN = No For the vehicle categories M2 , M3 , N2 and N3, please use the entry DriveAxleWithAirSuspOrEquivInd in case of a powered axle.", "tabs": ["O12", "O34"]}, {"jsonKey": "", "type": "headline", "title": "Reifen-/Felgenkombination der Achse I", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zu den Achsen und Radaufhängungen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreSize_35X1", "type": "string", "title": "35. Reifengröße Achse I <15.1>", "maxLength": "20", "unit": "", "decimalPlaces": "", "tooltip": "Tyre size\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LoadCapacityIndexSingleWheel_35X1", "type": "float", "title": "35. Tragfähigkeit Achse I (einfach) <15.1>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Load capacity index single wheel\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LoadCapacityIndexTwinWheel_35X1", "type": "float", "title": "35. Tragfähigkeit Achse I (zweifach) <15.1>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Load capacity index twin wheel\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014 : ********.\n2003/37/EC: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "SpeedCategorySymbol_35X1", "type": "string", "title": "35. Geschwindigkeitskategorie Achse I <15.1>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Speed category symbol\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********\n2003/37/EC: *******\n2015/504: *******.\n\n\nExample valueset:\nA1,A2,A3,A4,A5,A6,A7,A8,B,C,D,E,F,G,J,K,L,M … etc ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TypeOfTyre_35X1", "type": "string", "title": "35. <PERSON><PERSON><PERSON><PERSON><PERSON> <15.1>", "maxLength": "7", "unit": "", "decimalPlaces": "", "tooltip": "Type of tyre.\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35.\n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******.\t\n\nValues:\nAL   = Airless tyre\nAS   = All Season\nMC   = Motorcycle tyre\nMS   = M+S tyre\nMSE  = M+S tyre with spikes\nMSS  = M+S tyre studded\nMST  = Multi Service Tyre\nPAX  = PAX Runflat tyre\nRF   = Reinforced\nRFMS = Reinforced M&S tyre\nS    = Studded tyre\nXL   = Extra load\nXLMS = Extra load M&S Tyre ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreTubeTypeCode_35X1", "type": "string", "title": "35. Code des Reifenschlauchtyps Achse I <15.1>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Tyre tube type code.\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35.\n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******.\n\nValues:\nTL = Tubeless \nTT= Tube type ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreMaxSpeedIndicator_35X1", "type": "string", "title": "35. <PERSON><PERSON><PERSON> für Geschwindigkeiten über 300 km/h Achse I <15.1>", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "For tyres suitable for speeds in excess of 300 km/h. and motorcycle tyres, the service description shall be marked within brackets, for example,\"(95Y)\" .\n\n2015/166: 35.\n901/2014: ********.\n2003/37: *******.\n2015/504: *******.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyrePlyRatingNumber_35X1", "type": "float", "title": "35. <PERSON><PERSON><PERSON>alreifen Achse I <15.1>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "The ply-rating number of diagonal (bias-ply) tyres.\n\n2015/166: 35.\n901/2014: ********.\n2003/37: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreRemarks_35X1", "type": "string", "title": "35. <PERSON><PERSON>kungen zum Reifen Achse I <15.1>", "maxLength": "200", "unit": "", "decimalPlaces": "", "tooltip": "Tyre remarks Please use this field to add remarks on the tyre specification.", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "RollingResistanceClassCode_35X1", "type": "string", "title": "35. Rollwiderstandsklasse Achse I <15.1>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Rolling Resistance Class. As stated on the Tyre Label\n\n999/2017: 35. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Reifen-/Felgenkombination der Achse II <15.2>", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zu den Achsen und Radaufhängungen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreSize_35X2", "type": "string", "title": "35. Reifengröße Achse II <15.2>", "maxLength": "20", "unit": "", "decimalPlaces": "", "tooltip": "Tyre size\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LoadCapacityIndexSingleWheel_35X2", "type": "float", "title": "35. Tragfähigkeit Achse II (einfach) <15.2>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Load capacity index single wheel\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LoadCapacityIndexTwinWheel_35X2", "type": "float", "title": "35. Tragfähigkeit Achse II (zweifach) <15.2>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Load capacity index twin wheel\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014 : ********.\n2003/37/EC: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "SpeedCategorySymbol_35X2", "type": "string", "title": "35. Geschwindigkeitskategorie Achse II <15.2>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Speed category symbol\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********\n2003/37/EC: *******\n2015/504: *******.\n\n\nExample valueset:\nA1,A2,A3,A4,A5,A6,A7,A8,B,C,D,E,F,G,J,K,L,M … etc ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TypeOfTyre_35X2", "type": "string", "title": "35. <PERSON><PERSON><PERSON><PERSON><PERSON> Achse II <15.2>", "maxLength": "7", "unit": "", "decimalPlaces": "", "tooltip": "Type of tyre.\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35.\n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******.\t\n\nValues:\nAL   = Airless tyre\nAS   = All Season\nMC   = Motorcycle tyre\nMS   = M+S tyre\nMSE  = M+S tyre with spikes\nMSS  = M+S tyre studded\nMST  = Multi Service Tyre\nPAX  = PAX Runflat tyre\nRF   = Reinforced\nRFMS = Reinforced M&S tyre\nS    = Studded tyre\nXL   = Extra load\nXLMS = Extra load M&S Tyre ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreTubeTypeCode_35X2", "type": "string", "title": "35. Code des Reifenschlauchtyps Achse II <15.2>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Tyre tube type code.\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35.\n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******.\n\nValues:\nTL = Tubeless \nTT= Tube type ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreMaxSpeedIndicator_35X2", "type": "string", "title": "35. Re<PERSON>n für Geschwindigkeiten über 300 km/h Achse II <15.2>", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "For tyres suitable for speeds in excess of 300 km/h. and motorcycle tyres, the service description shall be marked within brackets, for example,\"(95Y)\" .\n\n2015/166: 35.\n901/2014: ********.\n2003/37: *******.\n2015/504: *******.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyrePlyRatingNumber_35X2", "type": "float", "title": "35. <PERSON><PERSON><PERSON>agonalreifen Achse II <15.2>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "The ply-rating number of diagonal (bias-ply) tyres.\n\n2015/166: 35.\n901/2014: ********.\n2003/37: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreRemarks_35X2", "type": "string", "title": "35. Bemerkungen zum Reifen Achse II <15.2>", "maxLength": "200", "unit": "", "decimalPlaces": "", "tooltip": "Tyre remarks Please use this field to add remarks on the tyre specification.", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "RollingResistanceClassCode_35X2", "type": "string", "title": "35. Rollwiderstandsklasse Achse II <15.2>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Rolling Resistance Class. As stated on the Tyre Label\n\n999/2017: 35. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Reifen-/Felgenkombination der Achse III <15.3>", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zu den Achsen und Radaufhängungen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreSize_35X3", "type": "string", "title": "35. Reifengröße Achse III <15.3>", "maxLength": "20", "unit": "", "decimalPlaces": "", "tooltip": "Tyre size\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LoadCapacityIndexSingleWheel_35X3", "type": "float", "title": "35. Tragfähigkeit Achse III (einfach) <15.3>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Load capacity index single wheel\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "LoadCapacityIndexTwinWheel_35X3", "type": "float", "title": "35. Tragfähigkeit Achse III (zweifach) <15.3>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Load capacity index twin wheel\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014 : ********.\n2003/37/EC: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "SpeedCategorySymbol_35X3", "type": "string", "title": "35. Geschwindigkeitskategorie Achse III <15.3>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Speed category symbol\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35. \n2002/24/EC: 32.\n901/2014: ********\n2003/37/EC: *******\n2015/504: *******.\n\n\nExample valueset:\nA1,A2,A3,A4,A5,A6,A7,A8,B,C,D,E,F,G,J,K,L,M … etc ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TypeOfTyre_35X3", "type": "string", "title": "35. <PERSON><PERSON><PERSON><PERSON><PERSON> III <15.3>", "maxLength": "7", "unit": "", "decimalPlaces": "", "tooltip": "Type of tyre.\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35.\n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******.\t\n\nValues:\nAL   = Airless tyre\nAS   = All Season\nMC   = Motorcycle tyre\nMS   = M+S tyre\nMSE  = M+S tyre with spikes\nMSS  = M+S tyre studded\nMST  = Multi Service Tyre\nPAX  = PAX Runflat tyre\nRF   = Reinforced\nRFMS = Reinforced M&S tyre\nS    = Studded tyre\nXL   = Extra load\nXLMS = Extra load M&S Tyre ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreTubeTypeCode_35X3", "type": "string", "title": "35. Code des Reifenschlauchtyps Achse III <15.3>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Tyre tube type code.\n\n2007/46/EC: 32.\n371/2010: 35.\n183/2011 IAC: 35.\n2002/24/EC: 32.\n901/2014: ********.\n2003/37/EC: *******\n2015/504: *******.\n\nValues:\nTL = Tubeless \nTT= Tube type ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreMaxSpeedIndicator_35X3", "type": "string", "title": "35. Re<PERSON><PERSON> für Geschwindigkeiten über 300 km/h Achse III <15.3>", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "For tyres suitable for speeds in excess of 300 km/h. and motorcycle tyres, the service description shall be marked within brackets, for example,\"(95Y)\" .\n\n2015/166: 35.\n901/2014: ********.\n2003/37: *******.\n2015/504: *******.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyrePlyRatingNumber_35X3", "type": "float", "title": "35. <PERSON><PERSON><PERSON>alreifen Achse III <15.3>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "The ply-rating number of diagonal (bias-ply) tyres.\n\n2015/166: 35.\n901/2014: ********.\n2003/37: *******.\n2015/504: *******. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TyreRemarks_35X3", "type": "string", "title": "35. Bemerkunge<PERSON> zum Reifen Achse III <15.3>", "maxLength": "200", "unit": "", "decimalPlaces": "", "tooltip": "Tyre remarks Please use this field to add remarks on the tyre specification.", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "RollingResistanceClassCode_35X3", "type": "string", "title": "35. Rollwiderstandsklasse Achse III <15.3>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Rolling Resistance Class. As stated on the Tyre Label\n\n999/2017: 35. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Bremsen / brakes", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON> zu den Bremsen", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TrailerBrakeConnectionsCode", "type": "string", "title": "36. Anhängerbremsverbindung mechanisch/elektrisch/pneumatisch/hydraulisch", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Trailer brake connections code\n\n371/2010: 36.\n2015/504: 43.6.4\n2018/986: 43.6.4, 43.7.1\n2016/1789: 43.6.1\n\nValues: \n1LD = Single line\n2LD = Two line\nCPE = Combination pneumatic/electric (EBS)\nELC = Electric\nHYD = Hydraulic\nINE = Inertia\nMEC = Mechanical\nPNE = Pneumatic\nNON = None In case of an incomplete, complete or completed vehicle, equiped with a coupling and trailer brake connection state connection code\nIn case of an incomplete, complete or completed vehicle, which can be equiped with a coupling and is equiped with a trailer brake connection state connection code\nIn case of an incomplete, complete or completed vehicle and the vehicle can not be equiped with a trailer brake connection skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "PressFeedLineTwoLineBraking", "type": "float", "title": "37. <PERSON><PERSON> in der Versorgungsleitung (2) des Anhänger-Bremssystems", "maxLength": "7", "unit": "BAR", "decimalPlaces": 2, "tooltip": "Pressure in feed line for two line trailer braking system (two line) in bar\n\n2007/46/EC: 36.\n371/2010: 37. In case of an incomplete, complete or completed vehicle and the vehicle can not be equiped and two line trailer braking system state maximum value maximum value “0” or skip this entry in the xml.", "tabs": ["N1", "N2", "N3", "M2", "M3"]}, {"jsonKey": "PressFeedLineSingleLineBraking", "type": "float", "title": "37. <PERSON><PERSON> in der Versorgungsleitung (1) des Anhänger-Bremssystems", "maxLength": "7", "unit": "BAR", "decimalPlaces": 2, "tooltip": "Pressure in feed line for single line trailer braking system (single line) in bar\n\nOut of use, please use PressFeedLineBraking ", "tabs": ["N1", "N2", "N3", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Aufbau / Bodywork", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie <PERSON>aben zum Aufbau", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "CodeForBodywork", "type": "string", "title": "38. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Code) <4>", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "Code for bodywork\n\n2007/46/EC: 37.\n371/2010: 38.\n183/2011 IAC: 38. \n2015/504: 3.5.3.\n2016/1789: 3.5.3.\n\nPlease note that the values SA-SK have been moved to CodeForBodyworkSpecPurpVeh in accordance with 678/2011.\nFor agricultural vehicles use:\nDA = Semi-trailer\nDB = Drawbar trailer\nDC = Centre-axle trailer\nDE = Rigid drawbar trailer ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "NumberForBodywork", "type": "float", "title": "38. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Nummer) <4>", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Number for bodywork\n\n371/2010: 38.\n183/2011 IAC: 38. \n\nValues: \n1 Flat bed; \n2 Drop-side; \n3 Box body; \n4 Conditioned body with insulated walls and equipment to maintain the interior temperature; \n5 Conditioned body with insulated walls but without equipment to maintain the interior temperature; \n6 Curtain-sided; \n7 Swap body (interchangeable superstructure); \n8 Container carrier; \n9 Vehicles fitted with hook lift; \n10 Tipper; \n11 Tank; \n12 Tank intended for transport of dangerous goods; \n13 Livestock carrier; \n14 Vehicle transporter; \n15 Concrete mixer; \n16 Concrete pump vehicle; \n17 Timber; \n18 Refuse collection vehicle; \n19 Street sweeper, cleansing and drain clearing; \n20 Compressor; \n21 Boat carrier; \n22 Glider carrier; \n23 Vehicles for retail or display purposes; \n24 Recovery vehicle; \n25 Ladder vehicle; \n26 Crane lorry (other than a mobile crane as defined in Section 5 of Part A of Annex II); \n27 Aerial work platform vehicle; \n28 Digger derrick vehicle; \n29 Low floor trailer; \n30 Glazing transporter; \n31 Fire engine; \n99 Bodywork that is not included in the present list ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "ClassOfVehicleCode", "type": "string", "title": "39. Fahrzeugklasse", "maxLength": "5", "unit": "", "decimalPlaces": "", "tooltip": "Class of vehicle\n\n371/2010: 39.\n\nValues: \nI   = Class I\nII  = Class II\nIII = Class III\nA =  Class A\nB = Class B ", "tabs": ["M2", "M3"]}, {"jsonKey": "NumberOfDoors", "type": "float", "title": "41. <PERSON><PERSON><PERSON>", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Number of doors\n\n2007/46/EC: 41.\n371/2010: 41.\n183/2011 IAC: 41.  \n2002/24/EC: 41.\n901/2014: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "ConfigurationOfDoors", "type": "string", "title": "41. <PERSON><PERSON><PERSON><PERSON> der Türen", "maxLength": "40", "unit": "", "decimalPlaces": "", "tooltip": "Configuration of doors\n\n2007/46/EC: 41.\n371/2010: 41.\n183/2011 IAC: 41.  \n2002/24/EC: 41.\n901/2014: ********.\n\nIndicate the configuration by following codes:\nR\t= right side of the vehicle\nL\t= left side of the vehicle\nF\t= front side of the vehicle\nRE\t= rear side of the vehicle\n\nExample for a vehicle with 2 left side doors and 1 right door:\n2L, 1R ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "NrOfSeatingPositions", "type": "float", "title": "42. <PERSON><PERSON><PERSON> (einschließlich Fahrer)", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Number of seating positions (including the driver)\n\n2007/46/EC: 42.1.\n371/2010: 42.\n183/2011 IAC: 42.  \n2002/24/EC: 42.1.\n901/2014: 6.16.1. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "SeatForUseOnlyWhenTheVehStat", "type": "float", "title": "42.1 <PERSON><PERSON>(e), der (die) nur zur Verwendung bei stehendem Fahrzeug bestimmt ist (sind)", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Seat(s) designated for use only when the vehicle is stationary.\n\n371/2010:  42.1.\n183/2011 IAC: 42.1. ", "tabs": ["M1", "M2", "M3"]}, {"jsonKey": "NrOfPassSeatingPosLowerDeck", "type": "float", "title": "42.2 <PERSON><PERSON>hl <PERSON> (Unterdeck)", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Number of passenger seating positions - lower deck\n\n371/2010: 42.2. ", "tabs": ["M2", "M3"]}, {"jsonKey": "NrOfPassSeatingPosUpperDeck", "type": "float", "title": "42.2 Anzahl Fahrgastsitze (Oberdeck) (einschließlich Fahrer)", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Number of passenger seating positions - upper deck\n\n371/2010: 42.2. ", "tabs": ["M2", "M3"]}, {"jsonKey": "NrOfWheelchairUserAccessPos", "type": "float", "title": "42.3 Anzahl der für Rollstuhlbenutzer zugänglichen Positionen", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Number of wheelchair user accessible position.\n\n371/2010: 42.3.\n183/2011 IAC: 42.3 ", "tabs": ["M1", "M2", "M3"]}, {"jsonKey": "NumberOfStandingPlaces", "type": "float", "title": "43. <PERSON><PERSON><PERSON> der Stehplätze", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Number of standing places\n\n2007/46/EC: 42.3.\n371/2010: 43. ", "tabs": ["M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Anhängevorrichtung / Coupling device", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zur Anhängevorrichtung", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "ApprovalNrCouplingDevice", "type": "string", "title": "44. Genehmigungsnummer oder -zeichen der Anhängevorrichtung (sofern angebaut)", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Approval number or approval mark of coupling device (if fitted)\nmechnical coupling type (s) \n\n2007/46/EC: 43.1.\n371/2010: 44.\n183/2011 IAC: 44.\n2002/24/EC: 43.1\n901/2014: 7.2.8.\n2003/37/EC: 12.2.3.\n2015/504: 38.3. and 38.4. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M2", "M3"]}, {"jsonKey": "", "type": "headline", "title": "Umweltverträglichkeit", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie Angaben zur Umweltverträglichkeit", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "CouplingCharacteristicValueD", "type": "float", "title": "45.1. <PERSON><PERSON><PERSON><PERSON> D (sofern angebaut)", "maxLength": "6", "unit": "kN", "decimalPlaces": 2, "tooltip": "Mechanical coupling characteristic D value\n\n2007/46/EC: 43.4.\n371/2010: 45.1.\n2003/37/EC: 12.2.4.\n2015/504: 38.3 AND 38.4. To be used for complete vehicles of the vehicle categories M2-M3,N and O with a coupling device occurrence in the MechanicalCouplingTable.\nFor complete vehicles of the vehicle categories M2-M3, N and O with a coupling device occurrence in every MechanicalCouplingGroup at least one of the items CouplingCharacteristicValueD, CouplingCharacteristicValueDC, CouplingCharacteristicValueS, CouplingCharacteristicValueU, CouplingCharacteristicValueV must be present.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "CouplingCharacteristicValueDC", "type": "float", "title": "45.1. Kennwerte DC (sofern angebaut)", "maxLength": "6", "unit": "kN", "decimalPlaces": 2, "tooltip": "Mechanical coupling characteristic DC value\n\n2007/46/EC: 43.4.\n371/2010: 45.1.\n2003/37/EC: 12.2.4.\n2015/504: 38.3 AND 38.4. To be used for complete vehicles of the vehicle categories M2-M3,N and O with a coupling device occurrence in the MechanicalCouplingTable.\nFor complete vehicles of the vehicle categories M2-M3, N and O with a coupling device occurrence in every MechanicalCouplingGroup at least one of the items CouplingCharacteristicValueD, CouplingCharacteristicValueDC, CouplingCharacteristicValueS, CouplingCharacteristicValueU, CouplingCharacteristicValueV must be present.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "CouplingCharacteristicValueV", "type": "float", "title": "45.1. Kenn<PERSON><PERSON> V (sofern angebaut)", "maxLength": "6", "unit": "kN", "decimalPlaces": 2, "tooltip": "Mechanical coupling characteristic V value\n\n2007/46/EC: 43.4.\n371/2010: 45.1.\n2003/37/EC: 12.2.4. To be used for complete vehicles of the vehicle categories M2-M3,N and O with a coupling device occurrence in the MechanicalCouplingTable.\nFor complete vehicles of the vehicle categories M2-M3, N and O with a coupling device occurrence in every MechanicalCouplingGroup at least one of the items CouplingCharacteristicValueD, CouplingCharacteristicValueDC, CouplingCharacteristicValueS, CouplingCharacteristicValueU, CouplingCharacteristicValueV must be present.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "CouplingCharacteristicValueS", "type": "float", "title": "45.1. Kenn<PERSON><PERSON> S (sofern angebaut)", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Mechanical coupling characteristic S value\n\n2007/46/EC: 43.4.\n371/2010: 45.1.\n2003/37/EC: 12.2.4.\n2015/504: 38.3 AND 38.4. To be used for complete vehicles of the vehicle categories M2-M3,N and O with a coupling device occurrence in the MechanicalCouplingTable.\nFor complete vehicles of the vehicle categories M2-M3, N and O with a coupling device occurrence in every MechanicalCouplingGroup at least one of the items CouplingCharacteristicValueD, CouplingCharacteristicValueDC, CouplingCharacteristicValueS, CouplingCharacteristicValueU, CouplingCharacteristicValueV must be present.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "CouplingCharacteristicValueU", "type": "float", "title": "45.1. <PERSON><PERSON><PERSON><PERSON> U (sofern angebaut)", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Mechanical coupling characteristic U value\n\n2007/46/EC: 43.4.\n371/2010: 45.1.\n2003/37/EC: 12.2.4. To be used for complete vehicles of the vehicle categories M2-M3,N and O with a coupling device occurrence in the MechanicalCouplingTable.\nFor complete vehicles of the vehicle categories M2-M3, N and O with a coupling device occurrence in every MechanicalCouplingGroup at least one of the items CouplingCharacteristicValueD, CouplingCharacteristicValueDC, CouplingCharacteristicValueS, CouplingCharacteristicValueU, CouplingCharacteristicValueV must be present.", "tabs": ["N1", "N2", "N3", "O12", "O34", "M2", "M3"]}, {"jsonKey": "SoundLevelStationary", "type": "float", "title": "46. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (A)", "maxLength": "5", "unit": "dB(A)", "decimalPlaces": 2, "tooltip": "Sound level Stationary\n\n2007/46/EC: 45.\n371/2010: 46.\n183/2011 IAC: 46.\n2002/24/EC: 45.\n901/2014: *******.\n2016/1825: *******.\n2003/37/EC: 13.1.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "SoundLevelStatEngineSpeed", "type": "float", "title": "46. be<PERSON> <PERSON> Motor<PERSON>hl", "maxLength": "5", "unit": "1/min", "decimalPlaces": "", "tooltip": "Sound level Stationary engine speed\n\n2007/46/EC: 45.\n371/2010: 46.\n183/2011 IAC: 46.\n2002/24/EC: 45.\n901/2014: *******.\n2016/1825: *******.\n2003/37/EC: 13.1.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "SoundLevelDriveBy", "type": "float", "title": "46. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (A)", "maxLength": "5", "unit": "dB(A)", "decimalPlaces": 2, "tooltip": "Sound level Drive-by\n\n371/2010: 46.\n183/2011 IAC: 46.\n2002/24/EC: 45.\n901/2014: *******.\n2016/1825: *******.\n2003/37/EC: 13.2.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "ExhaustEmissionLevelEuro", "type": "string", "title": "47. Emissionsklasse: Euro", "maxLength": "10", "unit": "", "decimalPlaces": "", "tooltip": "Exhaust emission level Euro\n\n371/2010: 47.\n183/2011 IAC: 47.\n2013/60/EU: 46.1\n901/2014: 4.0.1. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "EmisTestMassWLTP", "type": "float", "title": "47.1.1. <PERSON>r<PERSON>fmasse (kg)", "maxLength": "6", "unit": "kg", "decimalPlaces": "", "tooltip": "Test mass of the vehicle\n\n2017/1151:  47.1.1. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "FrontalAreaWLTP", "type": "float", "title": "47.1.2. Querschnittsfläche (m²)", "maxLength": "8", "unit": "m^2", "decimalPlaces": 6, "tooltip": "Frontal area of the vehicle.\n\n\n2018/1832:  47.1.2. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "ProjectFrontAreaAirFrontGrill", "type": "float", "title": "********. Voraussichtliche Querschnittsfläche des Lufteinlasses am Kühlergrill", "maxLength": "6", "unit": "cm^2", "decimalPlaces": "", "tooltip": "Projected frontal area of air entrance of the front grille (if applicable), cm2: …\n\n2017/1151: ********. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "RoadLoadCoefF0WLTP", "type": "float", "title": "********. f0, N", "maxLength": "9", "unit": "N", "decimalPlaces": 6, "tooltip": "f0 is the constant road load coefficient used in in the WLTP test performed with the vehicle.\n\n\n2017/1151:  ********. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "RoadLoadCoefF1WLTP", "type": "float", "title": "********. f1, N/(km/h)", "maxLength": "9", "unit": "N/(km/h)", "decimalPlaces": 6, "tooltip": "f1 is the first order road load coefficient used in in the WLTP test performed with the vehicle.\n\n2017/1151:  ********. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "RoadLoadCoefF2WLTP", "type": "float", "title": "********. f2, N/(km/h)²", "maxLength": "9", "unit": "N/(km/h)^2", "decimalPlaces": 6, "tooltip": "f2 is the second order road load coefficient used in in the WLTP test performed with the vehicle.\n\n2017/1151:  ********. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "DrivingCycleClassCode_47_2_1X1", "type": "string", "title": "47.2.1. Fahrzyklusklasse 1", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Drive cycle class: … to identify the drive cycle class, which depends on the Power/Weight ratio of the vehicle and the max. velocity \n\n2017/1151: 47.2.1.\n\nValues:\n1 \t= Class 1 <= 22 W/kg\n2 \t= Class 2 > 22 <= 34 W/kg\n3a \t= Class 3a > 34 W/kg Vmax < 120 km/h\n3b \t= Class 3b > 34 W/kg Vmax >= 120 km/h ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "DrivingCycleClassCode_47_2_1X2", "type": "string", "title": "47.2.1. Fahrzyklusklasse 2", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Drive cycle class: … to identify the drive cycle class, which depends on the Power/Weight ratio of the vehicle and the max. velocity \n\n2017/1151: 47.2.1.\n\nValues:\n1 \t= Class 1 <= 22 W/kg\n2 \t= Class 2 > 22 <= 34 W/kg\n3a \t= Class 3a > 34 W/kg Vmax < 120 km/h\n3b \t= Class 3b > 34 W/kg Vmax >= 120 km/h ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "DrivingCycleClassCode_47_2_1X3a", "type": "string", "title": "47.2.1. Fahrzyklusklasse 3a", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Drive cycle class: … to identify the drive cycle class, which depends on the Power/Weight ratio of the vehicle and the max. velocity \n\n2017/1151: 47.2.1.\n\nValues:\n1 \t= Class 1 <= 22 W/kg\n2 \t= Class 2 > 22 <= 34 W/kg\n3a \t= Class 3a > 34 W/kg Vmax < 120 km/h\n3b \t= Class 3b > 34 W/kg Vmax >= 120 km/h ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "DrivingCycleClassCode_47_2_1X3b", "type": "string", "title": "47.2.1. Fahrzyklusklasse 3b", "maxLength": "3", "unit": "", "decimalPlaces": "", "tooltip": "Drive cycle class: … to identify the drive cycle class, which depends on the Power/Weight ratio of the vehicle and the max. velocity \n\n2017/1151: 47.2.1.\n\nValues:\n1 \t= Class 1 <= 22 W/kg\n2 \t= Class 2 > 22 <= 34 W/kg\n3a \t= Class 3a > 34 W/kg Vmax < 120 km/h\n3b \t= Class 3b > 34 W/kg Vmax >= 120 km/h ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "DownscalingFactor", "type": "float", "title": "47.2.2. Miniatisierungsfaktor", "maxLength": "5", "unit": "", "decimalPlaces": 3, "tooltip": "Downscaling  factor (fdsc)\nDownscaling factor: calculated factor in case the vehicle can not follow the speed / time requirements of the driving cycle.\n\n2017/1151: 47.2.2. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "CappedSpeedIndicator", "type": "string", "title": "47.2.3. Begrenzte Geschwindigkeit", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "Capped speed: field to indicate if de maximum speed of the vehicle is lower than the maximum speed of the cycle. The vehicle would be technically able to follow the speed trace of the cycle but are capped by a speed limiter.\n\n2017/1151: 47.2.3.\n\nPossible values:\nY = Yes\nN = No ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "NrBaseRegulActLastAmendMotVeh", "type": "string", "title": "48. Abgasverhalten: Nummer des Rechtsakts und des letzten für die EG-Typgenehmigung gültigen Änderungsrechtsakts", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Number of the base regulatory act and latest amending regulatory act applicable (exhaust emissions) for motor vehicles.\nincl. Indication for implementation stage, e.g.   '715/2007*566/2011N'\n\n2007/46/EC: 46.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: 3.2.15 Either this field is mandatory for vehicles with a WVTA and a combustion engine or the NrBaseRegulActLastAmendEngines depending on the method of testing. The field must always contain the character corresponding to the provisions used for the type approval.\nIn some member states environmental zones for entering citycentres are based on this field.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "NrBaseRegulActLastAmendEngines", "type": "string", "title": "48. Abgasverhalten: Nummer des Rechtsakts und des letzten für die EG-Typgenehmigung gültigen Änderungsrechtsakts", "maxLength": "35", "unit": "", "decimalPlaces": "", "tooltip": "Number of the base regulatory act and latest amending regulatory act applicable (exhaust emissions) for engines.\nincl. Indication for implementation stage, e.g. '2055/55*2008/74J'\n\n2007/46/EC: 46.1.\n371/2010: 48.\n2003/37/EC: 15.\n2015/504: NN Either this field is mandatory for vehicles with a WVTA and a combustion engine or the NrBaseRegulActLastAmendMotVeh depending on the method of testing. The field must always contain the character corresponding to the provisions used for the type approval.\nIn some member states environmental zones for entering citycentres are based on this field.", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "SmokeCorrectedAbsorptionCoeff", "type": "float", "title": "48.1. <PERSON><PERSON> k<PERSON>rig<PERSON>ter Wert des Absorptionskoeffizienten", "maxLength": "9", "unit": "1/m", "decimalPlaces": 5, "tooltip": "Smoke corrected absorption coefficient.\n\n2007/46/EC: 46.1\n371/2010: 48.1.\n2002/24/EC: 46.\n901/2014: ********\n2003/37/EC: 15.1. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1CO_48_1_1", "type": "float", "title": "48.1.1. CO", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - CO\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L"]}, {"jsonKey": "TestprocType1HC_48_1_1", "type": "float", "title": "48.1.1. HC", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - HC\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1NOx_48_1_1", "type": "float", "title": "48.1.1. NOx", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - Nox\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1HC_Nox_48_1_1", "type": "float", "title": "48.1.1. HC + NOx", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - HC + NOx \n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1Particulates_48_1_1", "type": "float", "title": "48.1.1. <PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - Particulates\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEscCO", "type": "float", "title": "48.1.1. CO", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ESC - CO\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEscTHC", "type": "float", "title": "48.1.1. HC", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ESC - THC\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEscNOx", "type": "float", "title": "48.1.1. NOx", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ESC - NOx\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEscParticulates", "type": "float", "title": "48.1.1. <PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ESC - Particulates\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocElrSmokeValue", "type": "float", "title": "48.1.1. <PERSON><PERSON><PERSON><PERSON><PERSON> (ELR)", "maxLength": "9", "unit": "1/m", "decimalPlaces": 5, "tooltip": "Testprocedure ELR - Smoke value\n\n371/2010: 48. ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1CO_48_1_2", "type": "float", "title": "48.1.2. CO", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - CO\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L"]}, {"jsonKey": "TestprocType1HC_48_1_2", "type": "float", "title": "48.1.2. THC", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - HC\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1NOx_48_1_2", "type": "float", "title": "48.1.2. NOx", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - Nox\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1NMHC", "type": "float", "title": "48.1.2. NMHC", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - NMHC\n\n371/2010: 48.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "L", "M1", "M2"]}, {"jsonKey": "TestprocType1HC_Nox_48_1_2", "type": "float", "title": "48.1.2. HC + NOx", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - HC + NOx \n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n2002/24/EC: 46.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1Particulates_48_1_2", "type": "float", "title": "48.1.2. <PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "g/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - Particulates\n\n2007/46/EC: 46.1.1.\n371/2010: 48.\n901/2014: ********.\n2016/1825: ********. ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}, {"jsonKey": "TestprocType1NrOfParticles", "type": "float", "title": "48.1.2. <PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "1/km", "decimalPlaces": 5, "tooltip": "Testprocedure Type I - Number of Particles\n\n371/2010: 48. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscCO", "type": "float", "title": "48.1.2. CO", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - CO\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["M1", "M2"]}, {"jsonKey": "TestprocWhscTHC", "type": "float", "title": "48.1.2. THC", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - THC\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscNOx", "type": "float", "title": "48.1.2. NOx", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - NOx\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscNMHC", "type": "float", "title": "48.1.2. NMHC", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - NMHC\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscNH3", "type": "float", "title": "48.1.2. NH3", "maxLength": "9", "unit": "ppm", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - NH3\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscParticulates", "type": "float", "title": "48.1.2. <PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - Particulates\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscNumberOfParticles", "type": "float", "title": "48.1.2. <PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "1/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHSC - Number of Particles\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocWhscCO2", "type": "float", "title": "48.1.2. CO", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure Whsc- CO2\n\n2015/504: NN ", "tabs": ["N1", "N2"]}, {"jsonKey": "TestprocWhscHC_NOx", "type": "float", "title": "48.1.2. HC + NOx", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure Whsc- HC_Nox\n\n2015/504: NN ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocRdeCompleteRdeNOx", "type": "float", "title": "48.2. Vollständige RDE-Fahrt: Nox", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Complete RDE trip: NOx: \n\n2017/1154:  48.2 ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocRdeCompleteRdeNrOfPart", "type": "float", "title": "48.2. Vollständige RDE-Fahrt: <PERSON><PERSON>l (Anzahl)", "maxLength": "9", "unit": "1/km", "decimalPlaces": 5, "tooltip": "Complete RDE trip: Particles (number)\n\n2017/1154:  48.2 ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocRdeUrbanRdeNOx", "type": "float", "title": "48.2. Innerstädtische RDE-Fahrt: Nox", "maxLength": "8", "unit": "mg/km", "decimalPlaces": 2, "tooltip": "Urban RDE trip: NOx: \n\n2017/1154:  48.2 ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocRdeUrbanRdeNrOfPart", "type": "float", "title": "48.2. Innerstädtische RDE-Fahrt: Partikel (Anzahl)", "maxLength": "9", "unit": "1/km", "decimalPlaces": 5, "tooltip": "Urban RDE trip: Particles (number):\n\n2017/1154:  48.2 ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TestprocEtcCO", "type": "float", "title": "48.2.1. CO", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ETC - CO\n\n2007/46/EC: *******.\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEtcNOx", "type": "float", "title": "48.2.1. Nox", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ETC - NOx\n\n2007/46/EC: *******.\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEtcNMHC", "type": "float", "title": "48.2.1. NMHC", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ETC - NMHC\n\n2007/46/EC: *******.\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEtcTHC", "type": "float", "title": "48.2.1. THC", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ETC - THC\n\n2007/46/EC: *******.\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEtcCH4", "type": "float", "title": "48.2.1. CH4", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ETC - CH4\n\n2007/46/EC: *******.\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocEtcParticulates", "type": "float", "title": "48.2.1. <PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "g/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure ETC - Particulates\n\n2007/46/EC: *******.\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcCO", "type": "float", "title": "48.2.2. CO", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - CO\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcNOx", "type": "float", "title": "48.2.2. Nox", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - NOx\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcNMHC", "type": "float", "title": "48.2.2. NMHC", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - NMHC\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcTHC", "type": "float", "title": "48.2.2. T<PERSON>", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - THC\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcCH4", "type": "float", "title": "48.2.2. CH4", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - CH4\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcNH3", "type": "float", "title": "48.2.2. NH3", "maxLength": "9", "unit": "ppm", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - NH3\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcParticulates", "type": "float", "title": "48.2.2. <PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "mg/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - Particulates\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcNumberOfParticles", "type": "float", "title": "48.2.2. <PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "9", "unit": "1/kWh", "decimalPlaces": 5, "tooltip": "Testprocedure WHTC - Number of Particles\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "TestprocWhtcExponentParticles", "type": "float", "title": "48.2.2. <PERSON><PERSON><PERSON><PERSON>hlexponent", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "The exponent in the scientific notation of TestprocWhtcNumberOfParticles. E.g. the Number of Particles is 1,23 x 10^12, then TestprocWhtcNumberOfParticles is 1,23 and TestprocWhtcExponentParticles is 12\n\n371/2010: 48.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "M1", "M2", "M3"]}, {"jsonKey": "UrbanConditionsCO2", "type": "float", "title": "49.1. <PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "g/km", "decimalPlaces": "", "tooltip": "Urban conditions CO2\n\n2007/46/EC: 46.2.\n371/2010: 49. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "UrbanConditionsFuelConsumption", "type": "float", "title": "49.1. Innerorts l/100 km / m3/100 km", "maxLength": "4", "unit": "", "decimalPlaces": 2, "tooltip": "Urban conditions fuel consumption\n\n2007/46/EC: 46.2.\n371/2010: 49.\n\nDepending on the fuel type a different Unit is applicable:\n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "ExtraUrbanConditionsCO2", "type": "float", "title": "49.1. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "g/km", "decimalPlaces": "", "tooltip": "Extra-urban conditions CO2\n\n2007/46/EC: 46.2.\n371/2010: 49. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "ExtraUrbanConditionsFuelCons", "type": "float", "title": "49.1. Außerorts l/100 km / m3/100 km", "maxLength": "4", "unit": "", "decimalPlaces": 2, "tooltip": "Extra-urban conditions fuel consumption\n\n2007/46/EC: 46.2.\n371/2010: 49.\n\nDepending on the fuel type a different unit is applicable:\n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "CombinedCO2", "type": "float", "title": "49.1. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "g/km", "decimalPlaces": "", "tooltip": "Combined CO2\n\n2007/46/EC: 46.2.\n371/2010: 49.\n183/2011 IAC: 49. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "CombinedFuelConsumption", "type": "float", "title": "49.1. <PERSON><PERSON><PERSON><PERSON> l/100 km / m3/100 km", "maxLength": "4", "unit": "", "decimalPlaces": 2, "tooltip": "Combined fuel consumption\n\n2007/46/EC: 46.2.\n371/2010: 49.\n183/2011 IAC: 49.\n\nDepending on the fuel type a different Unit is applicable:\n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air Use for non hybrid, non plugin (NOVC) vehicles only.", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WeightedCombinedCO2", "type": "float", "title": "49.1. Weighted, combined", "maxLength": "3", "unit": "g/km", "decimalPlaces": "", "tooltip": "Weighted, combined CO2\n\n371/2010: 49.\n183/2011 IAC: 49.\n901/2014: *******. This field has to be used for the off vehicle charging vehicles (see also: OffVehicleChargingVehInd).", "tabs": ["N1", "N2", "L", "M1", "M2"]}, {"jsonKey": "WeightedCombinedFuelCons", "type": "float", "title": "49.1. Weighted, combined (l/100 km)", "maxLength": "5", "unit": "", "decimalPlaces": 3, "tooltip": "Weighted, combined fuel consumption\n\n371/2010: 49.\n183/2011 IAC: 49.\n901/2014: *******.\n\nDepending on the fuel type a different Unit is applicable:\n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air This field has to be used for the off vehicle charging vehicles (see also: OffVehicleChargingVehInd).", "tabs": ["N1", "N2", "L", "M1", "M2"]}, {"jsonKey": "NEDCDeviationFactor", "type": "float", "title": "49.1. <PERSON><PERSON>weichungsfaktor (falls zutreffend)", "maxLength": "7", "unit": "", "decimalPlaces": 5, "tooltip": "NEDC Deviation factor (also negative values possible)\n\n2017/1151: 49. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "NEDCVerificationFactor", "type": "float", "title": "49.1. Differenzierungsfaktor (falls zutreffend) ‚1‘ oder ‚0‘", "maxLength": "1", "unit": "", "decimalPlaces": "", "tooltip": "NEDC Verification factor\n\n2017/1151: 3.2.8.\n\nValues:\n0 = Conform\n1 = Not conform ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "ElectricEnergyConsPureElectric", "type": "float", "title": "49.2. <PERSON><PERSON><PERSON><PERSON><PERSON> (gewichtet)", "maxLength": "7", "unit": "Wh/km", "decimalPlaces": 2, "tooltip": "Electric energy consumption pure electric\n\n371/2010: 49.\n183/2011 IAC: 49.\n901/2014: *******.\n2016/1825: 4.0.4. ", "tabs": ["N1", "N2", "L", "M1", "M2"]}, {"jsonKey": "ElectricEnergyConsWeightedComb", "type": "float", "title": "49.2. <PERSON><PERSON><PERSON><PERSON><PERSON> (kombiniert)", "maxLength": "7", "unit": "Wh/km", "decimalPlaces": 2, "tooltip": "Electric energy consumption weighted, combined\n\n371/2010: 49.\n183/2011 IAC: 49.\n901/2014: *******.\n2016/1825: 4.0.4. Use this entry only for OVC hybrid vehicles.", "tabs": ["N1", "N2", "L", "M1", "M2"]}, {"jsonKey": "ElectricRange", "type": "float", "title": "49.2. <PERSON><PERSON><PERSON> elektrisch", "maxLength": "5", "unit": "km", "decimalPlaces": "", "tooltip": "Electric range for pure electric vehicles.\n\n371/2010: 49.\n901/2014: *******.\n2016/1825: 4.0.5.. Use this entry only for pure electric vehicles", "tabs": ["N1", "N2", "L", "M1", "M2"]}, {"jsonKey": "VehicleFittedWithEcoInnovInd", "type": "string", "title": "49.3. Fahrzeug mit Ökoinnovation(en) ausgestattet", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Vehicle fitted with eco-innovation(s) indicator.\n\n195/2013: 49.3.\n\nValues:\nY = Yes\nN = No ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "GeneralCodeOfTheEcoInnovations", "type": "string", "title": "49.3.1. Allgemeiner Code der Ökoinnovation(en)", "maxLength": "120", "unit": "", "decimalPlaces": "", "tooltip": "General code of the eco-innovation(s)\n\n195/2013: 49.3.1. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TotalCO2EmisSavDueEcoInnFuel", "type": "float", "title": "******** Einsparungen durch NEFZ (falls zutreffend)", "maxLength": "5", "unit": "g/km", "decimalPlaces": 2, "tooltip": "Total CO2 emissions saving due the eco-innovation(s).\n\n195/2013: 49. 3.2. From Version 1.1 of the XSD scheme this field has to be used in stead of \"TotalCO2EmisSavingDueEcoInnov\". The new field is defined within the fuelgroup.", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "TotalCO2WLTPEmSavDueEcoInnFuel", "type": "float", "title": "******** Einsparungen durch WLTP (falls zutreffend)", "maxLength": "5", "unit": "g/km", "decimalPlaces": 2, "tooltip": "Total CO2 emissions saving due the eco-innovation(s) in WLTP-test.\n\n2017/1151: 3.2.2 . This field has to be used instead of TotalCO2WLTPEmisSavDueEcoInnov.", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPLowCO2", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON>", "maxLength": "6", "unit": "g/km", "decimalPlaces": 2, "tooltip": "All power trains, CO2 emissions  of the WLTP low speed testing phase\n\n2017/1151: 4. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPMediumCO2", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 2, "tooltip": "All power trains,  CO2 emissions of the WLTP medium speed testing phase \n\n2017/1151: 4. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPHighCO2", "type": "float", "title": "49.4. <PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 2, "tooltip": "All power trains,  CO2 emissions of the WLTP high speed testing phase \n\n2017/1151: 4. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPExtraHighCO2", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 2, "tooltip": "All power trains,  CO2 emissions of the WLTP extra high speed testing phase \n\n2017/1151: 4. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPCombinedCO2", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "g/km", "decimalPlaces": "", "tooltip": "All power trains,  CO2 emissions of the WLTP combined testing phase \n\n2017/1151: 4. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPWeightedCombinedCO2", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "maxLength": "3", "unit": "g/km", "decimalPlaces": "", "tooltip": "All power trains, CO2 emissions weighted combined  of the WLTP test result \n\n2017/1151: 4. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPLowFuelConsumption", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 3, "tooltip": "WLTP fuel consumption of the WLTP low speed testing phase  \n\n2017/1151: 4.\n\nDepending on the fuel type a different Unit is applicable: \n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPMediumFuelConsumption", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 3, "tooltip": "WLTP fuel consumption of the WLTP medium speed testing phase \n\n2017/1151: 4.\n\nDepending on the fuel type a different Unit is applicable: \n\nFuelcode Unit \nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPHighFuelConsumption", "type": "float", "title": "49.4. <PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 3, "tooltip": "WLTP fuel consumption of the WLTP high speed testing phase \n\n2017/1151: 4.\n\nDepending on the fuel type a different Unit is applicable: \n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPExtraHighFuelConsumption", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 3, "tooltip": "WLTP fuel consumption of the WLTP extra high speed testing phase  \n\n2017/1151: 4.\n\nDepending on the fuel type a different Unit is applicable: \n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPCombinedFuelCons", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 3, "tooltip": "WLTP fuel consumption of the WLTP combined speed testing phase \n\n2017/1151: 4.\n\nDepending on the fuel type a different Unit is applicable: \n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPWeightedCombinedFuelCons", "type": "float", "title": "49.4. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "maxLength": "5", "unit": "g/km", "decimalPlaces": 3, "tooltip": "WLTP fuel consumption weighted combined of the WLTP test result \n\n2017/1151: 4.\n\nDepending on the fuel type a different Unit is applicable: \n\nFuelcode\t\t\tUnit\n10 - 13\tPetrol\t\t\tl/100 km\n15 - 18\tEthanol\t\t\tl/100 km\n19\tMixture\t\t\tl/100 km\n20 - 26\tDiesel \t\t\tl/100 km\n30\tLPG\t\t\tl/100 km\n40 - 43\tCNG\t\t\tm^3/100 km\n44 \tBiomethane\t\tm^3/100 km\n50 \tHydrogen\t\tkg/100 km\n55 \tH2NG\t\t\tm^3/100 km\n60 - 63\tLNG\t\t\tkg/100 km\n72 \tHe-15\t\t\tl/100 km\n81 - 87\tDiesel B5 - B50\t\tl/100 km\n90 \tOther\t\t\t-\n91 \tCompressed air ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPElecEnergyConsPureElectric", "type": "float", "title": "49.5.1. <PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "7", "unit": "Wh/km", "decimalPlaces": 2, "tooltip": "WLTP electric energy consumption pure electric \n\n2017/1151: 5.1. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPElectricRangePureElectric", "type": "float", "title": "49.5.1. Elektrische Reichweite", "maxLength": "5", "unit": "km", "decimalPlaces": "", "tooltip": "WLTP electric range pure electric \n\n2017/1151: 5.1. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPElectricRangeCityPureElec", "type": "float", "title": "49.5.1. Elektrische Reichweite innerorts", "maxLength": "5", "unit": "km", "decimalPlaces": "", "tooltip": "WLTP electric range city pure electric \n\n2017/1151: 5.1. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPElectEnergyConsExternCharg", "type": "float", "title": "49.5.2. <PERSON><PERSON><PERSON><PERSON><PERSON> (EC/AC, weighted)", "maxLength": "7", "unit": "Wh/km", "decimalPlaces": 2, "tooltip": "WLTP electric energy consumption externally chargeable \n\n2017/1151: 5.2. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPElectricRangeExternCharg", "type": "float", "title": "49.5.2. Elektrische Reichweite (EAER)", "maxLength": "5", "unit": "km", "decimalPlaces": "", "tooltip": "WLTP equivalent electric range externally chargeable   \n\n2020/683: 49.5. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "WLTPElectricRangeCityExtCharg", "type": "float", "title": "49.5.2. Elektrische Reichweite innerorts (EAER city)", "maxLength": "5", "unit": "km", "decimalPlaces": "", "tooltip": "WLTP equivalent electric range city externally chargeable \n\n2020/683: 49.5. ", "tabs": ["N1", "N2", "M1", "M2"]}, {"jsonKey": "", "type": "headline", "title": "Sonstige / Miscellaneous", "maxLength": "", "unit": "", "decimalPlaces": "", "tooltip": "Hier finden sie sonstige Angaben zum Fahrzeug", "tabs": ["N1", "N2", "N3", "O12", "O34", "L", "M1", "M2", "M3"]}, {"jsonKey": "TypeApprTranspDangerGoodsInd", "type": "string", "title": "50. EG-typgenehmigt nach den Konstruktionsvorschriften für die Beförderung gefährlicher Güter", "maxLength": "1", "unit": "Y/N", "decimalPlaces": "", "tooltip": "Type approved according to the design requirements for transporting dangerous goods indicator\n\n2007/46/EC: 48.1\n371/2010: 50.\n\nValues: \nY =  Yes\nN = No ", "tabs": ["N1", "N2", "N3", "O12", "O34"]}, {"jsonKey": "CodeForBodyworkSpecPurpVeh", "type": "string", "title": "51. <PERSON><PERSON><PERSON><PERSON><PERSON> mit besonderer Zweckbestimmung: Bezeichnung in Übereinstimmung mit Anh.II Absch.5", "maxLength": "2", "unit": "", "decimalPlaces": "", "tooltip": "For special purpose vehicles: designation in accordance with Annex II of regulation 2007/46/EC.\n\n371/2010:  51.\n\nPlease note that the values SA-SK have been moved from CodeForBodywork in accordance with 678/2011. ", "tabs": ["N1", "N2", "N3", "O12", "O34", "M1", "M2", "M3"]}, {"jsonKey": "CodeEmissionCategory", "type": "string", "title": "52. Code (<PERSON><PERSON><PERSON>ffs<PERSON>l.)zu V.9 (RL) und/oder 14 (Klartext)", "maxLength": "10", "unit": "", "decimalPlaces": "", "tooltip": "German emission code. Important issue to calculate the tax.\nValueset 0-9, A-Z\n\nRequested by Germany. ", "tabs": ["L"]}, {"jsonKey": "RemarksExceptions", "type": "string", "title": "52. Bemerkungen und Ausnahmen", "maxLength": "378", "unit": "", "decimalPlaces": "", "tooltip": "Special remarks and exception concerning the vehicle, for example the technically permissible maximum mass of the combination. ", "tabs": ["O12", "O34", "L"]}, {"jsonKey": "Remarks", "type": "string", "title": "52. <PERSON><PERSON><PERSON><PERSON><PERSON>", "maxLength": "2000", "unit": "", "decimalPlaces": "", "tooltip": "Remarks\n\n2007/46/EC: 50.\n371/2010: 52.\n183/2011 IAC: 52. \n2002/24/EC: 50.\n901/2014: 9.1.\n2003/37/EC: 17.\n2015/504: NN ", "tabs": ["N1", "N2", "N3", "L", "M1", "M2", "M3"]}]