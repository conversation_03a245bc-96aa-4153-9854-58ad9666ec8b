<?php
require_once __DIR__ . '/../../printout.helper.php';

class C_ZB2CocDokument
{
    /**
     * @throws Exception
     */
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $filePath = __DIR__ . '/data.json';
        if (!file_exists($filePath)) {
            throw new Exception("File not found: $filePath");
        }
        $data = json_decode(file_get_contents($filePath), true);

        $templateData = [];
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $document = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $rnr = $document['fullDocument']['documentRelKey1'];
        $templateData['vehicleResource'] = $this->getVehicleResource($rnr, $curl);
        $templateData['vehicleProperty'] = $this->getVehicleProperty($rnr, $curl);
        $cocValues = $this->getAllCocValues($data);
        $preparedData = $this->prepareData($data, $cocValues);

        $templateData['firstPage'] = $this->prepareFirstPage($preparedData);
        $templateData['otherPages'] = $this->prepareOtherPages($preparedData);
        $templateData['headlines'] = $this->getHeadlines($data);

        return $templateData;
    }

    /**
     * @return array<mixed>|null
     */
    private function getVehicleResource(string $rnr, PrintoutCurl $curl): ?array
    {
        return json_decode($curl->_simple_call('get', "v1/zb2vehicles/select_resource/$rnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    /**
     * @return array<mixed>
     */
    private function getVehicleProperty(string $rnr, PrintoutCurl $curl): array
    {
        $response = json_decode($curl->_simple_call('get', "v1/zb2vehicles/properties?rnr=$rnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);

        if ($response)
            return end($response);

        return [];
    }

    /**
     * @param array<mixed> $data
     * @return array<string>
     */
    private function getAllCocValues(array $data): array
    {
        $coc = [];
        foreach ($data as $child) {
            if ($child['type'] == 'headline')
                continue;
            $coc[] = $this->getFirstWord($child['title']);
        }

        return array_unique($coc);
    }

    /**
     * @param array<mixed> $data
     * @param array<string> $cocValues
     * @return array<string, mixed>
     */
    private function prepareData(array $data, array $cocValues): array
    {
        $returnData = [];
        foreach ($cocValues as $coc) foreach ($data as $da) {
            if ($da['type'] == 'headline')
                continue;
            $dataCoc = $this->getFirstWord($da['title']);

            if ($dataCoc === $coc) {
                $temp = [
                    'jsonKey' => $da['jsonKey'],
                    'title' => $da['title'],
                    'coc' => $coc,
                    'unit' => $da['unit']
                ];
                $returnData[$coc][] = $temp;
            }
        }
        return $returnData;
    }

    private function getFirstWord(string $data): string
    {
        $exploded = explode(" ", $data);
        return $exploded[0];
    }

    /**
     * @param array<string, mixed> $data
     * @return array<string, mixed>
     */
    private function prepareFirstPage(array $data): array
    {
        $returnData = [];
        foreach ($data as $key => $value) {
            if (intval($key) == 0) {
                $returnData[$key] = $value;
            }
        }
        return $returnData;
    }

    /**
     * @param array<string, mixed> $data
     * @return array<string, mixed>
     */
    private function prepareOtherPages(array $data): array
    {
        $returnData = [];
        foreach ($data as $key => $value) {
            if (intval($key) != 0) {
                $returnData[$key] = $value;
            }
        }
        return $returnData;
    }

    /**
     * @param array<mixed> $data
     * @return array<string, string>
     */
    private function getHeadlines(array $data): array
    {
        $headlines = [];

        $lastChild = null;
        foreach ($data as $child) {
            if ($child['type'] === "headline") {
                $headlines[explode(" ", $lastChild['title'])[0]] = $child['title'];
            }
            $lastChild = $child;
        }

        return $headlines;
    }
}