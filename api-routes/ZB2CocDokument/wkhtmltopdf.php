<?php
require_once __DIR__ . '/../../Pdf.php';

Pdf::$principal = 'zb2_online_test';
Pdf::$loginPnr = '1';
Pdf::$loginPassword = 'zb2021';
Pdf::$apiUrl = 'https://api.baubuddy.de/index.php/';

Pdf::$additionalArguments = [
    "--disable-smart-shrinking",
    "--no-pdf-compression",
    "--page-size", "a4",
    "--margin-bottom", "10mm",
    "--margin-top", "10mm",
    "--margin-left", "10mm",
    "--margin-right", "10mm",
    "--dpi", "300"
];
Pdf::compile(__DIR__, [
    'rnr' => 37
]);

Pdf::$outputFile = 'output-rnr-21.pdf';
Pdf::compile(__DIR__, [
    'rnr' => 21
]);

Pdf::$outputFile = 'output-rnr-22.pdf';
Pdf::compile(__DIR__, [
    'rnr' => 22
]);

Pdf::$outputFile = 'output-rnr-23.pdf';
Pdf::compile(__DIR__, [
    'rnr' => 23
]);

Pdf::$outputFile = 'output-rnr-30.pdf';
Pdf::compile(__DIR__, [
    'rnr' => 30
]);

Pdf::$outputFile = 'output-rnr-31.pdf';
Pdf::compile(__DIR__, [
    'rnr' => 31
]);

Pdf::$outputFile = 'output-rnr-33.pdf';
Pdf::compile(__DIR__, [
    'rnr' => 33
]);
