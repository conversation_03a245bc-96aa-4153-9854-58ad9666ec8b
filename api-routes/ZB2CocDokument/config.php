<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "schemaId", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentId", required: true);
$config->wkhtmltopdfArguments = [
    "--disable-smart-shrinking",
    "--no-pdf-compression",
    "--page-size", "a4",
    "--margin-bottom", "10mm",
    "--margin-top", "10mm",
    "--margin-left", "10mm",
    "--margin-right", "10mm",
    "--dpi", "300"
];
return $config;