<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ScaffoldListOversizes())->getData($_GET['projectNo'], $_GET['contractServiceSheetId'], $_GET['showPrices']);
}
?>

<!doctype html>
<html lang="<?= PrintoutHelper::getLanguage() ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>ScaffoldList Oversizes</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body>
<table class="logo-table">
    <tr>
        <td class="font-bold">OOO VEROSCAFFOLDING
            <span class="header-text">
                <?= PrintoutHelper::lang('uzbekistan') ?>
            </span>
        </td>
        <td class="logo-container">
            <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/logo.jpg") ?>" alt="logo">
        </td>
    </tr>
</table>
<div class="header-background">
    <div class="header-spacer"></div>
    <hr class="header-line">
    <div class="header-spacer-small"></div>
    <span class="oversize-title">
        <b>
            <?= PrintoutHelper::lang('oversize_no.') ?>
            <?= $data['scaffoldLists'][0]['rvaufnr'] . '-' . $data['scaffoldLists'][0]['rvteilaufnr'] . '-' . $data['scaffoldLists'][0]['rvuaufnr'] ?>
        </b>
    </span>
</div>

<table class="main-table">
    <tr>
        <td class="standard-padding-left"><b><?= PrintoutHelper::lang('project') ?>:</b></td>
        <td colspan="3">
            <?= $data['scaffoldLists'][0]['ktr'] ?>,
            <?= $data['projectName'] ?>
        </td>
    </tr>
    <tr>
        <td class="standard-padding-left"><b><?= PrintoutHelper::lang('client') ?>:</b></td>
        <td class="width-30"><?= $data['customerName'] ?></td>

        <td class="width-20"><b><?= PrintoutHelper::lang('set_up') ?></b></td>
        <td class="text-right">
            <?= date('d.m.Y', strtotime($data['scaffoldLists'][0]['vhbeg'])) ?>
        </td>
        <td class="width-5"></td>
    </tr>
    <tr>
        <td colspan="2"></td>
        <td><b><?= PrintoutHelper::lang('basic_rent') ?>:</b></td>
        <td class="text-right"> <?= $data['scaffoldLists'][0]['gvh'] . ' ' . PrintoutHelper::lang('days') ?></td>
    </tr>
    <tr>
        <td class="standard-padding-left"><b><?= PrintoutHelper::lang('order') ?>:</b></td>
        <td><b><?= $data['scaffoldLists'][0]['bestellnr'] ?? "" ?></b></td>
    </tr>
    <tr>
        <td class="standard-padding-left"><b><?= PrintoutHelper::lang('location') ?>:</b></td>
        <td><?= $data['scaffoldLists'][0]['einsatzort'] ?? "" ?></td>
    </tr>
    <tr>
        <td class="standard-padding-left vertical-align-top"><b><?= PrintoutHelper::lang('description') ?>
                :</b></td>
        <td colspan="3" class="padding-bottom-15">
            <?= $data['scaffoldLists'][0]['bezeich'] ?? "" ?> <br>
            <?= $data['scaffoldLists'][0]['details'] ?? "" ?>
        </td>
    </tr>
</table>
<div class="table-header-background">
    <table class="small-font table-full-width">
        <colgroup>
            <col span="1">
            <col span="1">
            <col span="3">
            <col span="3">
            <col span="1">
            <col span="1">
            <col span="1">
            <col span="1">
            <col span="1">
        </colgroup>
        <thead>
        <tr>
            <td colspan="1" class="small-field-center"><b><?= PrintoutHelper::lang('serial_number') ?></b></td>
            <td colspan="1" class="table-content-width-9"><b><?= PrintoutHelper::lang('item_number') ?></b></td>
            <td colspan="3" class="table-content-width-30">
                <b><?= PrintoutHelper::lang('specification_of_service') ?></b>
            <td colspan="3" class="table-content-width-18"></td>
            <td colspan="1" class="table-content-text-end"><b><?= PrintoutHelper::lang('amount') ?></b></td>
            <td colspan="1" class="table-content-width-10"><b><?= PrintoutHelper::lang('unit') ?></b></td>
            <td colspan="1" class="table-content-width-10"><b><?= PrintoutHelper::lang('percentage') ?></b></td>
            <?php if ($data['showPrices']) { ?>
                <td colspan="1" class="table-content-width-10"><b><?= PrintoutHelper::lang('unit_price') ?></b></td>
                <td colspan="1" class="table-content-width-10"><b><?= PrintoutHelper::lang('total') ?></b></td>
            <?php } ?>
        </tr>
        <tr>
            <td colspan="2" class="table-content-width-14"></td>
            <td colspan="1" class="small-field"><b><?= PrintoutHelper::lang('pc') ?></b></td>
            <td colspan="1" class="small-field"><b><?= PrintoutHelper::lang('length') ?></b></td>
            <td colspan="1" class="small-field"><b><?= PrintoutHelper::lang('depth') ?></b></td>
            <td colspan="1" class="small-field"><b><?= PrintoutHelper::lang('height') ?></b></td>
            <td colspan="1" class="small-field"><b><?= PrintoutHelper::lang('extent') ?></b></td>
            <td colspan="1" class="small-field"></td>
            <td colspan="5" class="width-35"></td>
        </tr>
        </thead>
    </table>
    <hr class="header-line-first">
</div>
</body>
</html>