@page {
    size: A4 portrait;
}

body {
    font-family: Arial, sans-serif;
    margin: 50px 10px 10px;
}

.logo-table {
    width: 100%;
}

.logo-table tr {
    margin-top: 20px;
}

.logo-container img {
    width: 75%;
}

.logo-table td:last-child {
    width: 100px;
}

.font-bold {
    font-weight: bold;
}

.header-background {
    background-color: #e0e0e0;
    height: 65px;
    padding-bottom: 10px;
}

.header-text {
    font-weight: normal;
    display: block;
}

.header-spacer {
    height: 4px;
}

.header-line {
    background-color: #000000;
    height: 1px;
}

.header-line-first {
    background-color: #000000;
    height: 1px;
}

.header-line-second {
    background-color: #000000;
    height: 1px;
    margin: 10px 2px 4px;
}

.header-spacer-small {
    height: 10px;
}

.oversize-title {
    font-size: 150%;
    padding-left: 20px;
}

.main-table {
    width: 100%;
}

.standard-padding-left {
    padding-left: 15px;
}

.width-30 {
    width: 30%;
}

.width-20 {
    width: 20%;
}

.text-right {
    text-align: right;
}

.width-5 {
    width: 5%;
}

.padding-bottom-15 {
    padding-bottom: 15px;
}

.table-header-background {
    background-color: #e0e0e0;
    height: 125px;
    padding-bottom: 10px;
}

.small-font {
    font-size: small;
    padding-top: 15px;
}

.small-font td {
    padding: 2px;
}

.table-full-width {
    width: 100%;
}

.width-8 {
    width: 8%;
}

.width-10 {
    width: 10%;
}

.width-4 {
    width: 4%;
}

.width-7 {
    width: 7%;
}

.width-15 {
    width: 15%;
}

.padding-left-15 {
    padding-left: 15px;
}

.vertical-align-top {
    vertical-align: top;
}

.total-border {
    background-color: #000000;
    height: 1px;
    width: 100%;
    margin: 3px 3px 1px;
}

.small-field {
    width: 7%;
}

.small-field-center {
    width: 7%;
    text-align: center;
}

.item-col {
    text-align: center;
    padding: 10px !important;
}

.table-border-bottom {
    height: 50px;
    border-bottom: 1px solid black;
}

.standard-padding-left {
    padding-left: 15px;
}

.small-font-footer {
    font-size: 75%;
}

.small-font hr {
    border: none;
}

.table-content-small-field-center {
    text-align: center;
    width: 7%;
}

.table-content-width-9 {
    width: 9%;
}

.table-content-width-30 {
    width: 30%;
}

.table-content-width-18 {
    width: 18%;
}

.table-content-text-end {
    text-align: end;
    width: 10%;
    padding-right: 12px;
}

.table-content-width-10 {
    width: 10%;
}

.table-content-width-14 {
    width: 14%;
}

.table-content-width-35 {
    width: 35%;
}

.table-content-small-field {
    width: 7%;
}

.table-content-small-field-center {
    text-align: center;
    width: 7%;
}

.table-content-width-9 {
    width: 9%;
}

.table-content-width-35 {
    width: 35%;
}

.table-content-width-7 {
    width: 7%;
}

.table-content-text-end {
    text-align: end;
    padding-right: 12px;
}

.table-content-width-10 {
    width: 10%;
}

.table-content-width-14 {
    width: 14%;
}

.table-content-small-field {
    width: 7%;
}

.table-content-hr {
    height: 1px;
    background-color: #000000;
    border: none;
}

.table-content-row-highlight {
    background-color: #e0e0e0;
    font-weight: bold;
}

.table-content-padding {
    padding: 10px !important;
}

.table-content-total-section {
    width: 100%;
    margin-top: 15px;
}

.table-content-total-table {
    width: 100%;
}

.table-content-left {
    width: 70%;
    text-align: left;
    padding-left: 95px;
}

.table-content-right {
    width: 30%;
    text-align: right;
    padding-right: 10px;
}

.footer {
    width: 100%;
}