<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../../printout.helper.php';

class C_ScaffoldListOversizes
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $projectNo, string $contractServiceSheetId, string $showPrices): array
    {
        $curl = new PrintoutCurl();
        $base = PrintoutHelper::getApiBaseUrl();

        $scaffoldLists = json_decode($curl->_simple_call('get', "$base/scaffoldlists/select/$projectNo/$contractServiceSheetId",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        $measurements = json_decode($curl->_simple_call('get', "$base/scaffoldlists/measurements/$projectNo-$contractServiceSheetId",
            [], PrintoutHelper::getHeadersForApiCalls()), true);

        $project = PrintoutHelper::downloadProject($projectNo, "projectName,customerName", $curl);
        return [
            'showPrices' => filter_var($showPrices, FILTER_VALIDATE_BOOLEAN),
            'scaffoldLists' => $scaffoldLists,
            'measurements' => $measurements,
            'projectName' => $project['projectName'],
            'customerName' => $project['customerName'],
        ];
    }
}