<?php
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ScaffoldListOversizes())->getData($_GET['projectNo'], $_GET['contractServiceSheetId'], $_GET['showPrices']);
}
$totalPriceSum = 0;
foreach ($data['measurements'] ?? [] as $measurement) {
    if (($measurement['itemType'] ?? '') === 'POS' &&
        isset($measurement['totalPrice']) &&
        is_numeric($measurement['totalPrice'])) {
        $totalPriceSum += $measurement['totalPrice'];
    }
}
?>

<!DOCTYPE html>
<html lang="<?= PrintoutHelper::getLanguage() ?>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ScaffoldList Oversizes</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>

<body>
<div style="padding-bottom: 30px">
    <table class="small-font">
        <colgroup>
            <col span="1">
            <col span="1">
            <col span="3">
            <col span="3">
            <col span="1">
            <col span="1">
            <col span="1">
            <col span="1">
            <col span="1">
        </colgroup>
        <tbody>
        <?php foreach ($data['measurements'] ?? [] as $measurement) { ?>
            <?php if (($measurement['itemType'] ?? '') === 'POS') { ?>
                <tr>
                    <td colspan="1" class="table-content-small-field-center">
                        <?= str_pad($measurement['itemId'] ?? '', 3, '0', STR_PAD_LEFT) ?></td>
                    <td colspan="1" class="table-content-width-9">
                        <?= $measurement['serviceItemNo'] ?? '' ?></td>
                    <td colspan="5" class="table-content-width-35">
                        <?= $measurement['detailDescription'] ?? '' ?></td>
                    <td colspan="1" class="table-content-width-7"></td>
                    <td colspan="1" class="table-content-text-end">
                        <?= isset($measurement['quantity']) ? number_format($measurement['quantity'], 2, ',', '') : '' ?>
                    </td>
                    <td colspan="1" class="table-content-width-10">
                        <?= $measurement['measuringUnitKey'] ?? '' ?></td>
                    <td colspan="1" class="table-content-width-10">
                        <?= !empty($measurement['percentage']) && $measurement['percentage'] > 0 ? $measurement['percentage'] : '' ?>
                    </td>
                    <?php if ($data['showPrices']) { ?>
                        <td colspan="1" class="table-content-width-10">
                            <?= isset($measurement['unitPrice']) ? number_format($measurement['unitPrice'], 2, ',', '') : '' ?>
                        </td>
                        <td colspan="1" class="table-content-width-10 table-content-text-end">
                            <?= isset($measurement['totalPrice']) ? number_format($measurement['totalPrice'], 2, ',', '') : '' ?>
                        </td>
                    <?php } ?>
                </tr>
                <tr>
                    <td colspan="2" class="table-content-width-14"></td>
                    <td colspan="1" class="table-content-small-field">
                        <?= isset($measurement['quantityCount']) && $measurement['quantityCount'] != 0 ? number_format($measurement['quantityCount'], 2, ',', '') : '' ?>
                    </td>
                    <td colspan="1" class="table-content-small-field">
                        <?= isset($measurement['quantityLength']) && $measurement['quantityLength'] != 0 ? number_format($measurement['quantityLength'], 2, ',', '') : '' ?>
                    </td>
                    <td colspan="1" class="table-content-small-field">
                        <?= isset($measurement['quantityDepth']) && $measurement['quantityDepth'] != 0 ? number_format($measurement['quantityDepth'], 2, ',', '') : '' ?>
                    </td>
                    <td colspan="1" class="table-content-small-field">
                        <?= isset($measurement['quantityHeight']) && $measurement['quantityHeight'] != 0 ? number_format($measurement['quantityHeight'], 2, ',', '') : '' ?>
                    </td>
                    <td colspan="1" class="table-content-small-field">
                        <?= isset($measurement['quantity']) ? number_format($measurement['quantity'], 2, ',', '') . ' ' . ($measurement['measuringUnitKey'] ?? '') : '' ?>
                    </td>
                    <td colspan="1" class="table-content-width-10"></td>
                    <td colspan="5"></td>
                </tr>
                <tr>
                    <td colspan="13">
                        <hr class="table-content-hr">
                    </td>
                </tr>
            <?php } elseif (($measurement['itemType'] ?? '') === 'ZS') { ?>
                <tr class="table-content-row-highlight">
                    <td class="item-col">
                        <?= str_pad($measurement['itemId'] ?? '', 3, '0', STR_PAD_LEFT) ?></td>
                    <td colspan="6" class="table-content-padding">
                        <?= $measurement['description'] ?? '' ?></td>

                    <?php if (!$data['showPrices']) { ?>
                        <td colspan="7"></td>
                    <?php } else { ?>
                        <td colspan="7" class="table-content-text-end table-content-padding">
                            <?= $measurement['totalPrice'] ?? '' ?></td>
                    <?php } ?>
                </tr>
            <?php } elseif (($measurement['itemType'] ?? '') === 'TK') { ?>
                <tr class="table-content-row-highlight">
                    <td class="item-col">
                        <?= str_pad($measurement['itemId'] ?? '', 3, '0', STR_PAD_LEFT) ?></td>
                    <td colspan="13" class="table-content-padding">
                        <?= $measurement['detailDescription'] ?? '' ?>
                    </td>
                </tr>
            <?php } ?>
        <?php } ?>
    </table>
</div>
<div style="width: 100%; margin-top: 15px">
    <?php if ($data['showPrices']) { ?>
        <div style="background-color:#e0e0e0; height: 45px; font-size: 80%; padding-top:10px">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 70%; text-align: left; height: 30px; padding-left: 95px">
                        <b><?= PrintoutHelper::lang('total') ?>:</b>
                    </td>
                    <td style="width: 30%; text-align: right; padding-right: 10px">
                        <b><?= $totalPriceSum; ?></b>
                        <hr class="total-border">
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td class="small-font-footer" style="padding-top: 10px">
                        <b><?= PrintoutHelper::lang('service confirmed -no handwritten changes-') ?></b>
                    </td>
                </tr>
            </table>
        </div>
    <?php } ?>
    <table style="width: 100%; margin-top: 20px;">
        <tr>
            <td style="width: 40%"></td>
            <td style="width: 60%"><?= PrintoutHelper::lang('contractor') ?></td>
        </tr>
        <tr>
            <td></td>
            <td class="table-border-bottom"></td>
        </tr>
        <tr>
            <td></td>
            <td class="text-right">
                <?= PrintoutHelper::lang('date') ?>,
                <?= PrintoutHelper::lang('name') ?>/
                <?= PrintoutHelper::lang('stamp') ?>,
                <?= PrintoutHelper::lang('signature') ?>
            </td>
        </tr>
        <tr>
            <td style="width: 30%"></td>
            <td style="width: 70%"><?= PrintoutHelper::lang('client') ?></td>
        </tr>
        <tr>
            <td></td>
            <td class="table-border-bottom"></td>
        </tr>
        <tr>
            <td></td>
            <td class="text-right">
                <?= PrintoutHelper::lang('date') ?>,
                <?= PrintoutHelper::lang('name') ?>/
                <?= PrintoutHelper::lang('stamp') ?>,
                <?= PrintoutHelper::lang('signature') ?>
            </td>
        </tr>
    </table>
</div>
</body>
</html>