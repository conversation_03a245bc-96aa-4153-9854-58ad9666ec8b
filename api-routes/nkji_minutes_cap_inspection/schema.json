{"name": "ПРОТОКОЛ за извършена проверка на ОСП - v.011", "description": "ЕРП-0002-01", "status": "active", "createdBy": "309", "createdOn": "2023-04-05T11:11:11Z", "printOptions": ["9"], "applicableEntities": [], "positions": [{"id": 0, "title": "ПРОТОКОЛ", "type": "headline"}, {"id": 1, "title": "1. Енергосекция (ЕНС)", "type": "combobox", "collapsed": "0", "values": ["РП Енергосекция-София", "РП Енергосекция-Пловдив", "РП Енергосекция-Горна Оряховица"]}, {"id": 2, "title": "2. <PERSON><PERSON><PERSON><PERSON>", "type": "date"}, {"id": 3, "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ПЕ)", "type": "combobox", "collapsed": "0", "values": ["<PERSON><PERSON>", "Бойчиновци", "Борово", "Брусарци", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Варна", "Ветово", "<PERSON>и<PERSON><PERSON><PERSON>", "Волуяк", "<PERSON>е<PERSON><PERSON><PERSON><PERSON><PERSON>", "Г<PERSON>рна Оряховица", "Димитровград", "Дупница", "Дългопол", "Завет", "Златица", "Карлово", "Карнобат", "Каспичан", "Клисура", "Костен<PERSON>ц", "Левски", "Мездра", "Нова Загора", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>ер<PERSON><PERSON><PERSON>", "Плевен", "<PERSON>л<PERSON><PERSON>ив", "Русе", "Самуил", "Свиленград", "Симитли", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Славяново", "Сливен", "София", "Стара Загора", "Твърдица", "Трявна", "Тулово", "Търговище", "Червен бряг", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>л"]}, {"id": 4, "title": "4. Основен секционен пост (ОСП) / ДСП", "type": "combobox", "collapsed": "0", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Белозем", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Буново", "Българо-Турска граница", "Владая", "Воднянци", "Гълъбник", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "До<PERSON>на махала", "Долно Церовене", "Же<PERSON><PERSON>д", "Зверино", "Згалево", "Зимница", "Златуша", "Иваново", "Казичене", "Ка<PERSON><PERSON><PERSON><PERSON>на Запад", "Капитановци", "Караджата", "Каспичан", "Кер<PERSON><PERSON>н", "Клисура", "Кочериново", "Кочово", "Кремиковци", "Кресна", "Кривня", "Криводол", "Лесичери", "Любеново", "Михайлово", "Моравица", "Николаево", "Нова Загора", "Орешак", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>л<PERSON><PERSON>ив", "Подвис", "Полски Тръмбеш", "Поповица", "Радунци", "Реброво", "Роман", "Русе", "Самуил", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Срацимир", "Стражица", "Телиш", "Трявна", "Тулово", "Тъжа", "Търнак", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>х", "Царева Ливада", "Ябълково"]}, {"id": 5, "title": "5. Извър<PERSON>и<PERSON> проверката", "collapsed": "0", "type": "employeeSelector", "values": []}, {"id": 6, "title": "6. Проба управление от място", "collapsed": "0", "type": "combobox", "values": ["Изправно", "Неизправно"]}, {"id": 7, "title": "7.1. Проба управление от гара", "collapsed": "0", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Белозем", "Ветово", "<PERSON>и<PERSON><PERSON><PERSON>", "Владая", "Гав<PERSON><PERSON><PERSON><PERSON><PERSON>о", "Гълъбник", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "До<PERSON>на махала", "Долно Камарци", "Драл<PERSON>а", "Дреновец", "Зверино", "Зимница", "Иваново", "Казичене", "Ка<PERSON><PERSON><PERSON><PERSON>на Запад", "Каспичан", "Кер<PERSON><PERSON>н", "Клисура", "Комунари", "Кочериново", "Кремиковци", "Кресна", "Криводол", "Любеново", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Медковец", "Мездра", "Мирково", "Михайлово", "Мърчево", "Николаево", "Нова Загора", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Павликени", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Плевен", "<PERSON>л<PERSON><PERSON>ив", "Подвис", "Полски Тръмбеш", "Поповица", "Попово", "Пордим", "Радунци", "Разменна", "Реброво", "Ресен", "Роман", "Русе", "Руска бяла", "Самуил", "Свиленград", "Сеново", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Смядово", "Срацимир", "Стражица", "Телиш", "Трявна", "Тулово", "Тъжа", "Търговище", "Търнак", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>х", "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "Храбърско", "Царева Ливада", "Чумерна", "Ябълково"]}, {"id": 8, "displayInside": 7, "title": "Статус", "type": "combobox", "values": ["Изправно", "Неизправно"]}, {"id": 9, "title": "7.2. Проба управление от гара", "collapsed": "0", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Белозем", "Ветово", "<PERSON>и<PERSON><PERSON><PERSON>", "Владая", "Гав<PERSON><PERSON><PERSON><PERSON><PERSON>о", "Гълъбник", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "До<PERSON>на махала", "Долно Камарци", "Драл<PERSON>а", "Дреновец", "Зверино", "Зимница", "Иваново", "Казичене", "Ка<PERSON><PERSON><PERSON><PERSON>на Запад", "Каспичан", "Кер<PERSON><PERSON>н", "Клисура", "Комунари", "Кочериново", "Кремиковци", "Кресна", "Криводол", "Любеново", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Медковец", "Мездра", "Мирково", "Михайлово", "Мърчево", "Николаево", "Нова Загора", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Павликени", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Плевен", "<PERSON>л<PERSON><PERSON>ив", "Подвис", "Полски Тръмбеш", "Поповица", "Попово", "Пордим", "Радунци", "Разменна", "Реброво", "Ресен", "Роман", "Русе", "Руска бяла", "Самуил", "Свиленград", "Сеново", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Смядово", "Срацимир", "Стражица", "Телиш", "Трявна", "Тулово", "Тъжа", "Търговище", "Търнак", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>х", "Х<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>и", "Храбърско", "Царева Ливада", "Чумерна", "Ябълково"]}, {"id": 10, "displayInside": 9, "title": "Статус", "type": "combobox", "values": ["Изправно", "Неизправно"]}, {"id": 11, "title": "8.1. Проба управление от тягова подстанция (ТП)", "collapsed": "0", "type": "combobox", "values": ["ТП Алдомировци", "ТП Белово ", "Т<PERSON> Бов", "ТП Бойчиновци", "ТП Борово", "ТП Брусарци", "ТП Бургас", "ТП Вакарел", "ТП Варна", "ТП Величково", "ТП Видин", "ТП Волуяк", "ТП Враца", "ТП Генерал Тодоров", "ТП Горна Оряховица", "ТП Дивдядово", "ТП Димитровград", "ТП Димово", "ТП Дряново", "ТП Дупница", "ТП Завет", "ТП Илиянци", "ТП Казанлък", "ТП Карлово", "ТП Карнобат", "ТП Крумово ", "ТП Кръстец", "ТП Левски", "ТП Мездра", "ТП Нова Загора", "ТП Перник", "ТП Пирдоп", "ТП Плевен", "ТП Провадия", "ТП Прослав ", "ТП Първомай ", "ТП Разград", "ТП Русе", "ТП Свиленград", "ТП Симеоновград", "ТП Симитли", "ТП Славяново", "ТП Сливен", "ТП Стара Загора", "ТП Столник", "ТП Твърдица", "ТП Търговище", "ТП Хитрино", "ТП Червен бряг", "ТП Червена вода", "ТП Черврен бряг", "ТП Честово", "ТП Чирпан", "ТП Яворовец", "ТП Ямбол", "Турция"]}, {"id": 12, "displayInside": 11, "title": "Статус", "type": "combobox", "values": ["Изправно", "Неизправно", "Няма"]}, {"id": 13, "title": "8.2. Проба управление от тягова подстанция (ТП)", "collapsed": "0", "type": "combobox", "values": ["ТП Алдомировци", "ТП Белово ", "Т<PERSON> Бов", "ТП Бойчиновци", "ТП Борово", "ТП Брусарци", "ТП Бургас", "ТП Вакарел", "ТП Варна", "ТП Величково", "ТП Видин", "ТП Волуяк", "ТП Враца", "ТП Генерал Тодоров", "ТП Горна Оряховица", "ТП Дивдядово", "ТП Димитровград", "ТП Димово", "ТП Дряново", "ТП Дупница", "ТП Завет", "ТП Илиянци", "ТП Казанлък", "ТП Карлово", "ТП Карнобат", "ТП Крумово ", "ТП Кръстец", "ТП Левски", "ТП Мездра", "ТП Нова Загора", "ТП Перник", "ТП Пирдоп", "ТП Плевен", "ТП Провадия", "ТП Прослав ", "ТП Първомай ", "ТП Разград", "ТП Русе", "ТП Свиленград", "ТП Симеоновград", "ТП Симитли", "ТП Славяново", "ТП Сливен", "ТП Стара Загора", "ТП Столник", "ТП Твърдица", "ТП Търговище", "ТП Хитрино", "ТП Червен бряг", "ТП Червена вода", "ТП Черврен бряг", "ТП Честово", "ТП Чирпан", "ТП Яворовец", "ТП Ямбол", "Турция"]}, {"id": 14, "displayInside": 13, "title": "Статус", "type": "combobox", "values": ["Изправно", "Неизправно", "Няма"]}, {"id": 15, "title": "9. Проба управление от SCADA", "type": "combobox", "collapsed": "0", "values": ["Изправно", "Неизправно", "Няма"]}, {"id": 16, "title": "10. Проба на връзка с енергодиспечер/Тел. връзка", "collapsed": "0", "type": "combobox", "values": ["Изправна", "Няма връзка с диспечинг", "Няма връзка с ЖП телефон"]}, {"id": 17, "title": "11. Почистване табла управление и притягане клемореди", "collapsed": "0", "type": "combobox", "values": ["Извършено", "Неизвършено"]}, {"id": 18, "title": "12. Проверка изправност захранване ТП1", "collapsed": "0", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Белозем", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Буново", "Българо-Турска граница", "Владая", "Воднянци", "Гълъбник", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "До<PERSON>на махала", "Долно Церовене", "Же<PERSON><PERSON>д", "Зверино", "Згалево", "Зимница", "Златуша", "Иваново", "Казичене", "Ка<PERSON><PERSON><PERSON><PERSON>на Запад", "Капитановци", "Караджата", "Каспичан", "Кер<PERSON><PERSON>н", "Клисура", "Кочериново", "Кочово", "Кремиковци", "Кресна", "Кривня", "Криводол", "Лесичери", "Любеново", "Михайлово", "Моравица", "Николаево", "Нова Загора", "Орешак", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>л<PERSON><PERSON>ив", "Подвис", "Полски Тръмбеш", "Поповица", "Радунци", "Реброво", "Роман", "Русе", "Самуил", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Срацимир", "Стражица", "Телиш", "Трявна", "Тулово", "Тъжа", "Търнак", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>х", "Царева Ливада", "Ябълково"]}, {"id": 19, "title": "13. Проверка изправност захранване ТП2", "collapsed": "0", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Белозем", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Буново", "Българо-Турска граница", "Владая", "Воднянци", "Гълъбник", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "До<PERSON>на махала", "Долно Церовене", "Же<PERSON><PERSON>д", "Зверино", "Згалево", "Зимница", "Златуша", "Иваново", "Казичене", "Ка<PERSON><PERSON><PERSON><PERSON>на Запад", "Капитановци", "Караджата", "Каспичан", "Кер<PERSON><PERSON>н", "Клисура", "Кочериново", "Кочово", "Кремиковци", "Кресна", "Кривня", "Криводол", "Лесичери", "Любеново", "Михайлово", "Моравица", "Николаево", "Нова Загора", "Орешак", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>л<PERSON><PERSON>ив", "Подвис", "Полски Тръмбеш", "Поповица", "Радунци", "Реброво", "Роман", "Русе", "Самуил", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Срацимир", "Стражица", "Телиш", "Трявна", "Тулово", "Тъжа", "Търнак", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>х", "Царева Ливада", "Ябълково"]}, {"id": 20, "title": "14. Проверка изправност захранване ТП3", "collapsed": "0", "type": "combobox", "values": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Белозем", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Буново", "Българо-Турска граница", "Владая", "Воднянци", "Гълъбник", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "До<PERSON>на махала", "Долно Церовене", "Же<PERSON><PERSON>д", "Зверино", "Згалево", "Зимница", "Златуша", "Иваново", "Казичене", "Ка<PERSON><PERSON><PERSON><PERSON>на Запад", "Капитановци", "Караджата", "Каспичан", "Кер<PERSON><PERSON>н", "Клисура", "Кочериново", "Кочово", "Кремиковци", "Кресна", "Кривня", "Криводол", "Лесичери", "Любеново", "Михайлово", "Моравица", "Николаево", "Нова Загора", "Орешак", "Паз<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>л<PERSON><PERSON>ив", "Подвис", "Полски Тръмбеш", "Поповица", "Радунци", "Реброво", "Роман", "Русе", "Самуил", "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Срацимир", "Стражица", "Телиш", "Трявна", "Тулово", "Тъжа", "Търнак", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>х", "Царева Ливада", "Ябълково"]}, {"id": 21, "title": "15. Проверка изправност захранване Градска мрежа", "collapsed": "0", "type": "combobox", "values": ["Изправно", "Неизправно", "Няма"]}, {"id": 22, "title": "16. Проверка изправност UPS", "collapsed": "0", "type": "combobox", "values": ["Изправно", "Неизправно", "Няма"]}, {"id": 23, "title": "17. Проверка осветление", "collapsed": "0", "type": "combobox", "values": ["Изправно", "Неизправно", "Няма"]}, {"id": 24, "title": "18. Проверка комуникационна свързаност №1", "collapsed": "0", "type": "combobox", "values": ["Оптика изправна", "Оптика неизправна", "Оптика няма"]}, {"id": 25, "title": "19. Проверка комуникационна свързаност №2", "collapsed": "0", "type": "combobox", "values": ["GSM изправна", "GSM неизправна", "GSM няма"]}, {"id": 26, "title": "20. Проверка комуникационна свързаност №3", "collapsed": "0", "type": "combobox", "values": ["WLAN изправна", "WLAN неизправна", "WLAN няма"]}, {"id": 27, "title": "21. Наличие на книга образец XXI-01", "collapsed": "0", "type": "combobox", "values": ["Налична", "Неналична"]}, {"id": 28, "title": "22. Предприети действия", "collapsed": "0", "type": "string"}, {"id": 29, "displayInside": 28, "title": "Подпис на извършилия проверката:", "type": "signatureField"}]}