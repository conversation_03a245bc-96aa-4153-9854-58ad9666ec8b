<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_NKJI_Minutes_Cap_Inspection())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<html lang="bg">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>ПРОТОКОЛ за извършена проверка на ОСП</title>
</head>
<body>

<div class="row" style="margin-bottom: 15px">
    <table style="width: 100%">
        <tr>
            <td>
                <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/logo.png') ?>"
                     alt="NKJI Logo" style="max-height: 36px">
            </td>
            <td>
                <div class="title">
                    ПРОТОКОЛ за извършена проверка на ОСП
                    <br>
                    <span style="font-size: initial">
						№ <?= $data['documentId'] ?>
					</span>
                </div>
            </td>
            <td>
                <div class="textRight">
                    <p style="margin-bottom: 0">ЕРП-0002-01</p>
                </div>
            </td>
        </tr>
    </table>
</div>

<div class="row">
    <table class="table">
        <tr class="blueBackground">
            <th>№</th>
            <th>&nbsp;</th>
            <th>&nbsp;</th>
        </tr>
        <?php foreach ($data['table'] as $row) { ?>
        <tr class="whiteBackground">
            <td class="cell"><?= $row['number'] ?></td>
            <td><?= $row['title'] ?></td>
            <td><?= $row['value'] ?></td>
            <?php } ?>
        </tr>
    </table>
</div>

<div class="row">
    <p>1. При попълване на колони 12 и 13, задължително се вписва и наименованието на ТПС</p>
    <p>2. При попълване на колони 18, 19 и 20 задължително се вписва типа на комуникационната връзка (Oптикa; GSM;
        WLAN)</p>
</div>

<div class="row">
    <div class="rightColumn">
        <p>Извършил проверката:</p>
        <p><?= $data['author'] ?></p>
        <?php if (isset($data['mappedDocument']['Подпис на извършилия проверката:'])) { ?>
            <img src="<?= $data['mappedDocument']['Подпис на извършилия проверката:']; ?>"
                 alt="Подпис на извършилия проверката:" class="signatures">
        <?php } ?>
    </div>
</div>

<div class="row">
    <div class="rightColumn">
        <p>Заверил проверката:</p>
    </div>
</div>
</body>
</html>

<style>
    .table, .table th, .table td {
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table {
        width: 90%;
        margin: auto;
    }

    td:first-child, td:last-child {
        text-align: center;
    }

    .signatures {
        width: 250px;
    }

    .rightColumn {
        float: right;
        width: 47%;
        margin-left: 20px;
    }

    .textRight {
        text-align: right;
    }

    .row:after {
        content: "";
        display: table;
        clear: both;
    }

    .title {
        font-size: large;
        font-weight: bold;
        text-align: center;
    }

    body {
        overflow-x: hidden;
    }
</style>
