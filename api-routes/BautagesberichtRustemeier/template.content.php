<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_BautagesberichtRustemeier())->getData($_GET['schemaId'], $_GET['documentId']);
}

$keys = [
    'Polier Std.',
    'gehobene Baufacharbeiter Std.',
    'Maschinisten Std.',
    'Werkpolier Std.',
    'Baufacharbeiter Std.',
    'Kraftfahrer Std.',
    'Bauvorarbeiter Std.',
    'Baufachwerker Std.',
    'Weitere Berufsgruppe #1 Std.',
    'Spezialbaufacharbeiter Std.',
    'Bauwerker Std.',
    'Weitere Berufsgruppe #2 Std.'
];

$total_std = 0;

foreach ($keys as $key) {
    $total_std += !empty($data[$key]) ? (float)$data[$key] : 0;
}

?>

<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Bautagesbericht Rustemeier</title>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>

<body>
<div>
    <p class="text-align-right">Nr. <u><?= $data['documentId'] ?? '' ?></u></p>
    <table class="header-table">
        <tr>
            <td class="font-size-30">Bau-Tagesbericht</td>
            <td>Baustelle:
                <u>
                    <?= $data['Bautagesbericht Rustemeier Baustelle'] ?? '' ?>
                </u>
            </td>
        </tr>
        <tr>
            <td><b>(täglich einzureichen)</b></td>
            <td>Datum:
                <u>
                    <?php
                    if (isset($data['Bautagesbericht Rustemeier Datum'])) {
                        echo date('d.m.Y', strtotime($data['Bautagesbericht Rustemeier Datum']));
                    }
                    ?>
                </u>
            </td>
        </tr>
    </table>

    <table class="first-table">
        <tr>
            <td colspan="4">Ausführende Firma: <u><?= $data['address']['name'] ?? '' ?></u></td>
        </tr>
        <tr>
            <td colspan="4">Anschrift/Tel:
                <u>
                    <?php
                    $address = PrintoutHelper::formatAddress($data['address']['address'], $data['address']['postcode'], $data['address']['city']);
                    if ($address) {
                        echo $address;
                        if (!empty($data['address']['phoneNumber'])) {
                            echo ' / ' . $data['address']['phoneNumber'];
                        }
                    } else {
                        if (!empty($data['address']['phoneNumber'])) {
                            echo $data['address']['phoneNumber'];
                        }
                    }
                    ?>
                </u>
            </td>
        </tr>
        <tr>
            <td colspan="1">
                Verantw. Bauführer:
            </td>
            <td colspan="1">
                <u><?= $data['Verantwortlicher Bauführer Name'] ?? '' ?></u>
            </td>
            <td colspan="1">
                Anschrift/Tel:
            </td>
            <td colspan="1">
                <u>
                    <?php
                    $parts = [];
                    if (!empty($data['Verantwortlicher Bauführer Anschrift'])) {
                        $parts[] = $data['Verantwortlicher Bauführer Anschrift'];
                    }
                    if (!empty($data['Verantwortlicher Bauführer Telefon'])) {
                        $parts[] = $data['Verantwortlicher Bauführer Telefon'];
                    }
                    echo implode(' / ', $parts);
                    ?>
                </u>
            </td>
        </tr>
        <tr>
            <td colspan="1">
                Stellvertreter:
            </td>
            <td colspan="1">
                <u><?= $data['Stellvertreter Name'] ?? '' ?></u>
            </td>
            <td colspan="1" style="text-align: center"> "</td>
            <td colspan="1">
                <u>
                    <?php
                    $parts = [];
                    if (!empty($data['Stellvertreter Anschrift'])) {
                        $parts[] = $data['Stellvertreter Anschrift'];
                    }
                    if (!empty($data['Stellvertreter Telefon'])) {
                        $parts[] = $data['Stellvertreter Telefon'];
                    }
                    echo implode(' / ', $parts);
                    ?>
                </u>
            </td>
        </tr>
        <tr>
            <td colspan="4">Baustelle/Bauteil:
                <u><?= $data['Bautagesbericht Rustemeier Baustelle/Bauteil'] ?? '' ?></u>
            </td>
        </tr>
        <tr>
            <td colspan="4">Bezeichnung der Arbeiten:
                <u><?= $data['Bautagesbericht Rustemeier Bezeichnung der Arbeiten'] ?? '' ?></u>
            </td>
        </tr>
    </table>

    <table class="second-table">
        <tr>
            <td class="first-col">1.</td>
            <td>
                <b class="table-heading">Witterung:</b>
                <u><?= $data['Witterung Name'] ?? '' ?></u>
                Temperatur:
                <u> <?php if (isset($data['Witterung Temperatur'])) { ?>
                        <?= $data['Witterung Temperatur'] ?>
                        °C
                    <?php } else {
                        echo '';
                    } ?>
                </u>
            </td>
            <td><?php PrintoutHelper::echoCheckboxByReportedValue($data['Witterung Regen'] ?? '') ?> Regen</td>
            <td>&nbsp;</td>
            <td><?php PrintoutHelper::echoCheckboxByReportedValue($data['Witterung Frost'] ?? '') ?> Frost</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td><?php PrintoutHelper::echoCheckboxByReportedValue($data['Witterung Wind'] ?? '') ?> Wind</td>
            <td>&nbsp;</td>
            <td><?php PrintoutHelper::echoCheckboxByReportedValue($data['Witterung Schnee'] ?? '') ?> Schnee</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td colspan="4"><?= $data['Witterung Kommentar'] ?? '' ?></td>
        </tr>
    </table>

    <table class="second-block-first-table">
        <tr>
            <td class="first-col" style="border: none">2.</td>
            <td><b class="table-heading">Anzahl der beschäftigten Arbeiter:</b></td>
        </tr>
    </table>

    <table class="second-block-second-table">
        <tr>
            <td colspan="9" style="width: 90%;border-right: none">
                Arbeitzeit von <u><?= $data['Anzahl der beschäftigten Arbeiter Arbeitszeit von'] ?? '' ?></u>
                bis <u><?= $data['Anzahl der beschäftigten Arbeiter Arbeitszeit bis'] ?? '' ?></u>
                Uhr
                <span class="padding-left">
                    Schicht <u><?= $data['Anzahl der beschäftigten Arbeiter Schicht'] ?? '' ?></u>
                </span>
            </td>
            <td colspan="1" style="width: 10%;border-left: none">
                <span>
                    Insgesamt Stunden:
                </span>
            </td>
        </tr>
        <!-- First Row -->
        <tr>
            <!-- Polier -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Polier Anzahl Polier'] ?? '' ?></u> Polier
                </span>
                <span style="float: right; white-space: nowrap;">
            <?php
            if (!empty($data['Polier Std.'])) {
                echo '<u>' . $data['Polier Std.'] . '</u> Std';
            }
            ?>
        </span>
            </td>
            <!-- gehob. Baufacharb. -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['gehobene Baufacharbeiter Anzahl gehobene Baufacharbeiter'] ?? '' ?></u> gehob. Baufacharb.
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['gehobene Baufacharbeiter Std.'])) {
                        echo '<u>' . $data['gehobene Baufacharbeiter Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Maschinisten -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Maschinisten Anzahl Maschinisten'] ?? '' ?></u> Maschinisten
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Maschinisten Std.'])) {
                        echo '<u>' . $data['Maschinisten Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Total of all Std values -->
            <td colspan="1">
                <?php
                if ($total_std > 0) {
                    echo $total_std;
                }
                ?>
            </td>
        </tr>

        <!-- Second Row -->
        <tr>
            <!-- Werkpolier -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Werkpolier Anzahl Werkpolier'] ?? '' ?></u> Werkpolier
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Werkpolier Std.'])) {
                        echo '<u>' . $data['Werkpolier Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Baufacharbeiter -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Baufacharbeiter Anzahl Baufacharbeiter'] ?? '' ?></u> Baufacharbeiter
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Baufacharbeiter Std.'])) {
                        echo '<u>' . $data['Baufacharbeiter Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Kraftfahrer -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Kraftfahrer Anzahl Kraftfahrer'] ?? '' ?></u> Kraftfahrer
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Kraftfahrer Std.'])) {
                        echo '<u>' . $data['Kraftfahrer Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <td colspan="1">
            </td>
        </tr>

        <!-- Third Row -->
        <tr>
            <!-- Bauvorarbeiter -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Bauvorarbeiter Anzahl Bauvorarbeiter'] ?? '' ?></u> Bauvorarbeiter
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Bauvorarbeiter Std.'])) {
                        echo '<u>' . $data['Bauvorarbeiter Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Baufachwerker -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Baufachwerker Anzahl Baufachwerker'] ?? '' ?></u> Baufachwerker
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Baufachwerker Std.'])) {
                        echo '<u>' . $data['Baufachwerker Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Weitere Berufsgruppe #1 -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Weitere Berufsgruppe #1 Anzahl Arbeiter einer weiteren Berufsgruppe'] ?? '' ?></u>
                    <u><?= $data['Weitere Berufsgruppe #1 Name der Berufsgruppe'] ?? '' ?></u>
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Weitere Berufsgruppe #1 Std.'])) {
                        echo '<u>' . $data['Weitere Berufsgruppe #1 Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <td colspan="1">
            </td>
        </tr>

        <!-- Fourth Row -->
        <tr>
            <!-- Spezialbaufacharb. -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Spezialbaufacharbeiter Anzahl Spezialbaufacharbeiter'] ?? '' ?></u> Spezialbaufacharb.
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Spezialbaufacharbeiter Std.'])) {
                        echo '<u>' . $data['Spezialbaufacharbeiter Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Bauwerker -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Bauwerker Anzahl Bauwerker'] ?? '' ?></u> Bauwerker
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Bauwerker Std.'])) {
                        echo '<u>' . $data['Bauwerker Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <!-- Weitere Berufsgruppe #2 -->
            <td colspan="3" style="width:33%">
                <span style="float: left; width: 70%; display: inline-block; white-space: normal;">
                    <u><?= $data['Weitere Berufsgruppe #2 Anzahl Arbeiter einer weiteren Berufsgruppe'] ?? '' ?></u>
                    <u><?= $data['Weitere Berufsgruppe #2 Name der Berufsgruppe'] ?? '' ?></u>
                </span>
                <span style="float: right; white-space: nowrap;">
                    <?php
                    if (!empty($data['Weitere Berufsgruppe #2 Std.'])) {
                        echo '<u>' . $data['Weitere Berufsgruppe #2 Std.'] . '</u> Std';
                    }
                    ?>
                </span>
            </td>
            <td colspan="1">
            </td>
        </tr>
    </table>

    <div class="third-block-div">
        <table>
            <tr>
                <td class="first-col">3.</td>
                <td><b class="table-heading"> Leistungsergebnisse:</b></td>
            </tr>
            <tr>
                <td></td>
                <td><?= $data['Bautagesbericht Rustemeier Leistungsergebnisse'] ?? '' ?></td>
            </tr>
        </table>
    </div>

    <div class="fourth-block-div">
        <table>
            <tr>
                <td class="first-col">4.</td>
                <td><b class="table-heading"> Bemerkungen:</b></td>
            </tr>
            <tr>
                <td></td>
                <td><?= $data['Bautagesbericht Rustemeier Bemerkungen'] ?? '' ?></td>
            </tr>
        </table>
    </div>

    <div class="fifth-div">
        Für <b>Tagelohn-</b> bzw. außervertragliche Arbeiten sind <b>besondere Tagelohnzettel</b> in
        3facher Ausfertigung einzureichen.
    </div>

    <p class="signature-text">Unterschriften</p>

    <table class="signature-table">
        <tr>
            <td style="width: 40%; text-align: center">
                <?php if (isset($data['Bautagesbericht Rustemeier Unterschrift Bauherr'])) { ?>
                    <img style="max-width: 180px; height: auto;max-height: 130px; contain: content"
                         src="<?= $data['Bautagesbericht Rustemeier Unterschrift Bauherr'] ?>" alt="">
                <?php } ?>
            </td>
            <td>&nbsp;</td>
            <td style="width: 40%; text-align: center">
                <?php if (isset($data['Bautagesbericht Rustemeier Unterschrift Bauunternehmer'])) { ?>
                    <img style="max-width: 180px; height: auto; max-height: 130px; contain: content"
                         src="<?= $data['Bautagesbericht Rustemeier Unterschrift Bauunternehmer'] ?>" alt="">
                <?php } ?>
            </td>
        </tr>
        <tr>
            <td class="text-align-center">Für den Bauherrn</td>
            <td>&nbsp;</td>
            <td class="text-align-center">Für den Bauunternehmer</td>
        </tr>
    </table>
</div>
</body
</html>