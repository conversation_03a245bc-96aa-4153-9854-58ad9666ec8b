{"name": "Bautagesbericht Rustemeier", "status": "active", "createdBy": "365", "createdOn": "", "positions": [{"id": 1, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 2, "title": "Datum", "type": "date"}, {"id": 3, "title": "Verantwortlicher Bauführer", "type": "headline"}, {"id": 4, "parentId": 3, "title": "Name", "type": "string"}, {"id": 5, "parentId": 3, "title": "Anschrift", "type": "string"}, {"id": 6, "parentId": 3, "title": "Telefon", "type": "string"}, {"id": 7, "title": "Stellvertreter", "type": "headline"}, {"id": 8, "parentId": 7, "title": "Name", "type": "string"}, {"id": 9, "parentId": 7, "title": "Anschrift", "type": "string"}, {"id": 10, "parentId": 7, "title": "Telefon", "type": "string"}, {"id": 11, "title": "Baustelle/Bauteil", "type": "string"}, {"id": 12, "title": "Bezeichnung der Arbeiten", "type": "string"}, {"id": 13, "title": "<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 14, "parentId": 13, "title": "Name", "type": "string"}, {"id": 15, "parentId": 13, "title": "Temperatur", "type": "float"}, {"id": 16, "parentId": 13, "title": "Regen", "type": "checkbox"}, {"id": 17, "parentId": 13, "title": "<PERSON>", "type": "checkbox"}, {"id": 18, "parentId": 13, "title": "Wind", "type": "checkbox"}, {"id": 19, "parentId": 13, "title": "Sc<PERSON><PERSON>", "type": "checkbox"}, {"id": 20, "parentId": 13, "title": "Kommentar", "type": "string"}, {"id": 21, "title": "Anzahl der beschäftigten Arbeiter", "type": "headline"}, {"id": 22, "parentId": 21, "title": "Arbeitsz<PERSON> von", "type": "time"}, {"id": 23, "parentId": 21, "title": "Arbeitszeit bis", "type": "time"}, {"id": 24, "parentId": 21, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 25, "parentId": 21, "title": "<PERSON><PERSON>", "type": "headline"}, {"id": 26, "parentId": 25, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 27, "parentId": 25, "title": "Std.", "type": "float"}, {"id": 28, "parentId": 21, "title": "Werkpolier", "type": "headline"}, {"id": 29, "parentId": 28, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 30, "parentId": 28, "title": "Std.", "type": "float"}, {"id": 31, "parentId": 21, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 32, "parentId": 31, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 33, "parentId": 31, "title": "Std.", "type": "float"}, {"id": 34, "parentId": 21, "title": "Spezialbaufacharbeiter", "type": "headline"}, {"id": 35, "parentId": 34, "title": "<PERSON><PERSON><PERSON>zialbaufacharbeiter", "type": "int"}, {"id": 36, "parentId": 34, "title": "Std.", "type": "float"}, {"id": 37, "parentId": 21, "title": "g<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 38, "parentId": 37, "title": "<PERSON><PERSON><PERSON> g<PERSON><PERSON>ne Baufacharbeiter", "type": "int"}, {"id": 39, "parentId": 37, "title": "Std.", "type": "float"}, {"id": 40, "parentId": 21, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 41, "parentId": 40, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 42, "parentId": 40, "title": "Std.", "type": "float"}, {"id": 43, "parentId": 21, "title": "Baufachwerker", "type": "headline"}, {"id": 44, "parentId": 43, "title": "<PERSON><PERSON><PERSON>achwer<PERSON>", "type": "int"}, {"id": 45, "parentId": 43, "title": "Std.", "type": "float"}, {"id": 46, "parentId": 21, "title": "Bauwerker", "type": "headline"}, {"id": 47, "parentId": 46, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 48, "parentId": 46, "title": "Std.", "type": "float"}, {"id": 49, "parentId": 21, "title": "Maschinisten", "type": "headline"}, {"id": 50, "parentId": 49, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 51, "parentId": 49, "title": "Std.", "type": "float"}, {"id": 52, "parentId": 21, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 53, "parentId": 52, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 54, "parentId": 52, "title": "Std.", "type": "float"}, {"id": 55, "parentId": 21, "title": "Weitere Berufsgruppe #1", "type": "headline"}, {"id": 56, "parentId": 55, "title": "Anzahl Arbeiter einer weiteren Berufsgruppe ", "type": "int"}, {"id": 57, "parentId": 55, "title": "Name der Berufsgruppe", "type": "string"}, {"id": 58, "parentId": 55, "title": "Std.", "type": "float"}, {"id": 59, "parentId": 21, "title": "Weitere Berufsgruppe #2", "type": "headline"}, {"id": 60, "parentId": 59, "title": "Anzahl Arbeiter einer weiteren Berufsgruppe", "type": "int"}, {"id": 61, "parentId": 59, "title": "Name der Berufsgruppe", "type": "string"}, {"id": 62, "parentId": 59, "title": "Std.", "type": "float"}, {"id": 63, "title": "Leistungsergebnisse", "type": "string"}, {"id": 64, "title": "Bemerkungen", "type": "string"}, {"id": 65, "title": "Unterschrift Bauherr", "type": "signatureField"}, {"id": 66, "title": "Unterschrift Bauunternehmer", "type": "signatureField"}]}