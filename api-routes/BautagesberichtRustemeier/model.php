<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_BautagesberichtRustemeier
{
    /**
     * @param string|int $schemaId
     * @param string|int $documentId
     * @return array<string, mixed>
     */
    public function getData($schemaId, $documentId): array
    {
        $curl = new PrintoutCurl(PrintoutHelper::getApiBaseUrl());
        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $data = PrintoutHelper::mapDocumentChildrenToValues($doc['fullDocument'], true);
        $data['address'] = $this->downloadAddressByd(1, $curl);
        $data['documentId'] = $documentId;
        return $data;
    }

    /**
     * @param int $id
     * @param PrintoutCurl $curl
     * @return array<string, mixed>
     */
    public function downloadAddressByd($id, $curl): array
    {
        return json_decode($curl->_simple_call('get', "v1/addresses/$id", [],
            PrintoutHelper::getHeadersForApiCalls()), true);
    }
}