<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$route = Route::KsbgChecklist;

$schemaId = 7;
$docId = 3230;
printoutGenerator(
    Principal::HAKA_KUECHE(),
    $route,
    baseFileName: "output-haka_kueche-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '1',
    ]
);

$schemaId = 822;
$docId = 1582;
printoutGenerator(
    Principal::KERAMIKO_2020(),
    $route,
    baseFileName: "output-keramiko_2020-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);

$schemaId = 862;
$docId = 1612;
printoutGenerator(
    Principal::KERAMIKO_2020(),
    $route,
    baseFileName: "output-keramiko_2020-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '1',
    ]
);

$schemaId = 142;
$docId = 1444;
printoutGenerator(
    Principal::KERAMIKO_2020(),
    $route,
    baseFileName: "output-keramiko_2020-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '0',
    ]
);

// test
// https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/239#note_573319
$schemaId = 142;
$docId = 1444;
printoutGenerator(
    Principal::KERAMIKO_2020(),
    $route,
    baseFileName: "output-keramiko_2020-$schemaId-$docId-hideEpGp",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '0',
        'hideEpGp' => '1',
    ]
);

$schemaId = 607;
$docId = 1435;
printoutGenerator(
    Principal::KERAMIKO_2020(),
    $route,
    baseFileName: "output-keramiko_2020-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '1',
    ]
);
// the API calls on this principal take ages to load in and do not respond even

// Pdf::$apiUrl = 'https://intern-test.digithebutler.at:8443/api/index.php/';
// Pdf::$principal = 'ksbg_test';
// Pdf::$loginPnr = '1';
// Pdf::$loginPassword = 'ksbg2019';

// Pdf::compile(__DIR__, [
// 'schemaId' => '88',
// 'documentId' => '49143',
// 'photoOnSameRow' => '0',
// 'highDefinitionImages' => '1',
// ]);

