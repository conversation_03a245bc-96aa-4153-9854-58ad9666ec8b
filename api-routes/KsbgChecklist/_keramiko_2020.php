<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 3038;
$docId = 4810;

printoutGenerator(
    Principal::KERAMIKO_2020(),
    Route::KsbgChecklist,
    baseFileName: "output-keramiko-2020-$schemaId-$docId",
//    fileType: PrintoutFileType::Html,
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0'
    ]
);
