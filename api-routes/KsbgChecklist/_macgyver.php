<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// https://gitlab.baubuddy.de/external/digu/printouts/-/issues/11
$schemaId = 2151;
$docId = 2205;
$tag = "MR";

printoutGenerator(
    Principal::MACGYVER(),
    Route::KsbgChecklist,
//    fileType: PrintoutFileType::Html,
    baseFileName: "output-macgyver-$schemaId-$docId-$tag-photoOnSameRow-0-highDefinitionImages-0",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0'
    ]
);

printoutGenerator(
    Principal::MACGYVER(),
    Route::KsbgChecklist,
//    fileType: PrintoutFileType::Html,
    baseFileName: "output-macgyver-$schemaId-$docId-$tag-photoOnSameRow-0-highDefinitionImages-1",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '1'
    ]
);

printoutGenerator(
    Principal::MACGYVER(),
    Route::KsbgChecklist,
//    fileType: PrintoutFileType::Html,
    baseFileName: "output-macgyver-$schemaId-$docId-$tag-photoOnSameRow-1-highDefinitionImages-0",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '0'
    ]
);

printoutGenerator(
    Principal::MACGYVER(),
    Route::KsbgChecklist,
//    fileType: PrintoutFileType::Html,
    baseFileName: "output-macgyver-$schemaId-$docId-$tag-photoOnSameRow-1-highDefinitionImages-1",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '1'
    ]
);