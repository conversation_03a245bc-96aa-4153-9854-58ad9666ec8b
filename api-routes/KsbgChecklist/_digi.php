<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 16;
$docId = 44;

printoutGenerator(
    Principal::DIGI(),
    Route::KsbgChecklist,
    baseFileName: "output-digi-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => 'false',
    ]
);

printoutGenerator(
    Principal::DIGI(),
    Route::KsbgChecklist,
    baseFileName: "output-high-definition-digi-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => 'true',
    ]
);
