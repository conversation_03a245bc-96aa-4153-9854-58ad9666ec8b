<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/625#note_545317
$schemaId = 72;
$docId = 55429;
printoutGenerator(
    $principal = Principal::KSBG_TEST(),
    Route::KsbgChecklist,
    baseFileName: "output-ksbg-test-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);

// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/625#note_545317
$schemaId = 61;
$docId = 93248;
printoutGenerator(
    $principal = Principal::KSBG(),
    Route::KsbgChecklist,
    baseFileName: "output-ksbg-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);

// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/625#note_545970
$schemaId = 54;
$docId = 91855;
printoutGenerator(
    $principal = Principal::KSBG(),
    Route::KsbgChecklist,
    baseFileName: "output-ksbg-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);