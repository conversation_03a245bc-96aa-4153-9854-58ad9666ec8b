{"name": "KsbChecklist all types", "description": "some description", "status": "active", "createdBy": "4", "createdOn": "2022-12-23T08:47:19Z", "editedOn": "2022-12-23T10:07:02Z", "positions": [{"id": -9, "title": "<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 3, "parentId": -9, "title": "Resourceselector", "type": "RESOURCESELECTOR"}, {"id": -1, "parentId": -9, "title": "Invoiceselector", "type": "INVOICESELECTOR"}, {"id": -11, "parentId": -9, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "EMPLOYEESELECTOR"}, {"id": -12, "parentId": -9, "title": "Partner", "type": "PARTNERSELECTOR"}, {"id": -13, "parentId": -9, "title": "Kunde", "type": "CUSTOMERSELECTOR"}, {"id": -15, "parentId": -9, "title": "<PERSON><PERSON><PERSON>", "type": "SUPPLIERSELECTOR"}, {"id": -16, "parentId": -9, "title": "Aufgabe", "type": "TASKSELECTOR"}, {"id": -17, "parentId": -9, "title": "measurement", "type": "MEASUREMENT"}, {"id": -18, "title": "measurement without parent", "type": "MEASUREMENT"}, {"id": -19, "title": "photo without parent", "type": "photo"}, {"id": -20, "title": "Info field", "type": "INFO"}, {"id": -21, "title": "Date field", "type": "DATE"}, {"id": -22, "title": "String field", "type": "STRING"}, {"id": -23, "title": "Checkbox field", "type": "CHECKBOX", "values": ["<PERSON>a", "<PERSON><PERSON>"]}, {"id": -24, "title": "Signature field", "type": "SIGNATUREFIELD"}, {"id": -25, "title": "Graphical measurement", "type": "GRAPHICALMEASUREMENT"}, {"id": -26, "title": "Formula field", "type": "FORMULA"}, {"id": -27, "title": "Combobox field", "type": "COMBOBOX", "values": [{"value": "Option 1"}, {"value": "Option 2"}, {"value": "Option 3"}]}, {"id": -28, "title": "Combobox multi field", "type": "COMBOBOX-MULTI", "values": [{"value": "Multi Option 1"}, {"value": "Multi Option 2"}, {"value": "Multi Option 3"}]}, {"id": -29, "title": "Combobox tap field", "type": "COMBOBOX-TAP", "values": [{"value": "i.o."}, {"value": "n.i.o."}, {"value": "n.v."}]}, {"id": -30, "title": "Integer field", "type": "INT"}, {"id": -31, "title": "Float field", "type": "FLOAT"}, {"id": -32, "title": "Time field", "type": "TIME"}, {"id": -33, "title": "Matrix measurement", "type": "MATRIXMEASUREMENT"}, {"id": -34, "title": "Expand sub project structure", "type": "EXPAND-SUB-PROJECT-STRUCTURE"}]}