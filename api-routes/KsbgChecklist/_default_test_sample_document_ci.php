<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';
require_once __DIR__ . '/../../scripts/SampleDocumentationCreator.php';

$creator = new SampleDocumentationCreator(__DIR__, forceDocumentationCreation: false);
$docs = $creator->createDocumentations([
    'schema-displayInside-2.json',
], RelType::Scaffold);

printoutGenerator(
    Principal::DEFAULT_TEST(),
    Route::KsbgChecklist,
    baseFileName: "KsbgChecklist_default_test_scaffold",
    //fileType: PrintoutFileType::Html,
    params: [
        "schemaId" => $docs[0]['schemaId'],
        "documentId" => $docs[0]['documentId'],
        "highDefinitionImages" => "0",
        "photoOnSameRow" => "0",
        "hideEpGp" => "false",
        "oversizeItemStates" => "reported"
		],
    cleanupCallback: function () use ($creator, $docs) {
        $creator->deleteSchemasByIds([$docs[0]['schemaId']]);
    }
);
