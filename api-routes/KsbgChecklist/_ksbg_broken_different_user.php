<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$route = Route::KsbgChecklist;
// breaks on user 296
$principal = Principal::KSBG_USER_3127();
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/660
$schemaId = 61;
$docId = 30237;
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-ksbg-different-user-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
    ]
);

$schemaId = 61;
$docId = 30209;
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-ksbg-different-user-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
    ]
);
