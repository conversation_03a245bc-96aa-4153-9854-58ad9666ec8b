<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$route = Route::KsbgChecklist;
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/673
$principal = Principal::KERAMIKO_2020();
$schemaId = 862;
$docId = 1612;
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-keramiko-2020-image-signatures-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '1'
    ]
);
