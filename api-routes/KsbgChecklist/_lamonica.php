<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/311#note_509756
$schemaId = 2;
$docId = 10;

printoutGenerator(
    Principal::LAMONICA(),
    Route::KsbgChecklist,
//    fileType: PrintoutFileType::Html,
    baseFileName: "output-lamonica-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId
    ]
);
