<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 24;
$docId = 534;
//printoutGenerator(
//    Principal::DEFAULT_TEST(),
//    $route,
//    baseFileName: "output-default_test-$schemaId-$docId.pdf",
//    params: [
//        'schemaId'             => $schemaId,
//        'documentId'           => $docId,
//        'photoOnSameRow'       => '1',
//        'highDefinitionImages' => '1',
//    ]
//);

$schemaId = 822;
$docId = 1582;
printoutGenerator(
    Principal::KERAMIKO_2020(),
    Route::KsbgChecklist,
    baseFileName: "output-keramiko-differences-$schemaId-$docId.pdf",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);


