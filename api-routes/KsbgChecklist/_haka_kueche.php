<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$route = Route::KsbgChecklist;
$principal = Principal::HAKA_KUECHE();
$tag = "MR";

$schemaId = 7;
$docId = 6186;
foreach (['0', '1'] as $photoOnSameRow) {
    foreach (['0', '1'] as $highDef) {
        printoutGenerator(
            $principal,
            $route,
            baseFileName: "output-haka-kueche-$schemaId-$docId-photoOnSameRow-$photoOnSameRow-highDefinitionImages-$highDef-$tag",
            params: [
                'schemaId' => $schemaId,
                'documentId' => $docId,
                'photoOnSameRow' => $photoOnSameRow,
                'highDefinitionImages' => $highDef,
            ]
        );
    }
}