<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 19;
$docId = 5886;
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/673

printoutGenerator(
    Principal::KANZLER_DACH_GMBH(),
    Route::KsbgChecklist,
    baseFileName: "output-kanzler-dach-gmbh-$schemaId-$docId",
    //fileType: PrintoutFileType::Html,
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '1'
    ]
);
