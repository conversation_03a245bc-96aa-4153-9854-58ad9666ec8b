<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "schemaId", required: true);
$config->addParameter(RouteConfigParameterType::int, "documentId", required: true);
$config->addParameter(RouteConfigParameterType::string, "photoOnSameRow", required: false);
$config->addParameter(RouteConfigParameterType::string, "highDefinitionImages", required: false);
$config->addParameter(RouteConfigParameterType::string, "staticClassAndFunctionForPostProcessing", required: false);
$config->addParameter(RouteConfigParameterType::bool, "hideEpGp", required: false);
$config->addParameter(RouteConfigParameterType::string, "oversizeItemStates", required: false);
$config->usePlaywright(new PlaywrightConfig(
    marginTopMm: 5,
    marginBottomMm: 10,
    marginLeftMm: 5,
    marginRightMm: 5,
));
return $config;