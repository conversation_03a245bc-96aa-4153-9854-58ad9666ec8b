<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/204
$schemaId = 70;
$docId = 91592;
printoutGenerator(
    Principal::KSBG(),
    Route::KsbgChecklist,
    baseFileName: "output-ksbg-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);

$schemaId = 44;
$docId = 62724;

//printoutGenerator(
//    $principal,
//    $route,
//    baseFileName: "output-ksbg-$schemaId-$docId",
//    params: [
//        'schemaId'             => $schemaId,
//        'documentId'           => $docId,
//        'photoOnSameRow'       => '0',
//        'highDefinitionImages' => '0',
//    ]
//);
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/625#note_507522


$schemaId = 72;
$docId = 73165;
printoutGenerator(
    Principal::KSBG(),
    Route::KsbgChecklist,
    baseFileName: "output-ksbg-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
    ]
);

$schemaId = 72;
$docId = 69912;
printoutGenerator(
    Principal::KSBG(),
    Route::KsbgChecklist,
    baseFileName: "output-ksbg-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
    ]
);