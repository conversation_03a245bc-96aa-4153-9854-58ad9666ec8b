<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$route = Route::KsbgChecklist;
$principal = Principal::KERAMIKO_2020_USER_125();

$schemaId = 2024;
$docId = 2761;
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/311#note_499149
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-keramiko_2020-measurements-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);

$schemaId = 2024;
$docId = 2764;
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/311#note_499971
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-keramiko_2020-measurements-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);