<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$route = Route::KsbgChecklist;
$principal = Principal::KSBG_TEST();
// https://gitlab.baubuddy.de/external/digu-digital-unlimited/-/issues/676
$schemaId = 61;
$docId = 49167;
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-ksbg_test-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);

$schemaId = 61;
$docId = 49166;
printoutGenerator(
    $principal,
    $route,
    baseFileName: "output-ksbg_test-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0',
    ]
);