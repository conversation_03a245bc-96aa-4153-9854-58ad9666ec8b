<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

// https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/556

$schemaId = 1057;
$docId = 16204;

printoutGenerator(
    Principal::SCHAEFER_GERUESTBAU_GMBH(),
    Route::KsbgChecklist,
//    fileType: PrintoutFileType::Html,
    baseFileName: "output-schaefer-geruestbau-gmbh-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        "highDefinitionImages" => "0",
        "photoOnSameRow" => "0",
        "hideEpGp" => "0",
    ]
);
