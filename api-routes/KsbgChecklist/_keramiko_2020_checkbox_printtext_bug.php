<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 3104;
$docId = 5957;

printoutGenerator(
    Principal::KERAMIKO_2020(),
    Route::KsbgChecklist,
    baseFileName: "output-keramiko-2020-checkbox-print-text-$schemaId-$docId",
//    fileType: PrintoutFileType::Html,
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '0',
        'highDefinitionImages' => '0'
    ]
);
