.pdf‐image‐cell {
    page-break-inside: avoid;
    padding: 5mm;
    box-sizing: border-box;
}

.photosTable tr.heading td {
    padding: 5px;
    page-break-before: always;
    page-break-inside: avoid;
    page-break-after: avoid;
}

.pdf-image-block {
    page-break-inside: avoid;
    display: block;
    max-height: 90vh;
}

.pdf‐image‐title {
    font-size: 13px;
    margin-bottom: 2mm;
}

.pdf‐image‐wrapper {
    page-break-inside: avoid;
}

.pdf‐image‐wrapper img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    display: inline-block;
    page-break-inside: avoid;
}

.arial {
    font-family: Arial, sans-serif;
    font-size: 13px;
}

.table-oversizes td {
    padding: 5px;
}

.stamp img {
    max-height: 200px;
    width: auto;
    display: block;
    margin: 0 auto;
}

.firstTable {
    margin-bottom: 30px;
}

.firstTable td {
    page-break-inside: avoid;
}


.table-full th, .table-full td {
    border: 1px solid lightgray;
}

.table-full {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid lightgray;
}

.table-full tr:nth-child(even) {
    background-color: #f2f2f2;
}

.table-full tr:nth-child(odd) {
    background-color: #ffffff;
}

.vAlign {
    vertical-align: top !important;
}

.hideTable {
    display: none;
}

p.MsoNoSpacing, li.MsoNoSpacing, div.MsoNoSpacing {
    margin: 0 0 .0001pt;
    font-size: 13px;
}

.description, .creationDate {
    font-size: 13px;
}

.signaturesTable {
    page-break-inside: avoid;
    page-break-before: auto;
}

.stamp {
    page-break-inside: avoid;
    page-break-before: auto
}

.photosTable {
    page-break-inside: avoid;
}

table.MsoTableGrid {
    border: 1px solid black;
    font-size: 13px;
}

.tab-interval {
    tab-interval: 35.4pt;
}

.full-width {
    width: 100%;
}

.header-row {
    height: 63.6pt;
}

.cell-border {
    border: 1px solid gray;
}

.cell-border-black {
    border: 1px solid black;
}

.bg-light {
    background: #F2F2F2;
}

.padding-5 {
    padding: 0 5.4pt;
}

.font-8 {
    font-size: 8.0pt;
    font-family: Arial, sans-serif;
}

.font-14 {
    font-size: 14.0pt;
    font-family: Arial, sans-serif;
    color: black;
}

.font-12 {
    font-size: 12.0pt;
    font-family: Arial, sans-serif;
}

.font-11 {
    font-size: 11.0pt;
    font-family: Arial, sans-serif;
}

.font-15 {
    font-size: 15px;
}

.no-spacing {
    margin: 0;
}

.arial {
    font-family: Arial, sans-serif;
}

.headline-cell {
    font-family: Arial, sans-serif;
    padding-left: 5px;
    padding-top: 5px;
    font-size: 11pt;
}

.cell-border-black-no {
    border-bottom: 1px solid black;
    padding: 5px 0;
}

.full-width {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.width-40 {
    width: 40%;
}

.width-60 {
    width: 60%;
}

.width-30 {
    width: 30%;
}

.width-33 {
    width: 33%;
}

.valign-top {
    vertical-align: top;
}

.align-right {
    text-align: right;
}

.table-cell-reset {
    padding: 0;
    margin: 0;
}

.cell-border {
    border: 1px solid gray;
}

.cell-border-black {
    border: 1px solid black;
}

.bg-light {
    background: #F2F2F2;
}

.padding-5 {
    padding: 0 5.4pt;
}

.font-8 {
    font-size: 8.0pt;
    font-family: Arial, sans-serif;
}

.font-14 {
    font-size: 14.0pt;
    font-family: Arial, sans-serif;
    color: black;
}

.font-12 {
    font-size: 12.0pt;
    font-family: Arial, sans-serif;
}

.font-11 {
    font-size: 11.0pt;
    font-family: Arial, sans-serif;
}

.font-15 {
    font-size: 15px;
}

.no-spacing {
    margin: 0;
}

.arial {
    font-family: Arial, sans-serif;
}

.small-text {
    font-size: 6pt;
}

.small-text td {
    padding: 5px 0;
}

.headline-cell {
    font-family: Arial, sans-serif;
    padding-left: 5px;
    padding-top: 5px;
    font-size: 11pt;
}

.same-row-image {
    max-width: 100%;
    max-height: 40vh;
    height: auto;
    display: block;
    margin: 0 auto;
    page-break-inside: avoid;
    break-inside: avoid;
}

.same-row-image-one {
    max-width: 100%;
    max-height: 40vh;
    object-fit: contain;
    display: inline-block;
    page-break-inside: avoid;
}

.same-row-images-container {
    width: 50% !important;
    max-height: 40vh;
    padding: 5px;
    page-break-inside: avoid;
    break-inside: avoid;
    vertical-align: middle;
    text-align: center;
}

.same-row-images-container img {
    max-width: 100%;
    max-height: 40vh;
    object-fit: contain;
    display: inline-block;
}

.same-row-images + .same-row-images {
    margin-left: 25px;
}

.same-row-image-container {
    width: 35%;
    max-height: 40vh;
    border: 1px solid lightgrey;
    font-size: 13px;
    padding: 5px;
    page-break-inside: avoid;
    break-inside: avoid;
    vertical-align: middle;
    text-align: center;
}