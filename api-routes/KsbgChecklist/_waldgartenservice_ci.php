<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 24;
$docId = 57;

printoutGenerator(
    Principal::WALDGARTEN_SERVICE(),
    Route::KsbgChecklist,
    //fileType: PrintoutFileType::Html,
    baseFileName: "output-waldgartenservice-$schemaId-$docId",
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        'photoOnSameRow' => '1',
        'highDefinitionImages' => '1'
    ]
);
