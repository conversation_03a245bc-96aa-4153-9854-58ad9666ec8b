<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$schemaId = 22;
$docId = 882;

printoutGenerator(
    Principal::VERO(),
    Route::KsbgChecklist,
    baseFileName: "output-vero-$schemaId-$docId",
    //fileType: PrintoutFileType::Html,
    params: [
        'schemaId' => $schemaId,
        'documentId' => $docId,
        "highDefinitionImages" => "0",
        "photoOnSameRow" => "0",
        "hideEpGp" => "false",
        "oversizeItemStates" => "reported"
    ]
);
