<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_Bedenkenmeldung())->getData($_GET['schemaId'], $_GET['documentId']);
}

$format = 'd.m.Y';
?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Bedenkenanmeldung</title>
</head>
<body>
<table class="info-table sender-info-table">
    <tr>
        <td>Absender</td>
    </tr>
    <tr>
        <td><?= $data['companyTitle'] ?> <?= $data['companyName'] ?></td>
    </tr>
    <tr>
        <td><?= $data['companyAddress'] ?></td>
    </tr>
    <tr>
        <td><?= $data['companyPostCode'] ?> <?= $data['companyCity'] ?></td>
    </tr>
</table>
<table class="info-table client-info-table">
    <tr>
        <td>Auftraggeber</td>
    </tr>
    <tr>
        <td><?= $data['principalDisplayName'] ?></td>
    </tr>
    <tr>
        <td><?= $data['principalRealAddress'] ?></td>
    </tr>
    <tr>
        <td>
            <?= $data['principalAddressCountryCode'] ?>
            <?= $data['principalPostcode'] ?>
            <?= $data['principalAddressCity'] ?>
        </td>
    </tr>
</table>
<table class="info-table project-info-table">
    <tr>
        <td>Bauvorhaben</td>
    </tr>
    <tr>
        <td><?= $data['projectName'] ?></td>
    </tr>
    <tr>
        <?= $data['projectSiteAddress'] ?>
        <?= $data['projectSiteCity'] ?>
        <?= $data['projectSiteZipCode'] ?>
    </tr>
    <tr>
        <td>
            Auftrag vom <b><?= PrintoutHelper::dateConverter($data['projectValidStartDate'], $format) ?></b>
        </td>
    </tr>
</table>
<div class="w-full" style="clear: both">
    <h3 >Bedenkenanmeldung gem. § 4 Abs. 3 VOB/B</h3>
    <p>Sehr geehrte Damen und Herren,</p>
    <p>
        wir haben es uns als anerkannte Fachfirma zur Aufgabe gemacht, Ihnen immer eine optimale und gleichbleibend
        hochwertige Leistung zu erbringen.
    </p>
    <p>
        In vorbezeichneter Angelegenheit sehen wir es daher als selbstverständlich an, Ihnen mitzuteilen, dass wir
        Bedenken gegen
    </p>
    <p>
        <b><?= $data['Ausführungsart'] ? "&#9745;" : "&squ;"; ?></b>
        <span class="ml-20">die vorgesehene Ausführungsart</span>
    </p>
    <p>
        <b><?= $data['Ausführungsart'] ? "&#9745;" : "&squ;"; ?></b>
        <span class="ml-20">die Güte der von Ihnen gelieferten Stoffe oder Bauteile</span>
    </p>
    <p>
        <b><?= $data['Leistungen'] ? "&#9745;" : "&squ;"; ?> </b>
        <span class="ml-20">die Leistungen anderer Unternehmer</span>
    </p>
    <p>haben weil:</p>
    <p><b><?= $data['Begründung']; ?></b></p>
    <p>
        Hiermit genügen wir unserer Pflicht, gem. § 4 Abs. 3 VOB/B, wonach wir Ihnen unverzüglich Bedenken gegen die
        vorgesehene Art der Ausführung (auch wegen der Sicherung gegen Unfallgefahren), gegen die Güte der von Ihnen
        gelieferten Stoffe oder Bauteile oder gegen die Leistungen anderer Unternehmer mitzuteilen haben.
    </p>
    <p>Wir bitten Sie, unsere Bedenken unverzüglich zu überprüfen und hierzu Stellung zu nehmen.</p>
</div>
<table class="w-full">
    <tr>
        <td style="width: 5%; vertical-align: top">
            <b><?= $data['Ausführung'] ? "&#9745;" : "&squ;"; ?></b>
        </td>
        <td style="padding-bottom: 15px">
            Wir sehen uns gezwungen, die Ausführung der vereinbarten Leistung vorerst nicht zu erbringen bzw. zu
            unterbrechen, bis uns eine Weisung Ihrerseits erreicht.
        </td>
    </tr>
    <tr>
        <td style="width: 5%; vertical-align: top">
            <b><?= $data['FristCheckbox'] ? "&#9745;" : "&squ;"; ?></b>
        </td>
        <td>
            Sollte uns bis zum
            <b><?= isset($data['FristDatum']) ? PrintoutHelper::dateConverter($data['FristDatum'], $format) : " nicht angegeben"; ?> </b>
            keine Stellungnahme von Ihnen vorliegen, gehen wir davon aus, dass der Vertrag entsprechend der
            ursprünglichen Vorgaben ausgeführt werden soll. Eine Haftung für eventuell dadurch später entstehende
            Unzulänglichkeiten lehnen wir jedoch schon jetzt ab.
        </td>
    </tr>
</table>
<br>
<br>
<p style="margin-top: 15px">Mit freundlichen Grüßen</p>
<table class="w-full">
    <tr>
        <td style="vertical-align: bottom">
            <?= $data['Ort'] . ", " . (isset($data['Datum']) ? PrintoutHelper::dateConverter($data['Datum'], $format) : "") ?>
        </td>
        <td style="width: 5%"></td>
        <td>
            <?php if ($data['Unterschrift des Auftragnehmers']) { ?>
                <img src="<?= $data['Unterschrift des Auftragnehmers']; ?>" alt="" style="max-height: 150px; max-width: 100%">
            <?php } ?>
        </td>
    </tr>
    <tr>
        <td style="border-top: 1px solid black">Ort/Datum</td>
        <td></td>
        <td style="border-top: 1px solid black">Unterschrift des Auftragnehmers</td>
    </tr>
</table>
</body>
</html>