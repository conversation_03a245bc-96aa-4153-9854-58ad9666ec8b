<?php
require_once __DIR__ . '/../../printout.helper.php';

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . '/model.php';
    $data = (new C_Bedenkenmeldung())->getData($_GET['schemaId'], $_GET['documentId']);
}
?>

<!doctype html>
<html lang="de">
<head>
    <title>Bedenkenanmeldung</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <style>
        html, body {
            padding: 0;
            margin: 0;
            font-family: Arial, sans-serif;
        }

        .header {
            width: 100%;
            display: -webkit-box;
            -webkit-box-pack: center;
            padding: 10px 0;
        }

        .header img {
            width: 150px;
            display: inline-block;
            clip-path: inset(0 10px 0 0);
        }

        .header .contact {
            display: -webkit-box;
            -webkit-box-pack: center;
            -webkit-box-orient: block-axis;
            margin-left: 15px;
        }

        .header .contact p {
            margin: 0;
            font-weight: lighter;
            display: inline-block;
            font-size: 13px;
            color: rgb(102, 102, 102);
        }
    </style>
</head>
<body>
<div class="header">
    <img src="<?= $data['logo'] ?>" alt="">
    <div class="contact">
        <p><?= $data['addressWithPhone'] ?>
    </div>
</div>
</body>
</html>