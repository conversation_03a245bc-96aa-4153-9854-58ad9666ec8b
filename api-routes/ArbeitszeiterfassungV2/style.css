body {
    margin: 0;
    width: 100%;
    font-family: Arial, sans-serif;
}

.card {
    border: 1px solid #727272;
    margin-bottom: 20px;
    border-radius: 2px;
    overflow: hidden;
}

.card-header {
    background-color: #f1f5fd;
    padding: 8px;
    font-weight: bold;
}

.info-table {
    width: 100%;
    border-collapse: collapse;
}

.info-table th,
.info-table td {
    border-top: 1px solid #727272;
    padding: 8px;
    vertical-align: top;
}

.info-table th {
    background-color: #f1f5fd;
    width: 200px;
    font-weight: 900;
    text-align: left;
}

.combined-table {
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
}

.combined-table .hours-info-row td {
    border: none;
    vertical-align: middle;
}

.main-header th {
    background-color: white;
}

.hours-text-table .main-header th {
    vertical-align: top;
    font-weight: 600;
    text-align: center;
}

.hours-text-table .main-header th svg {
    width: 14px;
    height: 14px;
    vertical-align: middle;
    margin-right: 4px;
    fill: #727272;
}

.hours-text-table th,
.hours-text-table td {
    padding: 0.75rem;
    vertical-align: top;
}


.combined-table .main-header th {
    padding: 0.75rem;
    vertical-align: top;
    font-weight: 600;
    text-align: center;
}

.combined-table .main-header th svg {
    width: 14px;
    height: 14px;
    vertical-align: middle;
    margin-right: 4px;
    fill: #727272;
}

.combined-table th,
.combined-table td {
    padding: 0.75rem;
    vertical-align: top;
}

.combined-table .textarea-row td {
    padding: 0 25px;
    border-top: none;
}

.combined-table .sum-row td {
    font-weight: bold;
    text-align: center;
    border: none;
    background-color: #e9f0ff;
}

.global-sum-row td {
    background-color: #e9f0ff;
    font-weight: bold;
    text-align: center;
}

.combined-table tbody:nth-of-type(even) {
    background-color: #f1f5fd;
}

.combined-table tbody:nth-of-type(odd) {
    background-color: #ffffff;
}

.info-group {
    display: flex;
    align-items: center;
    border: 1px solid #ddd;
    border-radius: 999px;
    padding: 10px
}

.rounded {
    align-items: center;
    border: 1px solid #727272;
    border-radius: 999px;
    display: block !important;
    padding: 10px !important;
    text-align-last: center;
    text-align: center;
}

.value-pill {
    padding-left: 50px;
    font-weight: bold;
}

.label-pill {
    color: #727272;
}

blockquote {
    border-left: 1px solid gray;
    padding-left: 20px;
    margin-inline-start: 60px;
}

.icon-label {
    vertical-align: middle;
    color: #727272;
}

h1 {
    font-size: 0;
    max-height: 0;
    color: transparent;
}

table.bordered, .bordered th, .bordered td {
    border: 1px solid black;
    border-collapse: collapse;
}

.align-top {
    vertical-align: top;
}

.font-bold {
    font-weight: 700;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.bg-blue {
    background: #f1f5fd;
}

.pl-left-td {
    padding-left: 12px;
}

.w-full {
    width: 100%;
}

.nested-table {
    width: 100%;
}

.nested-table td {
    border: 0;
    padding: 0;
}

.border-none {
    /* use !important to unset the .bordered style */
    border: none !important;
}

.main-table tr.besonderheiten td {
    height: 30mm;
    vertical-align: top;
    border-bottom: none
}

.main-table tr.unterschrift td:first-child {
    border-top: none;
}

.main-table tr.unterschrift td:last-child {
    width: 150px;
    border-top: 3px solid black;
}

.footer-body {
    border: 0;
    margin: 0;
}

.footer-table {
    width: 100%;
    font-family: Arial, sans-serif;
}

.footer-table tr td {
    text-align: right;
}

.header-body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
}

.main-table-info tbody:nth-of-type(odd) {
    background-color: #f1f5fd;
}

.main-table-info tbody:nth-of-type(even) {
    background-color: #ffffff;
}

.header-table {
    width: 100%;
    margin-top: 30px;
    border-collapse: collapse;
}

.header-table tr td {
    padding: 3px 6px;
    font-weight: 700;
    border: 1px solid black;
}

.header-table tr td:nth-child(4),
.header-table tr td:nth-child(8),
.header-table tr td:nth-child(9),
.header-table tr td:nth-child(10) {
    text-align: right;
}