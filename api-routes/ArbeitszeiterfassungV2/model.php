<?php
/** @noinspection DuplicatedCode */
require_once __DIR__ . '/../../printout.helper.php';

class C_ArbeitszeiterfassungV2
{
    /** @return array<string, mixed> */
    public function getData(int $projectId, int $workingOrderId, bool $hideBesonderheiten, bool $hideBreakColumn, bool $addBreakToTotal): array
    {
        if ($workingOrderId == null) {
            $workingOrderId = 0;
        }
        $isProject = ($workingOrderId == 0);
        $data = [
            'resources' => [],
            'projectId' => $projectId,
            'workingOrderId' => $workingOrderId,
            'isProject' => $isProject
        ];
        $curl = new PrintoutCurl();
        $base = PrintoutHelper::getApiBaseUrl();

        if (!$isProject) {
            $data['wo'] = PrintoutHelper::downloadWorkingOrder($projectId, $workingOrderId,
                "plannedDate,shortDescription,longDescription,taskName,resourcesNonHr", $curl);
            $resourceUrls = [];
            foreach ($data['wo']['resourcesNonHr'] as $rnr) {
                $resourceUrls[] = "$base/v1/vehicles/select_resource/$rnr";
            }
            $responses = $curl->_multi_call('get', $resourceUrls, [], PrintoutHelper::getHeadersForApiCalls());
            for ($i = 0; $i < count($responses); $i++) {
                $statusCode = $responses[$i]['statusCode'];
                $response = $responses[$i]['response'];
                if ($statusCode != 200) {
                    die_with_response_code(message: 'GET ' . $responses[$i] . " failed with " . $statusCode . "\n" . print_r($response, true));
                }
                $data['resources'][] = $response['kurzname'];
            }
        }

        $data['project'] = PrintoutHelper::downloadProject($projectId,
            "technicalContactKey,projectName,customerName,projectSiteAddress,projectSiteZipCode,projectSiteCity,projectSiteCellPhone,projectSiteEMail",
            $curl);

        $employees = json_decode($curl->_simple_call('get', "$base/v3/employees", [], PrintoutHelper::getHeadersForApiCalls()), true);

        if ($data['project']["technicalContactKey"] >= 1) {
            $found = null;
            foreach ($employees as $employee) {
                if ($employee['pnr'] == $data['project']["technicalContactKey"]) {
                    $found = $employee;
                    break;
                }
            }
            if ($found) {
                $data['bauleiter'] = $found['displayName'];
                $data['bauleiter-email'] = $found['email'];
                if ($found['phone']) {
                    $data['bauleiter-handy'] = $found['phone'];
                } else {
                    $data['bauleiter-handy'] = $found['mobile'];
                }
            }
        }

        $hours = json_decode($curl->_simple_call('get',
            "$base/v1/hours/all?ktr=$projectId&aanr=$workingOrderId&type=approved",
            [], PrintoutHelper::getHeadersForApiCalls()), true);

        usort($hours, function ($a, $b) {
            return strtotime($a['startUTC']) - strtotime($b['startUTC']);
        });

        $employeeHourData = [];
        foreach ($hours as $hour) {
            foreach ($employees as $employee) {
                if ($employee['pnr'] == $hour['pnr']) {
                    $hour['firstName'] = $employee['firstName'];
                    $hour['lastName'] = $employee['lastName'];
                    $hour['profession'] = $employee['profession'];
                    $employeeKey = $employee['pnr'];
                    if (!isset($employeeHourData[$employeeKey])) {
                        $employeeHourData[$employeeKey] = [
                            'firstName' => $employee['firstName'],
                            'lastName' => $employee['lastName'],
                            'profession' => $employee['profession'],
                            'hours' => []
                        ];
                    }
                    $employeeHourData[$employeeKey]['hours'][] = $hour;
                    break;
                }
            }
        }

        $pnrHourData = [];
        foreach ($hours as $hour) {
            foreach ($employees as $employee) {
                if ($employee['pnr'] == $hour['pnr']) {
                    $hour['firstName'] = $employee['firstName'];
                    $hour['lastName'] = $employee['lastName'];
                    $hour['profession'] = $employee['profession'];
                    break;
                }
            }
            $pnrHourData[$hour['pnr']][] = $hour;
        }

        if ($isProject) {
            $files = json_decode($curl->_simple_call('get', "$base/v2/files?filter[projectNo][eq]=$projectId",
                [], PrintoutHelper::getHeadersForApiCalls()), true);
            $data['fileCount'] = count($files);
        } else {
            $files = json_decode($curl->_simple_call('get', "$base/v2/files?"
                . "filter[projectNo][eq]=$projectId&"
                . "filter[workingOrderNo][eq]=$workingOrderId",
                [], PrintoutHelper::getHeadersForApiCalls()), true);
            $files = array_filter($files, fn($f) => str_contains($f['mimeType'] ?? "", "image/"));
            $data['fileIds'] = array_map(fn($f) => $f['fileID'], $files);
        }

        $data['hoursEmployees'] = array_values($employeeHourData);
        $data['hours'] = $pnrHourData;
        $data['logo'] = PrintoutHelper::downloadSettings($curl)['logo'];
        $data['hideBesonderheiten'] = filter_var($hideBesonderheiten, FILTER_VALIDATE_BOOLEAN);
        $data['hideBreakColumn'] = filter_var($hideBreakColumn, FILTER_VALIDATE_BOOLEAN);
        $data['addBreakToTotal'] = filter_var($addBreakToTotal, FILTER_VALIDATE_BOOLEAN);
        return $data;
    }
}