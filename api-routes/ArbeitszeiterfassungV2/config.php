<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "projectId", required: true);
$config->addParameter(RouteConfigParameterType::int, "workingOrderId", required: true);
$config->addParameter(RouteConfigParameterType::bool, "hideBesonderheiten", required: false);
$config->addParameter(RouteConfigParameterType::bool, "hideBreakColumn", required: false);
$config->addParameter(RouteConfigParameterType::bool, "addBreakToTotal", required: false);
$config->wkhtmltopdfArguments = array_merge($config->wkhtmltopdfArguments ?? [], [
    "--orientation", "Landscape",
]);
return $config;