<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection DuplicatedCode */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ArbeitszeiterfassungV2())->getData(
        $_GET['projectId'] ?? 0,
        $_GET['workingOrderId'] ?? 0,
        $_GET['hideBesonderheiten'] ?? false,
        $_GET['hideBreakColumn'] ?? false,
        $_GET['addBreakToTotal'] ?? false
    );
}
$hideBreakColumn = filter_var($_GET['hideBreakColumn'] ?? false, FILTER_VALIDATE_BOOLEAN);
$colWidth = [
    'nr' => $hideBreakColumn ? '75mm' : '40mm',
    'beginn' => $hideBreakColumn ? '55mm' : '28mm',
    'ende' => $hideBreakColumn ? '55mm' : '28mm',
    'arbeitszeit' => $hideBreakColumn ? '105mm' : '48mm',
    'pause' => $hideBreakColumn ? '10mm' : '48mm',
    'fahrtzeit' => $hideBreakColumn ? '110mm' : '42mm',
    'gesamt' => $hideBreakColumn ? '140mm' : '75mm'
];
?>
<!doctype html>
<html lang="de">
<head>
    <title>ArbeitszeiterfassungV2</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
</head>
<body class="header-body" onload="init()">
<table class="hours-text-table" id="table">
    <thead>
    <tr class="main-header">
        <th style="width:<?= $colWidth['nr'] ?>"><span class="icon-label">Nr.</span></th>
        <th style="width:<?= $colWidth['beginn'] ?>">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                <path d="M73 39c-14.8-9.1-33.4-9.4-48.5-.9S0 62.6 0 80L0 432c0 17.4 9.4 33.4 24.5 41.9s33.7 8.1 48.5-.9L361 297c14.3-8.7 23-24.2 23-41s-8.7-32.2-23-41L73 39z"/>
            </svg>
            <span class="icon-label">Beginn</span>
        </th>
        <th style="width:<?= $colWidth['ende'] ?>">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                <path d="M0 128C0 92.7 28.7 64 64 64H320c35.3 0 64 28.7 64 64V384c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128z"/>
            </svg>
            <span class="icon-label">Ende</span>
        </th>
        <th style="width:<?= $colWidth['arbeitszeit'] ?>">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
                <path d="M256 32c-17.7 0-32 14.3-32 32l0 2.3 0 99.6c0 5.6-4.5 10.1-10.1 10.1c-3.6 0-7-1.9-8.8-5.1L157.1 87C83 123.5 32 199.8 32 288l0 64 512 0 0-66.4c-.9-87.2-51.7-162.4-125.1-198.6l-48 83.9c-1.8 3.2-5.2 5.1-8.8 5.1c-5.6 0-10.1-4.5-10.1-10.1l0-99.6 0-2.3c0-17.7-14.3-32-32-32l-64 0zM16.6 384C7.4 384 0 391.4 0 400.6c0 4.7 2 9.2 5.8 11.9C27.5 428.4 111.8 480 288 480s260.5-51.6 282.2-67.5c3.8-2.8 5.8-7.2 5.8-11.9c0-9.2-7.4-16.6-16.6-16.6L16.6 384z"/>
            </svg>
            <span class="icon-label">Arbeitszeit (min)</span>
        </th>
        <?php if (!$hideBreakColumn) { ?>
            <th style="width:<?= $colWidth['pause'] ?>">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                    <path d="M96 64c0-17.7 14.3-32 32-32l320 0 64 0c70.7 0 128 57.3 128 128s-57.3 128-128 128l-32 0c0 53-43 96-96 96l-192 0c-53 0-96-43-96-96L96 64zM480 224l32 0c35.3 0 64-28.7 64-64s-28.7-64-64-64l-32 0 0 128zM32 416l512 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 480c-17.7 0-32-14.3-32-32s14.3-32 32-32z"/>
                </svg>
                <span class="icon-label">Pause (min)</span>
            </th>
        <?php } else { ?>
            <th style="width:<?= $colWidth['pause'] ?>"></th>
        <?php } ?>
        <th style="width:<?= $colWidth['fahrtzeit'] ?>">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                <path d="M176 56l0 40 160 0 0-40c0-4.4-3.6-8-8-8L184 48c-4.4 0-8 3.6-8 8zM128 96l0-40c0-30.9 25.1-56 56-56L328 0c30.9 0 56 25.1 56 56l0 40 0 32 0 352-256 0 0-352 0-32zM64 96l32 0 0 384-32 0c-35.3 0-64-28.7-64-64L0 160c0-35.3 28.7-64 64-64zM448 480l-32 0 0-384 32 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64z"/>
            </svg>
            <span class="icon-label">Fahrtzeit (min)</span>
        </th>
        <th style="width:<?= $colWidth['gesamt'] ?>">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                <path d="M48 192C21.49 192 0 170.5 0 144s21.49-48 48-48H400c26.51 0 48 21.49 48 48s-21.49 48-48 48H48zM400 320c26.51 0 48 21.49 48 48s-21.49 48-48 48H48c-26.51 0-48-21.49-48-48s21.49-48 48-48z"/>
            </svg>
            <span class="icon-label">Gesamt (min)</span>
        </th>
    </tr>
    </thead>
</table>
</body>
</html>
<script>
    function init() {
        const x = document.location.search.substring(1).split('&');
        for (var i in x) {
            const z = x[i].split('=', 2);
            if (z[0] === 'page' && z[1] === '1') {
                document.getElementById('table').style.display = 'none'
            }
        }
    }
</script>
