<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection HtmlDeprecatedAttribute */
/** @noinspection DuplicatedCode */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ArbeitszeiterfassungV2())->getData(
        $_GET['projectId'] ?? 0,
        $_GET['workingOrderId'] ?? 0,
        $_GET['hideBesonderheiten'] ?? false,
        $_GET['hideBreakColumn'] ?? false,
        $_GET['addBreakToTotal'] ?? false
    );
}
$isProject = $data['isProject'];
$hideBreakColumn = filter_var($_GET['hideBreakColumn'] ?? false, FILTER_VALIDATE_BOOLEAN);
$addBreakToTotal = filter_var($_GET['addBreakToTotal'] ?? false, FILTER_VALIDATE_BOOLEAN);

function formatDate(string $date): string
{
    // 2021-01-01 => 01.01.2021
    return substr($date, 8, 2) . "." . substr($date, 5, 2) . "." . substr($date, 0, 4);
}

function formatMinutes(string|int $minutes): string
{
    $minutesInt = is_string($minutes) ? (int)$minutes : $minutes;
    if (!is_numeric($minutes) || $minutesInt <= 0) {
        return '-';
    }
    $hours = intdiv($minutesInt, 60);
    $mins = $minutesInt % 60;
    $formatted = '';
    if ($hours > 0) {
        $formatted .= $hours . 'h';
    }
    if ($mins > 0) {
        $formatted .= ($hours > 0 ? ' ' : '') . $mins . 'min';
    }
    return $formatted;
}

/**
 * @return int 0 on any errors
 */
function calculateMinutes(string $startTime, string $endTime): int
{
    if ($startTime === '' || $endTime === '') {
        return 0;
    }
    try {
        $start = new DateTime($startTime);
        $end = new DateTime($endTime);
    } catch (Exception) {
        return 0;
    }
    $interval = $start->diff($end);
    return ($interval->h * 60) + $interval->i;
}

function emptyStringOnNull(int $number): string
{
    return $number === 0 ? '' : (string)$number;
}

$colWidth = [
    'nr' => $hideBreakColumn ? '75mm' : '40mm',
    'beginn' => $hideBreakColumn ? '55mm' : '28mm',
    'ende' => $hideBreakColumn ? '55mm' : '28mm',
    'arbeitszeit' => $hideBreakColumn ? '105mm' : '48mm',
    'pause' => $hideBreakColumn ? '10mm' : '48mm',
    'fahrtzeit' => $hideBreakColumn ? '110mm' : '42mm',
    'gesamt' => $hideBreakColumn ? '140mm' : '75mm'
];
?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>ArbeitszeiterfassungV2</title>
</head>

<body>
<table style="width: 100%">
    <tr>
        <td style="font-size: 30px; text-align: right; width: 70%; padding-right: 20px;">
            Arbeitszeiterfassung // Stundennachweis
        </td>
        <td style="width: 30%; height: 60px; text-align: right">
            <?php if (isset($data['logo'])) { ?>
                <img style="max-height: 60px; max-width: 100%;" alt="" src="<?= $data['logo'] ?>"/>
            <?php } ?>
        </td>
    </tr>
</table>
<table width="100%" border="0" cellspacing="0" cellpadding="0">
    <tr>
        <td width="70%" valign="top" style="padding-right: 20px">
            <div class="card">
                <div class="card-header">Projektdaten</div>
                <table class="info-table">
                    <tr>
                        <th>Kundenname</th>
                        <td><?= $data['project']['customerName'] ?? '' ?></td>
                    </tr>
                    <tr>
                        <th>Projektnummer</th>
                        <td><?= $data['projectId'] ?></td>
                    </tr>
                    <tr>
                        <th>Projektbeschreibung</th>
                        <td><?= $data['project']['projectName'] ?></td>
                    </tr>
                    <tr>
                        <th>Adresse</th>
                        <td>
                            <?= PrintoutHelper::formatAddress(
                                $data['project']['projectSiteAddress'] ?? '',
                                $data['project']['projectSiteZipCode'] ?? '',
                                $data['project']['projectSiteCity'] ?? ''
                            ); ?>
                        </td>
                    </tr>
                    <tr>
                        <th>zuständiger Bauleiter</th>
                        <td>
                            <?= $data['bauleiter'] ?? '';
                            if (isset($data['bauleiter-email']) && $data['bauleiter-email'] != "") { ?>
                                <svg style="overflow: visible; color: currentcolor; margin-bottom: -3px; margin-left: 9px"
                                     fill="currentColor" stroke-width="0" xmlns="http://www.w3.org/2000/svg"
                                     viewBox="0 0 1024 1024" height="1em" width="1em">
                                    <path d="M928 160H96c-17.7 0-32 14.3-32 32v640c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V192c0-17.7-14.3-32-32-32zm-40 110.8V792H136V270.8l-27.6-21.5 39.3-50.5 42.8 33.3h643.1l42.8-33.3 39.3 50.5-27.7 21.5zM833.6 232 512 482 190.4 232l-42.8-33.3-39.3 50.5 27.6 21.5 341.6 265.6a55.99 55.99 0 0 0 68.7 0L888 270.8l27.6-21.5-39.3-50.5-42.7 33.2z"></path>
                                </svg>
                                <?= $data['bauleiter-email'];
                            }
                            if (isset($data['bauleiter-handy']) && $data['bauleiter-handy'] != "") { ?>
                                <svg fill="currentColor" stroke-width="0" xmlns="http://www.w3.org/2000/svg"
                                     viewBox="0 0 1024 1024"
                                     style="overflow: visible; color: currentcolor; margin-bottom: -3px; margin-left: 9px"
                                     height="1em" width="1em">
                                    <path d="M885.6 230.2 779.1 123.8a80.83 80.83 0 0 0-57.3-23.8c-21.7 0-42.1 8.5-57.4 23.8L549.8 238.4a80.83 80.83 0 0 0-23.8 57.3c0 21.7 8.5 42.1 23.8 57.4l83.8 83.8A393.82 393.82 0 0 1 553.1 553 395.34 395.34 0 0 1 437 633.8L353.2 550a80.83 80.83 0 0 0-57.3-23.8c-21.7 0-42.1 8.5-57.4 23.8L123.8 664.5a80.89 80.89 0 0 0-23.8 57.4c0 21.7 8.5 42.1 23.8 57.4l106.3 106.3c24.4 24.5 58.1 38.4 92.7 38.4 7.3 0 14.3-.6 21.2-1.8 134.8-22.2 268.5-93.9 376.4-201.7C828.2 612.8 899.8 479.2 922.3 344c6.8-41.3-6.9-83.8-36.7-113.8z"></path>
                                </svg>
                                <?= $data['bauleiter-handy'];
                            } ?>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Beschreibung des Arbeitsfortschritts -->
            <div class="card">
                <div class="card-header">
                    Beschreibung des Arbeitsfortschritts der planmäßig zu erledigenden Arbeiten
                </div>
                <table class="info-table">
                    <tr>
                        <th>Kurztext</th>
                        <td><?= $data['wo']['shortDescription'] ?? '' ?></td>
                    </tr>
                    <tr>
                        <th>Langtext</th>
                        <td><?= $data['wo']['longDescription'] ?? '' ?></td>
                    </tr>
                </table>
            </div>
        </td>

        <!-- RIGHT COLUMN: Arbeitszeit/Tagesdaten -->
        <td width="30%" valign="top">
            <div class="card">
                <div class="card-header">Arbeitszeit/Tagesdaten</div>
                <table class="info-table">
                    <tr>
                        <th>Datum</th>
                        <td><?= PrintoutHelper::dateConverter($data['wo']['plannedDate'], 'd.m.Y') ?></td>
                    </tr>
                    <tr>
                        <th>Wochentag</th>
                        <td><?= PrintoutHelper::getWeekDayName(
                                intval(date('N', strtotime($data['wo']['plannedDate']))),
                                "de"
                            ) ?></td>
                    </tr>
                    <tr>
                        <th>geplante Tätigkeiten</th>
                        <td><?= $data['wo']['taskName'] ?? '' ?></td>
                    </tr>
                    <tr>
                        <th> geplante Fahrzeuge</th>
                        <td> <?= implode(", ", $data['resources']) ?></td>
                    </tr>
                    <tr>
                        <th>Arbeitsauftragsnummer</th>
                        <td><?= $data['projectId'] . "-" . $data['workingOrderId'] ?></td>
                    </tr>
                    <tr>
                        <th>Zugehörige Dokumente</th>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Bestellung vom Kunden</th>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Ladeliste</th>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Fotos (Konvolut)</th>
                        <td style="font-size: 12px">
                            <?php if ($isProject) {
                                echo $data['fileCount'] . " insgesamt";
                            } else {
                                echo implode(", ", $data['fileIds']);
                            } ?>
                        </td>
                    </tr>
                </table>
            </div>
        </td>
    </tr>
</table>
<?php
$globalTotalNetWork = 0;  // net work time (raw minus break)
$globalTotalRawWork = 0;  // raw work time
$globalTotalBreak = 0;
$globalTotalDrive = 0;
$globalTotalTotals = 0;
$rowNumber = 1;
?>
<table class="combined-table">
    <?php foreach ($data['hoursEmployees'] as $employeeIndex => $employeeData) {
        $firstName = $employeeData['firstName'] ?? '';
        $lastName = $employeeData['lastName'] ?? '';
        $profession = $employeeData['profession'] ?? '';
        $hoursList = $employeeData['hours'] ?? [];
        $employeeRawWork = 0;
        $employeeNetWork = 0;
        $employeeBreak = 0;
        $employeeDrive = 0;
        $employeeTotal = 0;
        ?>
        <!-- A "header" row for each employee (name, profession, etc.) -->
        <tr class="hours-info-row">
            <td colspan="2" style="background-color: white;">
                <span class="rounded">
                    <span class="label-pill">Vorname</span>
                    <span class="value-pill"><?= $firstName ?></span>
                </span>
            </td>
            <td style="background-color: white; padding-left: 50px ">
                <span style="border-right: 1px solid #d2d2d2;"></span>
            </td>
            <td colspan="2" style="background-color: white;">
                <span class="rounded">
                    <span class="label-pill">Nachname</span>
                    <span class="value-pill"><?= $lastName ?></span>
                </span>
            </td>
            <td style="background-color: white; padding-left: 100px">
                <span style="border-right: 1px solid #d2d2d2;"></span>
            </td>
            <td colspan="2" style="background-color: white;">
                <span class="rounded">
                    <span class="label-pill">Beruf</span>
                    <span class="value-pill"><?= $profession ?></span>
                </span>
            </td>
        </tr>
        <?php if ($employeeIndex === 0) { ?>
            <tr class=" hours-text-table main-header">
                <th style="width:<?= $colWidth['nr'] ?>"><span class="icon-label">Nr.</span></th>
                <th style="width:<?= $colWidth['beginn'] ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                        <path d="M73 39c-14.8-9.1-33.4-9.4-48.5-.9S0 62.6 0 80L0 432c0 17.4 9.4 33.4 24.5 41.9s33.7 8.1 48.5-.9L361 297c14.3-8.7 23-24.2 23-41s-8.7-32.2-23-41L73 39z"/>
                    </svg>
                    <span class="icon-label">Beginn</span>
                </th>
                <th style="width:<?= $colWidth['ende'] ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512">
                        <path d="M0 128C0 92.7 28.7 64 64 64H320c35.3 0 64 28.7 64 64V384c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128z"/>
                    </svg>
                    <span class="icon-label">Ende</span>
                </th>
                <th style="width:<?= $colWidth['arbeitszeit'] ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512">
                        <path d="M256 32c-17.7 0-32 14.3-32 32l0 2.3 0 99.6c0 5.6-4.5 10.1-10.1 10.1c-3.6 0-7-1.9-8.8-5.1L157.1 87C83 123.5 32 199.8 32 288l0 64 512 0 0-66.4c-.9-87.2-51.7-162.4-125.1-198.6l-48 83.9c-1.8 3.2-5.2 5.1-8.8 5.1c-5.6 0-10.1-4.5-10.1-10.1l0-99.6 0-2.3c0-17.7-14.3-32-32-32l-64 0zM16.6 384C7.4 384 0 391.4 0 400.6c0 4.7 2 9.2 5.8 11.9C27.5 428.4 111.8 480 288 480s260.5-51.6 282.2-67.5c3.8-2.8 5.8-7.2 5.8-11.9c0-9.2-7.4-16.6-16.6-16.6L16.6 384z"/>
                    </svg>
                    <span class="icon-label">Arbeitszeit (min)</span>
                </th>
                <?php if (!$hideBreakColumn) { ?>
                    <th style="width:<?= $colWidth['pause'] ?>">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512">
                            <path d="M96 64c0-17.7 14.3-32 32-32l320 0 64 0c70.7 0 128 57.3 128 128s-57.3 128-128 128l-32 0c0 53-43 96-96 96l-192 0c-53 0-96-43-96-96L96 64zM480 224l32 0c35.3 0 64-28.7 64-64s-28.7-64-64-64l-32 0 0 128zM32 416l512 0c17.7 0 32 14.3 32 32s-14.3 32-32 32L32 480c-17.7 0-32-14.3-32-32s14.3-32 32-32z"/>
                        </svg>
                        <span class="icon-label">Pause (min)</span>
                    </th>
                <?php } else { ?>
                    <th style="width:<?= $colWidth['pause'] ?>"></th>
                <?php } ?>
                <th style="width:<?= $colWidth['fahrtzeit'] ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path d="M176 56l0 40 160 0 0-40c0-4.4-3.6-8-8-8L184 48c-4.4 0-8 3.6-8 8zM128 96l0-40c0-30.9 25.1-56 56-56L328 0c30.9 0 56 25.1 56 56l0 40 0 32 0 352-256 0 0-352 0-32zM64 96l32 0 0 384-32 0c-35.3 0-64-28.7-64-64L0 160c0-35.3 28.7-64 64-64zM448 480l-32 0 0-384 32 0c35.3 0 64 28.7 64 64l0 256c0 35.3-28.7 64-64 64z"/>
                    </svg>
                    <span class="icon-label">Fahrtzeit (min)</span>
                </th>
                <th style="width:<?= $colWidth['gesamt'] ?>">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512">
                        <path d="M48 192C21.49 192 0 170.5 0 144s21.49-48 48-48H400c26.51 0 48 21.49 48 48s-21.49 48-48 48H48zM400 320c26.51 0 48 21.49 48 48s-21.49 48-48 48H48c-26.51 0-48-21.49-48-48s21.49-48 48-48z"/>
                    </svg>
                    <span class="icon-label">Gesamt (min)</span>
                </th>
            </tr>
        <?php } ?>
        <?php foreach ($hoursList as $hourRecord) {
            $rawWork = calculateMinutes($hourRecord['start'] ?? '', $hourRecord['end'] ?? '');
            $sumBreak = 0;
            foreach ($hourRecord['breaks'] ?? [] as $oneBreak) {
                $sumBreak += calculateMinutes($oneBreak['start'], $oneBreak['end']);
            }
            $netWork = $rawWork - $sumBreak;
            $sumDrive = calculateMinutes($hourRecord['startDriveFrom'] ?? '', $hourRecord['endDriveFrom'] ?? '')
                + calculateMinutes($hourRecord['startDriveTo'] ?? '', $hourRecord['endDriveTo'] ?? '');
            if ($addBreakToTotal) {
                $totals = $rawWork + $sumDrive;
            } else {
                $totals = $netWork + $sumDrive;
            }
            $employeeRawWork += $rawWork;
            $employeeNetWork += $netWork;
            $employeeBreak += $sumBreak;
            $employeeDrive += $sumDrive;
            $employeeTotal += $totals;
            ?>
            <tbody>
            <tr>
                <td><span class="rounded"><?= $rowNumber ?></span></td>
                <td><span class="rounded"><?= $hourRecord['start'] ?? '-' ?></span></td>
                <td><span class="rounded"><?= $hourRecord['end'] ?? '-' ?></span></td>
                <td><span class="rounded"><?= $netWork > 0 ? $netWork : '-' ?></span></td>
                <?php if (!$hideBreakColumn) { ?>
                    <td><span class="rounded"><?= $sumBreak > 0 ? $sumBreak : '-' ?></span></td>
                <?php } else { ?>
                    <td></td>
                <?php } ?>
                <td><span class="rounded"><?= $sumDrive > 0 ? $sumDrive : '-' ?></span></td>
                <td><span class="rounded"><?= $totals > 0 ? $totals : '-' ?></span></td>
            </tr>
            <tr class="textarea-row">
                <td colspan="7">
                    <blockquote>
                        <?= $hourRecord['task_name'] ?? '' ?>
                    </blockquote>
                </td>
            </tr>
            </tbody>
            <?php
            $rowNumber++;
        }
        $globalTotalRawWork += $employeeRawWork;
        $globalTotalNetWork += $employeeNetWork;
        $globalTotalBreak += $employeeBreak;
        $globalTotalDrive += $employeeDrive;
        $globalTotalTotals += $employeeTotal;
        ?>

        <!-- Sum row for the current employee -->
        <tbody style="margin-top: 10px">
        <tr class="sum-row" style="border-top: 12px solid #ffffff; border-bottom: 12px solid #ffffff;">
            <td style="border-top-left-radius: 42px; border-bottom-left-radius: 42px;">Summe</td>
            <td></td>
            <td></td>
            <td><?= formatMinutes(emptyStringOnNull($employeeNetWork)) ?></td>
            <?php if (!$hideBreakColumn) { ?>
                <td><?= formatMinutes(emptyStringOnNull($employeeBreak)) ?></td>
            <?php } else { ?>
                <td></td>
            <?php } ?>
            <td><?= formatMinutes(emptyStringOnNull($employeeDrive)) ?></td>
            <td style="border-top-right-radius: 42px; border-bottom-right-radius: 42px;">
                <?= formatMinutes(emptyStringOnNull($employeeTotal)) ?>
            </td>
        </tr>
        </tbody>

    <?php } ?>
    <!-- Global Totals -->
    <tr class="global-sum-row">
        <td>Gesamtsumme</td>
        <td></td>
        <td></td>
        <td>
            <div>Arbeit</div>
        </td>
        <?php if (!$hideBreakColumn) { ?>
            <td>
                <div>Pause</div>
            </td>
        <?php } else { ?>
            <td>
                <div></div>
            </td>
        <?php } ?>
        <td>
            <div>Fahrt</div>
        </td>
        <td>
            <div>Gesamt</div>
        </td>
    </tr>
    <tr class="global-sum-row">
        <td></td>
        <td></td>
        <td></td>
        <td>
            <div><?= formatMinutes(emptyStringOnNull($globalTotalNetWork)) ?></div>
        </td>
        <?php if (!$hideBreakColumn) { ?>
            <td>
                <div><?= formatMinutes(emptyStringOnNull($globalTotalBreak)) ?></div>
            </td>
        <?php } else { ?>
            <td>
                <div></div>
            </td>
        <?php } ?>
        <td>
            <div><?= formatMinutes(emptyStringOnNull($globalTotalDrive)) ?></div>
        </td>
        <td>
            <div><?= formatMinutes(emptyStringOnNull($globalTotalTotals)) ?></div>
        </td>
    </tr>
</table>
</body>
</html>