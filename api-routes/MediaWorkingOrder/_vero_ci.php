<?php
require_once __DIR__ . '/../../PrintoutCompiler.php';

$images = [
    "imagesPerPage" => 1,
    "saveHardCopy" => false,
    "images" => [
        [
            "fileId" => 203748,
            "description" => "Erweiterung Gerüst Gauben Rückfront.",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 203747,
            "description" => "Erweiterung Gerüst Gaube links Vorderfront.",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
        [
            "fileId" => 203746,
            "description" => "Erweiterung Gerüst Gaube rechts Vorderfront.",
            "showWorkingOrderNumber" => true,
            "showUploader" => true,
            "showComments" => true,
            "showUploadDate" => true,
            "showCreationDate" => true,
        ],
    ],
];

// https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/510
printoutGenerator(
    Principal::VERO(),
    Route::MediaWorkingOrder,
    apiRouteSuffix: "/2501013/5",
    method: RequestMethod::POST,
    requestBodyForPost: json_encode($images)
);
