<?php
$config = new RouteConfig();
$config->addRouteSegment(RouteConfigParameterType::int, "projectNo", required: true);
$config->addRouteSegment(RouteConfigParameterType::int, "workingOrderNo", required: true);
$config->redirectToRoute = 'MediaPrint';
$config->beforeRouteCallback = function (&$query) {
    $query['projectNo'] = $_GET['projectNo'] = Request::$query['projectNo'] ?? '';
    $query['workingOrderNo'] = $_GET['workingOrderNo'] = Request::$query['workingOrderNo'] ?? '';
    $query['imagesPerPage'] = $_GET['imagesPerPage'] = Request::input('imagesPerPage', 1);
};
$config->requestMethod = PrintoutRequestMethod::POST;
$config->wkhtmltopdfArguments = [
    "--disable-smart-shrinking",
    "--no-pdf-compression",
    "--page-size", "a4",
    "--margin-bottom", "0",
    "--margin-top", "0",
    "--margin-left", "0",
    "--margin-right", "0",
    "--dpi", "300",
];
return $config;