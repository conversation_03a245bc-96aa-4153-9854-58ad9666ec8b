body {
    background: none;
    -ms-zoom: 1.665;
    font-family: Arial, sans-serif;
    border: 0;
    margin: 0;
    padding: 0;
}

u {
    text-underline-offset: 2px;
}

@page {
    size: A4 landscape;
}

.landscape {
    height: 210mm;
    width: 297mm;
    padding: 5mm;
    box-sizing: border-box;
    display: flex;
    align-self: end;
    justify-content: space-between;
}

.table-right, .table-left {
    padding: 10px;
    display: table;
}

.table-right {
    width: 55%;
}

.table-left {
    width: 45%;
}

.table-left td {
    padding: 3px 15px;
}

.table-right td {
    padding: 3px;
}

.signatures {
    max-height: 60px;
    max-width: 100%;
    width: 90px;
    height: 60px;
    object-fit: contain;
}

.standard-font {
    font-size: 10px;
}

.border-bottom {
    border-bottom: 2px solid #cdcdcd;
}

.image-style {
    height: 80px;
    width: 100px;
    border: 3px solid #958b8b;
    padding: 3px;
}

.rotate {
    writing-mode: vertical-rl;
    transform: rotate(180deg);
    text-align: left;
}

.checkbox-style {
    border: 2px solid #cdcdcd;
    text-align: center;
}

.checkbox-size {
    font-size: 12px;
}
