{"name": "Prüfprotokoll für Arbeits- und Schutzgerüste", "status": "active", "createdBy": "4", "createdOn": "", "positions": [{"id": 1, "title": "Prüfprotokoll für Arbeits- und Schutzgerüste", "type": "headline"}, {"id": 2, "parentId": 1, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (ggf. Stempel)", "type": "headline"}, {"id": 3, "parentId": 2, "title": "<PERSON><PERSON><PERSON>", "type": "string"}, {"id": 4, "parentId": 2, "title": "Auftraggeber", "type": "string"}, {"id": 5, "parentId": 2, "title": "Befähigte Person", "type": "string"}, {"id": 6, "parentId": 1, "title": "Arbeitsgerüst (DIN EN 12811)", "type": "headline"}, {"id": 7, "parentId": 6, "title": "Arbeitsgerüst als", "type": "combobox-multi", "value": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": 8, "parentId": 1, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (DIN 4420)", "type": "headline"}, {"id": 9, "parentId": 8, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> als", "type": "combobox-multi", "value": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Treppenturm"]}, {"id": 10, "parentId": 8, "title": "Sondergerüste", "type": "string"}, {"id": 11, "parentId": 1, "title": "Lastklasse", "type": "headline"}, {"id": 12, "parentId": 11, "title": "Lastklasse", "type": "combobox-multi", "value": ["2 (1.5 kN/m²)", "3 (2.0 kN/m²)", "4 (3.0 kN/m²)"]}, {"id": 13, "parentId": 1, "title": "Breitenklasse", "type": "headline"}, {"id": 14, "parentId": 13, "title": "Breitenklasse", "type": "combobox-multi", "value": ["W06", "W09"]}, {"id": 15, "parentId": 13, "title": "Nutzungsbeschränkung", "type": "string"}, {"id": 16, "parentId": 1, "title": "Durch befähigte Person des Gerüsterstellers geprüft", "type": "headline"}, {"id": 17, "parentId": 16, "title": "Datum", "type": "date"}, {"id": 18, "parentId": 16, "title": "Name", "type": "string"}, {"id": 19, "parentId": 16, "title": "Unterschrift", "type": "signatureField"}, {"id": 20, "parentId": 1, "title": "Der Auftraggeber", "type": "headline"}, {"id": 21, "parentId": 20, "title": "Datum", "type": "date"}, {"id": 22, "parentId": 20, "title": "Name", "type": "string"}, {"id": 23, "parentId": 20, "title": "Unterschrift", "type": "signatureField"}, {"id": 24, "title": "Checkliste", "type": "headline"}, {"id": 25, "parentId": 24, "title": "Gerüstbauteile", "type": "headline"}, {"id": 26, "parentId": 25, "title": "Augenschein<PERSON> unbeschädigt", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 27, "parentId": 24, "title": "Standsicherheit", "type": "headline"}, {"id": 28, "parentId": 27, "title": "Tragfähigkeit der Aufstandsfläche", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 29, "parentId": 27, "title": "Fußspindel – Auszugslänge", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 30, "parentId": 27, "title": "Verstrebungen / Diagonalen", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 31, "parentId": 27, "title": "Längsriegel – in Fußpunkthöhe", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 32, "parentId": 27, "title": "Gitterträger – Aussteifungen", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 33, "parentId": 27, "title": "Verankerungen – nach Montageanweisung/Aufbau- und Verwendungsanleitung", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 34, "parentId": 24, "title": "Beläge", "type": "headline"}, {"id": 35, "parentId": 34, "title": "Gerüstlagen – voll ausgelegt / Belagssicherung", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 36, "parentId": 34, "title": "Systembeläge – einschließlich Konsolenbelag", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 37, "parentId": 34, "title": "Eckausbildung – in voller Breite herumgeführt", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 38, "parentId": 34, "title": "Gerüsthöhen – Querstabilität, Auflagerung", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 39, "parentId": 34, "title": "Öffnungen – zwischen den Belägen", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 40, "parentId": 24, "title": "Arbeits- und Betriebssicherheit", "type": "headline"}, {"id": 41, "parentId": 40, "title": "Seitenschutz – einschließlich Stirnseitenschutz", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 42, "parentId": 40, "title": "Wandabstand ≤ 30 cm", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 43, "parentId": 40, "title": "Innengeländer Seitenschutz", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 44, "parentId": 40, "title": "Aufstiege, Zugänge – Abstand ≤ 50 m", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 45, "parentId": 40, "title": "<PERSON><PERSON><PERSON><PERSON>urm, Gerüsttreppe", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 46, "parentId": 40, "title": "An<PERSON><PERSON><PERSON> ≤ 5 m, <PERSON><PERSON>gang", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 47, "parentId": 40, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 48, "parentId": 40, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 49, "parentId": 40, "title": "Verkehrssicherung – Beleuchtung", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 50, "parentId": 24, "title": "Fahrgerüste", "type": "headline"}, {"id": 51, "parentId": 50, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 52, "parentId": 50, "title": "Ballast / Verbreiterungen", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 53, "parentId": 24, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 54, "parentId": 53, "title": "Gerüstkennzeichnung – an den Zugängen", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 55, "parentId": 24, "title": "<PERSON><PERSON><PERSON>", "type": "headline"}, {"id": 56, "parentId": 55, "title": "Nicht fertig gestellte Bereiche abgesperrt und Verbotszeichen 'Zutritt verboten' angebracht", "type": "combobox", "value": ["ja", "nein", "nicht zutreffend"]}, {"id": 57, "parentId": 24, "title": "Bemerkungen / Hinweise", "type": "string"}]}