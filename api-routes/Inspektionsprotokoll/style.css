* {
    font-family: Arial, sans-serif;
    padding: 0;
    font-size: 12px;
}

.image {
    width: 100%;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
}

.table-bordered, .table-bordered td, .table-bordered td {
    border: 1px solid black;
    border-collapse: collapse;
}

.project-author {
    padding-left: 5px !important;
}

.signatures {
    height: 75px;
}

.protocol-table tr:nth-child(1),
.protocol-table tr:nth-child(2) {
    border-bottom: 1px solid black;
    border-top: 1px solid black;
    border-collapse: collapse !important;
}

.protocol-table td:nth-child(1),
.protocol-table tr:nth-child(1) {
    border-left: 1px solid black;
    border-right: 1px solid black;
}

.protocol-table td:nth-child(2),
.protocol-table td:nth-child(3) {
    border-collapse: collapse !important;
}

.protocol-table td:nth-child(3) {
    border-right: 1px solid black;
}

.protocol-table td {
    border-collapse: collapse !important;
}

.threehpx {
    width: 50px;
}

h4 {
    font-size: 15px;
}

p {
    float: left;
    font-size: 13px;
}

body {
    margin: 0;
    padding: 0;
    width: 190mm;
}

table {
    border-spacing: 0 !important;
    width: 100%;
    border-collapse: collapse;
}

.protocol-table {
    font-size: 13px;
    border-collapse: collapse;
}

.center-title {
    text-align: center;
    font-weight: bold;
}

.empty {
    max-width: 15px;
    visibility: hidden;
}

.font-weight-bold {
    font-weight: bold;
}

.protocol-rows-td {
    padding: 2px;
    width: 100%;
    border: 1px solid black;
    border-collapse: collapse;
    border-spacing: 0;
}

.cell-num {
    width: 50px !important;
    /** increase right padding which looks nicer */
    padding-right: 4px !important;
}

.no-border td {
    border-style: hidden !important;
}

.additional-remarks-table {
    font-weight: bold;
    font-size: 13px;
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
}

.additional-remarks-table td {
    padding: 2px;
    border-spacing: 0;
    border: 1px solid black;
    border-collapse: collapse;
    height: 11px;
}

.checkboxes {
    font-size: 18px;
}

.checkboxes span {
    font-size: 13px;
    margin-left: -4px;
    margin-right: 9px;
}

.table-no-borders * {
    border-style: hidden !important;
}

.project-author-label {
    width: 180px;
    padding: 6px;
}

.project-author-value {
    padding: 6px;
}

.width-180 {
    width: 180px;
}

.main-table-container {
    margin-top: 30px;
    height: 100%;
}

.full-width-cell {
    width: 100%;
}

.full-height-table {
    height: 100%;
}

.min-width-150 {
    min-width: 150px;
}

.margin-top-60 {
    margin-top: 60px;
}

.photo-container {
    width: 32.8%;
    display: inline-block;
    border: 1px solid black;
    margin-bottom: 4px;
}

.photo-image {
    height: 300px;
}

.photo-caption {
    padding: 3px;
    display: inline-block;
    width: 100%;
}

.photo-documentation-wrapper {
    page-break-inside: avoid;
}