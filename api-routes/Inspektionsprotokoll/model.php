<?php

require_once __DIR__ . '/../../printout.helper.php';

class C_Inspektionsprotokoll
{
    /**
     * @return array<string, mixed>
     */
    public function getData(string $schemaId, string $documentId): array
    {
        $curl = new PrintoutCurl();

        $doc = PrintoutHelper::downloadHierarchicalDocument($schemaId, $documentId, $curl);
        $partial = "projectName";
        $project = PrintoutHelper::downloadProject($doc['documentRelKey1'], $partial, $curl);
        $fullDocument = $doc['fullDocument'];
        $employee = PrintoutHelper::downloadEmployee($fullDocument['author'], $curl);

        $templateData = PrintoutHelper::mapDocumentChildrenToValues($fullDocument, true);
        $displayInsideChildren = $this->getDisplayInside($fullDocument);
        $templateData['documentData'] = $doc['document'];
        $templateData['projectName'] = $project['projectName'];
        $templateData['author'] = $employee['displayName'];
        $templateData['creationDate'] = $fullDocument['documentCreatedOn'];

        $photos = $this->processPhotos($templateData);
        $templateData['photos'] = $photos;

        // if the document contains no values, there are no children
        $documentData = $templateData['documentData'][0]["children"] ?? [];
        $templateData['protocolData'] = $this->getProtocolData($documentData);
        $templateData['additionalRemarksData'] = $this->getAdditionalRemarksData($documentData);
        $templateData['signatures'] = $this->getSignatures($documentData);

        // Format dates
        $templateData['documentDate'] = $this->formatDate($templateData['creationDate']);
        $templateData['creationDateFormatted'] = $this->formatDate($templateData['creationDate']);

        return array_merge($templateData, $displayInsideChildren);
    }

    /**
     * @param array<string, mixed> $templateData
     * @return array<mixed>
     */
    private function processPhotos(array $templateData): array
    {
        $photos = [];
        foreach ($templateData as $key => $value) {
            if (str_ends_with($key, " Foto")) {
                $number = explode(" ", $key, 2);
                $photos[] = [
                    'number' => $number[0],
                    // note that we are only displaying the first picture although type=photo supports more
                    'url' => $value[0]['filePath']
                ];
            }
        }

        $photosSliced = [];
        for ($i = 0; $i < count($photos); $i += 3) {
            $photosSliced[] = array_slice($photos, $i, 3);
        }

        return $photosSliced;
    }

    /**
     * @param array<mixed> $documentData
     * @return array<string>
     */
    private function getSignatures(array $documentData): array
    {
        foreach ($documentData as $child) {
            if (isset($child['title']) && $child['title'] === 'Signaturfelder' && isset($child['children'][1]['filePath'], $child['children'][2]['filePath'])) {
                return [$child['children'][1]['filePath'], $child['children'][2]['filePath']];
            }
        }
        return ['', ''];
    }

    /**
     * @param array<mixed> $documentData
     * @return array<mixed>
     */
    private function getAdditionalRemarksChildren(array $documentData): array
    {
        foreach ($documentData as $data) {
            if ($data['title'] === 'Zusätzliche Bemerkungen bzw. Abweichung:') {
                return $data['children'];
            }
        }
        return [];
    }

    private function isNumberedHeadline(string $title): bool
    {
        return preg_match('/^\d+\./', trim($title)) === 1;
    }

    private function formatDate(string $dateString): string
    {
        return date_format(date_create($dateString), "d.m.Y");
    }

    /**
     * @param array<mixed> $documentData
     * @return array<mixed>
     */
    private function getProtocolData(array $documentData): array
    {
        $protocolData = [];

        foreach ($documentData as $docDataItem) {
            $elType = $docDataItem["type"];
            if ($elType == "headline") {
                $mainTitle = $docDataItem["title"] ?? "";

                // Only include headlines that start with a number followed by a dot
                if (!$this->isNumberedHeadline($mainTitle)) {
                    continue;
                }

                $notApplicable = $docDataItem["children"][0]["title"] ?? "";

                if ($notApplicable == "nicht zutreffend") {
                    $notApplicableReportedValue = $docDataItem["children"][0]["reportedValue"] == "Ja" ? "1" : "0";
                    $protocolData[] = [
                        'type' => 'headline_with_not_applicable',
                        'title' => $mainTitle,
                        'notApplicableValue' => $notApplicableReportedValue
                    ];
                } else {
                    $protocolData[] = [
                        'type' => 'headline',
                        'title' => $mainTitle
                    ];
                }
            }

            if (isset($docDataItem["children"]) && is_array($docDataItem["children"])) {
                foreach ($docDataItem["children"] as $docDataSubItem) {
                    $elType = $docDataSubItem["type"];
                    $mainTitle = $docDataSubItem["title"];

                    if ($elType == "combobox" && $mainTitle != "nicht zutreffend") {
                        $numTitle = explode(" ", $docDataSubItem["title"])[0];
                        $mainTitle = explode(" ", $docDataSubItem["title"])[1];
                        $yesNoReportedValue = isset($docDataSubItem["reportedValue"]) && ($docDataSubItem["reportedValue"] === "Ja" ? "1" : "0");

                        $protocolData[] = [
                            'type' => 'combobox',
                            'number' => $numTitle,
                            'title' => $mainTitle,
                            'value' => $yesNoReportedValue
                        ];
                    }
                }
            }
        }

        return $protocolData;
    }

    /**
     * @param array<mixed> $documentData
     * @return array<mixed>
     */
    private function getAdditionalRemarksData(array $documentData): array
    {
        $remarksData = [];
        $additionalRemarksChildren = $this->getAdditionalRemarksChildren($documentData);

        foreach ($additionalRemarksChildren as $remarksChild) {
            $toNumber = explode(" ", $remarksChild["title"])[1];

            $remarkZuNr = '';
            $remarkReportedValue = '';
            $responsiblePerson = '';
            $finishedBy = '';
            foreach ($remarksChild["children"] ?? [] as $child) {
                if ($child['title'] === "Zu Nr.") {
                    $remarkZuNr = $child['reportedValue'];
                } else if ($child['title'] === "Bemerkung") {
                    $remarkReportedValue = $child['reportedValue'];
                } else if ($child['title'] === "Zuständig") {
                    $responsiblePerson = $child['reportedValue'];
                } else if ($child['title'] === "Zu erledigen bis") {
                    $finishedBy = $child['reportedValue'];
                }
            }

            $remarksData[] = [
                'toNumber' => $toNumber,
                'remarkZuNr' => $remarkZuNr,
                'remarkReportedValue' => $remarkReportedValue,
                'responsiblePerson' => $responsiblePerson,
                'finishedBy' => $finishedBy
            ];
        }

        return $remarksData;
    }

    /**
     * @param array<string, mixed> $document
     * @return array<string, mixed>
     */
    public function getDisplayInside(array $document): array
    {
        if (!isset($document['children'])) {
            return [];
        }
        $data = [];
        foreach ($document['children'] as $parent) {
            foreach ($document['children'] as $child) {
                if (isset($child['displayInside']) && $parent['id'] === $child['displayInside']) {
                    $title = $parent['title'] . ' ' . $child['title'];

                    if (strtoupper($child['type']) === SchemaTypes::PHOTO) {
                        $data[$title][0] = [
                            'thumbPath' => $child['thumbPath'],
                            'filePath' => $child['filePath']
                        ];
                    } else {
                        $data[$title] = $child['reportedValue'];
                    }
                }
            }
        }

        return $data;
    }
}
