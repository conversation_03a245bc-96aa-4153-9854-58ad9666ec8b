{"name": "Inspektionsprotokoll", "description": "Das Inspektionsprotokoll stellt die momentane Ist-Situation der Baustelle dar. Das Protokoll erhebt keinen Anspruch auf Vollständigkeit. Die Pflichten des Unternehmers bleiben hiervon unberührt. Im Protokoll erfasste Mängel werden intern an die Fachkraft für Arbeitssicherheit weitergeleitet.", "createdOn": "2025-08-04T00:00:00Z", "positions": [{"id": -1, "title": "Inspektionsprotokoll Baustelle - Revision", "type": "headline"}, {"id": -2, "parentId": -1, "title": "Baustelle:", "type": "string"}, {"id": -3, "parentId": -1, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>:", "type": "string"}, {"id": -4, "title": "1. Organisatorische Maßnahmen", "type": "headline"}, {"id": -5, "parentId": -4, "title": "1.1 Gefährdungsbeurteilung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -6, "title": "Foto", "type": "photo", "displayInside": -5}, {"id": -7, "title": "Bewertung", "type": "string", "displayInside": -5}, {"id": -8, "parentId": -4, "title": "1.2 Unterweisung / Einweisung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -9, "title": "Foto", "type": "photo", "displayInside": -8}, {"id": -10, "title": "Bewertung", "type": "string", "displayInside": -8}, {"id": -11, "parentId": -4, "title": "1.3 Lagerflächen (Regale/Ablagemöglichkeiten)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -12, "title": "Foto", "type": "photo", "displayInside": -11}, {"id": -13, "title": "Bewertung", "type": "string", "displayInside": -11}, {"id": -14, "parentId": -4, "title": "1.4 Sicherheitskennzeichnung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -15, "title": "Foto", "type": "photo", "displayInside": -14}, {"id": -16, "title": "Bewertung", "type": "string", "displayInside": -14}, {"id": -17, "parentId": -4, "title": "1.5 Betriebsanleitungen / -anweisungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -18, "title": "Foto", "type": "photo", "displayInside": -17}, {"id": -19, "title": "Bewertung", "type": "string", "displayInside": -17}, {"id": -20, "parentId": -4, "title": "1.6 Prüfungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -21, "title": "Foto", "type": "photo", "displayInside": -20}, {"id": -22, "title": "Bewertung", "type": "string", "displayInside": -20}, {"id": -23, "title": "2. Allgemeine Anforderungen", "type": "headline"}, {"id": -24, "parentId": -23, "title": "2.1 Allgemeine Ordnung / Sauberkeit", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -25, "title": "Foto", "type": "photo", "displayInside": -24}, {"id": -26, "title": "Bewertung", "type": "string", "displayInside": -24}, {"id": -27, "parentId": -23, "title": "2.2 Verkehrswege", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -28, "title": "Foto", "type": "photo", "displayInside": -27}, {"id": -29, "title": "Bewertung", "type": "string", "displayInside": -27}, {"id": -30, "parentId": -23, "title": "2.3 Sozialeinrichtungen / Sanitäre Einrichtungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -31, "title": "Foto", "type": "photo", "displayInside": -30}, {"id": -32, "title": "Bewertung", "type": "string", "displayInside": -30}, {"id": -33, "parentId": -23, "title": "2.4 Beleuchtung am Arbeitsplatz", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -34, "title": "Foto", "type": "photo", "displayInside": -33}, {"id": -35, "title": "Bewertung", "type": "string", "displayInside": -33}, {"id": -36, "parentId": -23, "title": "2.5 Staubarme Arbeitsverfahren", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -37, "title": "Foto", "type": "photo", "displayInside": -36}, {"id": -38, "title": "Bewertung", "type": "string", "displayInside": -36}, {"id": -39, "parentId": -23, "title": "2.6 Persönliche Schutzausrüstung PSA/ PSAgA", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -40, "title": "Foto", "type": "photo", "displayInside": -39}, {"id": -41, "title": "Bewertung", "type": "string", "displayInside": -39}, {"id": -42, "parentId": -23, "title": "2.7 Ladungssicherung/ Transport von Gefahrgut", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -43, "title": "Foto", "type": "photo", "displayInside": -42}, {"id": -44, "title": "Bewertung", "type": "string", "displayInside": -42}, {"id": -45, "title": "3. <PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": -46, "parentId": -45, "title": "3.1 Erste-Hilfe-Einrichtungen / Verbandkasten", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -47, "title": "Foto", "type": "photo", "displayInside": -46}, {"id": -48, "title": "Bewertung", "type": "string", "displayInside": -46}, {"id": -49, "parentId": -45, "title": "3.2 Ersthelfer", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -50, "title": "Foto", "type": "photo", "displayInside": -49}, {"id": -51, "title": "Bewertung", "type": "string", "displayInside": -49}, {"id": -52, "parentId": -45, "title": "3.3 Notfallplan / Fluchtwege", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -53, "title": "Foto", "type": "photo", "displayInside": -52}, {"id": -54, "title": "Bewertung", "type": "string", "displayInside": -52}, {"id": -55, "parentId": -45, "title": "3.4 Feuerlöscher / Brandschutz", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -56, "title": "Foto", "type": "photo", "displayInside": -55}, {"id": -57, "title": "Bewertung", "type": "string", "displayInside": -55}, {"id": -58, "title": "4. Arbeitsverfahren", "type": "headline"}, {"id": -59, "parentId": -58, "title": "4.1 Arbeiten im Gefahrenbereich", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -60, "title": "Foto", "type": "photo", "displayInside": -59}, {"id": -61, "title": "Bewertung", "type": "string", "displayInside": -59}, {"id": -62, "parentId": -58, "title": "4.2 Abstimmung mit anderen Unternehmen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -63, "title": "Foto", "type": "photo", "displayInside": -62}, {"id": -64, "title": "Bewertung", "type": "string", "displayInside": -62}, {"id": -65, "parentId": -58, "title": "4.3 Arbeitsausführung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -66, "title": "Foto", "type": "photo", "displayInside": -65}, {"id": -67, "title": "Bewertung", "type": "string", "displayInside": -65}, {"id": -68, "title": "5. Elektrische Anlagen und Betriebsmittel", "type": "headline"}, {"id": -69, "title": "nicht zutreffend", "type": "combobox", "displayInside": -68, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -70, "parentId": -68, "title": "5.1 Baustromverteiler", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -71, "title": "Foto", "type": "photo", "displayInside": -70}, {"id": -72, "title": "Bewertung", "type": "string", "displayInside": -70}, {"id": -73, "parentId": -68, "title": "5.2 FI-Schutzschalter", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -74, "title": "Foto", "type": "photo", "displayInside": -73}, {"id": -75, "title": "Bewertung", "type": "string", "displayInside": -73}, {"id": -76, "parentId": -68, "title": "5.3 Leitungen / Letungsroller", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -77, "title": "Foto", "type": "photo", "displayInside": -76}, {"id": -78, "title": "Bewertung", "type": "string", "displayInside": -76}, {"id": -79, "title": "6. <PERSON>ug<PERSON><PERSON> und Gräben", "type": "headline"}, {"id": -80, "title": "nicht zutreffend", "type": "combobox", "displayInside": -79, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -81, "parentId": -79, "title": "6.1 Böschungswinkel / Verbau", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -82, "title": "Foto", "type": "photo", "displayInside": -81}, {"id": -83, "title": "Bewertung", "type": "string", "displayInside": -81}, {"id": -84, "parentId": -79, "title": "6.2 Grabenbreite / Arbeitsraum", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -85, "title": "Foto", "type": "photo", "displayInside": -84}, {"id": -86, "title": "Bewertung", "type": "string", "displayInside": -84}, {"id": -87, "parentId": -79, "title": "6.3 Schutzstreifen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -88, "title": "Foto", "type": "photo", "displayInside": -87}, {"id": -89, "title": "Bewertung", "type": "string", "displayInside": -87}, {"id": -90, "parentId": -79, "title": "6.4 Zugang", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -91, "title": "Foto", "type": "photo", "displayInside": -90}, {"id": -92, "title": "Bewertung", "type": "string", "displayInside": -90}, {"id": -93, "parentId": -79, "title": "6.5 Absicherung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -94, "title": "Foto", "type": "photo", "displayInside": -93}, {"id": -95, "title": "Bewertung", "type": "string", "displayInside": -93}, {"id": -96, "title": "7. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": -97, "title": "nicht zutreffend", "type": "combobox", "displayInside": -96, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -98, "parentId": -96, "title": "7.1 Umgang / Kennzeichnung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -99, "title": "Foto", "type": "photo", "displayInside": -98}, {"id": -100, "title": "Bewertung", "type": "string", "displayInside": -98}, {"id": -101, "parentId": -96, "title": "7.2 Lagerung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -102, "title": "Foto", "type": "photo", "displayInside": -101}, {"id": -103, "title": "Bewertung", "type": "string", "displayInside": -101}, {"id": -104, "parentId": -96, "title": "7.3 Betriebsanweisung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -105, "title": "Foto", "type": "photo", "displayInside": -104}, {"id": -106, "title": "Bewertung", "type": "string", "displayInside": -104}, {"id": -107, "title": "8. Hochgelagene Arbeitsplätze", "type": "headline"}, {"id": -108, "title": "nicht zutreffend", "type": "combobox", "displayInside": -107, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -109, "parentId": -107, "title": "8.1 Abdeckungen (Bodenöffnungen)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -110, "title": "Foto", "type": "photo", "displayInside": -109}, {"id": -111, "title": "Bewertung", "type": "string", "displayInside": -109}, {"id": -112, "parentId": -107, "title": "8.2 Absturzsicherungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -113, "title": "Foto", "type": "photo", "displayInside": -112}, {"id": -114, "title": "Bewertung", "type": "string", "displayInside": -112}, {"id": -115, "parentId": -107, "title": "8.3 Anseilschutz", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -116, "title": "Foto", "type": "photo", "displayInside": -115}, {"id": -117, "title": "Bewertung", "type": "string", "displayInside": -115}, {"id": -118, "parentId": -107, "title": "8.4 Zugang", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -119, "title": "Foto", "type": "photo", "displayInside": -118}, {"id": -120, "title": "Bewertung", "type": "string", "displayInside": -118}, {"id": -121, "title": "9. <PERSON><PERSON><PERSON> und <PERSON>", "type": "headline"}, {"id": -122, "title": "nicht zutreffend", "type": "combobox", "displayInside": -121, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -123, "parentId": -121, "title": "9.1 <PERSON>ust<PERSON> (technisch / organisatorisch)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -124, "title": "Foto", "type": "photo", "displayInside": -123}, {"id": -125, "title": "Bewertung", "type": "string", "displayInside": -123}, {"id": -126, "parentId": -121, "title": "9.2 Vorschriftsmäßige Aufstellung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -127, "title": "Foto", "type": "photo", "displayInside": -126}, {"id": -128, "title": "Bewertung", "type": "string", "displayInside": -126}, {"id": -129, "parentId": -121, "title": "9.3 Zweckentsprechende Nutzung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -130, "title": "Bewertung", "type": "string", "displayInside": -129}, {"id": -131, "title": "10. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": -132, "title": "nicht zutreffend", "type": "combobox", "displayInside": -131, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -133, "parentId": -131, "title": "10.1 Vorschriftsmäßige Aufstellung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -134, "title": "Foto", "type": "photo", "displayInside": -133}, {"id": -135, "title": "Bewertung", "type": "string", "displayInside": -133}, {"id": -136, "parentId": -131, "title": "10.2 Belag/ Seitenschutz", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -137, "title": "Foto", "type": "photo", "displayInside": -136}, {"id": -138, "title": "Bewertung", "type": "string", "displayInside": -136}, {"id": -139, "parentId": -131, "title": "10.3 Kennzeichnung / Freigabe", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -140, "title": "Foto", "type": "photo", "displayInside": -139}, {"id": -141, "title": "Bewertung", "type": "string", "displayInside": -139}, {"id": -142, "parentId": -131, "title": "10.4 Sachgerechte Nutzung", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -143, "title": "Foto", "type": "photo", "displayInside": -142}, {"id": -144, "title": "Bewertung", "type": "string", "displayInside": -142}, {"id": -145, "title": "11. <PERSON><PERSON><PERSON><PERSON>", "type": "headline"}, {"id": -146, "title": "nicht zutreffend", "type": "combobox", "displayInside": -145, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -147, "parentId": -145, "title": "11.1 <PERSON><PERSON><PERSON> (technisch / organisatorisch)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -148, "title": "Foto", "type": "photo", "displayInside": -147}, {"id": -149, "title": "Bewertung", "type": "string", "displayInside": -147}, {"id": -150, "parentId": -145, "title": "11.2 Sicherheitseinrichtungen (z.B. Kamera)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -151, "title": "Foto", "type": "photo", "displayInside": -150}, {"id": -152, "title": "Bewertung", "type": "string", "displayInside": -150}, {"id": -153, "title": "12. <PERSON><PERSON> / Lastenaufzüge", "type": "headline"}, {"id": -154, "title": "nicht zutreffend", "type": "combobox", "displayInside": -153, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -155, "parentId": -153, "title": "12.1 Prüfungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -156, "title": "Foto", "type": "photo", "displayInside": -155}, {"id": -157, "title": "Bewertung", "type": "string", "displayInside": -155}, {"id": -158, "parentId": -153, "title": "12.2 Sicherheitseinrichtungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -159, "title": "Foto", "type": "photo", "displayInside": -158}, {"id": -160, "title": "Bewertung", "type": "string", "displayInside": -158}, {"id": -161, "parentId": -153, "title": "12.3 Beauftragung des Kranführers / Bedieners", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -162, "title": "Foto", "type": "photo", "displayInside": -161}, {"id": -163, "title": "Bewertung", "type": "string", "displayInside": -161}, {"id": -164, "parentId": -153, "title": "12.4 Sicherheitsabstände", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -165, "title": "Foto", "type": "photo", "displayInside": -164}, {"id": -166, "title": "Bewertung", "type": "string", "displayInside": -164}, {"id": -167, "parentId": -153, "title": "12.5 Zustand", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -168, "title": "Foto", "type": "photo", "displayInside": -167}, {"id": -169, "title": "Bewertung", "type": "string", "displayInside": -167}, {"id": -170, "title": "13. <PERSON><PERSON><PERSON><PERSON><PERSON>/ Anschlagmittel", "type": "headline"}, {"id": -171, "title": "nicht zutreffend", "type": "combobox", "displayInside": -170, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -172, "parentId": -170, "title": "13.1 Seile, Ketten", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -173, "title": "Foto", "type": "photo", "displayInside": -172}, {"id": -174, "title": "Bewertung", "type": "string", "displayInside": -172}, {"id": -175, "parentId": -170, "title": "13.2 Laufaufnahmemittel", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -176, "title": "Foto", "type": "photo", "displayInside": -175}, {"id": -177, "title": "Bewertung", "type": "string", "displayInside": -175}, {"id": -178, "parentId": -170, "title": "13.3 <PERSON><PERSON><PERSON><PERSON><PERSON>ng", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -179, "title": "Foto", "type": "photo", "displayInside": -178}, {"id": -180, "title": "Bewertung", "type": "string", "displayInside": -178}, {"id": -181, "title": "14. Sonstige Maschinen und Kleingeräte", "type": "headline"}, {"id": -182, "title": "nicht zutreffend", "type": "combobox", "displayInside": -181, "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -183, "parentId": -181, "title": "14.1 <PERSON>ust<PERSON> (technisch / organisatorisch)", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -184, "title": "Foto", "type": "photo", "displayInside": -183}, {"id": -185, "title": "Bewertung", "type": "string", "displayInside": -183}, {"id": -186, "parentId": -181, "title": "14.2 Sicherheitseinrichtungen", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "nicht relevant"]}, {"id": -187, "title": "Foto", "type": "photo", "displayInside": -186}, {"id": -188, "title": "Bewertung", "type": "string", "displayInside": -186}, {"id": -189, "title": "Zusätzliche Bemerkungen bzw. Abweichung:", "type": "headline"}, {"id": -190, "parentId": -189, "title": "Bemerkung #1", "type": "headline"}, {"id": -191, "parentId": -190, "title": "Zu Nr.", "type": "string"}, {"id": -192, "parentId": -190, "title": "Bemerkung", "type": "string"}, {"id": -193, "parentId": -190, "title": "Zuständig", "type": "string"}, {"id": -194, "parentId": -190, "title": "Zu erledigen bis", "type": "string"}, {"id": -195, "parentId": -189, "title": "Bemerkung #2", "type": "headline"}, {"id": -196, "parentId": -195, "title": "Zu Nr.", "type": "string"}, {"id": -197, "parentId": -195, "title": "Bemerkung", "type": "string"}, {"id": -198, "parentId": -195, "title": "Zuständig", "type": "string"}, {"id": -199, "parentId": -195, "title": "Zu erledigen bis", "type": "string"}, {"id": -200, "parentId": -189, "title": "Bemerkung #3", "type": "headline"}, {"id": -201, "parentId": -200, "title": "Zu Nr.", "type": "string"}, {"id": -202, "parentId": -200, "title": "Bemerkung", "type": "string"}, {"id": -203, "parentId": -200, "title": "Zuständig", "type": "string"}, {"id": -204, "parentId": -200, "title": "Zu erledigen bis", "type": "string"}, {"id": -205, "parentId": -189, "title": "Bemerkung #4", "type": "headline"}, {"id": -206, "parentId": -205, "title": "Zu Nr.", "type": "string"}, {"id": -207, "parentId": -205, "title": "Bemerkung", "type": "string"}, {"id": -208, "parentId": -205, "title": "Zuständig", "type": "string"}, {"id": -209, "parentId": -205, "title": "Zu erledigen bis", "type": "string"}, {"id": -210, "parentId": -189, "title": "Bemerkung #5", "type": "headline"}, {"id": -211, "parentId": -210, "title": "Zu Nr.", "type": "string"}, {"id": -212, "parentId": -210, "title": "Bemerkung", "type": "string"}, {"id": -213, "parentId": -210, "title": "Zuständig", "type": "string"}, {"id": -214, "parentId": -210, "title": "Zu erledigen bis", "type": "string"}, {"id": -215, "title": "Signaturfelder", "type": "headline"}, {"id": -216, "parentId": -215, "title": "Datum", "type": "date"}, {"id": -217, "parentId": -215, "title": "<PERSON><PERSON><PERSON>", "type": "signatureField"}, {"id": -218, "parentId": -215, "title": "Unterschrift", "type": "signatureField"}]}