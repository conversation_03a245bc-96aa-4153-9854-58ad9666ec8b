<?php
$config = new RouteConfig();
$config->addParameter(RouteConfigParameterType::int, "personalNo", required: true);
$config->addParameter(RouteConfigParameterType::date, "from", required: false);
$config->addParameter(RouteConfigParameterType::date, "to", required: false);
$config->addParameter(RouteConfigParameterType::ktrAanr, "ktrAanr", required: false);
$config->addParameter(RouteConfigParameterType::string, "displayTimeFormat", required: false);
$config->redirectToRoute = 'printHoursV3';
return $config;
