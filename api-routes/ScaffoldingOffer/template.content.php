<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . "/../../printout.helper.php";

$data = PrintoutHelper::readGetDataResultFromTempsFile();
if (count($data) === 0) {
    require_once __DIR__ . "/model.php";
    $data = (new C_ScaffoldingOffer())->getData($_GET['quotationNumber']);
}
?>

<!doctype html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . "/style.css") ?>">
    <title>Scaffolding Offer</title>
</head>
<body>
<div class="page">
    <div style="margin-bottom: 70px">
        <img src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/img/pqms-logo.jpeg'); ?>"
             style="width: 125px; height: auto"
             alt="logo">
    </div>
    <div class="right-logo">
        <img style="width: 175px; height: auto;"
             src="<?= PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/img/vero-logo.png'); ?>"
             alt="logo">
    </div>

    <div class="address">
        <div class="left-address">
            <p class="text-underline bold-text font-sm">
                VERO Scaffolding EOOD · Merseburger Str. 6-8 ·
                <br>33106 Paderborn
            </p>
            <p>
                <?= ($data['quotation']["addressSalutation"] ?? "") . "<br>" .
                ($data['quotation']["addressName"] ?? "") . "<br>" .
                ($data['quotation']["addressLine1"] ?? "") . "<br>" .
                ($data['quotation']["addressZipCode"] ?? "") . "&nbsp;" . ($data['quotation']["addressCity"] ?? ""); ?>
            </p>
        </div>
        <div class="right-address">
            <p><span class="bold-text font-bg">VERO Scaffolding EOOD</span><br>Niederlassung Deutschland<br><br>
                Merseburger Straße 6-8<br>33106 Paderborn<br><br>
                Tel.: +49 (0) 52 51/874 100<br>e-Mail: <EMAIL><br>www.vero.de
            </p>
        </div>
    </div>

    <table class="sub-header-table">
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td style="padding-bottom: 10px;"><?= $data['quotationCity'] . ', ' . $data['orderDate'] ?></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td class="font-sm text-underline"><?= $data['quotation']['commercialContactCaption'] ?? ""; ?></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td class="font-sm"><?= $data['quotation']["commercialContactName"] ?? ""; ?></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td class="font-sm text-underline"><?= $data['quotation']["technicalContactCaption"] ?? ""; ?></td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td class="font-sm"><?= $data['quotation']["technicalContactName"] ?? ""; ?></td>
        </tr>
        <tr>
            <td class="bold-text">Objekt:</td>
            <td class="bold-text"><?= $data['quotation']["objectDetailDescription"] ?? ''; ?></td>
            <td class="font-sm"><?= $data['quotation']["addressPhone"] ?? ""; ?></td>
        </tr>
        <tr>
            <td class="bold-text">Angebot:</td>
            <td class="bold-text"><?= $data['quotation']["quotationNo"] ?? ''; ?></td>
            <td class="font-sm"><?= $data['quotation']["addressEmail"] ?? ""; ?></td>
        </tr>
    </table>

    <p class="font-bg"
       style="margin-top: 40px;"><?= str_replace("\r\n", "<br>", $data['quotation']['headerText'] ?? ""); ?></p>
    <table class="main-table">
        <tr>
            <td class="bold-text">Position</td>
            <td class="bold-text">Menge EH</td>
            <td class="bold-text">Beschreibung</td>
            <td class="bold-text" style="text-align: center;">
                <div>Einzelpreis</div>
                <div>€</div>
            </td>
            <td class="bold-text" style="text-align: center;">
                <div>Gesamtpreis</div>
                <div>€</div>
            </td>
        </tr>
        <?php foreach ($data['quotationItems'] as $position) { ?>
            <tr>
                <td><?= $position['itemNo'] ?></td>
                <td><?= number_format((float)$position["quantity"], $position["quantityDecimalPlaces"], ',', '') ?></td>
                <td><?= $position["detailDescription"] ?></td>
                <td style="text-align: right"><?= number_format((float)$position['unitPrice'], 2, ',', '') ?></td>
                <td style="text-align: right"><?= number_format((float)$position['totalPrice'], 2, ',', '') ?></td>
            </tr>
        <?php } ?>
    </table>
    <table class="sum-table">
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="bold-text">Angebotssumme netto</td>
            <td class="bold-text"
                style="text-align: right;">
                <?php
                if (isset($data['quotation']["netTotal"])) {
                    echo number_format((float)$data['quotation']["netTotal"], 2, ',', '');
                } ?>
            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="bold-text">
                MwSt. <?php
                if (isset($data['quotation']['vatPercentage'])) {
                    echo PrintoutHelper::formatTimeNumber($data['quotation']['vatPercentage']);
                } ?>%
            </td>
            <td class="bold-text td-underline"
                style="text-align: right;">
                <?php
                if (isset($data['quotation']["vatValue"])) {
                    echo number_format((float)$data['quotation']["vatValue"], 2, ',', '');
                }
                ?>
            </td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td class="bold-text">Angebotssumme brutto</td>
            <td class="bold-text td-underline"
                style="text-align: right;"><?php
                if (isset($data['quotation']["grossTotal"])) {
                    echo number_format((float)$data['quotation']["grossTotal"], 2, ',', '');
                }
                ?>
            </td>
        </tr>
        <tr style="margin-top: -30px;">
            <td>&nbsp;</td>
            <td></td>
            <td>
                <div class="double-underline-div">&nbsp;</div>
            </td>
        </tr>
    </table>
    <p class="quotation-footer-text font-bg"><?= str_replace("\r\n", "<br>", $data['quotation']["footerText"] ?? ""); ?></p>
    <p style="font-size: 12px;">&lt;dieses Angebot ist maschinell erstellt und daher ohne Unterschrift gültig></p>
</div>
</body>
</html>
