* {
    font-family: Arial, sans-serif;
    font-size: 14px;
}

.bold-text {
    font-weight: bold !important;
}

.text-underline {
    text-decoration: underline !important;
}

body {
    padding: 0;
    margin: 0;
}

.page {
    width: 100%;
    padding: 10mm 0;
}

.right-logo img {
    width: 200px;
}

.right-logo {
    position: absolute;
    right: 0;
    top: 50px;
}

.address {
    position: relative;
    margin-top: 20px;
}

.right-address, .left-address {
    font-size: 12px;
}

.right-address {
    position: absolute;
    right: 0;
    top: -20px;
}

.main-table {
    width: 100%;
    border-collapse: collapse;
}

.main-table td {
    width: 10%;
    padding: 10px 0;
}

.main-table td:nth-child(3) {
    width: 50%;
}

.main-table tr:first-child {
    border-bottom: 2px solid black;
}

.sum-table {
    width: 100%;
    border-collapse: collapse;
    border-top: 2px solid black;
}

.sum-table td:first-child {
    width: 22%;
}

.sum-table td:nth-child(2) {
    width: 70%;
}

.td-underline {
    border-bottom: 2px solid black;
}

.double-underline-div {
    border-bottom: 2px solid black;
    margin-top: -20px;
}

.quotation-footer-text {
    margin-top: 20px;
}

.font-sm {
    font-size: 12px;
}

.font-bg {
    font-size: 16px;
}

.sub-header-table {
    width: 100%;
    margin-top: 70px;
}

.sub-header-table td {
    padding: 0;
}

.sub-header-table td:first-child {
    width: 11%;
}

.sub-header-table td:nth-child(2) {
    width: 68%;
}