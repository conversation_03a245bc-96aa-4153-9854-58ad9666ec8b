<html>
<?php $data = $this->data;?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Протокол за извършена дейност</title>
	</head>
	<body>
		<table style="width:100%" class="header">
		<tr>
			<td align="right"><img src="<?=WEB_ROOT_API.'vendor\printouts\NovofermLogo.png'?>" alt="NovofermLogo"></td>
		</tr>
		<tr>
			<th>
			<?=$data['companyDetails']['firma1'].' - '.$data['companyDetails']['firma2']?>
			</th>
		</tr>
		</table>
		<br /><br />
		<table class="protokolNumber" align="center">
			<tr>
				<th>Протокол за извършена дейност</th>
			</tr>
			<tr>
				<td align="center"><b>№:</b><?=str_repeat('&nbsp;', 2).($data['fileExternalId'] ? $data['fileExternalId'] : $data['documentId'])?></td>
			</tr>
		</table>
		<br /><br />
		<table width="100%">
			<tr>
				<th align="left"><b>Дата:</b><?=str_repeat('&nbsp;', 2).date('d.m.Y',strtotime($data['createdOn']));?></th>
				<th align="right"><b>ОБЕКТ:</b><?=str_repeat('&nbsp;', 2).$data['projectName']?></th>
			</tr>
		</table>
		<br /><br /><hr>
		<div class="descr"><b>ИЗВЪРШЕНА ДЕЙНОСТ:</b><?=str_repeat('&nbsp;', 2).$data['Забележки']?></div>
		<br /><br /><br /><hr>
		<table class="mounted" align="center">
			<tr>
				<th colspan="4"><b>МОНТИРАНИ ПРОДУКТИ:</b></th>
			</tr>
			
<?php $prCounter = 1; ?>
<?php 			foreach ($data['mountedProducts'] as $k=>$v) { ?>
<?php				if(isset($v)) { ?>
						<tr class="border">

<td width="25%" class="tdSpace"><b><?=$prCounter.'. '?></b><?=$v['Продукт']?></b></td>
<td width="25%" class="tdSpace"><b><?='Размер: '?></b><?=$v['Забележка']?></b></td>
<td width="25%" class="tdSpace"><b><?='Брой: '?></b><?=$v['Количество']?></b></td>
<td width="25%" class="tdSpace"><b><?='Цена: '?></b><?=$v['Обща сума в лв']?></b></td>																			
<?php 						} ?>							
						</tr>
<?php $prCounter++ ?>	
<?php 				} ?>

		</table>
		<br /><br />
		<div>
			<table style="float: left" width="48%">
				<tr>
					<th align="left"><b>Час на пристигане:</b><?=str_repeat('&nbsp;', 2).$data['arrivingTime']?></th>
				</tr>
				<tr>
					<th align="left"><b>Час на тръгване:</b><?=str_repeat('&nbsp;', 2).$data['leavingTime']?></th>
				</tr>
				<tr>
					<th align="left"><b>Изминати километри:</b><?=str_repeat('&nbsp;', 2).$data['Шофиране км'].' км'?></th>
				</tr>
				<tr>
					<th align="left"><b>Време на пътуване:</b><?=str_repeat('&nbsp;', 2).$data['Часове шофиране'].' часа'?></th>
				</tr>
			</table>
			&nbsp;
			<table style="float: right" class="border" width="48%">
				<tr>
					<th align="left"><b>Изпълнител:</b><?=str_repeat('&nbsp;', 2).$data['contractor']?></th>
				</tr>	
				<tr>
					<th align="left"><b>Калкулирана сума:</b><?=str_repeat('&nbsp;', 2).$data['Изчислена сума']?></th>
				</tr>
			</table>
		</div>
		<br /><br /><br /><br /><br /><hr>
		<div class="descr"><b>ЗАБЕЛЕЖКА:</b><?=str_repeat('&nbsp;', 2).$data['Забележка, ако не е одобрена']?></div>
		<br /><br /><br /><br /><hr>
		<table class="mounters" align="center" width="100%">
			<tr>
				<th align="left"><b>МОНТАЖНИЦИ:</b></td>
				<th align="left"><b>% Съотношение труд:</b></th>
				<th align="left"><b>Подпис:</b></th>
			</tr>
<?php $counter = 1; ?>
<?php 			foreach ($data['installers'] as $k=>$v) { ?>
<?php 				if(!empty($v)) { ?>
						<tr class="border">
<td width="33%"><?=$counter.'. '.$v['Име']?></td>
<td width="33%"><?='% '.$v['Съотношение на работа']?></td>
<td width="33%">Подпис: <img src="<?=$v['Подпис']?>" alt="Signature" height="42" width="42"></td>									
<?php $counter++ ?>
						</tr>
<?php 				} ?>
<?php 			} ?>						
		</table>
		<br /><br /><br /><br /><br />
		<div class="container">
			<table style="float: left" class="border" width="30%">
				<tr>
					<th align="left"><b>Оферта: Офр.</b><?=str_repeat('&nbsp;', 2).$data['calculatedAmount']?></th>
				</tr>
				<tr>
					<th align="left"><b>Договор: Дог.</b><?=str_repeat('&nbsp;', 2).$data['contract']?></th>
				</tr>
			</table>
			&nbsp;
			<table style="float: right" class="border" width="45%">
				<tr>
					<th align="left"><b>ОТГОВОРНИК:</b><td width="33%"><img src="<?=$data['Signature']?>" alt="Signature" height="42" width="42"></td></th>
				</tr>	
				<tr>
					<th align="left"><b>ОДОБРЕНА СУМА:</b><?=str_repeat('&nbsp;', 2).$data['Approved Amount']?></th>
				</tr>
			</table>
		</div>		
	</body>
</html>

<style>
	.header {border-bottom: 1px solid black;}
	.border {
			 border: 2px double black;
			 border-collapse: separate;
			 border-spacing: 1em;
			 }
	tr.border td {
        border-bottom: 1px solid black;
    }

    hr{
        border: 1px solid grey;
    }
	.tdSpace {padding:0 15px 0 15px;}
	div, table {page-break-after: auto; page-break-before: auto; }
	.container {page-break-before: auto; page-break-after: auto;}
	table {page-break-inside: avoid;}

    table.protokolNumber {
        font-size: 30px;
    }
	@media print {

  html, body {
    height:100%; 
    margin: 0 !important; 
    padding: 0 !important;
    <?php (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') ? 'overflow: hidden;' : ''; ?>
  }

}
</style>
