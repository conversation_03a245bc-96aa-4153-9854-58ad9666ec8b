<?php
require_once ABS_PATH.'Documentation.php';
if (defined('ABS_PATH')) {
	require_once ABS_PATH . 'printouts/printout.helper.php';
	require_once ABS_PATH . 'printouts/PrintoutCurl.php';
} else {
	require_once '../printout.helper.php';
    require_once '../PrintoutCurl.php';
}

class C_Wagner {
private $db;
private $documentation;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->settingsAPI = new Settings();
	}
	
	public function getLatestProjectData($ktr) {
		require_once ABS_PATH.'/models/Projects/ProjectsSelector.php';
		$projectsSelector = new ProjectsSelector();
		$manualQuery = new SelectorQueryParams();
		$manualQuery->partial = ['projectName','customerDisplayName','fiscalYearKey'];
		$manualQuery->alias = ['projectName' => 'name','customerDisplayName' => 'kname','fiscalYearKey' => 'year'];
		$parameters['projectNo']['eq'] = $ktr;
		// this is needed for the custom integration to fetch the proper project, I wonder wether the $parameters above can be dropped
		$manualQuery->filter['projectNo']['eq'] = $ktr;
		$projects = $projectsSelector->get($parameters,$manualQuery);
		return $projects[0];
	}
	
	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		if($documentData['visible'] == false ) {
			 throw new RestException(400, 'The visibility of the document is false!');
		}
		if($documentData) {
			$projectData = $this->getLatestProjectData($documentData['documentRelKey1']);
			return $this->combineData($documentData, $projectData);
		} else {
			throw new RestException(400, "No such document id or no data for this document!");
		}
	}
	
	private function combineData ($documentData, $projectData) {
		$templateData = [];
		$ids = [];

		foreach ($documentData['children'] as $k=>$v) {
			$ids[$v['id']] = $v;
		}
		
		foreach ($ids as $key => $value) {
			if (!array_key_exists($value['parentId'], $ids)) {
				if($value['type'] != 'headline') {
					$templateData[$value['title']] = $value['reportedValue'];
				}
			} else {
				if($value['type'] == 'signatureField') {
					$templateData[$ids[$value['parentId']]['title']][$value['title']] = $value['filePath'];
				} else {
					$templateData[$ids[$value['parentId']]['title']][$value['title']] = $value['reportedValue'];
				}
			} 
		}
		
		$templateData['projectName'] = $projectData['name'];
		$templateData['kName'] = $projectData['kname'];
		$templateData['logo'] = $this->fetchLogo();
		$templateData['companyInfo'] = $this->getCompanyInfo();
		$templateData['qrCodeUrl'] = $this->generateQrCodeUrl($documentData['documentRelKey1'], $projectData['year']);

		// add fallback to documents date, if not date is given
		if(!$templateData['Übergabe']['Datum'])
			$templateData['Übergabe']['Datum'] = $documentData['documentCreatedOn'];
		// add fallback to documents date, if not date is given
		if(!$templateData['Geprüft']['Datum'])
			$templateData['Geprüft']['Datum'] = $documentData['documentCreatedOn'];
		return $templateData;
	}

	/**
	 * returns logo url from settings
	 */
	private function fetchLogo()
	{
		$logoUrl = $this->settingsAPI->getcommon()['logo'];

		// TODO add placeholder logo

		return $logoUrl;
	}

	/**
	 * returns company info
	 */
	private function getCompanyInfo()
	{
		$info = $this->settingsAPI->getinfo()['address'];
		// TODO add placeholder info

		return $info;
	}

	/**
	 * Steps taken from https://gitlab.baubuddy.de/BauBuddy/scaffoldingWeb/issues/2469#note_128702
	 * @param $projectNo
	 * @param $year
	 * @return string
	 */
	private function generateQrCodeUrl($projectNo, $year)
	{
		$curl = new PrintoutCurl();
		$baseUrl = "https://web.baubuddy.de/index.php/".APP_LANG."/";
		// encrypt principal, project and year
		$encryptUrl = $baseUrl . "help/encrypt/".ACTIVE_PRINCIPAL."/project/".$projectNo."/".$year;
		$encryptedString = $curl->_simple_call('GET', $encryptUrl, '', [CURLOPT_FAILONERROR => false]);
		$liveTickerUrl = $baseUrl."bcef64b34f76f36105f70b4855436659/".$encryptedString;

		// generate QR-Code using an external API
		$qrCodeUrl = "https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=".$liveTickerUrl;
		return $qrCodeUrl;
	}
}
