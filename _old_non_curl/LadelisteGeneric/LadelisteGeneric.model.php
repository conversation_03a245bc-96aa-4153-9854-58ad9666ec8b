<?php
if (defined('ABS_PATH')) {
	require_once ABS_PATH . 'Documentation.php';
	require_once ABS_PATH . 'Projects.php';
	require_once ABS_PATH . 'WorkingOrders.php';
	require_once ABS_PATH . 'printouts/printout.helper.php';
}

class C_LadelisteGeneric
{
	private $db;
	private $documentation;
	private $projects;

	public function __construct()
	{
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
	}

	public function getData($schemaId, $documentId)
	{
		$documentData = $this->documentation->getDocumentId($schemaId, $documentId);

		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];

		array_unshift($documentData['children'], $main);

		$grouped = PrintoutHelper::groupChildrenUnderParentRecursive(
			$documentData['children'], 'id', 'parentId');

		$templateData = $this->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');

		return $templateData;
	}

	private function getDeepValueFromField(
		$keyFieldName, $valueFieldName, $node, $childrenFieldName)
	{
		$paths = [];
		if ($node[$valueFieldName]) {
			return [$node[$keyFieldName] => $node[$valueFieldName]];
		}
		if ($node[$childrenFieldName]) {
			foreach ($node[$childrenFieldName] as $child) {
				$paths[$node[$keyFieldName]][$child[$keyFieldName]] =
					$this->getDeepValueFromField($keyFieldName, $valueFieldName, $this->calculateFormulas($child), $childrenFieldName)[$child[$keyFieldName]];
			}
			return $paths;
		}
		return null;
	}

	private function calculateFormulas($child)
	{
		if ($child['type'] == 'formula') {
			$child['reportedValue'] = eval('return ' . PrintoutHelper::treatFormulaStructure(
					$child['reportedValue']) . ';');
		}

		return $child;
	}
}