<?php
if (defined('ABS_PATH')) {
	require_once ABS_PATH . 'printouts/printout.helper.php';
}
$data = $this->data;
?>

<html lang="de">
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
	<title>Ladeliste</title>
</head>
<body>
<table class="wrapper" cellspacing="0" cellpadding="2" border="0">
	<tbody>
	<tr>
		<td align="left" colspan="2">BV:</td>
	</tr>
	<tr>
		<td align="left" colspan="4">
			<table cellspacing="0" cellpadding="2" class="leftHeader content">
				<tbody>
				<tr>
					<td colspan="3"><b>LKW</b> <?= $data['Ladeliste Generic']['LKW']; ?></td>
					<td colspan="3"><b><PERSON><PERSON><PERSON></b> <?= $data['Ladeliste Generic']['Hänger']; ?></td>
				</tr>
				<tr>
					<td colspan="2"><b>Schubkarre</b> <?= $data['Ladeliste Generic']['Schubkarre']; ?></td>
					<td colspan="2"><b>Eich</b> <?= $data['Ladeliste Generic']['Eich']; ?></td>
					<td colspan="2"><b>Wagen</b> <?= $data['Ladeliste Generic']['Wagen']; ?></td>
				</tr>
				<tr>
					<td colspan="2"><b>Quadriga gr.| kJ</b> <?= $data['Ladeliste Generic']['Quadriga gr/kl.']; ?></td>
					<td colspan="2"><b>Ausleger</b> <?= $data['Ladeliste Generic']['Ausleger']; ?></td>
					<td colspan="2"><?= $data['Ladeliste Generic']['kg-Schild']; ?> <b>kg-Schild</b></td>
				</tr>
				<tr>
					<td colspan="2"><b>Handrolle</b> <?= $data['Ladeliste Generic']['Handrolle']; ?></td>
					<td colspan="2"><b>Schwenkseil</b> <?= $data['Ladeliste Generic']['Schwenkseil']; ?></td>
					<td colspan="2"><b>Winde kpl.</b> <?= $data['Ladeliste Generic']['Winde kpl.']; ?></td>
				</tr>
				<tr>
					<td colspan="3"><b>Schilder komplett</b> <?= $data['Ladeliste Generic']['Schilfer komplett']; ?>
					</td>
					<td colspan="3"></td>
				</tr>
				</tbody>
			</table>
		</td>
		<td align="center" colspan="3"></td>
	</tr>
	<tr>
		<td align="center" colspan="2"
			rowspan="2"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['System']['Plettac 100'], 'System Plettac 100'); ?></td>
		<td align="center" colspan="2"
			rowspan="2"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['System']['Plettac 70'], 'System Plettac 70'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Füße'], 'Füße'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Kupplungen'], 'Kupplungen'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Anker'], 'Anker'); ?></td>
	</tr>
	<tr>

		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Holzbohlen'], 'Holzbohlen'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Gitterträger'], 'Gitterträger'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Rohre'], 'Rohre'); ?></td>
	</tr>
	<tr>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Holzbelag'], 'Holzbelag'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Geländer'], 'Geländer'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Bordbrett'], 'Bordbrett'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Diagonal'], 'Diagonal'); ?></td>
	</tr>
	<tr>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Konsolen'], 'Konsolen'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Dachd.-Netz'], 'Dachd.-Netz'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Planen'], 'Planen'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Netze'], 'Netze'); ?></td>
		<td align="center"><?php PrintoutHelper::buildTable($data['Ladeliste Generic']['Diverses'], 'Diverses'); ?></td>
	</tr>
	</tbody>
</table>
</body>
</html>

<style>

	.wrapper {
		width: 100%;
	}

	.wrapper.inner td {
		border: 1px solid black;
		border-collapse: collapse;
	}

	.wrapper.inner th {
		border: 1px solid black;
		border-collapse: collapse;
	}

	.leftHeader td, th {
		border: 1px solid black;
	}

	.genericTable td, th {
		border: 1px solid black;
	}

	.leftHeader {
		border-collapse: collapse;
	}

	.content {
		font-size: small;
	}

	td {
		vertical-align: top;
	}

</style>
