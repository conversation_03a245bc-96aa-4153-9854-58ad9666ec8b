<!DOCTYPE html>
<html lang="de">
<head>
    <script>
        function subst() {
            var vars = {};
            var query_strings_from_url = document.location.search.substring(1).split('&');
            for (var query_string in query_strings_from_url) {
                if (query_strings_from_url.hasOwnProperty(query_string)) {
                    var temp_var = query_strings_from_url[query_string].split('=', 2);
                    vars[temp_var[0]] = decodeURI(temp_var[1]);
                }
            }
            var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
            for (var css_class in css_selector_classes) {
                if (css_selector_classes.hasOwnProperty(css_class)) {
                    var element = document.getElementsByClassName(css_selector_classes[css_class]);
                    for (var j = 0; j < element.length; ++j) {
                        element[j].textContent = vars[css_selector_classes[css_class]];
                    }
                }
            }
        }
    </script>
<link href="footerStyle.css" rel="stylesheet">
</head>
<body onload="subst()">
<?php
$json = file_get_contents("data.json");
$jsonData = json_decode($json, true);

$exactTime = explode(" ", $jsonData['document']['creationDate'])[0];
$datum = explode(" ", $jsonData['document']['creationDate'])[0];

?>
<table>
    <tr>
        <td class="little-padding-left"><?php echo $datum . " | " . $exactTime;?></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td class="align-right little-padding-right"><section>Seite <span class="page"></span>/<span class="topage"></span></section></td>
    </tr>
</table>

<?php

echo '';

?>

</body>
</html>
