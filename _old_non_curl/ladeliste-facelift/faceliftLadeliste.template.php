<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <meta name="description" content="Source code by <PERSON><PERSON><PERSON>">
    <meta name="author" content="<PERSON><PERSON><PERSON>!">
    <style>
        * {
            font-family: Arial, Helvetica Neue, Helvetica, sans-serif;
            border-collapse: collapse;
            border-spacing: 0;
        }

        .project-metadata-heads {
            font-size: 13px;
        }/*.project-metadata-heads {
    font-size: 13px;
}*/

        .color1 {
            height: 50px !important;
        }

        .color2 {
            height: 50px !important;
        }

        .checkboxes {
            width: 45px;
            height: 15px;
        }

        .align-center {
            text-align: center;
        }

        tbody {
            height: 100% !important;
        }

        .display-inline {
            display: inline;
        }

        .project-table-cell {
            width: 60% !important;
            padding-top: 0px !important;
            padding-bottom: 0px !important;
            padding-left: 5px;
            margin: 0px !important;
            border: 1px solid black;
            border-bottom: 0px;
        }

        .bb-parts {
            object-fit: contain;
            height: 100px;
            width: 100px;
        }

        .img-section {
            width: 100px;
            height: 100px;
            padding: 0 !important;
            margin: 0 !important;
        }

        #qr-section, #qr-code {
            height: auto;
            width: 125px;
            float: right;
        }

        .display-inline {
            display: inline;
        }

        .padding-top {
            padding-top: 5%;
        }

        .not-bold {
            font-weight: normal;
        }

        .drop-all-borders {
            border-top: 0px !important;
            border-bottom: 0px !important;
            border-right: 0px !important;
            border-left: 0px !important;
        }

        .display-none {
            visibility: hidden;
        }

        /*
			I noticed the top info table with Kunde & Projekt sticks to the very top,
			at the border of the rendered PDF page.
			To fix this, I added this hidden-with-span so that it gets pushed downwards, without the need to set the
			parameters that proved to be problematic with WKHTMLTOPDF in the past,
			e.g. padding & margin for the whole layout
		*/

        .hidden-with-span {
            visibility: hidden !important;
            height: 10px !important;
            border: 0 !important;
        }

        table.load-list-table {
            border-collapse: collapse;
            width: 100%;
        }

        table.load-list-table td:nth-of-type(1), table.load-list-table th:nth-of-type(1) {
            width: 100px;
            max-height: 20%;
        }

        table.load-list-table td:nth-of-type(2), table.load-list-table th:nth-of-type(2) {
            width: 8%;
            max-height: 20%;
        }

        .align-center {
            text-align: center;
        }

        table.load-list-table td:nth-of-type(4), table.load-list-table th:nth-of-type(4) {
            width: 8%;
            max-height: 20%;
        }

        table.load-list-table td:nth-of-type(3), table.load-list-table th:nth-of-type(3) {
            width: 200px;
            max-height: 20%;
        }

        table.load-list-table td, table.load-list-table th {
            border: 1px solid black;
        }

        table.load-list-table th {
            background-color: #E6E6E6 !important;
            height: 25px !important;
            padding: 5px;
        }

        table.load-list-table tbody td {
            font-size: 13px;
        }

        table.face-lift-client-info {
            border-collapse: collapse;
            width: 100%;
            height: 70px;
        }

        table.face-lift-client-info th {
            background-color: #E6E6E6 !important;
            height: 15px !important;
            padding: 5px;
        }

        #load-list-1st-cell-table {
            width: 100%;
        }

        #load-list-1st-cell-table td {
            border: 1px solid white;
            border-collapse: collapse;
        }

        #company-logo-cell {
            width: 40%;
            height: 100%;
            border: none;
        }

        #project-table {
            height: 100%;
            width: 100% !important;
        }

        .cell-titles {
            padding-bottom: 0%;
            margin: 0;
        }

        #client-cell-title {
            vertical-align: bottom;
        }

        #project-cell-title {
            vertical-align: top;
        }

        .padding-left {
            padding-left: 1%;
        }

        .color-gray {
            background-color: #e6e6e6;
        }

        h1, h3 {
            margin-top: 0px !important;
            margin-bottom: 0px !important;
        }

        .peri-ladeliste-metadata, .peri-ladeliste-metadata td {
            width: 100%;
            border-spacing: 0px;
            padding: 0px;
            padding-top: 0px !important;
            padding-bottom: 0px !important;
            margin-top: 0px !important;
            margin-bottom: 0px !important;
            border: none;
            border-collapse: collapse;
            height: 2%;
        }

        .peri-address {
            border-spacing: 0px;
            padding: 0px;
            width: 100%;
            border: 1px solid black;
            border-collapse: collapse;
            font-size: 13px;
        }

        .peri-address td {
            padding-bottom: 5px;
            padding-left: 5px;
            padding-top: 5px;
        }

        .customer-info-onerow {
            width: 100%;
            font-size: 13px;
        }

        thead, .load-list-table td {
            text-aligh: right !important;
        }

        .colspan7 {
            width: 66% !important;
            height: 50%;
        }

        .thicons {
            width: 20px;
            padding: 2px;
        }

        .load-list-table th {
            text-align: center;
            font-size: 11px;
        }

        #sumtotal {
            text-align: right;
            font-size: 15px;
            padding: 15px;
        }

        #activity-cell {
            vertical-align: bottom;
            padding-left: 10px;
            padding-bottom: 10px !important;
        }

        #load-list {
            padding-left: 10px;
            vertical-align: top;
        }

        #load-list-nr {
            padding-left: 10px;
            vertical-align: top;
            padding-top: 0px;
            margin-top: 0px;
            font-size: 18px !important;
        }
    </style>
</head>
<body>
<?php
require_once ABS_PATH.'/printouts/printout.helper.php';

$json = file_get_contents("data.json");
$jsonData = json_decode($json, true);
$jsonData = $this->data;

$sumTotalWeight = 0;

$customerName = $jsonData['project']['customerName'];
$projectName = $jsonData['project']['projectName'];

$projectManagerName = $jsonData['project']['projectManager'];
$projectManagerPhone = "0171 ***********";
$siteManagerName = $jsonData['project']['siteManager'];
$siteManagerPhone = "0151 ***********";


$creationDate = date('d.m.Y',strtotime($jsonData['document']['creationDate']));
$creationTime = date('H:i',strtotime($jsonData['document']['creationDate']));
$imgMatches = glob(__DIR__."/../peri-quicksolve-ladeliste/img/*.jpg");
$positions = $jsonData['schema']['positions'];

// missing fields
$activityType = "Beladen";
$companyVehicleLicensePlate = "NU PE 1812";
$authorName = "Max Mustermann";

$loadDay = "1/3";
$loadListNr = $jsonData['document']['id'];

/* Replace dots with commas function below. */
function floatCommaValue($weight)
{
    $weight = str_replace('.', ',', strval($weight));
    return $weight;
}


/* Replace dots with commas function above. */function takeArticleNrFromImage($artNr, $imgMatches)
{
	foreach ($imgMatches as $articleImageFile) {
		$imgNr = explode("_", basename($articleImageFile))[0];
		if ($artNr == $imgNr) {
			// TODO is there a better way switch to the external path?
            $fullFilePath = PrintoutHelper::translateLocalPathToServerPath($articleImageFile);
            return $fullFilePath;
		}
	}
}

function reportedValueCheck($repVal, $repFeed): string
{
    if ($repVal == "yes") {
        // TODO use PrintoutHelper::translateLocalPathToServerPath
        return ' <img class="checkboxes" src="/checked.png" alt="checked">' . $repFeed;
    } else {
        return ' <img class="checkboxes" src="/unchecked.png" alt="checked">' . $repFeed;
    }
}

function resolveColorCodePerArtNr($artNr,$colorCounter)
{
    // maps articleNo to humanReadableColor, sourced from mapping file 2022-09-07-PERI_UP-Easy_Parts_in_QS-fully-AEI18
	$articleNumberToHumanReadableColor = array("132858" => array("gelb", ""), "124118" => array("orange", ""), "124112" => array("braun", ""), "124109" => array("grün", ""), "123771" => array("rot", ""), "124915" => array("blau", ""), "124124" => array("blau", ""), "124121" => array("blau", ""), "124115" => array("blau", ""), "130450" => array("gelb", ""), "130448" => array("orange", ""), "130445" => array("braun", ""), "130441" => array("grün", ""), "130438" => array("rot", ""), "129272" => array("blau", ""), "134539" => array("blau", ""), "133524" => array("orange", ""), "133525" => array("orange", ""), "133523" => array("braun", ""), "135376" => array("braun", ""), "131789" => array("braun", ""), "130321" => array("braun", ""), "133522" => array("grün", ""), "135375" => array("grün", ""), "131791" => array("grün", ""), "130317" => array("grün", ""), "133521" => array("rot", ""), "135374" => array("rot", ""), "131788" => array("rot", ""), "130313" => array("rot", ""), "133492" => array("blau", ""), "135365" => array("blau", ""), "131790" => array("blau", ""), "130309" => array("blau", ""), "134549" => array("blau", ""), "134552" => array("blau", ""), "130426" => array("grün", ""), "132928" => array("grün", ""), "132882" => array("grün", ""), "135369" => array("grün", ""), "130431" => array("rot", ""), "133286" => array("rot", ""), "133289" => array("rot", ""), "135368" => array("rot", ""), "130425" => array("blau", ""), "133285" => array("blau", ""), "133287" => array("blau", ""), "135367" => array("blau", ""), "129947" => array("gelb", ""), "134641" => array("gelb", ""), "110073" => array("orange", ""), "134639" => array("orange", ""), "110160" => array("braun", ""), "134637" => array("braun", ""), "110176" => array("grün", ""), "134636" => array("grün", ""), "110208" => array("rot", ""), "134635" => array("rot", ""), "110211" => array("blau", ""), "134634" => array("blau", ""), "132592" => array("blau", ""), "110213" => array("blau", ""), "110514" => array("blau", ""), "134628" => array("blau", ""), "134643" => array("blau", ""), "134642" => array("blau", ""), "134640" => array("blau", ""), "134638" => array("blau", ""), "129492" => array("gelb", ""), "132556" => array("gelb", ""), "129496" => array("orange", ""), "132558" => array("orange", ""), "129498" => array("braun", ""), "132559" => array("braun", ""), "129500" => array("grün", ""), "132560" => array("grün", ""), "129502" => array("rot", ""), "132561" => array("rot", ""), "129504" => array("blau", ""), "132562" => array("blau", ""), "129490" => array("blau", ""), "129494" => array("blau", ""), "132555" => array("blau", ""), "132557" => array("blau", ""), "134542" => array("blau", ""), "130532" => array("blau", ""), "130191" => array("gelb", ""), "130193" => array("orange", ""), "130195" => array("braun", ""), "130197" => array("grün", ""), "130199" => array("rot", ""), "130201" => array("blau", ""), "130434" => array("blau", ""), "130162" => array("braun", "grün"), "130171" => array("grün", "grün"), "130180" => array("rot", "grün"), "130189" => array("blau", "grün"), "100416" => array("braun", "grün"), "100419" => array("grün", "grün"), "100422" => array("rot", "grün"), "100425" => array("blau", "grün"), "114629" => array("gelb", ""), "132213" => array("gelb", ""), "114632" => array("orange", ""), "132004" => array("orange", ""), "114641" => array("braun", ""), "132010" => array("braun", ""), "114645" => array("grün", ""), "132016" => array("grün", ""), "114648" => array("rot", ""), "132025" => array("rot", ""), "114651" => array("blau", ""), "132022" => array("blau", ""));
    // maps humanReadableColor to colorCode matching https://gitlab.baubuddy.de/external/schaeferveroleistungserfassung/-/commit/4416a9ba967c211cb1257d0073d4d8ac1d1dfdf4
	$humanReadableColorToHex = array("" => "#fffff", "gelb" => "#F5FF00", "schwarz" => "#0A0A0A", "orange" => "#FFA421", "braun" => "#5A3A29", "grün" => "#48A43F", "rot" => "#C1121C", "blau" => "#2874B2");
    $humanReadableColor = isset($articleNumberToHumanReadableColor[$artNr][$colorCounter-1]) ? $articleNumberToHumanReadableColor[$artNr][$colorCounter-1] : "";
    $colorCode = $humanReadableColorToHex[$humanReadableColor];
    return $colorCode;
}

?>
<section class="hidden-with-span">
    -
</section>
<table cellspacing="0" class="face-lift-client-info">
    <tbody>
    <tr>
        <td class="color-gray project-table-cell" colspan="5">
            <table id="project-table">
                <tr>
                    <td class="cell-titles drop-all-borders" colspan="5"><h3 id="client-cell-title">Kunde</h3></td>
                </tr>
                <tr>
                    <td class="drop-all-borders" colspan="5"> <?php echo $customerName; ?>
                    </td>
                </tr>

                <tr>
                    <td class="cell-titles drop-all-borders" colspan="5"><h3>Projekt</h3></td>
                </tr>
                <tr>
                    <td class="drop-all-borders" colspan="5"> <?php echo $projectName; ?>
                    </td>
                </tr>
            </table>
        </td>

        <td id="company-logo-cell" colspan="3">
            <table cellspacing="0" class="peri-ladeliste-metadata">
                <tr>
                    <td id="load-list">
                        <h1>Ladeliste</h1>
                    </td>
                    <td rowspan="2">
                        <section class="logo">

                            <img class="logo"
                                 src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/peri-logo.png"); ?>" alt="Crane Icon"
                                 alt="Company logo">
                        </section>
                    </td>
                </tr>
                <tr>
                    <td id="load-list-nr">
                        #<?php
                        echo $loadListNr; ?>
                    </td>

                </tr>

                <tr>
                    <td id="activity-cell" rowspan="2">
                        <h3> <?php echo $activityType ?> | Tag <?php ;
                            echo $loadDay; ?></h3>
                    </td>
                    <td rowspan="2">
                        <section id="qr-section">
                            <img id="qr-code"
                                 src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/peri-qrcode.png"); ?>"
                                 alt="QR code">
                        </section>
                    </td>
                </tr>

            </table>

        </td>
    </tr>
</table>
<table cellspacing="0" class="peri-address">

    <tr>
        <td class="home thicons">
            <i><img class="thicons"
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/icons/home.png"); ?>"
                    alt="Home Icon"></i>
        </td>
        <td class="project-metadata-heads"><b>ASP Büro</b></td>

        <td class="crane thicons">
            <i><img class="thicons"
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/icons/const-crane.png"); ?>"
                    alt="Crane Icon"></i>
        </td>
        <td class="project-metadata-heads"><b>ASP Baustelle</b></td>

        <td class="wrench thicons">
            <img class="thicons"
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/icons/wrench.png"); ?>"
                    alt="Wrench Icon"></i>
        </td>
        <td class="project-metadata-heads"><b>Tätigkeit</b></td>

        <td class="truck thicons">
            <img class="thicons"
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/icons/truck.png"); ?>"
                    alt="Truck Icon"></i>
        </td>
        <td class="project-metadata-heads"><b>Fahrzeug</b></td>

        <td class="calendar thicons">
            <img class="thicons"
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/icons/calendar.png"); ?>"
                    alt="Calendar Icon"></i>
        </td>
        <td class="project-metadata-heads"><b>Liefertermin</b></td>
    </tr>
    <tr>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $siteManagerName;?></td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $projectManagerName;?></td>
        <td>
            <section class="display-none">-
                <display-none
                /section>
        </td>
        <td><?php echo $activityType; ?></td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $companyVehicleLicensePlate; ?> </td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $creationDate; ?></td>
    </tr>
    <tr>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $projectManagerPhone; ?></td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $siteManagerPhone; ?></td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td></td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td></td>
        <td>
            <section class="display-none">-</section>
        </td>
        <td><?php echo $creationTime; ?> Uhr</td>
    </tr>
</table>
<section class="display-none">-</section>

<table class="customer-info-onerow">
    <tr>
        <td><b>Ersteller:</b> <?php
            echo "$siteManagerName"; ?></td>
        <td><b>&nbsp&nbsp&nbsp &#8226;&nbsp;&nbsp;Verantwortlicher
                Beladung:</b> <?php
            echo $projectManagerName; ?></td>
        <td><b>&nbsp&nbsp&nbsp &#8226;&nbsp;&nbsp;Rückempfänger:</b> <?php
            echo $siteManagerName; ?></td>
    </tr>
</table>

<section class="display-none">-</section>
<table class="load-list-table">
    <thead>
    <tr>
        <td class="hidden-with-span">

        </td>
    </tr>
    <tr>
        <th colspan="4">
            Artikel
            <section class="display-none">-</section>
        </th>
        <th colspan="1">
            <p class="display-inline padding-top">Anzahl
            <section class="not-bold">(Stk.)</section>
            </p>
        </th>
        <th colspan="1">
            <p class="display-inline padding-top">Korrektur/Feedback
            </p>
        </th>
        <th colspan="2">Gewicht (kg)</th>
    </tr>
    </thead>

    <?php

    foreach ($positions as $position) {

        $quantity = $position['quantity'];
        $corrections = $position['corrections'];
        $sum = $position['sum'];
        $directoryOfServiceCompoundKey = $position['directoryOfServiceCompoundKey'];
        $itemGroupId = $position['directoryOfServiceItem']['itemGroupId'];
        $shortDescription = $position['directoryOfServiceItem']['shortDescription'];
        $serviceItemNo = $position['directoryOfServiceItem']['serviceItemNo'];
        $weight = $position['directoryOfServiceItem']['Weight'];
        $totalWeight = $position['totalWeight'];
        $yesNoReportedValue = "no";
        $reportedFeedback = "";
        $corrections = "<table style='border: 2px solid white !important;'><tr>" . reportedValueCheck($yesNoReportedValue, $reportedFeedback) . "</tr></table>";
        echo
            '
    <tr>
        <td colspan="4">
            <table id="load-list-1st-cell-table">
                <tr>
                    <td rowspan="2">
                    ' .
                '<section class="img-section"> 
                    <img class="bb-parts" src="'
                .
                takeArticleNrFromImage($serviceItemNo, $imgMatches) . '" alt="An inventory article with number: ' . $serviceItemNo . '"'
                . '</section>'
                . '
                    </td>
                    <td class="colspan7" colspan="7">
                        <b>
                        ' .
                    $shortDescription
                    . '
                       </b> 
                   </td>
                   <td class="color1" style="width: 15px; height: 60px; padding-left: 10px; background-color:'.resolveColorCodePerArtNr($serviceItemNo,1).'">
                    
                    </td>
                </tr>
                <tr>
                    <td class="colspan7 color2" colspan="7">
                    ' .
                'Art. Nr. 
                  ' . $serviceItemNo
                . '
    |
                    ' .
            floatCommaValue($weight)
                . ' kg
                    </td>
                    <td style="width: 15px; height: 60px; padding-left: 10px; background-color:'.resolveColorCodePerArtNr($serviceItemNo,2).'">
                    </td>
                </tr>
            </table>
        </td>
        <td class="padding-left align-center" colspan="1">' . $quantity . '</td>
        <td class="padding-left" colspan="1">' . $corrections . '</td>
        <td class="padding-left align-center" colspan="2">' . floatCommaValue($totalWeight) . '</td>     
    </tr>
';
        $sumTotalWeight += $totalWeight;
    }

    echo "<tr><td id='sumtotal' colspan='15'>Total:&nbsp;&nbsp;" . floatCommaValue($sumTotalWeight) . "</td></tr>";
    ?>
    <tbody>
    </tbody>
</table>
</table>
</body>
</html>