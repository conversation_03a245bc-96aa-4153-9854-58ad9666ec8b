<?php
require_once ABS_PATH.'Documentation.php';

class C_Novoferm {

private $db;
private $documentation;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
	}
	
	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		if($documentData) {
			$projectData = $this->getLatestProjectData($documentData['documentRelKey1']);
			return $this->combineData($documentData, $projectData, $schemaId);
		} else {
			throw new RestException(400, "No such document id!");
		}
	
	}

	public function getRelatedFileExternalId($schemaId, $documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		return $this->documentation->documentationModel->getFileFromDocumentData($documentData);
	}
		
	public function getLatestProjectData($ktr) {
		$stmt = $this->db->prepare("
			SELECT TOP 1 ktr.name, ktr.kname FROM ktr WHERE ktr.ktr = :ktr ORDER BY ktr.jahr DESC
		");
		$stmt->execute(['ktr' => $ktr]);
		
		$data = $stmt->fetch();
		
		return $data;
	}

	private function combineData ($documentData, $projectData, $schemaId) {
		
		$installersIds = $this->getIds($schemaId, 'Монтажник');
		$mountedProductsIds = $this->getIds($schemaId, 'Монтиран продукт');
		
		$templateData = [];
		$templateData['createdOn'] = $documentData['documentCreatedOn'];
		$templateData['installers'] = [];
		$templateData['mountedProducts'] = [];
		
		
		foreach ($installersIds as $k=>$v) {
			$templateData['installers'][$v] = [];
		}
		
		foreach ($mountedProductsIds as $k=>$v) {
			$templateData['mountedProducts'][$v] = [];
		}
		
		
		foreach ($documentData['children'] as $k=>$v) {
			//the properties for a single installer
			if($v['title']==='Име' || $v['title'] ==='Съотношение на работа' || $v['title']==='Подпис') {
				foreach ($templateData['installers'] as $key=>$value) {
					//compare parentId for each of these 3 properties of installer to all existing parentId-s for these schemaId and documentId
					if ($v['parentId'] === $key) {
						if(array_key_exists($key, $templateData['installers'])) {
							if($v['title']!=='Подпис') {
								$templateData['installers'][$key][$v['title']] = $v['reportedValue'];
							} else {
								$templateData['installers'][$key][$v['title']] = $v['filePath'];
							}
						} else {
							if($v['title']!=='Подпис') {
								$templateData['installers'][$key] = [];
								array_push($templateData['installers'][$key], [$v['title']=>$v['reportedValue']]);
							} else {
								$templateData['installers'][$key] = [];
								array_push($templateData['installers'][$key], [$v['title']=>$v['filePath']]);
							}
						}
					}
				}
			//mounted product
			} else if ($v['title']==='Продукт' || $v['title'] === 'Количество' || $v['title']=== 'Обща сума в лв' || $v['title'] === 'Забележка') {
				foreach ($templateData['mountedProducts'] as $key=>$value) {
					//compare parentId for each of these mounted product's properties to all existing parentId-s  with such schemaId and documentId
					if ($v['parentId'] === $key) {
						$templateData['mountedProducts'][$key][$v['title']] =  $v['reportedValue'];
					}
				}
			} else if(isset($v['reportedValue'])) {
				$templateData[$v['title']] = $v['reportedValue'];
			} else {
				continue;
			}
			
		}
		
		$templateData['projectName'] = $projectData['name'];
		$templateData['kName'] = $projectData['kname'];
		// fetch principals customerNo to have the name for proper display
		require_once ABS_PATH.'class/param01.class.php';
		$param01DB = new Param01DB();
		$companyInfo = $param01DB->get_settings_info();

		$templateData['companyDetails'] = $companyInfo;
		
		return $templateData;
	
	}
		
	//get ids of mounted products or installers which will be used later to combine labor ratio, name and signature for each one 
	private function getIds($schemaId, $substring) {
		$stmt = $this->db->prepare("
			SELECT id from documentation_schema_positions where CHARINDEX(N'".$substring."',title) > 0 and schemaId = :schemaId
		");
		$stmt->execute(['schemaId' => $schemaId]);
		
		$data = $stmt->fetchAll();
		$ids = [];
		foreach ($data as $k=>$v) {
			array_push($ids, $v['id']);
		}
		return $ids;
	}
	
	private function getCustomerData($name) {
		$stmt = $this->db->prepare("
			SELECT name, plz,telefon, telefax, ort, adresse1 from kunden where suchname = :name
		");
		$stmt->execute(['name' => $name]);
		
		$data = $stmt->fetchAll();
	
		return $data[0];
	}
}