<?php
require_once ABS_PATH.'Documentation.php';

class C_Gefahrdungsbeurteilung {
private $db;
private $documentation;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
	}

	public function getData($schemaId, $documentationId)
	{
		$document = $this->documentation->getDocumentId($schemaId,$documentationId);

		// transform children into indexed array
		$documentChildren = array();
		foreach ($document['children'] as $child)
		{
			$documentChildren[$child['id']] = $child;
		}
		$document['children'] = $documentChildren;

		$schema = $this->documentation->getSchemasPosById($schemaId);

		// transform children to tree
		$childrenTree = array();
		$levelOneParent = null;
		$levelTwoParent = null;
		$levelThreeParent = null;
		// TODO this should be solved inside the documentation-node using a recursion
		foreach ($schema['children'] as $child) {
			// handle root level
			if(!$child['parentId'])
			{
				$childrenTree[$child['id']] = $child;
				$levelOneParent = $child['id'];
			}
			// handle level 1
			elseif($child['parentId'] == $levelOneParent)
			{
				if($documentChildren[$child['id']]['filePath'])
					$child['reportedValues'][] = $documentChildren[$child['id']]['filePath'];
				else
					$child['reportedValues'] = $documentChildren[$child['id']]['reportedValues'];
				$childrenTree[$levelOneParent]['children'][$child['id']] = $child;
				$levelTwoParent = $child['id'];
			}
			// handle level 2
			elseif($child['parentId'] == $levelTwoParent)
			{
				if($child['displayInside'])
					$childrenTree[$levelOneParent]['children'][$levelTwoParent]['children'][$child['displayInside']]['reportedValues'][] = $child['reportedValues'];
				else
				{
					$child['reportedValues'] = $documentChildren[$child['id']]['reportedValues'];
					$childrenTree[$levelOneParent]['children'][$levelTwoParent]['children'][$child['id']] = $child;
					$levelThreeParent = $child['id'];
				}
			}
			// handle level 3
			elseif($child['parentId'] == $levelThreeParent)
			{
				// fetch file in case of signatureField
				if($child['type'] == 'signatureField' && $documentChildren[$child['id']]['filePath'])
				{
					$child['reportedValues'][] = $documentChildren[$child['id']]['filePath'];
				}
				else
					$child['reportedValues'] = $documentChildren[$child['id']]['reportedValues'];
				$childrenTree[$levelOneParent]['children'][$levelTwoParent]['children'][$levelThreeParent]['children'][$child['id']] = $child;
			}
		}
		$document['children'] = $childrenTree;

		// fetch author
		$employeesApi = new v3\Employees();
		$author = $employeesApi->get($document['author'])[0];

		// fetch project
		$projectsApi = new Projects();
		$project = $projectsApi->get($document['documentRelKey1'])[0];

		// fetch project manager
		if($project['technicalContactKey'])
			$projectManager = $employeesApi->get($project['technicalContactKey']);

		return array('document' => $document,'author' => $author,'project' => $project,'projectManager'=>$projectManager);
	}
	
}