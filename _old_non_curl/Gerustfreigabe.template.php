<html>

<?php
$data = (array)$this->data;
?>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Scaffold Approval</title>
</head>
<body>
    <table class="header">
        <tr>
            <td class="logo" rowspan="2" style="text-align: center"> <img src="'.<?= $data['companyLogo']?>.'" alt="companyLogo"> </td>
            <td class="client-title" rowspan="2" style="border-right: 1px solid"> <?= $data['blankTitle']?> </td>
            <td style="width: 28px">Nr.:</td>
            <td style="width: 286px"> <?= $data['dokumentNr']?> </td>
        </tr>
        <tr>
            <td>Kunde:</td>
            <td><?= $data['projectData']['customer_kname']?> </td>
        </tr>
        <tr>
            <td colspan="2" rowspan="4" style="border-right: 1px solid; vertical-align: top">
                <b><?= $data['Arbeitsbeschreibung']?> </b>
            </td>
            <td>Örtlichkeit:</td>
            <td><?= $data['Örtlichkeit']?> </td>
        </tr>
        <tr>
            <td>Ebene:</td>
            <td><?= $data['Ebene']?> </td>
        </tr>
        <tr>
            <td>Koordinaten:</td>
            <td><?= $data['Koordinaten']?> </td>
        </tr>
        <tr>
            <td>Anforderer:</td>
            <td><?= $data['Anforderer']?> </td>
        </tr>
    </table>

    <table class="mainContent">
        <tr class="row-color">
            <td style="width: 60px" height="30px"><b>Ausführungsteam:</b></td>
            <td style="width: 142px"><b>Name:</b></td>
            <td style="width: 150px"><b>Unterschrift:</b></td>
            <td colspan="3"><b>Gerüstart:</b></td>
        </tr>
        <tr>
            <td height="30px"><?= $data['Job-Verantwortlicher']?>:</td>
            <td>
                <p style="word-wrap: break-word; word-break: break-word; width: 165px"><?= $data['Job-Verantwortlicher Name']?></p>
            </td>
            <td><img src="'.<?= $data['Unterschrift Verantwortlicher']?>.'" width="99%" height="auto" alt="unterschrift"></td>
            <td><input type="checkbox" name="Arbeitsgerüst" value="Arbeitsgerüst" <?php if ($data['Gerüstart'] == 'Arbeitsgerüst') echo "checked='checked'"; ?>> Arbeitsgerüst </td>
            <td><input type="checkbox" name="Lastgerüst" value="Lastgerüst" <?php if ($data['Gerüstart'] == 'Lastgerüst') echo "checked='checked'"; ?>> Lastgerüst </td>
            <td><input type="checkbox" name="Schutzgerüst" value="Schutzgerüst" <?php if ($data['Gerüstart'] == 'Schutzgerüst') echo "checked='checked'"; ?>> Schutzgerüst </td>
        </tr>
        <tr>
            <td><?= $data['Job-Ausführender']?>:</td>
            <td>
                <p style="word-wrap: break-word; word-break: break-word; width: 165px"><?= $data['Job-Ausführender Name']?></p>
            </td>
            <td><img src="'.<?= $data['Unterschrift Ausführender']?>.'" width="99%" height="auto" alt="unterschrift"></td>
            <td><input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Gerüstart'] == 'Hängegerüst') echo "checked='checked'"; ?>> Hängegerüst </td>
            <td><input type="checkbox" name="Fahrgerüst" value="Fahrgerüst" <?php if ($data['Gerüstart'] == 'Fahrgerüst') echo "checked='checked'"; ?>> Fahrgerüst </td>
            <td><input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if ($data['Gerüstart'] == 'Sonstiges') echo "checked='checked'"; ?>> Sonstiges </td>
        </tr>
        <tr>
            <td class="textSize" colspan="3" rowspan="4" style="vertical-align: top">

                <u><b>Sicherheitshinweise:</b></u> <br>
                <br />
                - Veränderungen am Gerüst nur durch den Gerüsterstseller <br>
                - Bei Materiallagerung ausreichend Durchgang frei lassen <br>
                - Auf Schutz- und Fanggerüsten keine Materiallagerung <br>
                - Gerüstbeläge nicht überlasten <br>
                - Nicht übereinander arbeiten <br>
                - Auf- und Abstieg nur über vorhandene Treppen oder Leitern<br>
                - Durchstiegsklappen geschlossen halten <br>
                - Nicht auf Gerüstbeläge abspringen oder Material abwerfen <br>
                - Standsicherheit des Gerüstes nicht durch Ausschachtung <br> &nbsp;&nbsp;gefährden
            </td>
            <td colspan="3">
                <table class="lastklasse-breitenklasse">
                    <tr>
                        <td colspan="3"><b>Lastklasse</b>  (gleichmäßig vertollte Last) </td>
                    </tr>
                    <tr>
                        <td style="text-align: left">
                            <input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Lastklasse'] == '1 (0,75 KN/m²)') echo "checked='checked'"; ?>>  1 (0,75 KN/m²)
                        </td>
                        <td style="text-align: left">
                            <input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Lastklasse'] == '2 (1,5 KN/m²)') echo "checked='checked'"; ?>>  2 (1,5 KN/m²)
                        </td>
                        <td style="text-align: left">
                            <input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Lastklasse'] == '3 (2,0 KN/m²)') echo "checked='checked'"; ?>>  3 (2,0 KN/m²)
                        </td>
                        <td style="text-align: left">
                            <input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Lastklasse'] == '4 (3,0 KN/m²)') echo "checked='checked'"; ?>>  4 (3,0 KN/m²)
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if ($data['Breitenklasse'] == 'Sonstiges') echo "checked='checked'"; ?>>  Sonstiges
                        </td>
                    </tr>
                    <tr><td><br></td></tr>
                    <tr><td></td></tr>
                    <tr>
                        <td><b>Breitenklasse:</b></td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Breitenklasse'] == 'W06') echo "checked='checked'"; ?>>  W 06 </td>
                        <td><input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['Breitenklasse'] == 'W09') echo "checked='checked'"; ?>>  W 09 </td>
                        <td><input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if ($data['Breitenklasse'] == 'Sonstiges') echo "checked='checked'"; ?>>  Sonstiges </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td class="row-color" colspan="3" height="30px" style="border-top: thin solid; border-bottom: thin solid;"><b>Freigabe + Verlängerung</b> </td>
        </tr>
        <tr>
            <td height="30px" style="vertical-align: bottom; border-top: thin solid;">Datum: </td>
            <td style="vertical-align: bottom; border-left: thin solid; border-top: thin solid;">Name: </td>
            <td style="vertical-align: bottom; border-left: thin solid; border-top: thin solid;">Unterschrift: </td>
        </tr>
        <tr>
            <td style="vertical-align: bottom"><?= date('d.m.Y',strtotime($data['Datum']))?> </td>
            <td>
                <p style="word-wrap: break-word; word-break: break-word; width: 94px; font-size: 14px"><?= $data['Name Gerüstbauer']?> </p>
            </td>
            <td style="text-align: center"><img src="'.<?= $data['Unterschrift Gerüstbauer']?>.'" width="99%" height="auto" alt="unterschrift"> </td>
        </tr>
    </table>

    <table class="header-abnahme">
        <tr>
            <td class="row-color" height="30px" colspan="3"><b>Abnahme durch Gerüstnutzer</b> </td>
            <td style="width: 82px"><br></td>
            <td style="width: 69px"><br></td>
            <td style="width: 81px"><br></td>
        </tr>
        <tr>
            <td style="width: 94px; height: 35px; vertical-align: bottom"><?= date('d.m.Y',strtotime($data['Datum']))?> </td>
            <td style="width: 120px; vertical-align: bottom; font-size: 14px;"><?= $data['Name Gerüstnutzer']?> </td>
            <td style="width: 78px"><img src="'.<?= $data['Unterschrift Gerüstnutzer']?>.'" width="99%" height="auto" alt="unterschrift"> </td>
            <td><br></td>
            <td><br></td>
            <td><br></td>
        </tr>
    </table>

    <table style="width: 100%; border-collapse: collapse">
        <tr>
            <td class="row-color eigenmahtige-text" colspan="6">eigenmächtige veränderungen  am  gerüst sind verboten!<br> </td>
        </tr>
        <tr class="row-color">
            <td class="eigenmahtige-text2">Die Unfallverhütungsvorschriften und rechtlichen Vorschriften sind zu beachten!</td>
        </tr>
    </table>

    <br />

    <table class="gerust-table">
        <tbody>
        <tr class="gerust-row-border">
            <td class="gerustGesperrt-title" colspan="9">!!!GERÜST GESPERRT!!! </td>
        </tr>
        <tr class="gerust-row-border cell-titles">
            <td style="width: 15%"><b><?= $data['dokumentNr']?></b> </td>
            <td colspan="3" style="width: 40%" ><b><?= $data['projectData']['customer_kname']?></b> </td>
            <td colspan="5"><b><?= $data['Örtlichkeit']?></b> </td>
        </tr>
        <tr>
            <td class="images-gerust borders" colspan="9" style="border-left: 2px solid; border-right: 2px solid; text-align: center">
                <img src="<?=WEB_ROOT_API.'vendor\printouts\img\img1.jpg'?>" alt="imageSign1">
                <img src="<?=WEB_ROOT_API.'vendor\printouts\img\img2.jpg'?>" alt="imageSign2">
                <img src="<?=WEB_ROOT_API.'vendor\printouts\img\img3.jpg'?>" alt="imageSign3"> <br>
            </td>
        </tr>
        </tbody>
    </table>

    <table class="textUnderImages">
        <tr>
            <td style="width: 9%"></td>
            <td class="row-color undersigns-text" style="vertical-align: center">Achtung - Gerüst nicht benutzen! </td>
            <td style="width: 9%"></td>
        </tr>
    </table>

    <table class="verboten_text">
        <tr>
            <td class="text-title" colspan="9">JEDE EIGENMÄCHTIGE BENUTZUNG DES GERÜSTES IST VERBOTEN ! </td>
        </tr>
        <tr>
            <td style="width: 36%; height: 30px"><b>Grund der Sperrung:</b> </td>
            <td colspan="3" style="width: 35%" ><b>Feststellung u. Sperrung durch:</b> </td>
            <td colspan="3"><b>Mangel beheben:</b> </td>
        </tr>
        <tr>
            <td height="30px"><b>Kurzbeschreibung des Mangels:</b> </td>
            <td style="text-align: center"><b>Name:</b> </td>
            <td style="text-align: center"><b>Datum:</b> </td>
            <td style="text-align: center"><b>Kurzzeich.:</b> </td>
            <td style="text-align: center"><b>Name:</b> </td>
            <td style="text-align: center"><b>Datum:</b> </td>
            <td style="text-align: center"><b>Kurzzeich.:</b> </td>
        </tr>
        <tr>
            <td height="30px"><br /> </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td height="30px"></br></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
    </table>
</body>
</html>

<style>
    .header {
        width: 100%;
        mso-cellspacing: 0;
        border-collapse: collapse;
        border-bottom: none;
    }
    .header tr td {
        border: 1px solid black;
    }
    .logo { width: 70px; }

    .logo > img {
        height: 50px;
        width: 50px;
    }
    .mainContent {
        width: 100%;
        border-collapse: collapse;
        mso-cellspacing: 0;
    }
    .mainContent tr td {
        border-top: 0;
        border-left: 1px solid black;
        border-right: 1px solid black;
        border-bottom: 1px solid black;
        padding: 0;
    }
    .lastklasse-breitenklasse {
        width: 100%;
        border-collapse: collapse;
        mso-cellspacing: 0;
    }
    .lastklasse-breitenklasse tr td{
        border: 0;
        padding: 0;
    }
    .header-abnahme {
        width: 100%;
        mso-cellspacing: 0;
        border-collapse: collapse;
        border-top: 0px;
    }
    .header-abnahme tr td {
        padding: 0;
        border-left: 1px solid black;
        border-right: 1px solid black;
        border-bottom: 1px solid black;

    }
    .client-title {
        width: 417px;
        text-transform: uppercase;
        font-weight: bold;
        font-size: 30px;
        text-align: center;
    }

    .row-color { background-color: lightgrey; }

    .textSize {
        font-size: 15px;
        padding: 2px;
    }

    .gerust-table {
        width: 100%;
        border-collapse: collapse;
        border-bottom: hidden;
    }
    .gerust-table tr td { border: 2px solid black; }

    .textUnderImages {
        width: 100%;
        border-collapse: collapse;
        mso-cellspacing: 0;
    }
    .textUnderImages tr {
        border-left: 2px solid black;
        border-right: 2px solid black;
        padding: 0;
    }
    .verboten_text {
        border-collapse: collapse;
    }
    .verboten_text tr td{
        border: 1px solid black;
        padding: 0;
    }
    .eigenmahtige-text{
        color: #ffffff;
        text-decoration: underline;
        text-transform: uppercase;
        font-weight: bold;
        font-size: 22px;
        text-align: center;
    }

    .eigenmahtige-text2 {
        color: #ffffff;
        font-weight: bold;
        font-size: 18px;
        text-align: center;
        padding: 0;
    }

    .gerustGesperrt-title{
        text-align: center;
        background-color: lightgrey;
        height: 20%;
        font-weight: 900;
        font-size: 55px;
        letter-spacing: 2px;
    }

    .gerust-row-border > td { border: 3px solid black!important; }

    .gerust-row-border.cell-titles { font-size: x-large; }

    .images-gerust > img {
        height: 200px;
        margin-right: 10px;
    }

    .images-gerust > img:nth-child(2) {
        height: 270px;
        margin-top: 5px;
    }

    table.images-underText > td.images-text.row-color {
        font-size: xx-large;
        font-weight: bold;
    }

    .undersigns-text {
        white-space: nowrap;
        font-weight: bold;
        font-size: 37px;
        letter-spacing: 3px;
    }

    .text-title {
        background-color: darkgrey;
        color: #ffffff;
        font-weight: bold;
        font-size: 30px;
        text-align: center;
        padding: 0;
    }
</style>
<!-- corrected php file to show correct kN values -->