{"name": "Stundenlohnarbeiten", "description": "", "status": "active", "createdBy": "289", "printText": null, "createdOn": "2022-06-30T10:00:00Z", "editedBy": null, "editedOn": null, "applicableEntities": [], "positions": [{"id": -1, "title": "Datum", "type": "date"}, {"id": -93, "title": "Tagesblattnummer", "type": "int"}, {"id": -3000, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "headline", "default_collapse": true, "repeatCountWithChildren": 5}, {"parentId": -3000, "id": -3001, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"parentId": -3000, "id": -3002, "title": "Qualifikation", "type": "combobox", "values": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"parentId": -3000, "id": -3003, "title": "<PERSON>", "type": "string"}, {"parentId": -3000, "id": -3004, "title": "Bis", "type": "string"}, {"id": -2, "title": "Durchgeführte Arbeiten", "type": "string"}, {"id": -2000, "title": "Eingesetztes Material", "type": "string"}, {"id": -3, "title": "Eingesetztes Werkzeug / Bauschutt/ Sonstiges", "type": "headline"}, {"id": -4, "parentId": -3, "title": "Kompressor / Presslufthammer in Stunden", "type": "string"}, {"id": -5, "parentId": -3, "title": "Elektrohammer in Stunden", "type": "string"}, {"id": -7, "parentId": -3, "title": "Schnittfläche Wandsäge / Beton / Mauerwerk in m²", "type": "float"}, {"id": -8, "parentId": -3, "title": "LKW in Stunden", "type": "float"}, {"id": -9, "parentId": -3, "title": "Stk. Kernbohrung", "type": "string"}, {"id": -100, "parentId": -3, "title": "Kernbohrung Durchmesser", "type": "string"}, {"id": -111, "parentId": -3, "title": "Kernbohrung Länge", "type": "string"}, {"id": -10, "parentId": -3, "title": "Teleskoplader in Stunden", "type": "string"}, {"id": -11, "parentId": -3, "title": "<PERSON><PERSON> / Radlader in Stunden", "type": "string"}, {"id": -12, "parentId": -3, "title": "Bauschutt / Restmüll in m³", "type": "string"}, {"id": -13, "parentId": -3, "title": "Sonstiges", "type": "string"}, {"id": -1001, "title": "Unterschriftsbereich", "type": "headline"}, {"id": -180, "parentId": -1001, "title": "Ort", "type": "string"}, {"id": -181, "parentId": -1001, "title": "Datum", "type": "date"}, {"id": -182, "parentId": -1001, "title": "Unterschrift Auftraggeber", "type": "signatureField"}, {"id": -183, "parentId": -1001, "title": "Unterschrift Auftragnehmer", "type": "signatureField"}], "group_ids": [1, 3]}