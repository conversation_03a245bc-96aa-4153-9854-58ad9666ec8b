<?php
require_once ABS_PATH.'printouts/printout.helper.php';

class StundenlohnarbeitenModel
{
	public function getData($schemaId,$documentationId)
	{
		$documentationApi = new Documentation();
		$helper = new PrintoutHelper();

		$documentData = $documentationApi->getDocumentId($schemaId,$documentationId);
		// TODO unify with Bestellung.pdf
		// generate array for root-node
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		// place it the first element in the children array
		array_unshift($documentData['children'], $main);

		$documentDataGrouped = $helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		$document = $helper->getDeepValueFromField('title', 'reportedValues', $documentDataGrouped[0], 'children');

		$settingsAPI = new Settings();
		$templateData['companyLogoUrl'] = $settingsAPI->getcommon()['logo'];

		$settingsDB = new \SettingsDB();
		// resolve own company
		$principalCompanyNo = $settingsDB->get_settings(['principalCompanyNo'])['principalCompanyNo'];
		if($principalCompanyNo)
		{
			$addressesAPI = new Addresses();
			$companyDetails = $addressesAPI->get($principalCompanyNo);
			$templateData['companyDetails'] = $companyDetails;
		}

		// resolve project
		$projectNo = $documentData['documentRelKey1'];
		$projectsSelector = new ProjectsSelector();
		$parameters['projectNo']['eq'] = $projectNo;
		$project = $projectsSelector->get($parameters)[0];
		$templateData['projectName'] = $project['projectName'];

		$templateData['documentData'] = $document['Stundenlohnarbeiten'];

		return $templateData;
	}
}