<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Stundenlohnarbeiten</title>
    <meta name="description" content="Source code by <PERSON><PERSON><PERSON>">
    <meta name="author" content="Svetlin!">
    <link rel="stylesheet" href="/stundenlohnarbeiten.css?v=<?php echo time(); ?>">
</head>
<body style="text-align: center">
<!-- PHP VARIABLES  -->
<?php
$data = $this->data;
$companyDetails = $data['companyDetails'];
$documentData = $data['documentData'];
$innerData = $documentData['Eingesetztes Werkzeug / Bauschutt/ Sonstiges'];
$signatureData = $data['documentData']['Unterschriftsbereich'];
#
$companyLogo = $data['companyLogoUrl'];
$companyName = $companyDetails['name'];
$phoneNum = $companyDetails['phoneNumber'];
$zipCodeAndCity = $companyDetails['postcode']." ".$companyDetails['city'];
$streetAddress = $companyDetails['address'];
$eMail = $companyDetails['email'];
#
$endDay = 1;
/* VARIABLES for Stk. Kernbohrung Länge Beton MW */
$dateOnTop = $documentData['Datum'][0];
$signatureDate = $signatureData['Datum'][0];
$signatureLocation = $signatureData['Ort'][0];
$signatureCustomer = $signatureData['Unterschrift Auftraggeber'][0];
$ownSignature = $signatureData['Unterschrift Auftragnehmer'][0];

$dailySheetNum = $documentData['Tagesblattnummer'][0];

$constructionSite = $data['projectName'];
$workDone = $documentData['Durchgeführte Arbeiten'][0];
$usedMaterial = $documentData['Eingesetztes Material'][0];

$coreDrill = $innerData['Stk. Kernbohrung'][0];
$coreDrillLength = $innerData['Kernbohrung Länge'][0];

$compressor = $innerData['Kompressor / Presslufthammer in Stunden'][0];
$elHammer = $innerData['Elektrohammer in Stunden'][0];
$telescope = $innerData['Teleskoplader in Stunden'][0];
$hBagger = $innerData['Bagger / Radlader in Stunden'][0];
$constRubble = $innerData['Bauschutt / Restmüll in m³'][0];
$etc = $innerData['Sonstiges'][0];
$hLKW = $innerData['LKW in Stunden'][0];
$cutSurface = $innerData['Schnittfläche Wandsäge / Beton / Mauerwerk in m²'][0];
/* END QUANTITATIVE VARIABLES */

$employeeKeyNames = preg_grep('/^Mitarbeiter.*/', array_keys($documentData));

// fill array
foreach ($employeeKeyNames as $employeeKeyName) {
    $employee = $documentData[$employeeKeyName];
    // skip empty employee entries
    if(!$employee)
        continue;
    $fromTime = $employee['Von'][0];
    $toTime = $employee['Bis'][0];
    $total = '';
    if($fromTime && $toTime)
	{
        $fromDateTimeString = date('Y-m-d').' '.$fromTime;
        $fromDateTime = DateTime::createFromFormat('Y-m-d H:i',$fromDateTimeString);
        // if fromTime is not in proper format (H:i , e.g. 08:00), we skip here
        if($fromDateTime)
		{
			$toDateTimeString = date('Y-m-d').' '.$toTime;
            $toDateTime = DateTime::createFromFormat('Y-m-d H:i',$toDateTimeString);
            // also check toDateTime
            if($toDateTime)
			{
                $difference = $toDateTime->diff($fromDateTime);
                $amount = $employee['Anzahl'][0];
                $total = $amount ? $amount * $difference->h : $difference->h;
			}
		}
    }
	$employeesTableData[] = ['anzahl' => $amount, 'mitarbeiter' => $employee['Qualifikation'][0], 'von' =>  $fromTime, 'bis' => $toTime, 'gesamtstunden' => $total];
}
$employeeTableHeader['anzahl'] = "Anzahl";
$employeeTableHeader['mitarbeiter'] = "Mitarbeiter";
$employeeTableHeader['von'] = "Von";
$employeeTableHeader['bis'] = "Bis";
$employeeTableHeader['gesamtstunden'] = "Gesamtstunden";
?>
<!-- Body Contents Begining -->
<div id="jumbopfad">
    <table id="jumbotable">
        <tbody>
        <tr>
            <td><img id="logo" src='<?php echo $companyLogo; ?>'></td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>
                <div class="datum">
                    Datum <br><?php  $newdate = new DateTime($dateOnTop); echo $newdate->format('d.m.Y'); ?>
                </div>
            </td>
            <td>&nbsp;</td>
            <td>
                <address>
                    <strong><?php echo $companyName; ?></strong><br><?php echo $streetAddress; ?><br> <?php echo $zipCodeAndCity; ?><br><br><abbr title="Phone">Tel: </abbr><?php echo $phoneNum; ?><br><?php echo $eMail;?>
                </address>
            </td>
        </tr>
        <tr>
            <td>
                <div id="stundenlohnarbeiten" class="align-left-padding">
                    <h2>Stundenlohnarbeiten</h2>
                </div>
            </td>
            <td>&nbsp;</td>
            <td>
                <div class="align-left-padding">
                    <h6>Tagesblattnummer: <?php echo $dailySheetNum; ?> </h6>
                </div>
            </td>
        </tr>
        <tr>
            <td id="stundenlohnhr" colspan="3">
                <hr>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <div id="baustelle" class="align-left-padding">
                    Baustelle/Bereich: <?php echo $constructionSite; ?>
                </div>
            </td>
        </tr>
        <tr>
            <div>
                <td id="thedatatable" colspan="3">
                    <!-- Beginning of Data Table -->
                    <div>
                        <table class="styled-table">
                            <thead>
                            <tr>
                                <?php
                                foreach($employeeTableHeader as $header) {
                                    echo "<th>" . $header . "</th>";
                                }
                                ?>
                            </tr>
                            </thead>

                            <tbody>
                                <?php
                                    foreach($employeesTableData as $row) {
                                        echo "<tr>";
                                        foreach($row as $cell){
                                            echo "<td>" . $cell . "</td>";
                                        }
                                        echo "</tr>";
                                    }
                                ?>
                            </tbody>

                        </table>
                        <!-- End of Data Table -->

                    </div>
                </td>
        </tr>
        <tr>
            <td colspan="3">
                <div class="align-left-padding">
                    <h4>
                        Durchgeführte Arbeiten:
                    </h4>
                    <div class="txt-arbeiten">
                        <p>
                            <?php echo $workDone; ?><br></p>
                    </div>
                </div>
            </td>

        </tr>
        <tr>
            <td colspan="3">
                <div class="align-left-padding">
                    <h4>
                        Eingesetztes Material:
                    </h4>
                    <p>
                        <?php echo $usedMaterial;?></p>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                <div class="align-left-padding">
                    <h4>
                        Eingesetztes Werkzeug / Bauschutt/ Sonstiges:
                        <hr>
                    </h4>
        <tr>
            <td>
                <div class="align-left-padding">
                    <p>
                        <?php echo $compressor; ?>h Kompressor/ Presslufthammer<br><?php echo $elHammer; ?>h Elektrohammer<br><?php echo $cutSurface; ?>m² Schnittfläche Wandsäge/Beton/Mauerwerk<br><?php echo $hLKW; ?>h LKW<br><?php echo $coreDrill; ?>Stk. Kernbohrung (<?php echo $coreDrillLength; ?>Länge <?php echo $coreDrillLength; ?>/Beton MW)
                    </p>
                </div>
            </td>
            <td>&nbsp;</td>
            <td>
                <div class="align-left-padding">
                    <p>
                        <?php echo $telescope; ?>h Teleskoplader<br> <?php echo $hBagger;?>h Bagger/ Radlader<br>
                        <?php echo $constRubble; ?>m³ Bauschutt/Restmüll<br>Sonstiges: <?php echo $etc; ?><br><br>
                    </p>
                </div>
            </td>
        </tr>
        <tr><td colspan="3"><div id="bottomhr" class="align-left-padding"><hr></div><td></tr>
        <tr>
            <td style="vertical-align: bottom" class="align-left-padding">
				<?php echo $signatureLocation; ?>, <?php   $date = new DateTime($signatureDate); echo $date->format('d.m.Y'); ?>
            </td>
            <td class="align-left-padding">
				<?php if (!empty($signatureCustomer)) {
					echo
						"<img width=50% src='" . $signatureCustomer . "' alt='Foto' class='image'>";
				} ?>
            </td>
            <td class="align-left-padding">
				<?php if (!empty($ownSignature)) {
					echo "<img width=50% src='" . $ownSignature . "' alt='Foto' class='image'>";
				} ?>
            </td>
        </tr>
        <tr><td colspan="3"><div class="align-left-padding"><hr></div><td></tr>
        <tr >
            <td   class="align-left-padding">
                <div class="align-left-padding">
                    Ort, Datum
                </div>
            </td>
            <td >
                <div class="align-left-padding">
                    Unterschrift Auftraggeber
                </div>
            </td>
            <td>
                <div class="align-left-padding">
                    Unterschrift Auftragnehmer Fecke
                </div>
            </td>
        </tr>
        </tbody>
    </table>
    <!-- Closing the main table. -->
</div>
</body>
</html>

<style>
    * {
        font-family: Arial;
        font-size: 1.05em;
        text-align: left;
        border-collapse: collapse !important;
    }

    #jumbotable {
        width: 120%;
        height: 100%;
    }

    #jumbopfad {
        height: 100%;
    }

    address, .datum, .align-left-padding {
        text-align: left;
    }

    #stundenlohnhr, #thedatatable {
        width: 100%;
    }

    .styled-table {
        border-collapse: collapse;
        margin: 0px 0 0 0px;
        font-size: 0.9em;
        font-family: sans-serif;
        min-width: 70%;
        max-width: 100%;
        text-align: right;
    }

    .styled-table thead tr {
        background-color: #b9b9b9;
        color: #ffffff;
        text-align: right;
        border-bottom: 1px solid #b1b1b1;
        border-left: 1px solid #b1b1b1;
        border-right: 1px solid #b1b1b1;
    }

    .styled-table th,
    .styled-table td {
        padding: 12px 15px;
        border-bottom: 1px solid #b1b1b1;
        border-left: 1px solid #b1b1b1;
        border-right: 1px solid #b1b1b1;
    }

    .styled-table tbody tr {
        border-bottom: 1px solid #dddddd;
        border-bottom: 1px solid #b1b1b1;
        border-left: 1px solid #b1b1b1;
        border-right: 1px solid #b1b1b1;
    }

    .styled-table tbody tr:nth-of-type(even) {
        background-color: #f3f3f3;
        border-bottom: 1px solid #b1b1b1;
        border-left: 1px solid #b1b1b1;
        border-right: 1px solid #b1b1b1;
    }

    .styled-table tbody tr:last-of-type {
        border-bottom: 1px solid #b1b1b1;
        border-left: 1px solid #b1b1b1;
        border-right: 1px solid #b1b1b1;
    }

    table {
        table-layout: fixed;
        width: 100%;
    }

    #logo {
        min-height: 300px;
        max-height: 460px;

        min-height: 15%;
        max-height: 20%;
    }
</style>