<html>
<?php $data = $this->data;

		function renderCheckbox($checkboxValue, $dbValue, $possibleValues) {
			//possibleValues used if Sonstiges option is available
			if(empty($possibleValues)) {
				if (strtolower($checkboxValue) == strtolower($dbValue)) {
					return '<img src="'.WEB_ROOT_API.'vendor\printouts\checked.png?>"'.'alt="checked" width="auto" height="8%">'.$checkboxValue;
				} else {
					return '<img src="'.WEB_ROOT_API.'vendor\printouts\unchecked.png?>"'.'alt="unchecked" width="auto" height="8%">'.$checkboxValue;
	
				}
			} else {
				if(!in_array($dbValue, $possibleValues)) {
					//Sonstiges value stored
					return '<img src="'.WEB_ROOT_API.'vendor\printouts\checked.png?>"'.'alt="checked" width="auto" height="8%">'.$dbValue;
				} else {
					if (strtolower($checkboxValue) == strtolower($dbValue)) {
						return '<img src="'.WEB_ROOT_API.'vendor\printouts\checked.png?>"'.'alt="checked" width="auto" height="8%">'.$checkboxValue;
					}
				}
			}
		}
		
		function isChecked($string) {
			if (strtolower($string) == 'ja' || $string == '1' || $string == 'true') {
				return "checked='checked'";
			} else {
				return '';
			}
	}

?>


	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Gefährdungsbeurteilung für kurzfristige Baustellen</title>
	</head>
	<body>
		<table class="headerTable" border="1" cellspacing="0">
			<tr>
				<th width="20%"></th>
				<th align="center" width="60%">
					<span class="title">Arbeitsschritt 6<br></span>
					Gefährdungsbeurteilung für kurzfristige Baustellen
				</th>
				<th align="left" width="20%">
					Dokument 5.6-1<br>
					Varfasser: E.C<br>
					Stand: 12/2016
				</th>
			</tr>
		</table>
		<br>
		<table class="main noBottomBorder" cellspacing="0">
		<tr>
			<td width="20%">Bauvorhaben:</td>
			<td width="30%"><?=$data['projectName'];?></td>
			<td width="20%">Auftraggeber</td>
			<td width="30%"><?=$data['customerName'];?></td>
		</tr>
		<tr>
			<td>Zeitraum der Arbeiten:</td>
			<td><?=$data['fromTo'];?></td>
			<td>Aufsichtsführender:</td>
			<td><?=$data['projectManager'];?></td>
		</tr>
		</table>
		
	
	
		<table class="gerustplanung">
			<tbody>
				<tr>
					<td rowspan="19" class="rotate">Prüfung der Gerüstplanung</td>
					<td rowspan="3" width="20%">Gerüstart</td>
					<td ><?=renderCheckbox('Arbeitsgerüst', $data['Gerüstart'],[])?></td>
					<td><?=renderCheckbox('Fanggerüst', $data['Gerüstart'],[])?></td>
					<td><?=renderCheckbox('Dachfanggerüst', $data['Gerüstart'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('Fahrgerüst', $data['Gerüstart'],[])?></td>
					<td><?=renderCheckbox('Fangnetz', $data['Gerüstart'],[])?></td>
					<td><?=renderCheckbox('Schutzdach', $data['Gerüstart'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('Hängegerüst', $data['Gerüstart'],[])?></td>
					<td colspan="2"></td>
				</tr>
				<tr>
					<td rowspan="2">Ausführungsart</td>
					<td><?=renderCheckbox('Fassadengerüst', $data['Ausführungsart'],[])?></td>
					<td><?=renderCheckbox('Raumgerüst', $data['Ausführungsart'],[])?></td>
					<td><?=renderCheckbox('Stahlrohr-Kupplung', $data['Ausführungsart'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('Modul', $data['Ausführungsart'],[])?></td>
					<td><?=renderCheckbox('Rahmen', $data['Ausführungsart'],[])?></td>
					<td><?=renderCheckbox('Gitterträger', $data['Ausführungsart'],[])?></td>
				</tr>
				<tr>
					<td>Lastklasse</td>
					<td class="smallerFont">
						<?=renderCheckbox('0,75 kN/m²', $data['Lastklasse'],[])?>
						<?=renderCheckbox('1,50 kN/m²', $data['Lastklasse'],[])?>
					</td>
					<td class="smallerFont">
						<?=renderCheckbox('1,50 kN/m²', $data['Lastklasse'],[])?>
						<?=renderCheckbox('2,00 kN/m²', $data['Lastklasse'],[])?>
					</td>
					<td class="smallerFont">
						<?=renderCheckbox('4,50 kN/m²', $data['Lastklasse'],[])?>
						<?=renderCheckbox('6,00 kN/m²', $data['Lastklasse'],[])?>
					</td>
				</tr>
				<tr>
					<td>Breitenklasse</td>
					<td>
						 <?php if ($data['Breitenklasse'] == 'W06') { ?>
							<img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" width="auto" height="8%"> W06 <?php echo str_repeat('&nbsp;', 8)?>
						 <?php  }else {?>
						 	<img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="unchecked" width="auto" height="8%"> W06 <?php echo str_repeat('&nbsp;', 8)?>
						 <?php }?>
						<?php if ($data['Breitenklasse'] == 'W09') { ?>
							<img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" width="auto" height="8%"> W09
						 <?php  }else {?>
						 	<img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="unchecked" width="auto" height="8%"> W09
						 <?php }?>
					</td>
					<td>
						<?php $values = ['W06','W09', 'Konsole, Breite']; ?> 
						<?php if (!in_array($data['Breitenklasse'], $values)) { ?>
							<img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" width="auto" height="8%"><?=$data['Breitenklasse'];?></td>
						<?php } else {?>
							<img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="unchecked" width="auto" height="8%">Sonstiges</td>
						<?php }?>
					<td>
						<?php if ($data['Breitenklasse'] == 'Konsole, Breite') { ?>
							<img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" width="auto" height="8%"> Konsole, Breite
						 <?php  }else {?>
						 	<img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="unchecked" width="auto" height="8%"> Konsole, Breite
						 <?php }?>
					</td>
				</tr>
				<tr>
					<td>Verwendungszweck</td>
					<td><?=renderCheckbox('Fassade', $data['Verwendungszweck'],[])?></td>
					<td colspan="2"><?=renderCheckbox('Industrie', $data['Verwendungszweck'],[])?></td>
				</tr>
				<tr>
					<td>Bekleidung</td>
					<td><?=renderCheckbox('Netz', $data['Bekleidung'],[])?></td>
					<td><?=renderCheckbox('Plane', $data['Bekleidung'],[])?></td>
					<td><?=renderCheckbox('Sonstiges', $data['Bekleidung'],[])?></td>
				</tr>
				<tr>
					<td>Konstruktion</td>
					<td><?=renderCheckbox('Regelausführung', $data['Konstruktion'],[])?></td>
					<td><?=renderCheckbox('statischer Einzelnachweis', $data['Konstruktion'],[])?></td>
					<td><?=renderCheckbox('Sonstiges', $data['Konstruktion'],[])?></td>
				</tr>
				<tr>
					<td rowspan="3">Verkehrssicherung</td>
					<td><?=renderCheckbox('Genehmigung liegt vor', $data['Verkehrssicherung'],[])?></td>
					<td><?=renderCheckbox('LKW-Benutzung möglich', $data['Verkehrssicherung'],[])?></td>
					<td><?=renderCheckbox('Baustromanschluss', $data['Verkehrssicherung'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('Straßensperrung erforderlich', $data['Verkehrssicherung'],[])?></td>
					<td><?=renderCheckbox('Baustellenschilder', $data['Verkehrssicherung'],[])?></td>
					<td><?=renderCheckbox('Freiletung, Hindernisse', $data['Verkehrssicherung'],[])?></td>
				</tr>
					<tr>
					<td><?=renderCheckbox('Blinklampen', $data['Verkehrssicherung'],[])?></td>
					<td><?=renderCheckbox('Warnbaken', $data['Verkehrssicherung'],[])?></td>
					<td><?=renderCheckbox('Sonstiges', $data['Verkehrssicherung'],[])?></td>
				</tr>
				<tr>
					<td rowspan="6">Persönliche Schutzausrüstung Erlaubnisse</td>
					<td><?=renderCheckbox('Helm', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td><?=renderCheckbox('Sicherheitsschuhe S3', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td><?=renderCheckbox('Brille', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('Handschuhe', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td><?=renderCheckbox('Gehörschutz', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td><?=renderCheckbox('Atemschutz', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('Sicherheitsgurte', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td><?=renderCheckbox('Warnweste', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td><?=renderCheckbox('Fluchtmaske', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
				</tr>
				<tr>
					<td><?=renderCheckbox('CO-2 Warner', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
					<td colspan="2"></td>
				</tr>
				<tr>
					<td colspan="3"><?=renderCheckbox('Erlaubnisse (z.B. Arbeits-, Feuer-, Befahrerlaubnis) liegen vor, Inhalte sind zu beachten.', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
				</tr>
				<tr>
					<td colspan="3"><?=renderCheckbox('SIGE-Plan liegt vor, ist zu beachten.', $data['Persönliche Schutzausrüstung Erlaubnisse'],[])?></td>
				</tr>
				<tr>
					<td rowspan="7" class="rotate verticalAlign">Gefährdungsbeurteilung</td>
					<td colspan="4">
						<b>Prüfung ergänzend zur unternehmensbezogenen Gefährdungsbeurteilung</b><br>
						<b>Gefahren/Gefahrenquellen</b><br>
						<b>Standort/Umgebung:</b>elektrische Freileitungen, Rohrleitungen, Schächte, Kanäle, Anlagen m. EX-Gefahr,<br>
						maschinelle Anlagen, Kran und Förderanlagen, nicht begehbare Fläche, Straßen-/Schienenverkehr, gleichzeitig<br>
						andere Gewerke, <b>Absturz:</b> Öffnungen, bei Montage, nach innen/außen, Beläge, Leitern, Kleingerüste, fahrbare<br>
						Arbeitsbühnen;<br>
						<b>Stolpern/Rutschen/Stürzen:</b> mangelhafte Stand- und Laufflächen, Witterung <br>
						<b>Bauliche Durchbildung:</b> siehe Prüfhinweise, Gerüstteile nicht werfen und sachgerecht lagern;<br>
						<b>Unkontrolliert bewegte Teile;, Umgang mit Maschinen, Elektrische Anlagen und Betriebsmittel</b><br>
						<b>Körperliche Überbelastung;</b> Gefahrstoffe, Lärm
					</td>
				</tr>
				<tr>
				<td>
					Vorhandene (zusätzliche/besondere)<br> 
					Schutzmaßnahmen veranlassen
				</td>
				<td><?=renderCheckbox('Auffangnetz', $data['Vorhandene (zusätzliche/besondere) Schutzmaßnahmen veranlassen'],[])?></td>
				<td><?=renderCheckbox('Anseilschutz', $data['Vorhandene (zusätzliche/besondere) Schutzmaßnahmen veranlassen'],[])?></td>
				<td><?=renderCheckbox('Abdeckung', $data['Vorhandene (zusätzliche/besondere) Schutzmaßnahmen veranlassen'],[])?></td>
			</tr>
			<tr>
				<td rowspan="2">Zusätzliche Montagehilfen z.B.</td>
				<td><?=renderCheckbox('Arbeitsbühne', $data['Zusätzliche Montagehilfen z.B.'],[])?></td>
				<td><?=renderCheckbox('Aufzug', $data['Zusätzliche Montagehilfen z.B.'],[])?></td>
				<td><?=renderCheckbox('Stapler', $data['Zusätzliche Montagehilfen z.B.'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Hilfsgerüst', $data['Zusätzliche Montagehilfen z.B.'],[])?></td>
				<td><?=renderCheckbox('Kran', $data['Zusätzliche Montagehilfen z.B.'],[])?></td>
				<td><?=renderCheckbox('Leiter', $data['Zusätzliche Montagehilfen z.B.'],[])?></td>
			</tr>
			<tr>
				<td>Betriebsmittel</td>
				<td><?=renderCheckbox('Bohrmaschine', $data['Betriebsmittel'],[])?></td>
				<td><?=renderCheckbox('Schleifmaschine', $data['Betriebsmittel'],[])?></td>
				<td><?=renderCheckbox('Sonstiges', $data['Betriebsmittel'],[])?></td>
			</tr>
			<tr>
				<td>Betriebsmittel</td>
				<td><?=renderCheckbox('Bohrmaschine', $data['Hitzearbeit'],[])?></td>
				<td colspan="2"><?=renderCheckbox('Schleifmaschine', $data['Atemschutzgeräte'],[])?></td>
			</tr>
			<tr>
				<td colspan="4">
					Die Betriebs- und Montageanweisungen, Sicherheitsvorschriften und technischen Vorschriften sind zu beachten.<br> 
					Bei Unklarheiten oder Fragen stets Rücksprache mit Vorgesetzten halten.
				</td>
			</tr>
			</tbody>
		</table>	
	<table class="prufungFreigabe verticalAlign noUpperBorder">
		<tbody>
			<tr>
				<td rowspan="10" class="rotate freigabeColumn">Prüfung/Freigabe</td>
				<td><b>Gerüstbauteile:</b></td>
				<td><b>Beläge</b></td>
				<td><b>Arbeits- und Betriebssicherheit</b></td>
			</tr>
			<tr>
				<td>
					<input type="checkbox" name="augenscheinlich" value="augenscheinlich" <?=isChecked($data['Gerüstbauteile: augenscheinlich unbeschädigt'])?>>augenscheinlich unbeschädigt 
				</td>
				<td><?=renderCheckbox('Gerüstbohlen/ -bretter', $data['Beläge'],[])?></td>
				<td><?=renderCheckbox('Seitenschutz', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><b>Standsicherheit:</b></td>
				<td><?=renderCheckbox('Systembelege', $data['Beläge'],[], false)?></td>
				<td><?=renderCheckbox('Wandabstand', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Tragfähigkeit der Aufstandsfläche', $data['Standsicherheit'],[])?></td>
				<td><b>Verankerungen</b></td>
				<td><?=renderCheckbox('Aufstiege, Zugänge', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Spindelauszugslänge', $data['Standsicherheit'],[])?></td>
				<td><?=renderCheckbox('Ankerprotokoll vorhanden', $data['Verankerungen'],[])?></td>
				<td><?=renderCheckbox('Eckausbildung', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Sonderkonstruktion n. Bauunterlagen', $data['Standsicherheit'],[])?></td>
				<td><?=renderCheckbox('Bei Bekleidung erhöhte Kräfte beachten', $data['Verankerungen'],[])?></td>
				<td><?=renderCheckbox('Schutzwand im Dachfanggerüst', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Verstrebungen', $data['Standsicherheit'],[])?></td>
				<td></td>
				<td><?=renderCheckbox('Verkehrssicherung, Beleuchtung', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Gitterträger', $data['Standsicherheit'],[])?></td>
				<td></td>
				<td><?=renderCheckbox('Benutzerhinweise übergeben', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Längsriegel in Fußpunkthöhe', $data['Standsicherheit'],[])?></td>
				<td></td>
				<td><?=renderCheckbox('Kennzeichnung angebracht', $data['Arbeits- und Betriebssicherheit'],[])?></td>
			</tr>
			<tr>
				<td><?=renderCheckbox('Fahrrollen', $data['Standsicherheit'],[])?></td>
				<td></td>
				<td></td>
			</tr>
		</tbody>
	</table>
	
	<table class="footer">
	<tbody>
		<tr>
			<td width="18%">Bemerkungen:</td>
			<td colspan="2" class="bemerkungen"><?=$data['Bemerkungen'];?></td>
		</tr>
		<tr>
			<td rowspan="2"></td>
			<td><?=$data['Ort'].', '.$data['Datum'];?></td>
			<td><img src="<?=$data['Unterschrift']?>" align="left" class="signature" width="75%"></td>
		</tr>
		<tr>
			<td>Ort,Datum</td>
			<td>Unterschrift</td>
		</tr>
	</tbody>
</table>
	</body>
</html>



<style>
	body {font-family:Helvetica; height:100px}
	.headerTable, .main, .gerustplanung, .prufungFreigabe, .footer {width:99%;}
	.title {font-size:x-large;}
	table{
		border-collapse: collapse;
		border-spacing:0;
	}
	.noBottomBorder {border-bottom: 1px solid white!important;}
	.noUpperBorder {border-top: 1px solid white!important;}
	.main, th, td {border: 1px solid black;}
	.gerustplanung, th, td {border: 1px solid black; font-size:small;}
	.gefahrdungsbeurteilung th, td {border: 1px solid black; font-size:small;}
	.prufungFreigabe th, td {border: 1px solid black; font-size:small;}
	.footer, th, td {border: 1px solid black; font-size:small;}
	.rotate {
		     -moz-transform: rotate(-90.0deg);  /* FF3.5+ */
       		 -o-transform: rotate(-90.0deg);  /* Opera 10.5 */
  			 -webkit-transform: rotate(-90.0deg);  /* Saf3.1+, Chrome */
             filter:  progid:DXImageTransform.Microsoft.BasicImage(rotation=0.083);  /* IE6,IE7 */
             -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0.083)"; /* IE8 */
             text-align:center;
    		 white-space:nowrap;
    		 display:inline-block;
	}
	.signature {height:auto;}
	
	.verticalAlign {
  		vertical-align: middle!important;
	}
	
	.footer {border-collapse:collapse; table-layout:fixed; width:99%;}
	.bemerkungen { word-wrap:break-word;}
	
	.freigabeColumn { <?php (strtoupper(substr(PHP_OS, 0, 3)) === 'Linux') ? 'width: 22%!important;' : 'width: 18%!important;'; ?>}
</style>