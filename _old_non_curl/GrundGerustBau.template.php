<html>
<?php $data = $this->data;?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Tagesbericht / Bautagebuch</title>
	</head>
	<body><div class="container">
		<table class="noBorderTable">
			<tr class="noBorder">
				<td rowspan="5"><img width="80px" src="<?=WEB_ROOT_API.'vendor\printouts\GrundGerustbauLogo.png'?>" alt="GrundGerustbauLogo"></td>
				<td colspan="4" class="title">Tagesbericht / Bautagebuch für
                    <?php echo $data['date'] ?>
                </td>
			</tr>
			<tr class="noBorder">
				<td>Kunde</td>
				<td>
                    <?php echo $data['customerName'] ?>
                </td>
                <td><u>Dokumentationskennzahlen</u>
                </td>
                <td>
                </td>
			</tr>
			<tr class="noBorder">
				<td>Projekt</td>
				<td>
					<?php echo $data['projectName'] ?>
                    (Kostenträger: <?php echo $data['projectNumber'] ?>)
                </td>
                <td>Anzahl Fotos:
					<?php echo $data['filesCount']?>
                </td>
                <td>
                </td>
			</tr>
		   <tr class="noBorder">
			<td>Adresse</td>
			<td>
                <?php echo $data['address'] ?>
            </td>
               <td>Anzahl Gerüstfreigaben:
				   <?php echo count($data['dynamicDocumentation'])?>
               </td>
               <td>
               </td>
		  </tr>
            <tr class="noBorder">
                <td>Arbeitsauftrag</td>
                <td>
					<?php echo $data['workingOrderName'] ?>
                </td>
                <td>
                </td>
                <td>
                </td>
            </tr>
		</table>
			<br><br><br>
		<table class="border">
			<tr>
			  <th class="noBorder"></th>
			  <th>Laden</th>
			  <th>Anfahrt</th>
			  <th>Arbeit</th>
			  <th>Pause</th>
			  <th>Arbeit</th>
			  <th>Pause</th>
			  <th>Arbeit</th>
			  <th>Abfahrt</th>
			  <th>Laden</th>
			  <th>Ende</th>
			  <th colspan="3">Summen</th>	
		  </tr>
		  <tr>
			<td><b>Mitarbeiter</b></td>
			<td>
				<?php echo ($data['employees']->getFirst())->getTaskNameFromType('LOAD',1); ?>
            </td>
			<td></td>
			<td>
                <?php echo ($data['employees']->getFirst())->getTaskNameFromType('WORK',1); ?>
            </td>
			<td></td>
			<td>
				<?php echo ($data['employees']->getFirst())->getTaskNameFromType('WORK',2); ?>
            </td>
			<td></td>
			<td>
				<?php echo ($data['employees']->getFirst())->getTaskNameFromType('WORK',3); ?>
            </td>
			<td></td>
			<td>
				<?php echo ($data['employees']->getFirst())->getTaskNameFromType('LOAD',2); ?>
            </td>
			<td class="rightBorder"></td>
			<td>Laden</td>
			<td>Fahrt</td>
			<td>Arbeit</td>
		  </tr>
        <?php
        /** @var $employee C_GrundGerustBauTageberichtEmployeeRow */
		foreach ($data['employees']->getEmployees() as $employee) {
        ?>
				<tr>
			<td>
                    <p>
                        <?php echo (!$employee->isPreplanned()) ? "<i>":""; ?>
                        <?php echo $employee->getDisplayString() ?>
						<?php echo (!$employee->isPreplanned()) ? "</i>":""; ?>
                    </p>
                    <p>
                        <?php echo $employee->getDriverString() ?>
                    <p/>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(1,'LOAD') ?><br>
				<?php echo $employee->getTimeFromType(1,'LOAD','end') ?>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(1,'DRIVETO') ?><br>
				<?php echo $employee->getTimeFromType(1,'DRIVETO','end') ?>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(1,'WORK') ?><br>
				<?php echo $employee->getTimeFromType(1,'WORK','end') ?>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(1,'BREAK') ?><br>
				<?php echo $employee->getTimeFromType(1,'BREAK','end') ?>
            </td>
			<td align="right">
				<?php
               if($employee->getTimeFromType(1,'BREAK','end'))
                    echo $employee->getTimeFromType(1,'BREAK','end');
                else
					echo $employee->getTimeFromType(2, 'WORK');
				echo "<br>".$employee->getTimeFromType(2, 'WORK','end');
				?>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(2,'BREAK') ?><br>
				<?php echo $employee->getTimeFromType(2,'BREAK','end') ?>
            </td>
			<td align="right">
				<?php
				if($employee->getTimeFromType(2,'BREAK','end'))
					echo $employee->getTimeFromType(2,'BREAK','end');
				else
					echo $employee->getTimeFromType(3, 'WORK');
				echo "<br>".$employee->getTimeFromType(3, 'WORK','end');
				?>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(1,'DRIVEFROM') ?><br>
				<?php echo $employee->getTimeFromType(1,'DRIVEFROM','end') ?>
            </td>
			<td align="right">
				<?php echo $employee->getTimeFromType(2,'LOAD') ?><br>
				<?php echo $employee->getTimeFromType(2,'LOAD','end') ?>
            </td>
			<td class="rightBorder">
				<?php echo $employee->getEndTime() ?>
            </td>
			<td>
				<?php echo $employee->getSumByType('LOAD') ?>
            </td>
			<td align="right">
				<?php echo $employee->getSumByType('DRIVE') ?>
            </td>
			<td align="right">
				<?php echo $employee->getSumByType('WORK')." (".$employee->getSumByType('BREAK').")" ?>
            </td>
		  </tr>
            <?php
            // closing foreach loop for employees
        }
        ?>
		  <tr class="topBorder">
			<td>Ladezeit</td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByIndexAndType(1,'LOAD');
				?>
            </td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
			<td align="right">
                <?php
                    echo $data['employees']->getSumByIndexAndType(2,'LOAD');
                ?>
            </td>
              <td class="noBorder"></td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByType('LOAD');
				?>
            </td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
		  </tr>
		  <tr>
			<td>Fahrtzeit</td>
              <td class="noBorder"></td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByIndexAndType(1,'DRIVETO');
				?>
            </td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByIndexAndType(1,'DRIVEFROM');
				?>
            </td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
			<td align="right"> 
				<?php
				    echo $data['employees']->getSumByType('DRIVE');
				?>
            </td>
              <td class="noBorder"></td>
		  </tr>
	      <tr>
			<td>Arbeitszeit</td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByIndexAndType(1,'WORK');
				?>
            </td>
              <td align="right">
				  <?php echo "(".$data['employees']->getSumByIndexAndType(1,'BREAK').")"; ?>
              </td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByIndexAndType(2,'WORK');
				?>
            </td>
              <td align="right">
				  <?php echo "(".$data['employees']->getSumByIndexAndType(2,'BREAK').")"; ?>
              </td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByIndexAndType(3,'WORK');
				?>
            </td>
			<td class="noBorder"></td>
            <td class="noBorder"></td>
            <td class="noBorder"></td>
            <td class="noBorder"></td>
            <td class="noBorder"></td>
			<td align="right">
				<?php
				    echo $data['employees']->getSumByType('WORK')." (".$data['employees']->getSumByType('BREAK').")";
				?>
            </td>
		  </tr>
		  <tr class="noBorder"><td></td></tr>
		  <tr class="noBorder"><td></td></tr>
		  <tr class="noBorder"><td></td></tr>
		  <tr class="noBorder"><td></td></tr>
		  <tr>
				<td><b>Fahrzeuge</b></td>
              <td class="noBorder"></td>
        	    <td>KM</td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
				<td>KM</td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
              <td class="noBorder"></td>
			</tr>
            <?php foreach ($data['vehicles'] as $vehicle) { ?>
			<tr class="noBorder">
			  <td><?=$vehicle['kurzname'];?></td>
                <td class="noBorder"></td>
        	    <td align="right">-</td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
				<td align="right">-</td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
                <td class="noBorder"></td>
			</tr>
				<?php
				// closing foreach loop
			} ?>
		</table>
	
			<br><br><br>
		<div align="center"><b>Bemerkungen (aus dyn. Dokumentation z.B. Freigabeschild)</b></div>
			<br><br><br>
		<div align="right"><b>Unterschrift (Grund)</b></div>
</div></body>
</html>

<style>
	.container { display:block; height:100%; width:100%;}
	@media only screen and (orientation:portrait){
  #container {  
    height: 100vw;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
  }
}
	@media only screen and (orientation:landscape){
  #container {  
     -webkit-transform: rotate(0deg);
     -moz-transform: rotate(0deg);
     -o-transform: rotate(0deg);
     -ms-transform: rotate(0deg);
     transform: rotate(0deg);
  }
}
    body {
        font-family: "Arial", sans-serif;
    }
	table, th, td {
      border: 1px solid black;
      border-collapse: collapse;
	}

	table{
    	width:100%;
	}

	tr.noBorder td {
  		border: 0;
	}

    tr.topBorder {
        border-top: 3px solid black;
    }

    td.title{
        font-size: 30px;
        border-spacing: 1em;
    }
    td.rightBorder{
        border-right: 3px solid black;
    }
    td.noBorder {
        border: 0;
    }

    td.right {
        text-align: right;
    }
    
	.noBorderTable {border-collapse: collapse; border:0;}
	
</style>
