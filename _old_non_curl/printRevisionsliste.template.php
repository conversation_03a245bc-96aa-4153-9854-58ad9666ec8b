<html>
<?php $data = $this->data;

 	function isChecked($string) {
		if (strtolower($string) == 'ja' || $string == '1' || strtolower($string) == 'true') {
			return "checked='checked'";
		} else {
			return '';
		}
	}
	
	function isCheckedNo($string) {
		if (strtolower($string) != 'ja' && $string != '1' && strtolower($string) != 'true' && strtolower($string) != '') {
			return "checked='checked'";
		} else {
			return '';
		}
	}
?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Revisionsliste zur Gefährdungsbeurteilung</title>
	</head>
	<body>
	<table class="headerTable" cellspacing="0" cellpadding="0">
			<tr>
				<th class="firstHeader">
				Adresse der Baustelle:
				</th>
				<th class="secondHeader">
				Verantwortlicher vor Ort (AvO)
				</th>
				<th class="thirdHeader"><img src="<?=WEB_ROOT_API.'vendor\printouts\SchaeferGeruestbauLogo.png'?>" alt="SchaeferGeruestbauLogo" class="companyLogo"></th>		
			</tr>
			<tr>
				<th></th>
				<th></th>
				<th></th>
			</tr>
			<tr>
				<td colspan="3">
					<p class="orange bigFont" align="center">Revisionsliste zur Gefährdungsbeurteilung</p>
					Diese Check-Liste ist auf der Baustelle vor Beginn der Arbeiten abzuarbeiten. Hierdurch wird der Ist-Zustand auf der Bau-/Montagestelle dokumentiert, die Gefährdungen analysiert und der Auftragsverantwortliche vor Ort (AvO) in die Lage versetzt, die erforderlichen Maßnahmen einzuleiten um ein Unfall- und Verletzungsrisiko weitgehendst für Ihr Gewerk auszuschließen bzw. auf ein Minimum zu reduzieren.
				</td>
			</tr>
	</table>
	<table class="checkboxes" cellpadding="0" cellspacing="0" width="100%">
		<tr class="bordered">
			<th align="left" class="firstColumn">1) Dokumentation</th>
			<th class="secondColumn">ja</th>
			<th class="thirdColumn">nein</th>
			<th class="fourthColumn">Bemerkung / Maßnahmen</th>
		</tr>
		<tr>
			<td><span class="orange">1.1</span>Schriftlicher Arbeitsauftrag vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['1.1 Schriftlicher Arbeitsauftrag vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['1.1 Schriftlicher Arbeitsauftrag vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['1.1 Schriftlicher Arbeitsauftrag vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">1.2</span>Fahrtbeschreibung vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['1.2 Fahrtbeschreibung vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['1.2 Fahrtbeschreibung vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['1.2 Fahrtbeschreibung vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">1.3</span> Montageanweisung (Auf-,Um- oder Abbau) vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['1.3 Montageanweisung (Auf-,Um- oder Abbau) vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['1.3 Montageanweisung (Auf-,Um- oder Abbau) vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['1.3 Montageanweisung (Auf-,Um- oder Abbau) vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">1.4</span> Techn. Zeichnungen vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['1.4 Techn. Zeichnungen vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['1.4 Techn. Zeichnungen vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['1.4 Techn. Zeichnungen vorhanden'];?></td>
		</tr>
		<tr class="bordered">
			<td>2) Allg. Verkehrssicherung (Straßen, Durchfahrt, Gehweg)</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">2.1</span> Einweiser zum Einparken erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['2.1 Einweiser zum Einparken erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['2.1 Einweiser zum Einparken erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['2.1 Einweiser zum Einparken erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">2.2</span> Einweiser zur Verkehrssicherung erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['2.2 Einweiser zur Verkehrssicherung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['2.2 Einweiser zur Verkehrssicherung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['2.2 Einweiser zur Verkehrssicherung erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">2.3</span> Fahrzeugsicherung (Pylone, Warnbake etc.) durchgeführt</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['2.3 Fahrzeugsicherung (Pylone, Warnbake etc.) durchgeführt'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['2.3 Fahrzeugsicherung (Pylone, Warnbake etc.) durchgeführt'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['2.3 Fahrzeugsicherung (Pylone, Warnbake etc.) durchgeführt'];?></td>
		</tr>
		<tr>
			<td><span class="orange">2.4</span> Lade- und Transportbereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['2.4 Lade- und Transportbereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['2.4 Lade- und Transportbereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['2.4 Lade- und Transportbereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?>Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich </td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich'];?></td>
		</tr>
		<tr class="bordered">
			<td>3) Baustelleneinrichtung</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">3.1</span> WC bzw. mobiles WC vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['3.1 WC bzw. mobiles WC vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['3.1 WC bzw. mobiles WC vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['3.1 WC bzw. mobiles WC vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">3.2</span> Waschgelegenheit vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['3.2 Waschgelegenheit vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['3.2 Waschgelegenheit vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['3.2 Waschgelegenheit vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">3.3</span> Pausenraum vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['3.3 Pausenraum vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['3.3 Pausenraum vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['3.3 Pausenraum vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">3.4</span> Parkmöglichkeiten für unsere Fahrzeuge ausreichend vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['3.4 Parkmöglichkeiten für unsere Fahrzeuge ausreichend vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['3.4 Parkmöglichkeiten für unsere Fahrzeuge ausreichend vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['3.4 Parkmöglichkeiten für unsere Fahrzeuge ausreichend vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">3.5</span> Abstellflächen zur Materiallagerung ausreichend vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['3.5 Abstellflächen zur Materiallagerung ausreichend vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['3.5 Abstellflächen zur Materiallagerung ausreichend vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['3.5 Abstellflächen zur Materiallagerung ausreichend vorhanden'];?></td>
		</tr>
		<tr class="bordered">
			<td>4) Baustellengegebenheiten</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">4.1</span> Montagebereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen </td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.1 Montagebereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.1 Montagebereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.1 Montagebereich unmittelbar an Straßen, Durchfahrten, Fahrrad oder Gehwegen'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich </td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Sicherung, Absperrung oder Kennzeichnung (z.B. Flatter-band, Absperrzäune etc.) erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">4.2</span> Baustromverteiler zum Anschluss der Geräte (z.B. Ladegerät) vor- handen</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.2 Baustromverteiler zum Anschluss der Geräte (z.B. Ladegerät) vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.2 Baustromverteiler zum Anschluss der Geräte (z.B. Ladegerät) vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.2 Baustromverteiler zum Anschluss der Geräte (z.B. Ladegerät) vorhanden'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Strombedarf über Hausanschluss notwendig</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Strombedarf über Hausanschluss notwendig'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Strombedarf über Hausanschluss notwendig'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Strombedarf über Hausanschluss notwendig'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Schutzverteiler (PRCD) zwischengeschalten</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Schutzverteiler (PRCD) zwischengeschalten'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Schutzverteiler (PRCD) zwischengeschalten'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Schutzverteiler (PRCD) zwischengeschalten'];?></td>
		</tr>
		<tr>
			<td><span class="orange">4.3</span> Elektrische Freileitungen in der Nähe des Montagebereiches vor-handen </td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.3 Elektrische Freileitungen in der Nähe des Montagebereiches vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.3 Elektrische Freileitungen in der Nähe des Montagebereiches vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.3 Elektrische Freileitungen in der Nähe des Montagebereiches vorhanden'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Sicherheitsabstand gegen unbeabsichtigte Berührung auch mit Montagematerial ausreichend</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Sicherheitsabstand gegen unbeabsichtigte Berührung auch mit Montagematerial ausreichend'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Sicherheitsabstand gegen unbeabsichtigte Berührung auch mit Montagematerial ausreichend'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Sicherheitsabstand gegen unbeabsichtigte Berührung auch mit Montagematerial ausreichend'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Freileitung gesichert (spannungsfrei abgeschalten, gegen Berührung abgedeckt oder abgeschrankt)</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Freileitung gesichert (spannungsfrei abgeschalten, gegen Berührung abgedeckt oder abgeschrankt)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Freileitung gesichert (spannungsfrei abgeschalten, gegen Berührung abgedeckt oder abgeschrankt)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Freileitung gesichert (spannungsfrei abgeschalten, gegen Berührung abgedeckt oder abgeschrankt)'];?></td>
		</tr>
		<tr>
			<td><span class="orange">4.4</span> Verkehrswege (Laufflächen) zum Materialtransport geeignet</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.4 Verkehrswege (Laufflächen) zum Materialtransport geeignet'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.4 Verkehrswege (Laufflächen) zum Materialtransport geeignet'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.4 Verkehrswege (Laufflächen) zum Materialtransport geeignet'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Trittsicherheit gegeben (Untergrund, Breite, Stolperstellen) </td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Trittsicherheit gegeben (Untergrund, Breite, Stolperstellen)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Trittsicherheit gegeben (Untergrund, Breite, Stolperstellen)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Trittsicherheit gegeben (Untergrund, Breite, Stolperstellen)'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Bodenöffnungen, steile Böschungen vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Bodenöffnungen, steile Böschungen vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Bodenöffnungen, steile Böschungen vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Bodenöffnungen, steile Böschungen vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">4.5</span> Montageuntergrund befestigt, zur Montage ohne zusätzl. Aufwand geeignet</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.5 Montageuntergrund befestigt, zur Montage ohne zusätzl. Aufwand geeignet'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.5 Montageuntergrund befestigt, zur Montage ohne zusätzl. Aufwand geeignet'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.5 Montageuntergrund befestigt, zur Montage ohne zusätzl. Aufwand geeignet'];?></td>
		</tr>
		<tr>
			<td><span class="orange">4.6</span> Regen, Starkregen, Wind, Sturm etc.</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.6 Regen, Starkregen, Wind, Sturm etc.'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.6 Regen, Starkregen, Wind, Sturm etc.'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.6 Regen, Starkregen, Wind, Sturm etc.'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Besondere Schutzmaßnahmen (z.B. sofort ankern, Fall-stecker, zusätzl. Sicherungsperson etc.) erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Besondere Schutzmaßnahmen (z.B. sofort ankern, Fall-stecker, zusätzl. Sicherungsperson etc.) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Besondere Schutzmaßnahmen (z.B. sofort ankern, Fall-stecker, zusätzl. Sicherungsperson etc.) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Besondere Schutzmaßnahmen (z.B. sofort ankern, Fall-stecker, zusätzl. Sicherungsperson etc.) erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">4.7</span> Helligkeit ausreichend</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['4.7 Helligkeit ausreichend'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['4.7 Helligkeit ausreichend'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['4.7 Helligkeit ausreichend'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Zusätzliche Beleuchtung erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Zusätzliche Beleuchtung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Zusätzliche Beleuchtung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Zusätzliche Beleuchtung erforderlich'];?></td>
		</tr>
		<tr class="bordered">
			<td>5) Maschinen und Geräte (Montage-, Transporthilfe)</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">5.1</span> Einsatz eines Turmdrehkranes bzw. Autokranes</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['5.1 Einsatz eines Turmdrehkranes bzw. Autokranes'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['5.1 Einsatz eines Turmdrehkranes bzw. Autokranes'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['5.1 Einsatz eines Turmdrehkranes bzw. Autokranes'];?></td>
		</tr>
		<tr>
			<td><span class="orange">5.2</span> Einsatz eines Teleskopstaplers oder Flurförderzeuges (Stapler)</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['5.2 Einsatz eines Teleskopstaplers oder Flurförderzeuges (Stapler)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['5.2 Einsatz eines Teleskopstaplers oder Flurförderzeuges (Stapler)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['5.2 Einsatz eines Teleskopstaplers oder Flurförderzeuges (Stapler)'];?></td>
		</tr>
		<tr>
			<td><span class="orange">5.3</span> Einsatz einer kraftbetriebenen Arbeitsbühne</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['5.3 Einsatz einer kraftbetriebenen Arbeitsbühne'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['5.3 Einsatz einer kraftbetriebenen Arbeitsbühne'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['5.3 Einsatz einer kraftbetriebenen Arbeitsbühne'];?></td>
		</tr>
		<tr>
			<td><span class="orange">5.4</span> Einsatz sonstiger Baumaschinen oder Transportgeräte</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['5.4 Einsatz sonstiger Baumaschinen oder Transportgeräte'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['5.4 Einsatz sonstiger Baumaschinen oder Transportgeräte'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['5.4 Einsatz sonstiger Baumaschinen oder Transportgeräte'];?></td>
		</tr>
		<tr class="bordered">
			<td>6) Erste-Hilfe / Brandschutz</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">6.1</span> Verbandsmaterial mit Augenspülflasche vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['6.1 Verbandsmaterial mit Augenspülflasche vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['6.1 Verbandsmaterial mit Augenspülflasche vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['6.1 Verbandsmaterial mit Augenspülflasche vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">6.2</span> Ersthelfer vor Ort</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['6.2 Ersthelfer vor Ort'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['6.2 Ersthelfer vor Ort'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['6.2 Ersthelfer vor Ort'];?></td>
		</tr>
		<tr>
			<td><span class="orange">6.3</span> Feuerlöscher vor Ort</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['6.3 Feuerlöscher vor Ort'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['6.3 Feuerlöscher vor Ort'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['6.3 Feuerlöscher vor Ort'];?></td>
		</tr>
		<tr>
			<td><span class="orange">6.4</span> Notfallplan (Notruf-Nr., Vorgehensweise etc.) vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['6.4 Notfallplan (Notruf-Nr., Vorgehensweise etc.) vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['6.4 Notfallplan (Notruf-Nr., Vorgehensweise etc.) vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['6.4 Notfallplan (Notruf-Nr., Vorgehensweise etc.) vorhanden'];?></td>
		</tr>
		<tr class="bordered">
			<td>7) Montagebedingungen</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">7.1</span> Montage nach Regelausführung bzw. Aufbau- und Verwendungsan-leitung des Herstellers</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['7.1 Montage nach Regelausführung bzw. Aufbau- und Verwendungsanleitung des Herstellers'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['7.1 Montage nach Regelausführung bzw. Aufbau- und Verwendungsanleitung des Herstellers'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['7.1 Montage nach Regelausführung bzw. Aufbau- und Verwendungsanleitung des Herstellers'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Abweichung der Regelausführung notwendig</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Abweichung der Regelausführung notwendig'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Abweichung der Regelausführung notwendig'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Abweichung der Regelausführung notwendig'];?></td>
		</tr>
		<tr>
			<td><?=str_repeat('&nbsp;', 5);?><span class="orange dot">&sdot;</span>Rechnerischer Nachweis erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['•Rechnerischer Nachweis erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['•Rechnerischer Nachweis erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['•Rechnerischer Nachweis erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">7.2</span> Einsatz von Montagesicherungsgeländer (MSG) erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['7.2 Einsatz von Montagesicherungsgeländer (MSG) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['7.2 Einsatz von Montagesicherungsgeländer (MSG) erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['7.2 Einsatz von Montagesicherungsgeländer (MSG) erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">7.3</span> Einsatz von zusätzlicher PSA gegen Absturz erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['7.3 Einsatz von zusätzlicher PSA gegen Absturz erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['7.3 Einsatz von zusätzlicher PSA gegen Absturz erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['7.3 Einsatz von zusätzlicher PSA gegen Absturz erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">7.4</span> Einsatz von Warnkleidung erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['7.4 Einsatz von Warnkleidung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['7.4 Einsatz von Warnkleidung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['7.4 Einsatz von Warnkleidung erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">7.5</span> Einsatz von Wetterschutzkleidung erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['7.5 Einsatz von Wetterschutzkleidung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['7.5 Einsatz von Wetterschutzkleidung erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['7.5 Einsatz von Wetterschutzkleidung erforderlich'];?></td>
		</tr>
		<tr>
			<td><span class="orange">7.6</span> Sonstige PSA z.B. Overall, Staubmaske etc. erforderlich</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['7.6 Sonstige PSA z.B. Overall, Staubmaske etc. erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['7.6 Sonstige PSA z.B. Overall, Staubmaske etc. erforderlich'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['7.6 Sonstige PSA z.B. Overall, Staubmaske etc. erforderlich'];?></td>
		</tr>
		<tr class="bordered">
			<td>8)  Handmaschinen, Werkzeuge, Zubehör</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">8.1</span> Elektr. Maschinen augenscheinlich auf Mängel geprüft</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['8.1 Elektr. Maschinen augenscheinlich auf Mängel geprüft'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['8.1 Elektr. Maschinen augenscheinlich auf Mängel geprüft'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['8.1 Elektr. Maschinen augenscheinlich auf Mängel geprüft'];?></td>
		</tr>
		<tr>
			<td><span class="orange">8.2</span> Elektr. Leitungen augenscheinlich auf Mängel geprüft</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['8.2 Elektr. Leitungen augenscheinlich auf Mängel geprüft'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['8.2 Elektr. Leitungen augenscheinlich auf Mängel geprüft'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['8.2 Elektr. Leitungen augenscheinlich auf Mängel geprüft'];?></td>
		</tr>
		<tr>
			<td><span class="orange">8.3</span> Werkzeuge augenscheinlich auf Mängel geprüft</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['8.3 Werkzeuge augenscheinlich auf Mängel geprüft'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['8.3 Werkzeuge augenscheinlich auf Mängel geprüft'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['8.3 Werkzeuge augenscheinlich auf Mängel geprüft'];?></td>
		</tr>
		<tr>
			<td><span class="orange">8.4</span> Kabeltrommel (entspr. isoliert bzw. aus Kunststoff) und Stromleit-ungen (mind. HO7…… oder mehr) entsprechen der BGI 608</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['8.4 Kabeltrommel (entspr. isoliert bzw. aus Kunststoff) und Stromleitungen (mind. HO7…… oder mehr) entsprechen der BGI 608'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['8.4 Kabeltrommel (entspr. isoliert bzw. aus Kunststoff) und Stromleitungen (mind. HO7…… oder mehr) entsprechen der BGI 608'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['8.4 Kabeltrommel (entspr. isoliert bzw. aus Kunststoff) und Stromleitungen (mind. HO7…… oder mehr) entsprechen der BGI 608'];?></td>
		</tr>
		<tr class="bordered">
			<td>9) Personal</td>
			<td align="center">ja</td>
			<td align="center">nein</td>
			<td>Bemerkung / Maßnahmen</td>
		</tr>
		<tr>
			<td><span class="orange">9.1</span> Qualifiziertes Personal für die Arbeitsaufgabe vorhanden</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['9.1 Qualifiziertes Personal für die Arbeitsaufgabe vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['9.1 Qualifiziertes Personal für die Arbeitsaufgabe vorhanden'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['9.1 Qualifiziertes Personal für die Arbeitsaufgabe vorhanden'];?></td>
		</tr>
		<tr>
			<td><span class="orange">9.2</span> Personalstärke für die Arbeitsaufgabe ausreichend</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['9.2 Personalstärke für die Arbeitsaufgabe ausreichend'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['9.2 Personalstärke für die Arbeitsaufgabe ausreichend'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['9.2 Personalstärke für die Arbeitsaufgabe ausreichend'];?></td>
		</tr>
		<tr>
			<td><span class="orange">9.3</span> Zeitvorgaben (Vorplanung) für die Arbeitsaufgabe realistisch</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['9.3 Zeitvorgaben (Vorplanung) für die Arbeitsaufgabe realistisch'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['9.3 Zeitvorgaben (Vorplanung) für die Arbeitsaufgabe realistisch'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['9.3 Zeitvorgaben (Vorplanung) für die Arbeitsaufgabe realistisch'];?></td>
		</tr>
		<tr>
			<td><span class="orange">9.4</span> Mitarbeiter mit besonderer Fürsorgepflicht vor Ort(Auszubildende, Praktikanten, neue Kollegen im Betrieb etc.)</td>
			<td align="center">
			  	<?php if(isChecked($data['checkboxes']['9.4 Mitarbeiter mit besonderer Fürsorgepflicht vor Ort (Auszubildende, Praktikanten, neue Kollegen im Betrieb etc.)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php }else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  <?php	} ?>
			</td>
			<td align="center">
			  	<?php if(isCheckedNo($data['checkboxes']['9.4 Mitarbeiter mit besonderer Fürsorgepflicht vor Ort (Auszubildende, Praktikanten, neue Kollegen im Betrieb etc.)'])) { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\checked2.jpg'?>" alt="checked">
			  	<?php } else { ?>
			  		<img src="<?=WEB_ROOT_API.'vendor\printouts\notChecked2.jpg'?>" alt="notChecked">
			  	<?php } ?>
			</td>
			<td><?=$data['notes']['9.4 Mitarbeiter mit besonderer Fürsorgepflicht vor Ort (Auszubildende, Praktikanten, neue Kollegen im Betrieb etc.)'];?></td>
		</tr>
		<tr>
			<td colspan="4" class="bordered">10) Sonstiges</td>
		</tr>
		<tr>
			<td colspan="4"></td>
		</tr>
		<tr>
			<td colspan="4" class="bordered">
				Die Angaben in der Revisionsliste entsprechen den Tatsachen, sie wurden nach bestem Wissen und Gewissen von dem Auftragsverantwortlichen vor Ort (AvO) 
				durchgeführt. Anhand der Ergebnisse konnten die erforderlichen Maßnahmen eingeleitet, die Mitarbeiter vor Ort eingewiesen und auf explizite Gefahrenpunkte 
				hingewiesen werden.
			</td>
		</tr>
	</table>
	<table class="footer">
		<tr>
			<th><?=$data['Ort'];?></th>
			<th><?=date('d.m.Y',strtotime($data['Datum']));?></th>
			<th></th>
			<th><img src="<?=$data['Unterschrift'];?>" alt="Signature"></th>
		</tr>
		<tr class="bordered">
			<td>Ort</td>
			<td>Datum</td>
			<td>Uhrzeit</td>
			<td>Unterschrift des Auftragsverantwortlichen vor Ort (AvO)</td>
		</tr>
	</table>
	</body>
</html>

<style>
	.headerTable,th,td {width:100%; border: 1px solid black;}
	.checkboxes {border: 1px solid black;}
	.bordered{font-weight:bold; background-color:lightgray}
	th.firstColumn {width: 50%;}
	th.secondColumn {width: 8%;}
	th.thirdColumn {width: 8%;}
	th.fourthColumn {width: 34%;}
	.checkboxes, .headerTable {table-layout:fixed;}
	
	th.firstHeader {width: 45%;}
	th.secondHeader {width: 35%;}
	th.thirdHeader {width: 30%;}
	.companyLogo {padding: 10px; width:70%; height:auto;}
	.footer {border-collapse: collapse;}
	.orange {color: #FF6600;}
	.bigFont {font-size: xx-large;}
	.dot{font-size: 30px;}	
</style>
