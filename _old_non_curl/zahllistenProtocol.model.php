<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'WorkingOrders.php';

class C_Zahllisten {
private $db;
private $documentation;
private $documentationModel;
private $query;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->aauftrag = new AauftragDB();
		$this->documentationModel = new DocumentationModel();
		$this->query = '';
	}
	
	public function getWoData($ktr, $aanr) {
		$data = [];
		
		$woData = $this->aauftrag->select_aauftrag($ktr, $aanr);
		$data['projectName'] = $woData['projectName'];
		$data['aanr'] = $aanr;
		$data['woName'] = $woData['task_selection'];
		return $data;
	}
	
	public function getData($schemaId, $ktr,$aanr) {
		
		if($aanr !== 0) {
			$documentData = $this->documentation->getDocumentationByKey('workingOrder', $ktr, $aanr, $schemaId);
		} else {
			$documentData = $this->documentation->getDocumentationByKey('project', $ktr, null, $schemaId);
		}
		
		if($documentData) {
			$projectData = $this->getWoData($ktr, $aanr);
			$projectData['externalId'] = $this->getExternalId($ktr);
			$pnr = $this->getCustomerNumber($ktr);
			$projectData['customerData'] = $this->getCustomerData($pnr);

			if(!$projectData['projectName'] && $target = getTunnelingTarget('GET', 'projects/select/'.$ktr.'/2020'))
			{
				$project = getDataFromTunnel($target);
				$projectData['projectName'] = $project->project_name;
				$projectData['externalId'] = $project->externalProjectNo;
				$projectData['customerData']['name'] = $project->businessUnit;
			}

			$schemaData = $this->documentation->getSchemasPosById($schemaId);
			
			foreach ($schemaData['children'] as $index => $child) {
				if ($child['indentationLevel'] == 1 && $child['type'] == 'headline') {
					$documentData['suppliers'][$child['id']] = $child['title'];
				}
				if ($child['indentationLevel'] == 2 && $child['type'] == 'headline') {
					$documentData['headlines'][$child['id']] = $child;
				}
			}
			
			$data = $this->combineData($documentData, $projectData, $schemaId);
			
			//parent children documents array
			$parents = [];
			
			foreach ($data['documents'] as $k=>$doc) {
				if(isset($doc['originDocumentId'])) {
					array_push($parents, $doc['originDocumentId']);
				}
			}
			$parents = array_unique($parents);
			
			$data['parents'] = $parents;
			
			return $data;
		} else {
			throw new RestException(400, "No such document id!");
		}
	
	}
	
	private function combineData ($documentData, $projectData, $schemaId) {
		
		$templateData = [];
		$serials = [];
		
		foreach ($documentData as $index=>$doc) {
				foreach ($doc as $key=>$value) {
					if ($key == 'children') {
						foreach ($value as $number=>$child) {
							if($child['title'] !== 'Seriennummer' && $child['title'] !== 'Foto Seriennummer') {
								if($child['type'] === 'formula') {
									$templateData['documents'][$index][$child['id']] = eval('return '.$child['reportedValues'][0].';');
								} else {
									if(isset($child['reportedValue']) && ($child['type'] === 'photo' || $child['type'] === 'signatureField') ) {
										$templateData['employeesAndImages'][$index][$child['title']] = $child['filePath'];
									} else if (isset($child['reportedValue']) && ($child['title'] === 'Name Zähler' || $child['title'] === 'Name Aufschreiber')) {
										$templateData['employeesAndImages'][$index][$child['title']] = $child['reportedValue'];
									} else if ($child['title'] == 'Beschreibung der Seite/Lage') {
										$templateData['description'][$index] = $child['reportedValue'].'-'.$doc['documentRelKey2'];
									}else {
										$templateData['documents'][$index][$child['id']] = $child['reportedValue'];
									}
								}
							} else {
									$serials[$doc['documentId']][$child['parentId']][$child['title']] = ($child['type'] == 'string') ? $child['reportedValue'] : $child['filePath'];						 								 
							}
						}
					}
					
					if($key == 'localId') {
						$templateData['documents'][$index]['localId'] = $documentData[$index]['localId'];
					}
					elseif($key == 'id') {
						$templateData['documents'][$index]['id'] = $documentData[$index]['id'];
					}
					elseif($key == 'documentId') {
						$templateData['documents'][$index]['documentId'] = $documentData[$index]['documentId'];
					}
					elseif($key == 'originDocumentId') {
						$templateData['documents'][$index]['originDocumentId'] = $documentData[$index]['originDocumentId'];
					}
				}	
		}
		
		$templateData['headlines'] = $documentData['headlines'];
		$templateData['formulas'] = $this->documentationModel->getNonHeadlines($schemaId);
	    
	    foreach ($templateData['formulas'] as $k=>$v) {
			$splitted = explode(':',$v['title']);
			$templateData['formulas'][$k]['title'] = $splitted[1] ? trim($splitted[1]) : '';
			$templateData['formulas'][$k]['positionNumber'] = $splitted[0] ? trim($splitted[0]) : '';
		}
		
		foreach ($documentData['headlines'] as $i => $headline) {
			foreach ($serials as $docNumber => $serial) {
				foreach ($serial as $headlineId => $ser) {
					if($headlineId == $headline['id']) {
				    	$templateData['serials'][$docNumber][$headline['title']] = $ser;
					}
				}
			}
		}
		
		$templateData['projectData']['projectName'] = $projectData['projectName'];
		$templateData['projectData']['aanr'] = $projectData['aanr'];
		$templateData['projectData']['woName'] = $projectData['woName'];
		$templateData['schemaData']['title'] = $documentData[0]['title'];
		$templateData['schemaData']['date'] = date("d.m.Y", strtotime($documentData[0]['documentCreatedOn'])); 
		$templateData['schemaData']['documentId'] = $documentData[0]['documentId']; 
		$templateData['projectData']['externalId'] = $projectData['externalId'];
		$templateData['customerData']['name'] = $projectData['customerData']['name'];
		$templateData['customerData']['address'] = $projectData['customerData']['adresse1'];
		$templateData['customerData']['city'] = $projectData['customerData']['ort'];
		$templateData['customerData']['postCode'] = $projectData['customerData']['plz'];
		$templateData['suppliers'] = $documentData['suppliers'];
		
		return $templateData;	
		
	}
	
	private function getExternalId($ktr) {
		
		$stmt = $this->db->prepare('SELECT externalProjectNo FROM ktrExtension where ktrId = :ktr');
		$stmt->execute(['ktr' => $ktr]);	
		$externalId = $stmt->fetch();
		return $externalId[0];
	}
	
	private function getCustomerData($knr) {
		$stmt = $this->db->prepare("
			SELECT name, plz,telefon, telefax, ort, adresse1 from kunden where knr = :pnr
		");
		$stmt->execute(['pnr' => $knr]);
		
		$data = $stmt->fetchAll();
	
		return $data[0];
	}
	
	private function getCustomerNumber($ktr) {
		$stmt = $this->db->prepare("
			SELECT knr from ktr where ktr = :ktr
		");
		$stmt->execute(['ktr' => $ktr]);
		
		$data = $stmt->fetchAll();
	
		return $data[0][0];
	}
	
	
	
	private function getAllSerials ($schemaId) {
		$stmt = $this->db->prepare("
			SELECT ddp.documentationId, dsp.id, dsp.parentId,  dsp.title, dsp2.parentId as supplierId, ddp.value 
			FROM documentation_schema_positions  AS dsp
			LEFT JOIN documentation_schema_positions AS dsp2
			ON dsp.parentId = dsp2.id
			LEFT JOIN documentation_document_positions AS ddp
			ON dsp.id = ddp.schemaPositionId
			WHERE dsp.schemaId = :schemaId AND dsp.title IN ('Foto Seriennummer', 'Seriennummer')
			AND ddp.value IS NOT NULL
			ORDER BY ddp.documentationId ASC
		");
		
		$stmt->setFetchMode(PDO::FETCH_ASSOC);
		$stmt->execute(['schemaId' => $schemaId]);
		$data = $stmt->fetchAll();
		return $data;
	}
}