<?php

class C_PrintTest {

	private $db;

	public function __construct() {

		$this->db = CommonData::getDbConnection();
	}

	public function processData(I_PrintTest &$data) {

		$stmt = $this->db->prepare("
			SELECT name FROM personal WHERE pnr = :pnr
		");
		$stmt->execute(['pnr' => $data->fromBody]);

		$stmt->bindColumn(1, $data->fromDB);

		if (!$stmt->fetch(PDO::FETCH_BOUND)) {
			$data->fromDB = "No person with id = '{$data->fromBody}'";
		}
	}
}

class I_PrintTest extends IO_Doc {
	/**
	 * @var string Takes that field from the JSON body {@required false} */
	public $fromBody;
	/**
	 * @var string Takes that field from the Database (based on $fromBody field) {@required false} */
	public $fromDB;
}