<?php
require_once ABS_PATH.'Documentation.php';

class PORTED_C_Abfahrtskontrolle {
    private $db;
    private $documentation;

    public function __construct() {
        $this->db = CommonData::getDbConnection();
        $this->documentation = new Documentation();
        $this->settingsAPI = new Settings();
    }



    public function getData($schemaId,$documentId) {
        $documentData = $this->documentation->getDocumentId($schemaId,$documentId);

        $tempateData = [];

        if($documentData) {
            foreach ($documentData['children'] as $k=>$v) {
                if($v['type'] == 'date') {
                    $tempateData[$v['title']] = date('d.m.Y',strtotime($v['reportedValue']));
                } else if ($v['type'] == 'signatureField') {
                    $tempateData[$v['title']] = $v['filePath'];
                } else {
                    //checkboxes and strings in this case
                    $tempateData[$v['title']] = $v['reportedValue'];
                }
            }
        } else {
            throw new RestException(400, "No such document and schema or no data for this document!");
        }

        return $tempateData;
    }
}
