<html>
<?php
	$data = $this->data;
	$children = $data['document']['children'];
?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Tagesbericht / Bautagebuch</title>
	</head>
	<body>
	<div id="wrapper">
		<table class="leftTable">
			<tr>
				<td colspan="2" style="font-size: 14pt">
					<div align="center">Gefährdungsbeurteilung und Wirksamkeitskontrolle</div>
					<div align="center">nach §§ 5, 6 ArbSchG und § 3 BetrSichV</div>
				</td>
			</tr>
            <!-- Table Head-->
				<tr>
					<td width="50%"><img src="<?=WEB_ROOT_API.'vendor\printouts\handerGerustbauLogo.jpg'?>" alt="HanderGerustbauLogo"></td>
					<td class="topAligned">
						<div>Ersteller:
							<?= $data['author']['displayName']?>
						</div>
						<br>
						<div>Datum:
							<?php
								$date = DateTime::createFromFormat('Y-m-d\TH:i:s+',$data['document']['documentCreatedOn']);
								echo $date->format('d.m.Y')
							?>
						</div>
						<br>
						<div>Baustelle:
							<?php
								echo $data['project']['project_name'];
							?>
						</div>
						<br>
						<div>Technische Leitung:
							<?php
								if($data['projectManager'])
									echo $data['projectManager']['displayName'];
								else
									echo "nicht definiert";
							?>
						</div>
						<br>
                        <div>Projektnummer:
							<?php
							if($data['project'])
								echo $data['project']['externalProjectNo'];
							else
								echo "nicht definiert";
							?>
                        </div>
						<br>
						<div>Checkliste:
							<?php
							if($data['document'])
								echo '#'.$data['document']['documentId'];
							else
								echo "nicht definiert";
							?>
						</div>
					</td>
				</tr>
		</table>
        <br>
		<table class="leftTable">
			<?php
			function generateTableHead()
			{
				echo '<thead style="display: table-header-group">
				<tr>
					<td width="23%" rowspan="2"><b>Gefährdung</b></td>
					<td width="10%">
						<div><b>Risiko</b></div>
					</td>
					<td width="10%" colspan="2">
						<div><b>Handlungs-<br>bedarf</b></div>
					</td>
					<td width="35%" rowspan="2"><b>Maßnahmen</b></td>
					 <td width="22%">
						 <div><b>Wirksamkeitskontrolle:</b></div>
					 </td>
				</tr>
				<tr>
					<td>
						<div><b>Hoch</b></div>
						<div><b>Mittel</b></div>
						<div><b>Niedrig</b></div>
					</td>
					<td>
						<div><b>BL</b></div>
					</td>
					<td>
						<div><b>MA</b></div>
					</td>
					<td>
						<div><b>Datum, Unterschrift</b></div>
					</td>
				</tr>
			</thead>';
			}

			generateTableHead();
			?>
			<!-- End of Table Head -->
			<tbody>
			<?php
			/**
			 * @param $value
			 * @param $cell
			 */
			function drawCheckbox($value, $cell, $indent = 0, $showTitleForCheckbox = false)
			{
			    // generate indent
				for ($i = 0; $i < $indent; $i++) {
					echo "&emsp;";
				}
				if (in_array($value, $cell['reportedValues']))
                {
                    // add color-coding for risk (Risiko)
                	if($cell['title'] == 'Risiko')
					{
                		$backgroundColor = '';
                		switch ($value){
							case 'Hoch': $backgroundColor = 'red';break;
							case 'Mittel': $backgroundColor = 'orange';break;
							case 'Niedrig': $backgroundColor = 'green';break;
						}
                		echo "<span style='background-color: {$backgroundColor}; display: inline-block; border-radius: 50%'>&#9745;</span> ";
					}
					else
						echo "&#9745; ";
				}
				else
					echo "&#9744; ";
                if($cell['type'] != 'checkbox')
					echo $value;
                if($showTitleForCheckbox)
                    echo "<b>".$cell['title']."</b>";
				echo "<br>";
			}

			/**
			 * @param $cell
			 * @param $row
             * @param $intend
			 * @param bool $showTitle
			 */
			function generateCell($cell, $row, $intend = 0, $showTitle = false)
			{
				// show printText if existing
				if($cell['printText'])
					echo $cell['printText']."<br><br>";

                // draw boxes
				if ($cell['type'] == 'combobox' || $cell['type'] == 'combobox-multi') {
				    if($showTitle)
				        echo $cell['title']."<br>";
					foreach ($cell['values'] as $value) {
						drawCheckbox($value['value'], $cell,$intend);
					}
				} elseif ($cell['type'] == 'checkbox') {
					drawCheckbox('on', $cell,$intend,$showTitle);
				} elseif ($cell['type'] == 'date' && $cell['reportedValues'][0]) {
                    $dateTime = DateTime::createFromFormat('Y-m-d\TH:i:s+',$cell['reportedValues'][0]);
                    echo $dateTime->format('d.m.Y');
                } elseif($cell['type'] == 'signatureField' && $cell['reportedValues'][0]) {
                    echo "<img style='max-height:70px; object-fit:maintain' src='".$cell['reportedValues'][0]."'>";
                }
				else
					echo implode(',', $row['reportedValues']);
			}

			/**
			 * @param $headline
			 */
			// TODO unify with KSBG
			function generateSignatureRow($location,$date,$signature)
			{
			    if($date)
                {
                    $dateTime = DateTime::createFromFormat('Y-m-d\TH:i:s+',$date);
                    $date = $dateTime->format('d.m.Y');
                }

				echo "<tr>";
					echo "<td colspan='3' style='border: 0'>";
				    	echo $location . ($location? ", " : '') . $date;
					echo "</td>";
					echo "<td style='border: 0'></td>";
					echo "<td colspan='2' style='border: 0'>";
				    	echo "<img style='max-height:150px; object-fit:maintain' src='".$signature['reportedValues'][0]."'>";
					echo "</td>";
				echo "</tr>";
				echo "<tr>";
					echo "<td colspan='3' style='border-bottom: 0px;border-left:0px;border-right: 0px'>";
						echo "Ort, Datum";
					echo "</td>";
					echo "<td style='border: 0'></td>";
					echo "<td colspan='2' style='border-bottom: 0px;border-left:0px;border-right: 0px'>";
						echo $signature['title'];
					echo "</td>";
				echo "</tr>";
			}

			foreach ($children as $headline) {
				// special treatment of last row
				if($headline['title'] == 'Projektbezogene Gefährdungsbeurteilung in Kraft gesetzt und bekannt gegeben:')
				{
				    ?>
                <!-- finish main table first -->
                </table>
                <br><br>
                <!-- create own table -->
                <table>
					<tr >
						<td colspan="6" style="border: 0; border-left: 0px;border-right: 0px;border-bottom: 0px">
							<br><br><br>
							Bemerkung:<br>
							Gefährdungsbeurteilung gegebenenfalls bei späterer Veränderung der Gefahrenquellen oder des Gefährdungspotenzials an die jeweiligen Gegebenheiten anpassen.<br><br>
							Projektbezogene Gefährdungsbeurteilung in Kraft gesetzt und bekannt gegeben:<br>
						</td>
					</tr>

				<?php
                    $firstKey = array_keys($headline['children'])[0];
					generateSignatureRow($headline['children'][$firstKey]['reportedValues'][0],$headline['children'][$firstKey+1]['reportedValues'][0],$headline['children'][$firstKey+2]);
					// just the last indice for the other signature differs here
					generateSignatureRow($headline['children'][$firstKey]['reportedValues'][0],$headline['children'][$firstKey+1]['reportedValues'][0],$headline['children'][$firstKey+3]);

				}
			    elseif($headline['type'] == 'headline' && !$headline['parentId'])
			    {
					echo '<tr><td colspan="6" class="grey"><b>'.$headline['title'].'</b></td></tr>';

                    	foreach($headline['children'] as $row) {
							echo "<tr>";
							// special treatment of that very headline, due to free-text input, hence we skip the title to not break the layout
							if($headline['title'] != 'Weitere Gefährdungen und Maßnahmen') {
								echo "<td>".$row['title']."</td>";
							foreach($row['children'] as $cell)
								if(!$cell['children']) {
									// special handling of Sonstiges, it should be appended to the "Maßnahmen"-cell
                                    // only open cell if its not "Sonstiges"
                                    if($cell['title'] != 'Sonstiges')
                                	    echo "<td>";

									generateCell($cell, $row);

									// only close cell if the value-array does not contain "Sonstiges", otherwise it is closed in the next if
                                    if(is_array($cell['value']) && !in_array('Sonstiges',$cell['value']))
										echo "</td>";
                                    // add content of "Sonstiges" and close cell
                                    if($cell['title'] == 'Sonstiges')
									{
										if($cell['reportedValues'])
											echo $cell['reportedValues'][0];
										echo "</td>";
									}
								} else {
									echo "<td>";
									foreach ($cell['children'] as $subCell) {
										// calc indent
										$indent = $subCell['type'] == 'checkbox' ? 0 : 1;
										generateCell($subCell,$cell,$indent,true);
									}
									echo "</td>";
								}
								if(count($row['children']) < 5) {
									//this is the case when the count of the children is less and a table cell/s is missing 
									echo str_repeat('<td></td>', 5-count($row['children']));
								}
							echo "</tr>";
							} else {
								//$headline['title'] == 'Weitere Gefährdungen und Maßnahmen') 
									foreach ($row['children'] as $kk => $vv) {
										if($vv['title'] == 'Gefährdung') {
											$subtitle = $vv['reportedValues'][0];
										}
									}
								echo "<td>".$row['title'].$subtitle."</td>";
								
								foreach($row['children'] as $cell) {
									if(!$cell['children']) {
										if($cell['title'] != 'Gefährdung') {
											echo "<td>";
											generateCell($cell, $row);
											// only close cell if the value-array does not contain "Sonstiges", otherwise it is closed in the next if
											if(is_array($cell['value']) && !in_array('Sonstiges',$cell['value']))
												echo "</td>";   
										}                           
									} 
								}	
								echo "</tr>";
							}							
						}
			    	}
				}
			?>
			</tbody>
		 </table>
	</div>	
		
</body>
</html>


<style>
    body {
		font-family: Arial;
	}
	table.leftTable {
		position:center;
		float:left;
		border:1px solid black;
		border-collapse: collapse;
		width:100%;
		font-size: 11pt;
   }
  
  .leftTable , th, td {
   		border:1px solid black;
   }
   
   .rightTable , th, td {
   		border:1px solid black;
   		vertical-align:text-top;
   }
   
   .grey {background-color:lightgray;}
   
	#wrapper {
		width:100%;
		height:auto%; 
	}
	
	table { 
    	table-layout:fixed;
	}

    tr{
        page-break-inside: avoid;
    }

   	img {
		width: 100%;
		height: auto;
	}
   	
   .centerAligned {vertical-align: middle !important;}
   .topAligned {vertical-align:top !important;}
</style>
