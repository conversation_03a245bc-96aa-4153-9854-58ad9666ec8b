<?php
	require_once ABS_PATH.'Hours.php';
	require_once ABS_PATH.'Documentation.php';
	require_once ABS_PATH.'Addresses.php';
	
class C_Auftragszettel {
	private $db;
	private $hours;
	private $documentation;
	private $addresses;
	
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->hours = new Hours();
		$this->documentation = new Documentation();
		$this->addresses = new Addresses();
	}
	
	public function getData($schemaId, $documentId) {
		
		$templateData = [];
		$existingSchema = $this->checkDocumentExistence($schemaId, $documentId);

		// fetch document
		$documentData = $this->documentation->getDocumentId($schemaId, $documentId);
		if($documentData) {
			$ktr = $documentData['documentRelKey1'];
			$aanr = $documentData['documentRelKey2'];
			if($documentData['children']) {
				foreach ($documentData['children'] as $k=>$v) {
						if ($v['title'] == 'Diagnose / Fehlerbeschreibung' || $v['title'] == 'Arbeitsbericht') {
							$templateData['notes'] = $v['reportedValue'];
						}

						if($v['title'] == 'Unterschrift Kunde') {
							$templateData['signatures']['customerSignaturePath'] = $v['filePath'];
						}

						if($v['title'] == 'Unterschrift Monteur') {
							$templateData['signatures']['monteurSignaturePath'] = $v['filePath'];
						}

						if($v['title'] == 'Folgearbeiten erforderlich') {
							$templateData['folgearbeiten'] = $v['reportedValue'];
						}
				}
			}
		} else {
			throw new RestException(400, "No project or working order found for this shchema and document id!");
		}
				

		//employees API tunneling
		if ($target = getTunnelingTarget('GET', 'v3/employees')) {
			$employees = (array)getDataFromTunnel($target);
			$pnrsString = '';
			if(!empty($employees)) {
				foreach ($employees as $k=>$v) {
					$pnrsString.= $v->employeeNo.',';
				}
				$pnrsString = rtrim($pnrsString, ',');
			}
		}
		if($pnrsString) {
			//add hardcoded params to check fetching hours
			$hoursData = $this->hours->getall($pnrsString, null,null,'',(string)$ktr, $aanr,'mixed', 0, '','','','');
		}  else {
			$hoursData = [];
		}
					
		//working order  API call tunneling
		if ($target = getTunnelingTarget('GET', 'workingorders/select/'.$ktr.'/'.$aanr)) {
			$woData = (array)getDataFromTunnel($target);

			$templateData['woData'] = (array)$woData;
			$knr = $templateData['woData']['knr'];
			$templateData['drivers'] = [];
			$templateData['teamData'] = [];
				if (empty($woData['staff_preplanned'])) {
					$templateData['teamData'] = [];
				} else {
					foreach ($employees as $index=>$employee) {
						$employeeNo = $employee->employeeNo;
						// skip employee if not preplanned
						if(!in_array($employeeNo,$woData['staff_preplanned']))
							continue;

						$templateData['teamData'][$employeeNo]['fullName'] = $employee->displayName;
						foreach ($hoursData as $k => $hour) {
							if($employee->employeeNo == $hour['pnr']) {
								$templateData['teamData'][$employeeNo]['fullName'] = $employee->displayName;
								$templateData['teamData'][$employeeNo]['startTime'] = $hour['start'];
								$templateData['teamData'][$employeeNo]['endTime'] = $hour['end'];
								$templateData['teamData'][$employeeNo]['hours'] = $hour['hours'];
								if(isset($hour['plannedDriver']) && !is_null($hour['plannedDriver'])) {
									array_push($templateData['drivers'], $hour['pnr']);
								}
							}

						}
					}
				}
		}

		// customer API call tunneling
		if ($target = getTunnelingTarget('GET', 'addresses/'.$knr)) {
			$customerData = (array)getDataFromTunnel($target);
		}

		$templateData['customerData'] = [];
		$templateData['customerData']['displayName'] = $customerData['displayName'];
		$templateData['customerData']['name'] = $templateData['woData']['bvap'];
		$templateData['customerData']['telefon'] = $templateData['woData']['bvtelefon'];
		$templateData['customerData']['phoneNumber'] = $customerData['phoneNumber'];
		$templateData['customerData']['mobile'] = $customerData['mobile'];
		$templateData['customerData']['customerNumber'] = $knr;
		$templateData['customerData']['fullAddress'] = '';

		if($customerData['address']) {
			$templateData['customerData']['fullAddress'] .= $customerData['address'].', ';
		}

		if($customerData['postCode']) {
			$templateData['customerData']['fullAddress'] .= $customerData['postCode'].' ';
		}

		if($customerData['city']) {
			$templateData['customerData']['fullAddress'] .= $customerData['city'];
		}
		rtrim($templateData['customerData']['fullAddress'], ',');

		$templateData['customerData']['name'] = $customerData['name'];

		// fetch existing document for comparison in case of Reperaturauftrag Heizung v2 (schemaId 5)
		if($schemaId == 5)
		{
			if($target = getTunnelingTarget('GET','documentation/address/'.$knr))
			{
				$existingDocumentData = getDataFromTunnel($target);
				$arrayIdOfAnlagenDaten = null;

				$anlagenDaten = array();
				// map data into documentDatas children
				if ($existingDocumentData) {
					foreach ($existingDocumentData->children as $existingChild) {
						foreach ($documentData['children'] as $key => $newChild) {
							if ($newChild['id'] == $existingChild->id) {
								// copy new value
								$existingChild->newReportedValue = $newChild['reportedValue'];
							}
						}
						$anlagenDaten[] = $existingChild;
					}
					$templateData['Anlagendaten'] = $anlagenDaten;
				}
			}
		}

		//oversizes API tunneling
 		if ($target = getTunnelingTarget('GET', 'oversizes/items/'.$ktr.'/'.$aanr)) {
			 $oversizes = getDataFromTunnel($target);
			 // wrap $oversizes into an array if its only one result
			 if(is_object($oversizes))
			 {
			 	$oversizesArray[] = $oversizes;
			 	$oversizes = $oversizesArray;
			 }
			 $templateData['oversizes'] = $oversizes;
		}

		// populate projectAddress from WO Data
		$templateData['projectAddress'] = '';
		
		if($woData['bvname']) {
			$templateData['projectAddress'] .= $woData['bvname'].', ';
		}
		
		if($woData['bvplz']) {
			$templateData['projectAddress'] .= $woData['bvplz'].' ';
		}

		if($woData['bvort']) {
			$templateData['projectAddress'] .= $woData['bvort'].', ';
		}
		rtrim($templateData['projectAddress'], ',');
		$woData['bvname'].', '.$woData['bvplz'].', '.$woData['bvort'];	
		
		return $templateData;
	}
	
	private function checkDocumentExistence ($schemaId, $documentId) {
		$stmt = $this->db->prepare("
			SELECT * FROM documentation_document WHERE id = :documentId and schemaId = :schemaId
		");
		$stmt->execute(['documentId' => $documentId, 'schemaId'=> $schemaId]);
		
		$data = $stmt->fetchAll();
		
		return $data;
	}
}
