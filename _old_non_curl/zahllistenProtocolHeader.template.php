<!DOCTYPE HTML>
<?php $headerData = $this->data['customerData'];?> 
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Protokoll für Bautagebuch</title>
        <!-- taken from https://wkhtmltopdf.org/usage/wkhtmltopdf.txt -->
	</head>
    <body>
		<table class="separateHeader">
			<tr>
				<th>
					<?=trim($headerData['name'].', ');?>
					<?=trim($headerData['address'].', ');?>
					<?=trim($headerData['postCode'].' ');?>
					<?=trim($headerData['city']);?>
				</th>
			</tr>
		</table>
	</body>
</html>
	
<style>
	.separateHeader {background-color: yellow; width:100%;}
</style>