<html>
<?php $data = $this->data; ?>
<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php echo lang('print') ?></title>

</head>
<body class="smooth-css">

<div class="container">
    <div class="row header">
        <div class="col-xs-2 logo-panel M-T20 inline">
            <img src="<?=WEB_ROOT_API.$data['logoUrl'];?>" alt="companyLogo" id="principal_logo_img">
        </div>
        <div class="col-xs-3 M-T20 header_date inline">
            <div class="inner">
                <div class="P-10 border-bottom"><?php echo lang('day') . ': ' . lang('cal_' . strtolower(date('l', strtotime($data['hours'][0]['date'])))) ?></div>
                <div class="P-10 date"><?php echo lang('date') . ': ' . date("d.m.Y", strtotime($data['hours'][0]['date'])) ?></div>
            </div>
        </div>
        <div class="col-xs-4 main_info text-bold text-right inline">
            <div class="text-bold"><?php echo lang('site_journal') . '/' . lang('performance_message') ?></div>
            <div class="ktr-aanr text-bold"><?php echo $data['hours'][0]['ktr'] . '-' . $data['hours'][0]['aanr'] ?></div>
            <div><?php echo lang('costs') ?></div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-5 column-left inline">
            <div class="row">
                <div class="col-xs-12 P-10 border-all">

                    <div class="text-bold"><?php echo lang('customer_name') ?></div>
                    <div><?php echo $data['customer_data']['displayName'] ? $data['customer_data']['displayName'] : '' ?></div>
                    <div>
						<?php echo $data['customer_data']['address'] ? $data['customer_data']['address'] : '&nbsp' ?></br>
						<?php echo $data['customer_data']['postcode'] ? $data['customer_data']['postcode'] : '&nbsp' ?>
						<?php echo $data['customer_data']['city'] ? $data['customer_data']['city'] : '&nbsp' ?>
                    </div>
                    <div class="text-bold"><?php echo lang('project_description') ?></div>
                    <div><?php echo $data['description'] ? $data['description'] : '' ?></div>
                    <div><?= $data['project_address'] ? $data['project_address'] : '-' ?></div>
                    <div class="text-bold"><?php echo lang('task') ?></div>
                    <div><?= $data['hours'][0]['task_name'] ? $data['hours'][0]['task_name'] : '-' ?></div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">&nbsp;</div>
            </div>
            <div class="row">
                <div class="col-xs-12 P-10 border-all">
                    <!--<div><?php echo lang('reload_time') ?>:</div>-->
					<?php if($data['truckBusNumber']) {?>
                    <div>
                        <span><?php echo lang('truck_bus_number') ?>:</span>
                        <span>
                            <span><?php echo $data['truckBusNumber']; ?></span>
                        </span>
                    </div>
					<?php } ?>
                    <!--<div><?php echo lang('container') . ': ' ?></div>-->
                    <!--<div><?php echo lang('mileage') ?>:</div>-->
					<?php if($data['arrivalTime']) {?>
                    <div>
                        <span><?php echo lang('arrive') . ': ' ?></span>
                        <span><?php echo $data['arrivalTime']; ?></span>
                    </div>
					<?php } ?>
                    <!--<div><?php echo lang('traveled_km') . ': ' ?></div>-->
                    <!-- only display if there is a result to prevent key without value being printed -->
					<?php if($data['driver']) {?>
                    <div><?php echo lang('driver') . ': ' ?>
                        <span>
                            <?php echo $data['driver'];?>
                        </span>
                    </div>
					<?php } ?>
                </div>
            </div>
        </div>
        <div class="col-xs-4 right-content border-all inline">
            <div class="text-bold"><?php echo lang('work_done_description') ?></div>
			<?php foreach ($data['comments'] as $comment) { ?>
                <div class="P-5 commentContainer">
                    <div class="justifyText"><?php echo $comment; ?></div>
                </div>
			<?php } ?>
			<?php foreach ($data['documentation'] as $doc) { ?>
                <div class="P-5 border-bottom">
                    <div class="justifyText"><?php echo sprintf(lang('daily_work_hours_doc_print'), date('d.m.Y H:i', strtotime($doc["dateTime"])), $doc["documentation_user_name"], $doc["docFields"]["notes"]) ?></div>
                </div>
			<?php } ?>
        </div>
    </div>
    <div class="row">&nbsp;</div>
    <div class="row">
        <table class="table text-center col-xs-12">
            <thead class="text-center">
            <tr class="border-all">
                <th class="border-all"></th>
                <th class="border-all"><?php echo lang('name') ?></th>
                <th class="border-all"><?php echo lang('start_time') ?></th>
                <th class="border-all"><?php echo lang('end_time') ?></th>
                <th class="border-all"><?php echo lang('hours_break') ?></th>
                <th class="border-all"><?php echo lang('total_working_time') ?></th>
                <th class="border-all"><?php echo lang('hours_drive') ?></th>
                <th class="border-all"><?php echo lang('hours_total') ?></th>
            </tr>
            </thead>
            <tbody>
			<?php $rowNumber = 1 ?>
			<?php foreach ($data['approvedHours'] as $pnr => $wo) { ?>
                <tr>
                    <td><?= $rowNumber++; ?></td>
<?php				foreach ($data['employees_names'] as $index=>$name) { ?>
<?php 					if($index == $pnr) { ?>
                            <td style="text-align: left"><?= $name; ?></td>
<?php 					} ?>                    	
<?php 				} ?>
                    <td><?= date('H:i', min($data['timestamps'][$pnr])); ?></td>
                    <td><?= date('H:i', max($data['timestamps'][$pnr])); ?></td>
                    <td><?= sprintf('%.2f', $wo['break']); ?></td>
                    <td><?= sprintf('%.2f', $wo['total']); ?></td>
                    <td><?= $wo['reisestd'] == '0.00' ? '' : sprintf('%.2f', $wo['drive']) ?></td>
                    <td><?= (sprintf('%.2f', $wo['total']) + sprintf('%.2f', $wo['drive'])) ?></td>
                </tr>
			<?php } ?>
            <?php if($data['hoursTotal']) {?>
                <tr>
                    <td style="border-bottom: 0px" colspan="7"></td>
                    <td><b><?php echo $data['hoursTotal'];?></b></td>
                </tr>
            <?php } ?>
            </tbody>
        </table>
    </div>
    <div class="row footer-text P-10 border-all">
        <div class="text-bold">
            <pre><?php echo lang('measurement_calculation') ?></pre>
        </div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
        <div class="row">&nbsp;</div>
    </div>
</div>

</body>
</html>
<style>
    
    .header {
        width: 100%;
    }

    .smooth-css {
        padding: 10px;
    }
    
    .table {
        width: 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }

    pre {
        border: 0px;
    }

  

    img {
        width: 75%;
        height: auto;
    }

    .day {
        border-bottom: 1px solid;
    }

    .inline {
        display: inline-block;
        margin:20px;
    }
    
    .table,
    .table > thead > tr > th {
        border-color: #000000;
        text-align: center;
    }

    .table > tbody > tr > td {
        border-top: none;
        border-left: 1px solid;
        border-right: 1px solid;
        border-bottom: 1px solid;
    }

    /*.table > tbody > tr:last-child {*/
    /*    border-bottom: 1px solid;*/
    /*}*/

    /*.table-striped > tbody > tr:nth-of-type(2n+1) {*/
    /*    background-color: #FFFFFF !important;*/
    /*}*/
    
    /*.table-striped > tbody > tr:nth-of-type(2n) {*/
    /*    background-color: #e6f2ff !important;*/
    /*}*/


    .table td,
    .table th {
        text-align: center;
        background-color: inherit !important;
    }

    #principal_logo_img {
        padding-top: 1.5em;
        width: 60%;
        height: auto;
    }

    table {
        page-break-after: auto;
        border-collapse: collapse;
    }

    tr {
        page-break-inside: avoid;
        page-break-after: auto
    }

    td {
        page-break-inside: avoid;
        page-break-after: auto
    }

    thead {
        display: table-header-group
    }

    tfoot {
        display: table-footer-group
    }

    .footer-text {
        page-break-before: auto;
    }

    @page {
        size: A4 landscape;
    }

    html * {
        font-size: 1em !important
    }

    html, body {
        height: 100%;
        width: 100%;
        margin: 0;
        padding: 0;
        font-family: Arial;
    }
    
    .container {
        padding-right: 15px;
        padding-left: 15px;
        margin-right: auto;
        margin-left: auto;
    }

    .right-content {
        float: right;
       	display: table;
    }
    .column-left {float:left; margin-left: 1em;}
    
    .row {
        margin-left: -26px;
        box-sizing: border-box;
    }
    .inner{
        border: 1px solid black;
    }
    
    .main_info{
       float:right;
       margin-top: -6em;
       margin-right: -2em;
    }
    
    .justifyText {
        text-align: justify;
        display: block;
        white-space: normal;
        word-wrap: break-word;
        border: 1px solid #9d9d9d;
        border-radius: 5px;
        padding: 7px;
        background-color: #eee;
    }

    

    .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-12 {
        min-height: 1px;
        padding-right: 15px;
        padding-left: 15px;
    }

    .border-all {
        border: 1px solid;
    }

    .border-bottom {
        border-bottom: 1px solid;
    }

    .text-bold {
        font-weight: bold;
    }

    .text-left {
        text-align: left;
    }

    .text-right {
        text-align: right;
    }

    .text-center {
        text-align: center;
    }

    .text-nowrap {
        white-space: nowrap;
    }

    .col-xs-12 {
        width: 100%;
    }

    .col-xs-6 {
        width: 50%;
    }

    .col-xs-5 {
        width: 41.66666667%;
    }

    .col-xs-4 {
        width: 30.33333333%;
    }

    .col-xs-3 {
        width: 25%;
    }
    .col-xs-2 {
        width: 16.66666667%;
    }
    .M-T20 {
        margin-top: 20px;
    }

    .P-5 {
        padding: 5px;
    }

    .P-10 {
        padding: 10px;
    }
    
    .table {border:1px solid black;}
	
	.commentContainer {object-fit: cover; }
</style>