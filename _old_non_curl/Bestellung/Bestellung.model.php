<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Employees.php';
require_once ABS_PATH.'printouts/printout.helper.php';

class C_Bestellung_Old  {

    private $documentation;
    private $helper;
    private $projects;
    private $employees;

    public function __construct() {
        $this->documentation = new Documentation();
    	$this->helper = new PrintoutHelper();
    	$this->projects = new Projects();
    	$this->employees = new Employees();
    }

    public function getData($schemaId, $documentId) {
		
       	$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
 
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		
		array_unshift($documentData['children'], $main);
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		
		$bestelllos = $this->helper->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
		$bestelllosData = $bestelllos['Bestelllos v7'];

		// resolve relations from attached Leistungslos
		$bestelllosRelations = $this->helper->getDeepValueFromField('title', 'linkedPositions', $grouped[0], 'children');

		foreach($bestelllosRelations['Bestelllos v7'] as $key => $value)
		{
			// Skip all Non-Position Keys
			if(strpos($key,'Position'))
				continue;
			if($value)
			{
				// assumption there is only a single linked position
				$innerArray = $value['Titel']['0'];
				$schemaId = $innerArray['schemaId'];
				$documentId = $innerArray['documentId'];

				// fetch respective Leistungslos, if existing
				if(!$documentId)
					continue;
				$leistungslos = $this->documentation->getDocumentId($schemaId,$documentId);
				$groupedLeistungslos = $this->helper->groupChildrenUnderParentRecursive($leistungslos['children'], 'id', 'parentId', 'children');

				// index shift to get the parentsId
				$documentationPositionId = (string) ((int) $innerArray['documentationPositionId'] - 1);
				// find the index of the object in the array
				$index = array_search($documentationPositionId, array_column($groupedLeistungslos,'id'));
				// fetch the positions headline children
				$positionHeadlineChildren = $groupedLeistungslos[$index]['children'];
				// find the field holding "menge"
				$amountFieldIndex = array_search('Menge',array_column($positionHeadlineChildren,'title'));
				$amountField = $positionHeadlineChildren[$amountFieldIndex];
				$bestelllosData[$key]['Menge'] = $amountField['reportedValue'] ? :'?';
			}
		}

		// drop empty values
		foreach ($bestelllosData as $key => $value) {
			if(!is_null($value))
				$templateData['positions'][$key] = $value;
		}
		// Resolve supplier from SUPPLIERSELECTOR field
		$addressesAPI = new Addresses();
		$supplierId = $bestelllosData['Lieferant'];

		// if its an Id it is resolved and the suppliers displayName is set
		// else the text entered in the "Lieferant" field is kept
		if(is_numeric($supplierId))
		{
			$supplier = $addressesAPI->get($supplierId);
			$templateData['supplierData'] = $supplier;
		}

		$templateData['pdfGenerationDate'] = date('d-m-Y');
		$templateData['artificialKey'] = $documentData['artificialKey'];
		$templateData['projectData'] = $this->getProjectData($documentData['documentRelKey1']);
		$templateData['author'] = $this->getAuthorName($documentData['author']);

		// resolution of "Lieferort"
		$lieferort = $bestelllosData['Lieferort'];
		switch($lieferort) {
			case 'Baustelle':
				$lieferort = $templateData['projectData']['customer_address'].', '.$templateData['projectData']['customer_postcode'].' '.$templateData['projectData']['customer_city'];
				break;
			case 'Lager':
				$lieferort = 'Chromstraße 128, 33415 Verl';
				break;
			case 'Büro':
				$lieferort = 'Carl-Bertelsmann-Straße 32, 33332 Gütersloh';
				break;
		}
		$templateData['positions']['Lieferort'] = $lieferort;

        return $templateData;
    }
    
    private function getProjectData($projectNo) {
        $result = $this->projects->get($projectNo);
        $projects = [];

        foreach ($result as $k=>$v) {
            $projects[$v['year']] = $v;
        }

        ksort($projects);

        return end($projects);
    }
    
    private function getAuthorName($pnr) {
    	$employeeData = $this->employees->getselect_employee((int)$pnr);
    	return trim($employeeData['vorname'].' '.$employeeData['name']);
    }

}