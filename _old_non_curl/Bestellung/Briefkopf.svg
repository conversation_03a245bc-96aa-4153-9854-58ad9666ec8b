<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 658 635" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <g id="Ebene-1" serif:id="Ebene 1" transform="matrix(4.16667,0,0,4.16667,0,0)">
        <g transform="matrix(1,0,0,1,11.6931,38.4555)">
            <path d="M0,3.306C0,2.757 -0.21,2.353 -0.63,2.095L-2.059,1.174C-1.615,0.908 -1.171,0.642 -0.727,0.375C-0.275,0.036 -0.048,-0.384 -0.048,-0.884L-0.048,-6.516C-0.048,-6.911 -0.174,-7.234 -0.424,-7.485C-0.674,-7.735 -0.997,-7.86 -1.393,-7.86L-7.594,-7.86L-7.594,11.093L-1.344,11.093C-0.949,11.093 -0.626,10.968 -0.375,10.718C-0.125,10.468 0,10.145 0,9.749L0,3.306ZM-3.427,0.472L-5.91,0.472L-5.91,-6.371L-1.744,-6.371L-1.744,-0.666L-3.427,0.472ZM-5.91,9.604L-5.91,1.937L-3.403,1.937L-1.696,3.052L-1.696,9.604L-5.91,9.604ZM9.41,0.678C9.41,-0.234 8.958,-0.691 8.054,-0.691L4.178,-0.691C3.274,-0.691 2.822,-0.234 2.822,0.678L2.822,2.664L4.384,2.664L4.384,0.654L7.848,0.654L7.848,4.202L4.057,4.202C3.145,4.202 2.689,4.654 2.689,5.559L2.689,9.858C2.689,10.73 3.129,11.166 4.009,11.166C4.485,11.166 5.119,11.117 5.91,11.021C6.548,10.94 7.186,10.855 7.824,10.766L7.824,11.093L9.41,11.093L9.41,0.678ZM4.251,9.701L4.251,5.462L7.848,5.462L7.848,9.628L4.251,9.701ZM18.796,-0.691L17.21,-0.691L17.21,9.555L13.637,9.652L13.637,-0.691L12.05,-0.691L12.05,9.858C12.05,10.73 12.486,11.166 13.358,11.166C13.867,11.166 14.509,11.117 15.284,11.021C15.922,10.94 16.563,10.855 17.21,10.766L17.21,11.093L18.796,11.093L18.796,-0.691ZM28.012,7.593L26.45,7.593L26.45,9.749L22.986,9.749L22.986,5.486L27.467,5.486L28.012,4.917L28.012,0.678C28.012,-0.234 27.56,-0.691 26.656,-0.691L22.781,-0.691C21.876,-0.691 21.424,-0.234 21.424,0.678L21.424,9.725C21.424,10.637 21.876,11.093 22.781,11.093L26.656,11.093C27.56,11.093 28.012,10.637 28.012,9.725L28.012,7.593ZM22.986,0.654L26.45,0.654L26.45,4.202L22.986,4.202L22.986,0.654ZM32.36,-7.86L30.773,-7.86L30.773,11.093L32.36,11.093L32.36,-7.86ZM41.686,7.593L40.123,7.593L40.123,9.749L36.66,9.749L36.66,5.486L41.141,5.486L41.686,4.917L41.686,0.678C41.686,-0.234 41.233,-0.691 40.329,-0.691L36.454,-0.691C35.549,-0.691 35.097,-0.234 35.097,0.678L35.097,9.725C35.097,10.637 35.549,11.093 36.454,11.093L40.329,11.093C41.233,11.093 41.686,10.637 41.686,9.725L41.686,7.593ZM36.66,0.654L40.124,0.654L40.124,4.202L36.66,4.202L36.66,0.654ZM56.194,0.557C56.194,-0.323 55.75,-0.763 54.862,-0.763C54.41,-0.763 53.744,-0.707 52.864,-0.594C52.194,-0.497 51.52,-0.404 50.841,-0.315C50.607,-0.614 50.244,-0.763 49.751,-0.763C49.251,-0.763 48.617,-0.715 47.85,-0.618C47.22,-0.529 46.59,-0.444 45.961,-0.364L45.961,-0.691L44.35,-0.691L44.35,11.093L45.961,11.093L45.961,0.848L49.473,0.751L49.473,11.093L51.071,11.093L51.071,0.848L54.584,0.751L54.584,11.093L56.194,11.093L56.194,0.557ZM65.399,7.593L63.836,7.593L63.836,9.749L60.373,9.749L60.373,5.486L64.854,5.486L65.399,4.917L65.399,0.678C65.399,-0.234 64.946,-0.691 64.042,-0.691L60.167,-0.691C59.262,-0.691 58.81,-0.234 58.81,0.678L58.81,9.725C58.81,10.637 59.262,11.093 60.167,11.093L64.042,11.093C64.946,11.093 65.399,10.637 65.399,9.725L65.399,7.593ZM60.373,0.654L63.837,0.654L63.837,4.202L60.373,4.202L60.373,0.654ZM74.833,0.557C74.833,-0.323 74.385,-0.763 73.489,-0.763C72.988,-0.763 72.35,-0.715 71.575,-0.618C70.937,-0.529 70.303,-0.444 69.674,-0.364L69.674,-0.691L68.063,-0.691L68.063,11.093L69.674,11.093L69.674,0.848L73.222,0.751L73.222,11.093L74.833,11.093L74.833,0.557ZM81.676,9.676L79.617,9.676L79.617,0.726L81.676,0.726L81.676,-0.691L79.617,-0.691L79.617,-4.36L78.03,-4.36L78.03,-0.691L76.613,-0.691L76.613,0.726L78.03,0.726L78.03,9.725C78.03,10.637 78.482,11.093 79.387,11.093L81.676,11.093L81.676,9.676ZM90.323,7.593L88.76,7.593L88.76,9.749L85.297,9.749L85.297,5.486L89.778,5.486L90.323,4.917L90.323,0.678C90.323,-0.234 89.87,-0.691 88.966,-0.691L85.091,-0.691C84.187,-0.691 83.734,-0.234 83.734,0.678L83.734,9.725C83.734,10.637 84.187,11.093 85.091,11.093L88.966,11.093C89.87,11.093 90.323,10.637 90.323,9.725L90.323,7.593ZM85.297,0.654L88.761,0.654L88.761,4.202L85.297,4.202L85.297,0.654ZM105.704,1.598L101.985,1.598L101.985,3.052L104.008,3.052L104.008,9.604L99.721,9.604L99.721,-6.371L104.008,-6.371L104.008,-2.108L105.704,-2.108L105.704,-6.516C105.704,-6.911 105.578,-7.234 105.328,-7.485C105.078,-7.735 104.755,-7.86 104.359,-7.86L99.382,-7.86C98.986,-7.86 98.663,-7.735 98.413,-7.485C98.162,-7.234 98.037,-6.911 98.037,-6.516L98.037,9.749C98.037,10.145 98.162,10.468 98.413,10.718C98.663,10.968 98.986,11.093 99.382,11.093L104.359,11.093C104.755,11.093 105.078,10.968 105.328,10.718C105.578,10.468 105.704,10.145 105.704,9.749L105.704,1.598ZM120.358,0.557C120.358,-0.323 119.914,-0.763 119.025,-0.763C118.573,-0.763 117.907,-0.707 117.027,-0.594C116.357,-0.497 115.683,-0.404 115.005,-0.315C114.77,-0.614 114.407,-0.763 113.915,-0.763C113.414,-0.763 112.78,-0.715 112.013,-0.618C111.384,-0.529 110.754,-0.444 110.124,-0.364L110.124,-0.691L108.513,-0.691L108.513,11.093L110.124,11.093L110.124,0.848L113.636,0.751L113.636,11.093L115.235,11.093L115.235,0.848L118.747,0.751L118.747,11.093L120.358,11.093L120.358,0.557ZM129.744,0.557C129.744,-0.323 129.295,-0.763 128.399,-0.763C127.939,-0.763 127.305,-0.711 126.498,-0.606C125.86,-0.525 125.222,-0.444 124.584,-0.364L124.584,-7.86L122.974,-7.86L122.974,11.093L127.237,11.093C127.955,11.093 128.553,10.853 129.029,10.373C129.505,9.892 129.744,9.289 129.744,8.562L129.744,0.557ZM126.813,9.676L124.584,9.676L124.584,0.848L128.133,0.751L128.133,8.32C128.133,9.224 127.693,9.676 126.813,9.676M140.498,-7.86L138.815,-7.86L138.815,0.472L134.249,0.472L134.249,-7.86L132.565,-7.86L132.565,11.093L134.249,11.093L134.249,1.986L138.815,1.986L138.815,11.093L140.498,11.093L140.498,-7.86Z" style="fill:rgb(0,140,79);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,12.9695,13.3003)">
            <path d="M0,-1.417L-4.885,-1.417L-4.885,0.993L-2.807,0.993L-2.807,6.298L-6.45,6.298L-6.45,-7.787L-2.807,-7.787L-2.807,-4.433L0,-4.433L0,-8.478C0,-9.64 -0.569,-10.222 -1.708,-10.222L-7.549,-10.222C-8.68,-10.222 -9.245,-9.64 -9.245,-8.478L-9.245,7C-9.245,8.155 -8.692,8.732 -7.585,8.732L-1.708,8.732C-0.569,8.732 0,8.155 0,7L0,-1.417ZM10.117,-3.5L7.394,-3.5L7.394,6.37L4.802,6.431L4.802,-3.5L2.09,-3.5L2.09,7.291C2.09,8.3 2.588,8.805 3.583,8.805C4.085,8.805 4.722,8.744 5.495,8.623C6.132,8.518 6.765,8.413 7.394,8.308L7.394,8.732L10.117,8.732L10.117,-3.5ZM9.245,-6.915L6.629,-6.915L6.629,-4.311L9.245,-4.311L9.245,-6.915ZM5.602,-6.915L2.986,-6.915L2.986,-4.311L5.602,-4.311L5.602,-6.915ZM17.308,6.37L15.481,6.37L15.481,-1.138L17.236,-1.138L17.236,-3.5L15.481,-3.5L15.481,-6.818L12.757,-6.818L12.757,-3.5L11.467,-3.5L11.467,-1.138L12.757,-1.138L12.757,7.218C12.757,8.227 13.263,8.732 14.274,8.732L17.308,8.732L17.308,6.37ZM26.625,4.663L24.009,4.663L24.009,6.697L21.441,6.697L21.441,3.27L25.873,3.27L26.625,2.507L26.625,-1.986C26.625,-2.995 26.112,-3.5 25.084,-3.5L20.366,-3.5C19.339,-3.5 18.825,-2.995 18.825,-1.986L18.825,7.218C18.825,8.227 19.339,8.732 20.366,8.732L25.084,8.732C26.112,8.732 26.625,8.227 26.625,7.218L26.625,4.663ZM21.441,-1.465L24.009,-1.465L24.009,1.466L21.441,1.466L21.441,-1.465ZM36.217,-2.059C36.217,-3.052 35.719,-3.549 34.724,-3.549C34.27,-3.549 33.704,-3.488 33.028,-3.367C32.47,-3.262 31.909,-3.157 31.343,-3.052L31.343,-3.5L28.608,-3.5L28.608,8.732L31.343,8.732L31.343,-1.138L33.505,-1.187L33.505,1.611L36.217,1.611L36.217,-2.059ZM45.343,4.19C45.343,3.609 45.064,3.157 44.507,2.834L40.326,0.351L40.326,-1.417L42.727,-1.417L42.727,0.472L45.319,0.472L45.319,-1.986C45.319,-2.995 44.813,-3.5 43.802,-3.5L39.251,-3.5C38.224,-3.5 37.71,-2.995 37.71,-1.986L37.71,0.872C37.71,1.429 37.985,1.873 38.534,2.204L42.727,4.711L42.727,6.649L40.326,6.649L40.326,4.59L37.71,4.59L37.71,7.218C37.71,8.227 38.216,8.732 39.227,8.732L43.826,8.732C44.837,8.732 45.343,8.227 45.343,7.218L45.343,4.19ZM50.013,-10.222L47.325,-10.222L47.325,8.731L50.013,8.731L50.013,-10.222ZM59.999,-1.986C59.999,-2.995 59.493,-3.5 58.482,-3.5L53.585,-3.5C52.558,-3.5 52.044,-2.995 52.044,-1.986L52.044,7.218C52.044,8.227 52.558,8.732 53.585,8.732L58.482,8.732C59.493,8.732 59.999,8.227 59.999,7.218L59.999,-1.986ZM54.732,-1.187L57.324,-1.187L57.324,6.431L54.732,6.431L54.732,-1.187ZM70.021,-2.059C70.021,-3.052 69.523,-3.549 68.528,-3.549C68.058,-3.549 67.421,-3.484 66.617,-3.355C65.98,-3.25 65.343,-3.149 64.706,-3.052L64.706,-10.222L61.97,-10.222L61.97,8.732L64.706,8.732L64.706,-1.138L67.274,-1.187L67.274,8.732L70.021,8.732L70.021,-2.059ZM79.78,4.663L77.164,4.663L77.164,6.697L74.596,6.697L74.596,3.27L79.027,3.27L79.78,2.507L79.78,-1.986C79.78,-2.995 79.266,-3.5 78.239,-3.5L73.521,-3.5C72.494,-3.5 71.98,-2.995 71.98,-1.986L71.98,7.218C71.98,8.227 72.494,8.732 73.521,8.732L78.239,8.732C79.266,8.732 79.78,8.227 79.78,7.218L79.78,4.663ZM74.596,-1.465L77.164,-1.465L77.164,1.466L74.596,1.466L74.596,-1.465ZM89.372,-2.059C89.372,-3.052 88.874,-3.549 87.879,-3.549C87.425,-3.549 86.859,-3.488 86.182,-3.367C85.625,-3.262 85.064,-3.157 84.498,-3.052L84.498,-3.5L81.763,-3.5L81.763,8.732L84.498,8.732L84.498,-1.138L86.66,-1.187L86.66,1.611L89.372,1.611L89.372,-2.059Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,7.7719,83.8612)">
            <path d="M0,6.558C0,6.718 -0.052,6.848 -0.156,6.949C-0.261,7.05 -0.393,7.1 -0.552,7.1L-2.52,7.1C-2.679,7.1 -2.81,7.05 -2.91,6.949C-3.011,6.848 -3.062,6.718 -3.062,6.558L-3.062,0C-3.062,-0.159 -3.011,-0.289 -2.91,-0.39C-2.81,-0.491 -2.679,-0.542 -2.52,-0.542L-0.552,-0.542C-0.393,-0.542 -0.261,-0.491 -0.156,-0.39C-0.052,-0.289 0,-0.159 0,0L0,1.778L-0.684,1.778L-0.684,0.059L-2.383,0.059L-2.383,6.499L-0.684,6.499L-0.684,4.62L0,4.62L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,10.9359,86.8304)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.101,4.141 -1.355,4.16 -1.548,4.16C-1.902,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.436 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.447,-0.62 0.63,-0.436 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,15.1156,89.3687)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.094 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.011 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,32.6,22.096)">
            <rect x="15.98" y="61.223" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(-1,0,0,1,37.33,23.909)">
            <rect x="17.659" y="63.835" width="2.012" height="0.605" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,23.1283,83.9206)">
            <path d="M0,6.44L0,3.798L-0.688,3.349L-1.699,3.349L-1.699,6.44L0,6.44ZM-0.02,2.299L-0.02,0L-1.699,0L-1.699,2.758L-0.698,2.758L-0.02,2.299ZM0.684,6.499C0.684,6.658 0.633,6.788 0.532,6.889C0.432,6.99 0.301,7.041 0.142,7.041L-2.378,7.041L-2.378,-0.601L0.122,-0.601C0.281,-0.601 0.412,-0.551 0.513,-0.45C0.613,-0.349 0.664,-0.219 0.664,-0.059L0.664,2.211C0.664,2.413 0.573,2.583 0.391,2.719C0.212,2.827 0.032,2.934 -0.146,3.042L0.43,3.413C0.599,3.517 0.684,3.68 0.684,3.901L0.684,6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,24.987)">
            <path d="M26.947,61.765L25.551,61.765L25.551,63.196L26.947,63.196L26.947,61.765ZM27.577,65.422C27.577,65.79 27.394,65.974 27.03,65.974L25.467,65.974C25.103,65.974 24.92,65.79 24.92,65.422L24.92,61.775C24.92,61.407 25.103,61.223 25.467,61.223L27.03,61.223C27.394,61.223 27.577,61.407 27.577,61.775L27.577,63.484L27.357,63.713L25.55,63.713L25.55,65.432L26.947,65.432L26.947,64.563L27.577,64.563L27.577,65.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,31.1361,89.3687)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.094 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.011 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,33.6703,84.7312)">
            <path d="M0,6.23L-0.923,6.23C-1.287,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,24.987)">
            <path d="M36.527,61.765L35.131,61.765L35.131,63.196L36.527,63.196L36.527,61.765ZM37.157,65.422C37.157,65.79 36.974,65.974 36.61,65.974L35.047,65.974C34.683,65.974 34.5,65.79 34.5,65.422L34.5,61.775C34.5,61.407 34.683,61.223 35.047,61.223L36.61,61.223C36.974,61.223 37.157,61.407 37.157,61.775L37.157,63.484L36.937,63.713L35.13,63.713L35.13,65.432L36.527,65.432L36.527,64.563L37.157,64.563L37.157,65.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,77.18,22.096)">
            <rect x="38.27" y="61.223" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,42.5717,86.7624)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.502,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.368 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.193,-0.552 -0.01,-0.368 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.53 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,48.3773,86.1812)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.065,0.649L-2.065,4.78L-2.71,4.78L-2.71,0.61L-4.126,0.649L-4.126,4.78L-4.775,4.78L-4.775,0.029L-4.126,0.029L-4.126,0.161C-3.872,0.128 -3.618,0.094 -3.364,0.058C-3.055,0.019 -2.8,0 -2.598,0C-2.399,0 -2.253,0.06 -2.158,0.18C-1.885,0.145 -1.613,0.107 -1.343,0.068C-0.988,0.023 -0.72,0 -0.537,0C-0.179,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,51.4877,86.8304)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.101,4.141 -1.355,4.16 -1.548,4.16C-1.902,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.436 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.447,-0.62 0.63,-0.436 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,55.9115,86.1812)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.128 -1.57,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,59.6908,86.1812)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.128 -1.57,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,123.366,23.909)">
            <rect x="60.677" y="63.835" width="2.012" height="0.605" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,66.7465,83.8612)">
            <path d="M0,6.558C0,6.718 -0.05,6.848 -0.149,6.949C-0.248,7.05 -0.378,7.1 -0.537,7.1L-2.461,7.1C-2.62,7.1 -2.751,7.05 -2.854,6.949C-2.956,6.848 -3.008,6.718 -3.008,6.558L-3.008,4.62L-2.329,4.62L-2.329,6.499L-0.679,6.499L-0.679,4.678L-2.788,2.129C-2.935,1.954 -3.008,1.76 -3.008,1.548L-3.008,0C-3.008,-0.159 -2.956,-0.289 -2.854,-0.39C-2.751,-0.491 -2.62,-0.542 -2.461,-0.542L-0.537,-0.542C-0.378,-0.542 -0.248,-0.491 -0.149,-0.39C-0.05,-0.289 0,-0.159 0,0L0,1.778L-0.679,1.778L-0.679,0.059L-2.329,0.059L-2.329,1.67L-0.21,4.219C-0.07,4.389 0,4.579 0,4.79L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,69.5785,84.7312)">
            <path d="M0,6.23L-0.923,6.23C-1.287,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,72.894,89.3687)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.094 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.011 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,75.7748,86.8304)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.101,4.141 -1.355,4.16 -1.548,4.16C-1.902,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.436 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.447,-0.62 0.63,-0.436 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,80.2475,83.8519)">
            <path d="M0,6.577C0,6.932 -0.178,7.109 -0.532,7.109L-1.431,7.109L-1.431,6.538L-0.64,6.538L-0.64,3.921L-1.44,3.179L-1.44,3.017L-0.649,2.29L-0.649,0.591C-0.649,0.223 -0.827,0.039 -1.182,0.039L-2.129,0.039L-2.129,7.109L-2.778,7.109L-2.778,-0.532L-1.021,-0.532C-0.728,-0.532 -0.486,-0.436 -0.295,-0.242C-0.105,-0.048 -0.01,0.195 -0.01,0.488L-0.01,2.031C-0.01,2.288 -0.081,2.494 -0.225,2.646C-0.41,2.796 -0.596,2.948 -0.781,3.1C-0.589,3.25 -0.398,3.4 -0.21,3.55C-0.07,3.693 0,3.9 0,4.17L0,6.577Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,24.987)">
            <path d="M83.343,61.765L81.947,61.765L81.947,63.196L83.343,63.196L83.343,61.765ZM83.973,65.422C83.973,65.79 83.79,65.974 83.426,65.974L81.864,65.974C81.499,65.974 81.317,65.79 81.317,65.422L81.317,61.775C81.317,61.407 81.499,61.223 81.864,61.223L83.426,61.223C83.79,61.223 83.973,61.407 83.973,61.775L83.973,63.484L83.753,63.713L81.947,63.713L81.947,65.432L83.343,65.432L83.343,64.563L83.973,64.563L83.973,65.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,90.1352,83.8612)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,94.144,83.3192)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,7.7621,95.8612)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,11.9809,95.8612)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,16.1996,95.8612)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,20.4184,95.8612)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,24.4272,95.3192)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,30.5356,95.8612)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.549,7.1C-2.708,7.1 -2.839,7.05 -2.939,6.949C-3.04,6.848 -3.091,6.718 -3.091,6.558L-3.091,0C-3.091,-0.159 -3.04,-0.289 -2.939,-0.39C-2.839,-0.491 -2.708,-0.542 -2.549,-0.542L-0.542,-0.542C-0.383,-0.542 -0.252,-0.491 -0.151,-0.39C-0.051,-0.289 0,-0.159 0,0L0,1.778L-0.684,1.778L-0.684,0.059L-2.412,0.059L-2.412,6.499L-0.684,6.499L-0.684,3.858L-1.499,3.858L-1.499,3.272L0,3.272L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,47.859)">
            <path d="M34.017,49.931L33.299,49.931L33.299,49.194L34.017,49.194L34.017,49.931ZM32.757,49.931L32.039,49.931L32.039,49.194L32.757,49.194L32.757,49.931ZM34.388,55.102L33.748,55.102L33.748,54.97C33.488,55.006 33.229,55.04 32.972,55.073C32.66,55.112 32.401,55.131 32.196,55.131C31.844,55.131 31.668,54.956 31.668,54.604L31.668,50.351L32.308,50.351L32.308,54.521L33.748,54.482L33.748,50.351L34.388,50.351L34.388,55.102Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,37.1566,96.7312)">
            <path d="M0,6.23L-0.923,6.23C-1.287,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,48.987)">
            <path d="M40.013,49.765L38.617,49.765L38.617,51.196L40.013,51.196L40.013,49.765ZM40.643,53.422C40.643,53.79 40.46,53.974 40.096,53.974L38.534,53.974C38.169,53.974 37.987,53.79 37.987,53.422L37.987,49.775C37.987,49.407 38.169,49.223 38.534,49.223L40.096,49.223C40.46,49.223 40.643,49.407 40.643,49.775L40.643,51.484L40.423,51.713L38.617,51.713L38.617,53.432L40.013,53.432L40.013,52.563L40.643,52.563L40.643,53.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,44.2025,101.369)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.094 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.011 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,47.5863,98.7624)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.502,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.368 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.193,-0.552 -0.01,-0.368 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.53 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,97.95,46.096)">
            <rect x="48.655" y="49.223" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,48.987)">
            <path d="M52.479,49.794L51.048,49.794L51.048,53.402L52.479,53.402L52.479,49.794ZM53.128,53.422C53.128,53.79 52.945,53.974 52.577,53.974L50.946,53.974C50.581,53.974 50.399,53.79 50.399,53.422L50.399,49.775C50.399,49.407 50.581,49.223 50.946,49.223L52.577,49.223C52.945,49.223 53.128,49.407 53.128,49.775L53.128,53.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,56.9174,95.3192)">
            <path d="M0,7.642L-0.649,7.642L-0.649,3.472L-2.08,3.511L-2.08,7.642L-2.729,7.642L-2.729,0L-2.08,0L-2.08,3.023C-1.823,2.99 -1.565,2.958 -1.309,2.925C-0.983,2.883 -0.728,2.862 -0.542,2.862C-0.181,2.862 0,3.039 0,3.394L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,7.3422,118.36)">
            <path d="M0,-6.44L-1.182,-6.44L-1.182,0.601L-1.86,0.601L-1.86,-6.44L-3.052,-6.44L-3.052,-7.041L0,-7.041L0,-6.44Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,80.987)">
            <path d="M10.086,33.765L8.69,33.765L8.69,35.196L10.086,35.196L10.086,33.765ZM10.716,37.422C10.716,37.79 10.534,37.974 10.169,37.974L8.607,37.974C8.243,37.974 8.06,37.79 8.06,37.422L8.06,33.775C8.06,33.407 8.243,33.223 8.607,33.223L10.169,33.223C10.534,33.223 10.716,33.407 10.716,33.775L10.716,35.484L10.496,35.713L8.69,35.713L8.69,37.432L10.086,37.432L10.086,36.563L10.716,36.563L10.716,37.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,24.298,78.096)">
            <rect x="11.829" y="33.223" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,80.987)">
            <path d="M15.599,33.765L14.203,33.765L14.203,35.196L15.599,35.196L15.599,33.765ZM16.229,37.422C16.229,37.79 16.046,37.974 15.682,37.974L14.12,37.974C13.755,37.974 13.573,37.79 13.573,37.422L13.573,33.775C13.573,33.407 13.755,33.223 14.12,33.223L15.682,33.223C16.046,33.223 16.229,33.407 16.229,33.775L16.229,35.484L16.009,35.713L14.203,35.713L14.203,37.432L15.599,37.432L15.599,36.563L16.229,36.563L16.229,37.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,19.1,118.39)">
            <path d="M0,-6.499L-0.918,-6.499L-0.918,-4.18L-0.107,-4.18L-0.107,-3.608L-0.918,-3.608L-0.918,0.571L-1.558,0.571L-1.558,-3.608L-2.109,-3.608L-2.109,-4.18L-1.558,-4.18L-1.558,-6.519C-1.558,-6.886 -1.375,-7.07 -1.011,-7.07L0,-7.07L0,-6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,80.987)">
            <path d="M21.761,33.794L20.33,33.794L20.33,37.402L21.761,37.402L21.761,33.794ZM22.411,37.422C22.411,37.79 22.227,37.974 21.859,37.974L20.228,37.974C19.864,37.974 19.681,37.79 19.681,37.422L19.681,33.775C19.681,33.407 19.864,33.223 20.228,33.223L21.859,33.223C22.227,33.223 22.411,33.407 22.411,33.775L22.411,37.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,26.1996,114.181)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.128 -1.57,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,78.097)">
            <path d="M32.152,33.823L30.389,33.823L30.389,40.263L32.152,40.263L32.152,33.823ZM32.83,40.322C32.83,40.482 32.779,40.612 32.674,40.713C32.57,40.814 32.438,40.864 32.279,40.864L30.252,40.864C30.093,40.864 29.962,40.814 29.862,40.713C29.761,40.612 29.71,40.482 29.71,40.322L29.71,33.765C29.71,33.605 29.761,33.475 29.862,33.374C29.962,33.273 30.093,33.223 30.252,33.223L32.279,33.223C32.438,33.223 32.57,33.273 32.674,33.374C32.779,33.475 32.83,33.605 32.83,33.765L32.83,40.322Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,37.9906,111.861)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.05 -2.81,6.949C-2.909,6.848 -2.959,6.718 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.35 -0.151,2.452C-0.051,2.553 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,42.0043,111.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,47.0424,113.312)">
            <path d="M0,3.657L-0.581,3.657L-0.581,5.649L-1.221,5.649L-1.221,3.657L-3.379,3.657L-3.379,3.31L-1.528,-1.992L-0.889,-1.992L-0.889,-1.963L-2.646,3.081L-1.221,3.081L-1.221,0.307L-0.581,0.307L-0.581,3.081L0,3.081L0,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,48.6537,111.319)">
            <path d="M0,7.642L-0.684,7.642L-0.684,2.344L-0.981,2.344L-0.981,2.32L-0.552,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,54.4047,118.917)">
            <path d="M0,-7.554L-3.232,0.044L-3.882,0.044L-3.882,0.005L-0.649,-7.598L0,-7.598L0,-7.554Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,59.0668,111.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,64.1674,111.861)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,68.1762,111.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,78.097)">
            <path d="M72.545,33.823L70.875,33.823L70.875,36.845L72.545,36.845L72.545,33.823ZM73.224,40.322C73.224,40.482 73.174,40.612 73.075,40.713C72.976,40.814 72.846,40.864 72.687,40.864L70.778,40.864C70.619,40.864 70.487,40.814 70.382,40.713C70.278,40.612 70.226,40.482 70.226,40.322L70.226,38.633L70.905,38.633L70.905,40.264L72.545,40.264L72.545,37.436L70.734,37.436C70.575,37.436 70.445,37.386 70.346,37.285C70.246,37.184 70.197,37.054 70.197,36.894L70.197,33.765C70.197,33.605 70.246,33.475 70.346,33.374C70.445,33.273 70.575,33.223 70.734,33.223L72.687,33.223C72.846,33.223 72.976,33.273 73.075,33.374C73.174,33.475 73.224,33.605 73.224,33.765L73.224,40.322Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,78.097)">
            <path d="M76.906,33.823L75.143,33.823L75.143,40.263L76.906,40.263L76.906,33.823ZM77.584,40.322C77.584,40.482 77.533,40.612 77.428,40.713C77.324,40.814 77.192,40.864 77.033,40.864L75.006,40.864C74.847,40.864 74.716,40.814 74.616,40.713C74.515,40.612 74.464,40.482 74.464,40.322L74.464,33.765C74.464,33.605 74.515,33.475 74.616,33.374C74.716,33.273 74.847,33.223 75.006,33.223L77.033,33.223C77.192,33.223 77.324,33.273 77.428,33.374C77.533,33.475 77.584,33.605 77.584,33.765L77.584,40.322Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,81.057,111.319)">
            <path d="M0,7.642L-0.684,7.642L-0.684,2.344L-0.981,2.344L-0.981,2.32L-0.552,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,85.266,111.861)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.05 -2.81,6.949C-2.909,6.848 -2.959,6.718 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.35 -0.151,2.452C-0.051,2.553 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,7.1713,130.36)">
            <path d="M0,-6.44L-1.782,-6.44L-1.782,-3.54L-0.142,-3.54L-0.142,-2.929L-1.782,-2.929L-1.782,0.601L-2.461,0.601L-2.461,-7.041L0,-7.041L0,-6.44Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,10.0863,126.83)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.101,4.141 -1.355,4.16 -1.548,4.16C-1.902,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.436 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.447,-0.62 0.63,-0.436 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,14.3588,126.21)">
            <path d="M0,4.751L-0.659,4.751L-1.46,2.871L-2.271,4.751L-2.91,4.751L-2.91,4.731L-1.802,2.31L-2.842,0L-2.183,0L-1.45,1.743L-0.698,0L-0.068,0L-0.068,0.02L-1.108,2.29L0,4.751Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,102.097)">
            <path d="M32.152,21.823L30.389,21.823L30.389,28.263L32.152,28.263L32.152,21.823ZM32.83,28.322C32.83,28.482 32.779,28.612 32.674,28.713C32.57,28.814 32.438,28.864 32.279,28.864L30.252,28.864C30.093,28.864 29.962,28.814 29.862,28.713C29.761,28.612 29.71,28.482 29.71,28.322L29.71,21.765C29.71,21.605 29.761,21.475 29.862,21.374C29.962,21.273 30.093,21.223 30.252,21.223L32.279,21.223C32.438,21.223 32.57,21.273 32.674,21.374C32.779,21.475 32.83,21.605 32.83,21.765L32.83,28.322Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,37.9906,123.861)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.05 -2.81,6.949C-2.909,6.848 -2.959,6.718 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.35 -0.151,2.452C-0.051,2.553 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,42.0043,123.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,47.0424,125.312)">
            <path d="M0,3.657L-0.581,3.657L-0.581,5.649L-1.221,5.649L-1.221,3.657L-3.379,3.657L-3.379,3.31L-1.528,-1.992L-0.889,-1.992L-0.889,-1.963L-2.646,3.081L-1.221,3.081L-1.221,0.307L-0.581,0.307L-0.581,3.081L0,3.081L0,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,48.6537,123.319)">
            <path d="M0,7.642L-0.684,7.642L-0.684,2.344L-0.981,2.344L-0.981,2.32L-0.552,0L0,0L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,54.4047,130.917)">
            <path d="M0,-7.554L-3.232,0.044L-3.882,0.044L-3.882,0.005L-0.649,-7.598L0,-7.598L0,-7.554Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,59.0668,123.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,64.1674,123.861)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.51,7.1C-2.669,7.1 -2.8,7.05 -2.9,6.949C-3.001,6.848 -3.052,6.718 -3.052,6.558L-3.052,4.62L-2.373,4.62L-2.373,6.499L-0.684,6.499L-0.684,4.161L-2.031,3.15L-2.031,2.998L-0.693,2.002L-0.693,0.059L-2.363,0.059L-2.363,1.778L-3.042,1.778L-3.042,0C-3.042,-0.159 -2.991,-0.289 -2.891,-0.39C-2.79,-0.491 -2.659,-0.542 -2.5,-0.542L-0.552,-0.542C-0.393,-0.542 -0.262,-0.491 -0.161,-0.39C-0.061,-0.289 -0.01,-0.159 -0.01,0L-0.01,1.861C-0.01,2.105 -0.107,2.295 -0.303,2.432L-1.211,3.072L-0.293,3.711C-0.098,3.845 0,4.038 0,4.292L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,68.1762,123.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,102.097)">
            <path d="M72.545,21.823L70.875,21.823L70.875,24.845L72.545,24.845L72.545,21.823ZM73.224,28.322C73.224,28.482 73.174,28.612 73.075,28.713C72.976,28.814 72.846,28.864 72.687,28.864L70.778,28.864C70.619,28.864 70.487,28.814 70.382,28.713C70.278,28.612 70.226,28.482 70.226,28.322L70.226,26.633L70.905,26.633L70.905,28.264L72.545,28.264L72.545,25.436L70.734,25.436C70.575,25.436 70.445,25.386 70.346,25.285C70.246,25.184 70.197,25.054 70.197,24.894L70.197,21.765C70.197,21.605 70.246,21.475 70.346,21.374C70.445,21.273 70.575,21.223 70.734,21.223L72.687,21.223C72.846,21.223 72.976,21.273 73.075,21.374C73.174,21.475 73.224,21.605 73.224,21.765L73.224,28.322Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,75.5922,123.319)">
            <path d="M0,7.642L-0.669,7.642L0.923,0.601L-0.708,0.601L-0.708,1.724L-1.387,1.724L-1.387,0L1.641,0L1.641,0.352L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,81.9242,123.319)">
            <path d="M0,7.642L-3.032,7.642L-3.032,7.281L-0.693,2.422L-0.693,0.601L-2.251,0.601L-2.251,2.32L-2.935,2.32L-2.935,0.542C-2.935,0.383 -2.884,0.253 -2.783,0.152C-2.683,0.051 -2.552,0 -2.393,0L-0.552,0C-0.393,0 -0.262,0.051 -0.161,0.152C-0.061,0.253 -0.01,0.383 -0.01,0.542L-0.01,2.163C-0.01,2.359 -0.07,2.582 -0.19,2.832L-2.212,7.041L0,7.041L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,85.9232,123.861)">
            <path d="M0,6.558C0,6.718 -0.051,6.848 -0.151,6.949C-0.252,7.05 -0.383,7.1 -0.542,7.1L-2.422,7.1C-2.581,7.1 -2.711,7.05 -2.81,6.949C-2.909,6.848 -2.959,6.718 -2.959,6.558L-2.959,4.62L-2.28,4.62L-2.28,6.499L-0.679,6.499L-0.679,2.901L-2.939,2.901L-2.852,-0.542L-0.059,-0.542L-0.059,0.059L-2.188,0.059L-2.271,2.3L-0.542,2.3C-0.383,2.3 -0.252,2.35 -0.151,2.452C-0.051,2.553 0,2.683 0,2.842L0,6.558Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,135.127)">
            <path d="M5.311,11.834L4.671,11.834L4.671,7.083L5.311,7.083L5.311,11.834ZM5.321,6.043L4.662,6.043L4.662,5.223L5.321,5.223L5.321,6.043Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,9.144,142.181)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.128 -1.57,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,11.9906,146.39)">
            <path d="M0,-6.499L-0.918,-6.499L-0.918,-4.18L-0.107,-4.18L-0.107,-3.608L-0.918,-3.608L-0.918,0.571L-1.558,0.571L-1.558,-3.608L-2.109,-3.608L-2.109,-4.18L-1.558,-4.18L-1.558,-6.519C-1.558,-6.886 -1.375,-7.07 -1.011,-7.07L0,-7.07L0,-6.499Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M14.652,5.794L13.221,5.794L13.221,9.402L14.652,9.402L14.652,5.794ZM15.301,9.422C15.301,9.79 15.118,9.974 14.749,9.974L13.119,9.974C12.754,9.974 12.572,9.79 12.572,9.422L12.572,5.775C12.572,5.407 12.754,5.223 13.119,5.223L14.749,5.223C15.118,5.223 15.301,5.407 15.301,5.775L15.301,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,134.097)">
            <path d="M19.08,9.124L18.206,9.124L18.206,10.735L19.08,10.735L19.08,9.124ZM20.858,12.864L16.756,12.864C16.544,12.864 16.439,12.757 16.439,12.542L16.439,5.545C16.439,5.33 16.544,5.223 16.756,5.223L20.54,5.223C20.752,5.223 20.858,5.33 20.858,5.545L20.858,10.852C20.858,11.067 20.752,11.175 20.54,11.175L18.06,11.175C17.826,11.175 17.708,11.061 17.708,10.833L17.708,9.036C17.708,8.802 17.826,8.684 18.06,8.684L19.08,8.684L19.08,7.322L18.241,7.322L18.241,8.123L17.747,8.123L17.747,7.225C17.747,6.99 17.865,6.873 18.099,6.873L19.217,6.873C19.452,6.873 19.569,6.99 19.569,7.225L19.569,10.804L20.487,10.804L20.487,5.564L16.81,5.564L16.81,12.522L20.858,12.522L20.858,12.864Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,24.0561,144.554)">
            <path d="M0,1.836L0,-1.753C-0.515,-1.786 -0.813,-1.802 -0.898,-1.802C-1.257,-1.802 -1.436,-1.62 -1.436,-1.255L-1.436,1.289C-1.436,1.654 -1.257,1.836 -0.898,1.836L0,1.836ZM0.645,3.657C0.645,4.025 0.461,4.209 0.093,4.209L-1.45,4.209C-1.824,4.209 -2.012,3.976 -2.012,3.511C-2.012,3.456 -2.01,3.373 -2.004,3.264C-2,3.155 -1.997,3.076 -1.997,3.027L-1.357,3.027L-1.357,3.657L0,3.657L0,2.407L-1.069,2.407C-1.359,2.407 -1.6,2.311 -1.792,2.117C-1.984,1.923 -2.08,1.68 -2.08,1.387L-2.08,-1.392C-2.08,-1.671 -1.995,-1.905 -1.826,-2.092C-1.657,-2.279 -1.434,-2.373 -1.157,-2.373C-1.082,-2.373 -0.696,-2.319 0,-2.212L0,-2.344L0.645,-2.344L0.645,3.657Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,28.4897,142.239)">
            <path d="M0,4.722L-0.64,4.722L-0.64,4.59C-0.9,4.626 -1.159,4.66 -1.416,4.693C-1.729,4.732 -1.987,4.751 -2.192,4.751C-2.544,4.751 -2.72,4.576 -2.72,4.224L-2.72,-0.029L-2.08,-0.029L-2.08,4.141L-0.64,4.102L-0.64,-0.029L0,-0.029L0,4.722Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M31.576,5.765L30.18,5.765L30.18,7.196L31.576,7.196L31.576,5.765ZM32.205,9.422C32.205,9.79 32.023,9.974 31.659,9.974L30.096,9.974C29.732,9.974 29.549,9.79 29.549,9.422L29.549,5.775C29.549,5.407 29.732,5.223 30.096,5.223L31.659,5.223C32.023,5.223 32.205,5.407 32.205,5.775L32.205,7.484L31.986,7.713L30.179,7.713L30.179,9.432L31.576,9.432L31.576,8.563L32.205,8.563L32.205,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,34.9887,140.731)">
            <path d="M0,6.23L-0.923,6.23C-1.287,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M37.845,5.765L36.449,5.765L36.449,7.196L37.845,7.196L37.845,5.765ZM38.475,9.422C38.475,9.79 38.292,9.974 37.928,9.974L36.366,9.974C36.001,9.974 35.819,9.79 35.819,9.422L35.819,5.775C35.819,5.407 36.001,5.223 36.366,5.223L37.928,5.223C38.292,5.223 38.475,5.407 38.475,5.775L38.475,7.484L38.255,7.713L36.449,7.713L36.449,9.432L37.845,9.432L37.845,8.563L38.475,8.563L38.475,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,42.0346,145.369)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.094 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.011 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,45.4184,142.762)">
            <path d="M0,3.647C0,4.015 -0.184,4.199 -0.552,4.199L-2.041,4.199C-2.409,4.199 -2.593,4.015 -2.593,3.647L-2.593,2.568L-1.953,2.568L-1.953,3.637L-0.64,3.637L-0.64,2.69L-2.339,1.298C-2.502,1.168 -2.583,0.999 -2.583,0.791L-2.583,-0C-2.583,-0.368 -2.399,-0.552 -2.031,-0.552L-0.562,-0.552C-0.193,-0.552 -0.01,-0.368 -0.01,-0L-0.01,0.947L-0.64,0.947L-0.64,0.009L-1.953,0.009L-1.953,0.839L-0.239,2.231C-0.08,2.358 0,2.53 0,2.749L0,3.647Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,93.614,134.096)">
            <rect x="46.487" y="5.223" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M50.311,5.794L48.88,5.794L48.88,9.402L50.311,9.402L50.311,5.794ZM50.96,9.422C50.96,9.79 50.777,9.974 50.409,9.974L48.778,9.974C48.413,9.974 48.231,9.79 48.231,9.422L48.231,5.775C48.231,5.407 48.413,5.223 48.778,5.223L50.409,5.223C50.777,5.223 50.96,5.407 50.96,5.775L50.96,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,54.7494,139.319)">
            <path d="M0,7.642L-0.649,7.642L-0.649,3.472L-2.08,3.511L-2.08,7.642L-2.729,7.642L-2.729,0L-2.08,0L-2.08,3.023C-1.823,2.99 -1.565,2.958 -1.309,2.925C-0.983,2.883 -0.728,2.862 -0.542,2.862C-0.181,2.862 0,3.039 0,3.394L0,7.642Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M57.826,5.765L56.43,5.765L56.43,7.196L57.826,7.196L57.826,5.765ZM58.455,9.422C58.455,9.79 58.273,9.974 57.909,9.974L56.346,9.974C55.982,9.974 55.799,9.79 55.799,9.422L55.799,5.775C55.799,5.407 55.982,5.223 56.346,5.223L57.909,5.223C58.273,5.223 58.455,5.407 58.455,5.775L58.455,7.484L58.236,7.713L56.429,7.713L56.429,9.432L57.826,9.432L57.826,8.563L58.455,8.563L58.455,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,62.015,145.369)">
            <path d="M0,-1.596L-0.64,-1.596L-0.64,-2.577L-1.836,-2.538L-1.836,1.592L-2.485,1.592L-2.485,-3.158L-1.836,-3.158L-1.836,-3.027C-1.621,-3.059 -1.404,-3.094 -1.187,-3.129C-0.926,-3.168 -0.71,-3.188 -0.537,-3.188C-0.179,-3.188 0,-3.011 0,-2.656L0,-1.596Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,127.564,135.909)">
            <rect x="62.776" y="7.835" width="2.012" height="0.605" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,67.8695,140.438)">
            <path d="M0,5.405L0,2.353L-1.431,2.392L-1.431,5.952L-0.532,5.952C-0.178,5.952 0,5.77 0,5.405M0.649,5.503C0.649,5.796 0.554,6.039 0.361,6.233C0.169,6.426 -0.071,6.523 -0.361,6.523L-2.08,6.523L-2.08,-1.118L-1.431,-1.118L-1.431,1.904C-1.174,1.871 -0.916,1.839 -0.659,1.806C-0.334,1.764 -0.078,1.743 0.107,1.743C0.469,1.743 0.649,1.92 0.649,2.275L0.649,5.503Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,71.6342,142.83)">
            <path d="M0,3.54L0,1.86L-1.45,1.86L-1.45,3.569L0,3.54ZM0.63,4.131L-0.01,4.131L-0.01,3.999C-0.267,4.035 -0.524,4.069 -0.781,4.102C-1.101,4.141 -1.355,4.16 -1.548,4.16C-1.902,4.16 -2.08,3.984 -2.08,3.633L-2.08,1.899C-2.08,1.535 -1.896,1.352 -1.528,1.352L0,1.352L0,-0.078L-1.396,-0.078L-1.396,0.732L-2.026,0.732L-2.026,-0.068C-2.026,-0.436 -1.844,-0.62 -1.479,-0.62L0.083,-0.62C0.447,-0.62 0.63,-0.436 0.63,-0.068L0.63,4.131Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,76.0482,142.239)">
            <path d="M0,4.722L-0.64,4.722L-0.64,4.59C-0.9,4.626 -1.159,4.66 -1.416,4.693C-1.729,4.732 -1.987,4.751 -2.192,4.751C-2.544,4.751 -2.72,4.576 -2.72,4.224L-2.72,-0.029L-2.08,-0.029L-2.08,4.141L-0.64,4.102L-0.64,-0.029L0,-0.029L0,4.722Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M79.134,5.765L77.738,5.765L77.738,7.196L79.134,7.196L79.134,5.765ZM79.764,9.422C79.764,9.79 79.581,9.974 79.217,9.974L77.655,9.974C77.29,9.974 77.108,9.79 77.108,9.422L77.108,5.775C77.108,5.407 77.29,5.223 77.655,5.223L79.217,5.223C79.581,5.223 79.764,5.407 79.764,5.775L79.764,7.484L79.544,7.713L77.738,7.713L77.738,9.432L79.134,9.432L79.134,8.563L79.764,8.563L79.764,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,162.394,134.096)">
            <rect x="80.877" y="5.223" width="0.64" height="7.642" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M84.647,5.765L83.251,5.765L83.251,7.196L84.647,7.196L84.647,5.765ZM85.277,9.422C85.277,9.79 85.094,9.974 84.73,9.974L83.167,9.974C82.803,9.974 82.621,9.79 82.621,9.422L82.621,5.775C82.621,5.407 82.803,5.223 83.167,5.223L84.73,5.223C85.094,5.223 85.277,5.407 85.277,5.775L85.277,7.484L85.057,7.713L83.25,7.713L83.25,9.432L84.647,9.432L84.647,8.563L85.277,8.563L85.277,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,91.1264,142.181)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.065,0.649L-2.065,4.78L-2.71,4.78L-2.71,0.61L-4.126,0.649L-4.126,4.78L-4.775,4.78L-4.775,0.029L-4.126,0.029L-4.126,0.161C-3.872,0.128 -3.618,0.094 -3.364,0.058C-3.055,0.019 -2.8,0 -2.598,0C-2.399,0 -2.253,0.06 -2.158,0.18C-1.885,0.145 -1.613,0.107 -1.343,0.068C-0.988,0.023 -0.72,0 -0.537,0C-0.179,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M94.207,5.765L92.811,5.765L92.811,7.196L94.207,7.196L94.207,5.765ZM94.837,9.422C94.837,9.79 94.655,9.974 94.29,9.974L92.728,9.974C92.364,9.974 92.181,9.79 92.181,9.422L92.181,5.775C92.181,5.407 92.364,5.223 92.728,5.223L94.29,5.223C94.655,5.223 94.837,5.407 94.837,5.775L94.837,7.484L94.618,7.713L92.811,7.713L92.811,9.432L94.207,9.432L94.207,8.563L94.837,8.563L94.837,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,98.641,142.181)">
            <path d="M0,4.78L-0.649,4.78L-0.649,0.61L-2.08,0.649L-2.08,4.78L-2.729,4.78L-2.729,0.029L-2.08,0.029L-2.08,0.161C-1.826,0.128 -1.57,0.094 -1.313,0.058C-1.001,0.019 -0.744,0 -0.542,0C-0.181,0 0,0.177 0,0.532L0,4.78Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,101.4,140.731)">
            <path d="M0,6.23L-0.923,6.23C-1.287,6.23 -1.47,6.046 -1.47,5.678L-1.47,2.05L-2.041,2.05L-2.041,1.479L-1.47,1.479L-1.47,0L-0.83,0L-0.83,1.479L0,1.479L0,2.05L-0.83,2.05L-0.83,5.659L0,5.659L0,6.23Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M104.256,5.765L102.86,5.765L102.86,7.196L104.256,7.196L104.256,5.765ZM104.886,9.422C104.886,9.79 104.704,9.974 104.339,9.974L102.777,9.974C102.413,9.974 102.23,9.79 102.23,9.422L102.23,5.775C102.23,5.407 102.413,5.223 102.777,5.223L104.339,5.223C104.704,5.223 104.886,5.407 104.886,5.775L104.886,7.484L104.666,7.713L102.86,7.713L102.86,9.432L104.256,9.432L104.256,8.563L104.886,8.563L104.886,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,212.516,140.879)">
            <rect x="105.887" y="5.223" width="0.742" height="0.859" style="fill:rgb(35,31,32);"/>
        </g>
        <g transform="matrix(1,0,0,1,109.676,139.939)">
            <path d="M0,6.431L0,2.842L-0.898,2.842C-1.257,2.842 -1.436,3.026 -1.436,3.394L-1.436,5.933C-1.436,6.298 -1.257,6.48 -0.898,6.48C-0.813,6.48 -0.515,6.464 0,6.431M0.645,7.022L0,7.022L0,6.89C-0.696,6.997 -1.082,7.051 -1.157,7.051C-1.434,7.051 -1.657,6.957 -1.826,6.77C-1.995,6.583 -2.08,6.349 -2.08,6.07L-2.08,3.291C-2.08,2.998 -1.984,2.755 -1.792,2.561C-1.6,2.368 -1.359,2.271 -1.069,2.271L0,2.271L0,-0.62L0.645,-0.62L0.645,7.022Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,136.987)">
            <path d="M113.416,5.765L112.02,5.765L112.02,7.196L113.416,7.196L113.416,5.765ZM114.046,9.422C114.046,9.79 113.864,9.974 113.499,9.974L111.937,9.974C111.573,9.974 111.39,9.79 111.39,9.422L111.39,5.775C111.39,5.407 111.573,5.223 111.937,5.223L113.499,5.223C113.864,5.223 114.046,5.407 114.046,5.775L114.046,7.484L113.827,7.713L112.02,7.713L112.02,9.432L113.416,9.432L113.416,8.563L114.046,8.563L114.046,9.422Z" style="fill:rgb(35,31,32);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,7.1615,66.1934)">
            <path d="M0,-6.763L-1.871,-6.763L-1.871,-3.718L-0.148,-3.718L-0.148,-3.077L-1.871,-3.077L-1.871,0.63L-2.584,0.63L-2.584,-7.393L0,-7.393L0,-6.763Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M10.192,85.93L8.726,85.93L8.726,87.432L10.192,87.432L10.192,85.93ZM10.853,89.77C10.853,90.157 10.662,90.349 10.279,90.349L8.638,90.349C8.255,90.349 8.064,90.157 8.064,89.77L8.064,85.94C8.064,85.554 8.255,85.361 8.638,85.361L10.279,85.361C10.662,85.361 10.853,85.554 10.853,85.94L10.853,87.735L10.622,87.975L8.726,87.975L8.726,89.78L10.192,89.78L10.192,88.868L10.853,88.868L10.853,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,14.8471,61.8035)">
            <path d="M0,5.02L-0.682,5.02L-0.682,0.642L-2.185,0.683L-2.185,5.02L-2.866,5.02L-2.866,0.032L-2.185,0.032L-2.185,0.17C-1.917,0.136 -1.649,0.1 -1.379,0.062C-1.051,0.021 -0.781,0 -0.569,0C-0.189,0 0,0.187 0,0.56L0,5.02Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,18.6352,62.4144)">
            <path d="M0,3.83C0,4.216 -0.193,4.409 -0.579,4.409L-2.143,4.409C-2.529,4.409 -2.722,4.216 -2.722,3.83L-2.722,2.697L-2.051,2.697L-2.051,3.819L-0.671,3.819L-0.671,2.825L-2.456,1.364C-2.627,1.227 -2.712,1.049 -2.712,0.83L-2.712,0C-2.712,-0.386 -2.519,-0.579 -2.133,-0.579L-0.589,-0.579C-0.203,-0.579 -0.01,-0.386 -0.01,0L-0.01,0.995L-0.671,0.995L-0.671,0.01L-2.051,0.01L-2.051,0.882L-0.251,2.343C-0.084,2.476 0,2.658 0,2.887L0,3.83Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,21.5121,60.2815)">
            <path d="M0,6.542L-0.969,6.542C-1.353,6.542 -1.543,6.349 -1.543,5.963L-1.543,2.153L-2.144,2.153L-2.144,1.554L-1.543,1.554L-1.543,0L-0.872,0L-0.872,1.554L0,1.554L0,2.153L-0.872,2.153L-0.872,5.942L0,5.942L0,6.542Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M24.511,85.93L23.045,85.93L23.045,87.432L24.511,87.432L24.511,85.93ZM25.172,89.77C25.172,90.157 24.981,90.349 24.598,90.349L22.957,90.349C22.575,90.349 22.383,90.157 22.383,89.77L22.383,85.94C22.383,85.554 22.575,85.361 22.957,85.361L24.598,85.361C24.981,85.361 25.172,85.554 25.172,85.94L25.172,87.735L24.942,87.975L23.045,87.975L23.045,89.78L24.511,89.78L24.511,88.868L25.172,88.868L25.172,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,28.9096,65.1518)">
            <path d="M0,-1.676L-0.671,-1.676L-0.671,-2.707L-1.928,-2.666L-1.928,1.672L-2.609,1.672L-2.609,-3.317L-1.928,-3.317L-1.928,-3.178C-1.701,-3.213 -1.475,-3.248 -1.245,-3.286C-0.972,-3.327 -0.745,-3.348 -0.563,-3.348C-0.187,-3.348 0,-3.161 0,-2.789L0,-1.676Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,66.635,-26.561)">
            <rect x="32.961" y="85.361" width="0.713" height="8.023" style="fill:rgb(0,91,135);"/>
        </g>
        <g transform="matrix(1,0,0,1,41.4428,58.8005)">
            <path d="M0,8.023L-0.713,8.023L-0.713,4.168L-2.646,4.168L-2.646,8.023L-3.358,8.023L-3.358,0L-2.646,0L-2.646,3.527L-0.713,3.527L-0.713,0L0,0L0,8.023Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,44.8002,62.4864)">
            <path d="M0,3.717L0,1.953L-1.522,1.953L-1.522,3.747L0,3.717ZM0.662,4.337L-0.01,4.337L-0.01,4.198C-0.28,4.237 -0.55,4.272 -0.82,4.306C-1.155,4.347 -1.423,4.368 -1.625,4.368C-1.997,4.368 -2.184,4.183 -2.184,3.814L-2.184,1.994C-2.184,1.612 -1.99,1.42 -1.604,1.42L0,1.42L0,-0.082L-1.466,-0.082L-1.466,0.769L-2.127,0.769L-2.127,-0.072C-2.127,-0.458 -1.936,-0.651 -1.553,-0.651L0.088,-0.651C0.471,-0.651 0.662,-0.458 0.662,-0.072L0.662,4.337Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,49.435,61.8665)">
            <path d="M0,4.957L-0.671,4.957L-0.671,4.818C-0.944,4.856 -1.217,4.892 -1.486,4.926C-1.814,4.967 -2.086,4.988 -2.302,4.988C-2.671,4.988 -2.855,4.803 -2.855,4.434L-2.855,-0.031L-2.184,-0.031L-2.184,4.347L-0.671,4.306L-0.671,-0.031L0,-0.031L0,4.957Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,53.2348,62.4144)">
            <path d="M0,3.83C0,4.216 -0.193,4.409 -0.579,4.409L-2.143,4.409C-2.529,4.409 -2.722,4.216 -2.722,3.83L-2.722,2.697L-2.051,2.697L-2.051,3.819L-0.671,3.819L-0.671,2.825L-2.456,1.364C-2.627,1.227 -2.712,1.049 -2.712,0.83L-2.712,0C-2.712,-0.386 -2.519,-0.579 -2.133,-0.579L-0.589,-0.579C-0.203,-0.579 -0.01,-0.386 -0.01,0L-0.01,0.995L-0.671,0.995L-0.671,0.01L-2.051,0.01L-2.051,0.882L-0.251,2.343C-0.084,2.476 0,2.658 0,2.887L0,3.83Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,56.1117,60.2815)">
            <path d="M0,6.542L-0.969,6.542C-1.353,6.542 -1.543,6.349 -1.543,5.963L-1.543,2.153L-2.144,2.153L-2.144,1.554L-1.543,1.554L-1.543,0L-0.872,0L-0.872,1.554L0,1.554L0,2.153L-0.872,2.153L-0.872,5.942L0,5.942L0,6.542Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-24.71)">
            <path d="M59.45,86.104L58.696,86.104L58.696,85.33L59.45,85.33L59.45,86.104ZM58.126,86.104L57.372,86.104L57.372,85.33L58.126,85.33L58.126,86.104ZM59.838,91.533L59.167,91.533L59.167,91.395C58.894,91.433 58.621,91.469 58.352,91.503C58.024,91.544 57.752,91.564 57.537,91.564C57.167,91.564 56.983,91.38 56.983,91.011L56.983,86.545L57.655,86.545L57.655,90.923L59.167,90.882L59.167,86.545L59.838,86.545L59.838,91.533Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,63.56,65.1518)">
            <path d="M0,-1.676L-0.671,-1.676L-0.671,-2.707L-1.928,-2.666L-1.928,1.672L-2.609,1.672L-2.609,-3.317L-1.928,-3.317L-1.928,-3.178C-1.701,-3.213 -1.475,-3.248 -1.245,-3.286C-0.972,-3.327 -0.745,-3.348 -0.563,-3.348C-0.187,-3.348 0,-3.161 0,-2.789L0,-1.676Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M66.555,85.93L65.089,85.93L65.089,87.432L66.555,87.432L66.555,85.93ZM67.216,89.77C67.216,90.157 67.025,90.349 66.642,90.349L65.001,90.349C64.619,90.349 64.427,90.157 64.427,89.77L64.427,85.94C64.427,85.554 64.619,85.361 65.001,85.361L66.642,85.361C67.025,85.361 67.216,85.554 67.216,85.94L67.216,87.735L66.986,87.975L65.089,87.975L65.089,89.78L66.555,89.78L66.555,88.868L67.216,88.868L67.216,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,71.2104,61.8035)">
            <path d="M0,5.02L-0.682,5.02L-0.682,0.642L-2.185,0.683L-2.185,5.02L-2.866,5.02L-2.866,0.032L-2.185,0.032L-2.185,0.17C-1.917,0.136 -1.649,0.1 -1.379,0.062C-1.051,0.021 -0.781,0 -0.569,0C-0.189,0 0,0.187 0,0.56L0,5.02Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,151.705,-26.561)">
            <rect x="75.496" y="85.361" width="0.713" height="8.023" style="fill:rgb(0,91,135);"/>
        </g>
        <g transform="matrix(1,0,0,1,84.0248,58.8005)">
            <path d="M0,8.023L-0.749,8.023L-2.241,3.727L-2.144,3.583L-0.872,3.583L-0.872,0.63L-2.702,0.63L-2.702,8.023L-3.415,8.023L-3.415,0L-0.729,0C-0.561,0 -0.424,0.052 -0.318,0.158C-0.212,0.264 -0.159,0.401 -0.159,0.568L-0.159,3.614C-0.159,4 -0.419,4.193 -0.938,4.193C-0.989,4.193 -1.065,4.19 -1.164,4.186C-1.264,4.18 -1.335,4.178 -1.38,4.178C-0.918,5.456 -0.458,6.738 0,8.023" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M87.193,85.961L85.69,85.961L85.69,89.75L87.193,89.75L87.193,85.961ZM87.874,89.77C87.874,90.157 87.681,90.349 87.295,90.349L85.582,90.349C85.2,90.349 85.008,90.157 85.008,89.77L85.008,85.94C85.008,85.554 85.2,85.361 85.582,85.361L87.295,85.361C87.681,85.361 87.874,85.554 87.874,85.94L87.874,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,178.728,-26.561)">
            <rect x="89.028" y="85.361" width="0.672" height="8.023" style="fill:rgb(0,91,135);"/>
        </g>
        <g transform="matrix(-1,0,0,1,182.47,-26.561)">
            <rect x="90.899" y="85.361" width="0.672" height="8.023" style="fill:rgb(0,91,135);"/>
        </g>
        <g transform="matrix(-1,0,0,1,186.214,-26.561)">
            <rect x="92.771" y="85.361" width="0.672" height="8.023" style="fill:rgb(0,91,135);"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-24.71)">
            <path d="M97.068,86.104L96.314,86.104L96.314,85.33L97.068,85.33L97.068,86.104ZM96.759,90.913L96.759,89.15L95.237,89.15L95.237,90.944L96.759,90.913ZM95.745,86.104L94.991,86.104L94.991,85.33L95.745,85.33L95.745,86.104ZM97.421,91.533L96.749,91.533L96.749,91.395C96.479,91.433 96.209,91.469 95.939,91.503C95.604,91.544 95.336,91.564 95.134,91.564C94.762,91.564 94.576,91.38 94.576,91.011L94.576,89.191C94.576,88.808 94.769,88.616 95.155,88.616L96.759,88.616L96.759,87.114L95.293,87.114L95.293,87.965L94.632,87.965L94.632,87.124C94.632,86.738 94.824,86.545 95.206,86.545L96.847,86.545C97.23,86.545 97.421,86.738 97.421,87.124L97.421,91.533Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,100.697,59.4514)">
            <path d="M0,6.752L0,2.983L-0.943,2.983C-1.319,2.983 -1.507,3.177 -1.507,3.563L-1.507,6.229C-1.507,6.612 -1.319,6.803 -0.943,6.803C-0.854,6.803 -0.54,6.786 0,6.752M0.678,7.372L0,7.372L0,7.233C-0.731,7.347 -1.136,7.403 -1.215,7.403C-1.505,7.403 -1.739,7.305 -1.917,7.108C-2.095,6.912 -2.184,6.667 -2.184,6.372L-2.184,3.455C-2.184,3.148 -2.083,2.892 -1.881,2.689C-1.68,2.485 -1.427,2.384 -1.122,2.384L0,2.384L0,-0.651L0.678,-0.651L0.678,7.372Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M104.624,85.93L103.158,85.93L103.158,87.432L104.624,87.432L104.624,85.93ZM105.286,89.77C105.286,90.157 105.094,90.349 104.711,90.349L103.071,90.349C102.688,90.349 102.496,90.157 102.496,89.77L102.496,85.94C102.496,85.554 102.688,85.361 103.071,85.361L104.711,85.361C105.094,85.361 105.286,85.554 105.286,85.94L105.286,87.735L105.055,87.975L103.159,87.975L103.159,89.78L104.624,89.78L104.624,88.868L105.286,88.868L105.286,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,109.28,61.8035)">
            <path d="M0,5.02L-0.682,5.02L-0.682,0.642L-2.185,0.683L-2.185,5.02L-2.866,5.02L-2.866,0.032L-2.185,0.032L-2.185,0.17C-1.917,0.136 -1.649,0.1 -1.379,0.062C-1.051,0.021 -0.781,0 -0.569,0C-0.189,0 0,0.187 0,0.56L0,5.02Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(-1,0,0,1,227.847,-26.561)">
            <rect x="113.567" y="85.361" width="0.713" height="8.023" style="fill:rgb(0,91,135);"/>
        </g>
        <g transform="matrix(1,0,0,1,122.094,58.8005)">
            <path d="M0,8.023L-0.749,8.023L-2.241,3.727L-2.144,3.583L-0.872,3.583L-0.872,0.63L-2.702,0.63L-2.702,8.023L-3.415,8.023L-3.415,0L-0.729,0C-0.561,0 -0.424,0.052 -0.318,0.158C-0.212,0.264 -0.159,0.401 -0.159,0.568L-0.159,3.614C-0.159,4 -0.419,4.193 -0.938,4.193C-0.989,4.193 -1.065,4.19 -1.164,4.186C-1.264,4.18 -1.335,4.178 -1.38,4.178C-0.918,5.456 -0.458,6.738 0,8.023" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,125.237,62.4864)">
            <path d="M0,3.717L0,1.953L-1.522,1.953L-1.522,3.747L0,3.717ZM0.662,4.337L-0.01,4.337L-0.01,4.198C-0.28,4.237 -0.55,4.272 -0.82,4.306C-1.155,4.347 -1.423,4.368 -1.625,4.368C-1.997,4.368 -2.184,4.183 -2.184,3.814L-2.184,1.994C-2.184,1.612 -1.99,1.42 -1.604,1.42L0,1.42L0,-0.082L-1.466,-0.082L-1.466,0.769L-2.127,0.769L-2.127,-0.072C-2.127,-0.458 -1.936,-0.651 -1.553,-0.651L0.088,-0.651C0.471,-0.651 0.662,-0.458 0.662,-0.072L0.662,4.337Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,128.903,66.2237)">
            <path d="M0,-6.824L-0.964,-6.824L-0.964,-4.388L-0.112,-4.388L-0.112,-3.789L-0.964,-3.789L-0.964,0.6L-1.635,0.6L-1.635,-3.789L-2.215,-3.789L-2.215,-4.388L-1.635,-4.388L-1.635,-6.845C-1.635,-7.231 -1.444,-7.424 -1.061,-7.424L0,-7.424L0,-6.824Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,131.399,66.2237)">
            <path d="M0,-6.824L-0.964,-6.824L-0.964,-4.388L-0.112,-4.388L-0.112,-3.789L-0.964,-3.789L-0.964,0.6L-1.635,0.6L-1.635,-3.789L-2.215,-3.789L-2.215,-4.388L-1.635,-4.388L-1.635,-6.845C-1.635,-7.231 -1.444,-7.424 -1.061,-7.424L0,-7.424L0,-6.824Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,134.696,62.4144)">
            <path d="M0,3.83C0,4.216 -0.193,4.409 -0.579,4.409L-2.143,4.409C-2.529,4.409 -2.722,4.216 -2.722,3.83L-2.722,2.697L-2.051,2.697L-2.051,3.819L-0.671,3.819L-0.671,2.825L-2.456,1.364C-2.627,1.227 -2.712,1.049 -2.712,0.83L-2.712,0C-2.712,-0.386 -2.519,-0.579 -2.133,-0.579L-0.589,-0.579C-0.203,-0.579 -0.01,-0.386 -0.01,0L-0.01,0.995L-0.671,0.995L-0.671,0.01L-2.051,0.01L-2.051,0.882L-0.251,2.343C-0.084,2.476 0,2.658 0,2.887L0,3.83Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,137.573,60.2815)">
            <path d="M0,6.542L-0.969,6.542C-1.353,6.542 -1.543,6.349 -1.543,5.963L-1.543,2.153L-2.144,2.153L-2.144,1.554L-1.543,1.554L-1.543,0L-0.872,0L-0.872,1.554L0,1.554L0,2.153L-0.872,2.153L-0.872,5.942L0,5.942L0,6.542Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M140.628,85.961L139.125,85.961L139.125,89.75L140.628,89.75L140.628,85.961ZM141.31,89.77C141.31,90.157 141.117,90.349 140.731,90.349L139.018,90.349C138.635,90.349 138.444,90.157 138.444,89.77L138.444,85.94C138.444,85.554 138.635,85.361 139.018,85.361L140.731,85.361C141.117,85.361 141.31,85.554 141.31,85.94L141.31,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,145.032,65.1518)">
            <path d="M0,-1.676L-0.671,-1.676L-0.671,-2.707L-1.928,-2.666L-1.928,1.672L-2.609,1.672L-2.609,-3.317L-1.928,-3.317L-1.928,-3.178C-1.701,-3.213 -1.475,-3.248 -1.245,-3.286C-0.972,-3.327 -0.745,-3.348 -0.563,-3.348C-0.187,-3.348 0,-3.161 0,-2.789L0,-1.676Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,0,-23.526)">
            <path d="M148.026,85.93L146.56,85.93L146.56,87.432L148.026,87.432L148.026,85.93ZM148.687,89.77C148.687,90.157 148.496,90.349 148.113,90.349L146.472,90.349C146.089,90.349 145.898,90.157 145.898,89.77L145.898,85.94C145.898,85.554 146.089,85.361 146.472,85.361L148.113,85.361C148.496,85.361 148.687,85.554 148.687,85.94L148.687,87.735L148.456,87.975L146.56,87.975L146.56,89.78L148.026,89.78L148.026,88.868L148.687,88.868L148.687,89.77Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
        <g transform="matrix(1,0,0,1,152.681,61.8035)">
            <path d="M0,5.02L-0.682,5.02L-0.682,0.642L-2.185,0.683L-2.185,5.02L-2.866,5.02L-2.866,0.032L-2.185,0.032L-2.185,0.17C-1.917,0.136 -1.649,0.1 -1.379,0.062C-1.051,0.021 -0.781,0 -0.569,0C-0.189,0 0,0.187 0,0.56L0,5.02Z" style="fill:rgb(0,91,135);fill-rule:nonzero;"/>
        </g>
    </g>
</svg>
