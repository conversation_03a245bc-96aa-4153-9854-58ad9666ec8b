<html>

<?php 
require_once ABS_PATH.'class/templateHelper.php';
$data = $this->data;
$bestelllosData = $data['positions'];
$supplierData = $data['supplierData'];
?> 
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Bestellung</title>
	</head>
	<body>
		<table class="header">
			<tbody>
				<tr>
					<th class="bestellungText" align="left" valign="center" width="25%">Bestellung</th>
					<th align="right" valign="bottom" width="75%" class="logo">
						<br><br>
						<img src="<?=WEB_ROOT_API.'vendor\printouts\Bestellung\Briefkopf.svg?>'?>" alt="logo" class="logo" width="150%" height="140%">
					</th>
				</tr>
			</tbody>
		</table>
		<table class="table">
			<tr>
				<td align="left" valign="top" width="40%">
                    <?=$supplierData['title'];?>
                    <br>
                    <?=$supplierData['name'];?>
                    <br>
					<?=$supplierData['address'];?>
                    <br>
					<?=$supplierData['postcode'].' '.$supplierData['city'];?>
                    <br><br><br><br>
                </td>
				<td width="20%"><?php str_repeat('&nbsp' ,2);?></td>
				<td align="left" valign="top" width="20%"></td>
                <td width="20%"></td>
			</tr>
			<tr>
				<td class="blueText">Kommission:</td>
				<td class="blueText">Bestellnummer:</td>
                <td class="blueText">Datum:</td>
                <td class="blueText">Sachbearbeiter:</td>
			</tr>
			<tr>
				<td><?=trim($data['projectData']['internalProjectNo'].'-'.$data['projectData']['customer_kname'].'-'.$data['projectData']['commissionNumber'], '-');?></td>
				<td><?=$data['artificialKey'];?></td>
				<td><?=date('d.m.Y',strtotime($data['pdfGenerationDate']));?></td>
                <td><?=$data['author'];?></td>
			</tr>
		</table>
		<br>
		<div>Bitte geben Sie auf allen Belegen unsere Kommission und unsere Bestellnummer an.</div>
		<br>
		<div>Sehr geehrte Damen und Herren, <br> hiermit bestellen wir folgende Materialien bzw. Leistungen</div>
		<br>
		<div>
			<span class="blueText">Liefertermin:</span>
			<span><?=date('d.m.Y',strtotime($bestelllosData['Soll-Termin'])). ' '.$bestelllosData['Soll-Uhrzeit'];?></span>
		</div>
		<div>
			<span class="blueText">Lieferort:</span>
			<span><?=$bestelllosData['Lieferort'];?></span>
		</div>
		<br>
		<table class="positions">
            <tr>
                <td class="blueText">
                    Position
                </td>
                <td class="blueText">
                    Titel
                </td>
                <td class="blueText">
                    Menge
                </td>
                <td class="blueText">
                    Anlage
                </td>
            </tr>
		<?php $positionCounter = '1';?>
			<?php foreach ($bestelllosData as $k => $v) {?>
				<?php if(strpos($k, 'Position') !== false) { ?>
					<tr>
						<td width="25%" valign="top" align="left"><?='Position '.$positionCounter?></td>
						<td width="25%" valign="top" align="left"><?=$v['Titel'];?></td>
                        <td width="25%" valign="top" align="left"><?=$v['Menge'];?></td>
						<td width="25%" valign="top" align="left">
							<?php if($v['Anhang']) { ?>
                                ✔️
							<?php } ?>
						</td>
					</tr>
				<?php $positionCounter++;} ?>
			<?php } ?>
		</table>
	</body>
	<br>
	<div align="left">Wir bitten um Auftrags- und Terminbestätigung</div>
	<div align="left">Vielen Dank.</div>
	<br>
	<div>Freundliche Grüße</div>
	<div><b>Gütersloher Bauelemente GmbH</b></div>
</html>

<style>
	@font-face {
  				font-family: 'AgencyFbBold';
  				src: url(<?=ABS_PATH;?>.'vendor/printouts/Bestellung/ufonts.com_agency-fb-bold.ttf') format('truetype');	
	}
  
	.header, .table, .positions {width:100%; font-size:large!important;}
	.positions {border-collapse:collapse!important;}
	.bestellungText {color:#005988; font-size:46px; font-weight:bold;}
	.blueText {font-weight:bold; color:#005988; font-size:large}
	.logo {top:10px;}
	.blankRow{height: 50px !important;}
	.positions tr {font-size:large;}
	body {font-family: AgencyFbBold; margin-top:10px!important;}
</style>
