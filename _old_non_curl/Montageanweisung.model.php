<?php
require_once ABS_PATH.'Documentation.php';

class C_Montageanweisung {
private $db;
private $documentation;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
	}

	
	public function getData($schemaId,$documentId) {
		
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		$templateData = [];
		$templateData['documentDate'] = $documentData['documentCreatedOn'];
		$templateData['documentId'] = $documentId;
		$parents = [];
		
		foreach ($documentData['children'] as $k=>$v) {
			
			if($v['title'] == 'Ansprechpartner Baustelle') {
				$parents[$v['id']] = $v['title'];
			}
			
			if($v['title'] == 'Weisungsbefugt Projektleiter') {
				$parents[$v['id']] = $v['title'];
			}
			
			if($v['title'] == 'SiGeKo') {
				$parents[$v['id']] = $v['title'];
			}
		}
		
		
		foreach ($parents as $key => $parent) {
			foreach ($documentData['children'] as $ch=>$child) {
				if($child['parentId'] == $key && $child['type'] != 'headline') {
					$templateData['parents'][$parent][$child['title']] = $child['reportedValue'];
				}
			}
		}
		
		foreach ($documentData['children'] as $key=>$child) {
			if($child['type'] == 'signatureField') {
				$templateData['nonParents'][$child['title']] = $child['filePath'];			
			} else {
				$templateData['nonParents'][$child['title']] = $child['reportedValue'];
			}
		}
		
		$cms = $this->getCmValues($schemaId, $documentId);
		
		//relation between Giebel-> in cm and Traufe -> in cm have not temain the same. A fix is needed in the schemaPosition id-s. Workarround for the presentation
		$templateData['Dachüberstand NEU']['Giebel']['cm'] = $cms[5]['value'];
		$templateData['Dachüberstand NEU']['Giebel']['value'] = $cms[4]['value'];
		$templateData['Dachüberstand NEU']['Traufe']['cm'] = $cms[7]['value'];
		$templateData['Dachüberstand NEU']['Traufe']['value'] = $cms[6]['value'];
		$templateData['Dachüberstand ALT']['Giebel']['cm'] = $cms[1]['value'];
		$templateData['Dachüberstand ALT']['Giebel']['value'] = $cms[0]['value'];
		$templateData['Dachüberstand ALT']['Traufe']['cm'] = $cms[3]['value'];
		$templateData['Dachüberstand ALT']['Traufe']['value'] = $cms[2]['value'];
		
		return $templateData;
	
	}
	
	//relation between Giebel-> in cm and Traufe -> in cm have not temain the same. A fix is needed in the schemaPosition id-s 
	private function getCmValues($schemaId, $documentationId) {
		$stmt = $this->db->prepare("
			   SELECT dsp.title, ddp.value,
			  (SELECT title FROM documentation_schema_positions AS dsp2 WHERE dsp2.id = dsp.parentId) as Parent
			  FROM documentation_schema_positions as dsp
			  LEFT JOIN documentation_document_positions as ddp ON dsp.id = ddp.schemaPositionId
			  WHERE dsp.schemaId = :schemaId AND dsp.title in ('Dachüberstand NEU','Dachüberstand ALT', 'Giebel', 'Traufe', 'in cm') 
			  and ddp.documentationId = :documentationId
		");
		$stmt->execute(['schemaId' => $schemaId, 'documentationId' => $documentationId]);
		
		$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
		
		return $data;
	}
}