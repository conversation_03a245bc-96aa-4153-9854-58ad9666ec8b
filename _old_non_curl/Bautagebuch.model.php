<?php
	require_once ABS_PATH.'Hours.php';
	require_once ABS_PATH.'Documentation.php';
	require_once ABS_PATH.'Addresses.php';
	
class C_Bautagebuch {
	private $db;
	private $hours;
	private $documentation;
	private $addresses;
	
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->hours = new Hours();
		$this->documentation = new Documentation();
		$this->addresses = new Addresses();
		$this->documentationModel = new doc_schema_positions();
	}
	
	public function getData($schemaId, $documentId) {
		
		$templateData = [];
		
		$documentData = $this->documentation->getDocumentId($schemaId, $documentId);
		
		$templateData['documentDate'] = date('d.m.Y',strtotime($documentData['documentCreatedOn']));
		$templateData['projectNo'] = $documentData['documentRelKey1'];
		$templateData['documentId'] = $documentId;
		
		if(!empty($documentData)) {
			foreach ($documentData['children'] as $k=>$v) {
				$ids[$v['id']] = $v;
			}
		
			foreach ($ids as $key => $value) {
				switch ($value['indentationLevel']) {
					case 4:
						if($value['type'] == 'date') {
							$templateData[$ids[$ids[$ids[$value['parentId']]['parentId']]['title']]][$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = date('d.m.Y',strtotime($value['reportedValue'])); 
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['filePath'];
						} else {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['reportedValue'];
						}
						break;
					case 3:
						if($value['type'] == 'date') {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = date('d.m.Y',strtotime($value['reportedValue'])); 
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['filePath'];
						} else {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['reportedValue'];
						}
						break;
					case 2:
						if($value['type'] == 'date') {
							$templateData[$ids[$value['parentId']]['title']][$value['title']] = date('d.m.Y',strtotime($value['reportedValue'])); 
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$ids[$value['parentId']]['title']][$value['title']] = $value['filePath'];
						} else {
							$templateData[$ids[$value['parentId']]['title']][$value['title']] = $value['reportedValue'];
						}
						break;
					 default:
						if($value['type'] == 'date') {
							$templateData[$value['title']] = date('d.m.Y',strtotime($value['reportedValue'])); 
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$value['title']] = $value['filePath'];
						} else {
						    // handle combobox-multi reportedValues-Array
						    if($value['type'] === 'combobox-multi')
                                $templateData[$value['title']] = implode(", ",$value['reportedValues']);
                            else
							    $templateData[$value['title']] = $value['reportedValue'];
						} 	
				}	
			}
		} else {
			return [];
		}
		return $templateData;		
	}
}
