<html>

<?php
    $data = $this->data;
?>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Media Print</title>
</head>
<body>

<?php

    $imagesPerPage = $data['imagesPerPage'];

        foreach ($data['files'] as $key => $value) {

            // TODO: I see 3 times almost similiar code, just the height of the image seems to change, this can be heavily simplified
            if ($imagesPerPage == 1) { ?>
                <p style="page-break-before: always"></p>
                <div class="oneImgPerPage">
                    <table>
<?=                     '<tr><td class="img"> <img src="'.$value['filepath'].'" alt="image" height="700px" width="auto" /> </td></tr>' ?>
                        <tr><td class="textSize"><b><?=lang('title')?>:</b> <?php echo (!empty($value['postDescription'])) ? $value['postDescription'] : $value['dbDescription'] ?> </td></tr>
<?php           if ($value['showWorkingOrderNumber']) { ?>
                            <tr><td class="textSize"><b><?=lang('mail_checklist_documentation_th_workingOrderNo')?>:</b> <?= $value['rel_key'] ?> </td></tr>
<?php           } ?>
<?php           if ($value['showUploader'] && $value['uploaderName'] != '') { ?>
                            <tr><td class="textSize"><b><?=lang('mail_checklist_documentation_th_author')?>:</b> <?= $value['uploaderName'] ?> </td></tr>
<?php           } ?>
<?php           if ($value['showUploadDate']) { ?>
                            <tr><td class="textSize"><b><?=lang('uploaded_at')?>:</b> <?= date('d.m.Y H:i:s',strtotime($value['upload_time'])) ?> </td></tr>
<?php           } ?>
<?php           if ($value['showCreationDate']) { ?>
                            <tr><td class="textSize"><b><?=lang('taken_at')?>:</b> <?= date('d.m.Y H:i:s',strtotime($value['creation_time'])) ?> </td></tr>
<?php           } ?>
<?php           if (!empty($value['comments']) && $value['showComments']) { ?>
                            <tr>
                              <td class="textSize"><b><?=lang('comments')?>:</b> <br />
<?php                           foreach ($value['comments'] as $k => $v) { ?>
<?=                                 $v['commentAuthor']. '(' .date('d.m.Y H:i:s',strtotime($v['createdOn'])). '):' .$v['commentText']. '<br />' ?>
<?php                           } ?>
                              </td>
                            </tr>
<?php           } ?>
                    </table>
                </div>
<?php      } ?>

<?php      if ($imagesPerPage == 2) { ?>
                <div class="twoImgPerPage">
                    <table>
<?=                     '<tr><td class="img"> <img src="'.$value['filepath'].'" alt="image" height="450px" width="auto" /> </td></tr>' ?>
                        <tr><td class="textSize"><b><?=lang('title')?>:</b> <?php echo (!empty($value['postDescription'])) ? $value['postDescription'] : $value['dbDescription'] ?> </td></tr>
<?php           if ($value['showWorkingOrderNumber']) { ?>
                            <tr><td><b><?=lang('mail_checklist_documentation_th_workingOrderNo')?>:</b> <?= $value['rel_key'] ?> </td></tr>
<?php           } ?>
<?php           if ($value['showUploader'] && $value['uploaderName'] != '') { ?>
                            <tr><td><b><?=lang('mail_checklist_documentation_th_author')?>:</b> <?= $value['uploaderName'] ?> </td></tr>
<?php           } ?>
<?php           if ($value['showUploadDate']) { ?>
                            <tr><td><b><?=lang('uploaded_at')?>:</b> <?= date('d.m.Y H:i:s',strtotime($value['upload_time'])) ?> </td></tr>
<?php           } ?>
<?php           if ($value['showCreationDate']) { ?>
                            <tr><td><b><?=lang('taken_at')?>:</b> <?= date('d.m.Y H:i:s',strtotime($value['creation_time'])) ?> </td></tr>
<?php           } ?>
<?php           if (!empty($value['comments']) && $value['showComments']) { ?>
                            <tr>
                              <td><b><?=lang('comments')?>:</b> <br />
<?php                           foreach ($value['comments'] as $k => $v) { ?>
<?=                                 $v['commentAuthor']. '(' .date('d.m.Y H:i:s',strtotime($v['createdOn'])). '):' .$v['commentText']. '<br />' ?>
<?php                           } ?>
                              </td>
                            </tr>
<?php           } ?>
                    </table>
                </div>
<?php       }

            if ($imagesPerPage == 4) { ?>
                <div class="fourImgPerPage">
                        <table>
<?=             '<tr><td class="img"> <img src="'.$value['filepath'].'"  alt="image"/> </td></tr>' ?>
                            <tr><td class="textSize"><b><?=lang('title')?>:</b> <?php echo (!empty($value['postDescription'])) ? $value['postDescription'] : $value['dbDescription'] ?> </td></tr>
<?php           if ($value['showWorkingOrderNumber']) { ?>
                            <tr><td class="textSize"><b><?=lang('mail_checklist_documentation_th_workingOrderNo')?>:</b> <?= $value['rel_key'] ?> </td></tr>
<?php           } ?>
<?php           if ($value['showUploader'] && $value['uploaderName'] != '') { ?>
                            <tr><td class="textSize"><b><?=lang('mail_checklist_documentation_th_author')?>:</b> <?= $value['uploaderName'] ?> </td></tr>
<?php           } ?>
<?php           if ($value['showUploadDate']) { ?>
                            <tr><td class="textSize"><b><?=lang('uploaded_at')?>:</b> <?= date('d.m.Y H:i:s',strtotime($value['upload_time'])) ?> </td></tr>
<?php           } ?>
<?php           if ($value['showCreationDate']) { ?>
                            <tr><td class="textSize"><b><?=lang('taken_at')?>:</b> <?= date('d.m.Y H:i:s',strtotime($value['creation_time'])) ?> </td></tr>
<?php           } ?>
<?php           if (!empty($value['comments']) && $value['showComments']) { ?>
                            <tr>
                                <td class="textSize"><b><?=lang('comments')?>:</b> <br />
<?php                           foreach ($value['comments'] as $k => $v) { ?>
<?=                                 $v['commentAuthor']. '(' .date('d.m.Y H:i:s',strtotime($v['createdOn'])). '):' .$v['commentText']. '<br />' ?>
<?php                           } ?>
                                </td>
                            </tr>
<?php           } ?>
                        </table>
                </div>
<?php        } ?>

<?php   } ?>


  <style type="text/css" media="screen,print">
    @font-face {
        font-family: 'seguiemj';
        src: url('<?=ABS_PATH ?>printouts/seguiemj.ttf') format('truetype');
    }

    html, body {
        font-family: seguiemj, sans-serif;
        margin: 0;
    }

    .oneImgPerPage {
        margin-left: 70px;
        margin-top: 5px;
        height: 800px;
    }
    .oneImgPerPage .textSize {
        font-size: 25px;
    }
    .twoImgPerPage {
        margin-left: 70px;
        margin-top: 5px;
        height: 640px;
    }

    .fourImgPerPage {
        margin-left: 10px;
        margin-top: 10px;
        width: 45%;
        height: 50%;
        float: left;
    }
    .fourImgPerPage td img{
        max-height: 400px;
        max-width: 300px;
    }
    .fourImgPerPage .textSize {
        font-size: 20px;
    }

    table { width: 80%; }

    img { margin-top: 5px; }
  </style>

</body>
</html>