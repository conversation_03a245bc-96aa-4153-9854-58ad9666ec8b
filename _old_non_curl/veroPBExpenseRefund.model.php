<?php
require_once ABS_PATH.'Documentation.php';

class C_veroPBExpenseRefund {

	private $documentation;

	public function __construct() {
		$this->documentation = new Documentation();
	}

	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		#die(json_encode($documentData));
		// TODO: redudant code in VeroDSTravelOrder.model.php
		if($documentData) {
			$children = $documentData['children'];
			$printIndicedChildrenArray = array();
			// if values are bundled in the reportedValues-array
			// they have to be deducted to have the template working properly
			$printIndexDeduction = 0;
			// capture invoiceYear
			$invoiceYear = null;
			foreach($children as $child)
			{
				// deduct the printIndex, if needed
				$printIndex = $child['printIndex'] - $printIndexDeduction;
				// in case printIndex is missing we skip that $child
				if(!$printIndex || $printIndex < 0)
					continue;
				$type = $child['type'];
				// handle multiple values inside reportedValues-Array
				if(count($child['reportedValues']) == 1)
					$reportedValue = $child['reportedValues'][0];
				else
				{
					$reportedValue = implode(',',$child['reportedValues']);
					// maintain printIndexDeduction
					// by counting the reportedValues of this $child and deducting it by 1
					$printIndexDeduction += count($child['reportedValues'])-1;
				}
				// for photo and signature the full data is needed
				if (in_array($type, ['photo', 'signatureField'])) {
					// for testing on local machine
					// $child['filePath'] = 'http://api.baubuddy.de/infomaterial/Dokumente_vero_test/DYNAMICDOCUMENTATION_37/Bilder/2019-06-12_09-26-32--289-37-5C84FFCE-07EE-4AED-B7C0-950257E55C76.jpg';
					$printIndicedChildrenArray[$printIndex] = $child;
				} else {
					if($type == 'date') {
						$reportedValue = new DateTime($reportedValue);
					}
					elseif($type == 'INVOICESELECTOR')
					{
						// unset reportedValue
						$reportedValue = "";
						foreach ($child['reportedValues'] as $singleReportedValue)
						{
							$parts = explode("-",$singleReportedValue);
							$reportedValue .= $parts[0].",";
							if(!$invoiceYear)
								$invoiceYear = (int) $parts[1];
						}
						// remove last comma
						$reportedValue = substr($reportedValue,0,-1);
					}
					$printIndicedChildrenArray[$printIndex] = $reportedValue;
				}
			}

			// fetch principals customerNo to have the name for proper display
			require_once ABS_PATH.'class/param01.class.php';
			$param01DB = new Param01DB();
			$companyInfo = $param01DB->get_settings_info();
			$printIndicedChildrenArray[] = $companyInfo['firma1'];
			$printIndicedChildrenArray[] = '<br>'.$companyInfo['firma2'];

			// fetch and handle invoices
			$invoiceIds = explode(',',$printIndicedChildrenArray[6]);
			require_once ABS_PATH.'Invoices.php';
			$invoicesApi = new Invoices();

			require_once ABS_PATH.'Files.php';
			$filesApi = new Files();

			// calculate year to call invoices
			$creationDate = DateTime::createFromFormat('Y-m-d\TH:i:s\Z',$documentData['documentCreatedOn']);
			$year = $invoiceYear;
			$total = 0;
			require_once ABS_PATH.'Addresses.php';
			$addressesAPI = new Addresses();
			$pdfsToMerge = array();
			foreach ($invoiceIds as $invoiceId) {
				$invoice = $invoicesApi->get($year,trim($invoiceId));

				// get amount from invoice
				$invoiceAmount = ($invoice['brutto'] > 0)? $invoice['brutto'] : $invoice['netto'];

				// complete suppliers data if missing
				if(empty($invoice['kname']) && !empty($invoice['knr']))
				{
					$address = $addressesAPI->get($invoice['knr']);
					$invoice['kname'] = $address['searchName'];
					$invoice['kort'] = $address['city'];
				}
				// format date properly
				$invoice['rechdatum'] = date('d.m.Y',strtotime($invoice['rechdatum']));
				$invoice['amount'] = number_format($invoiceAmount,2);
				$invoiceFile = $filesApi->get('inv',$invoiceId)[0];
				$invoice['file'] = $invoiceFile;

				// add invoice to array
				$printIndicedChildrenArray['invoices'][] = $invoice;

				// convert Lev to €
				if($invoice['dsymbol'] == 'lv')
					$invoiceAmount = $invoiceAmount/1.95583;
				// add amount to total
				$total = $total + $invoiceAmount;
			}
			$printIndicedChildrenArray[] = $total;

			// fetch employeeDetails from author to add bank details
			require_once ABS_PATH.'v3/Employees.php';
			$employeesAPI = new v3\Employees();
			$employee = $employeesAPI->get($documentData['author'])[0];
			$printIndicedChildrenArray[] = $employee['bankName'];
			$printIndicedChildrenArray[] = $employee['iban'];
			$printIndicedChildrenArray[] = $employee['bic'];
			$printIndicedChildrenArray[] = $employee['location'];
			$printIndicedChildrenArray[] = $creationDate;
			return $printIndicedChildrenArray;
		} else {
			throw new RestException(400, "No such document id!");
		}
	}
}
