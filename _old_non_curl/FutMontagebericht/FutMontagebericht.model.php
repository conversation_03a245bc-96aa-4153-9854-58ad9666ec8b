<?php
require_once ABS_PATH.'Hours.php';
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Addresses.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';
require_once ABS_PATH.'v3/Employees.php';
require_once ABS_PATH.'printouts/printout.helper.php';
	
class C_FutMontagenbericht {
	private $db;
	private $hours;
	private $documentation;
	private $addresses;
	private $projects;
	private $customers;
	private $helper;
	private $employees;
	
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->hours = new Hours();
		$this->documentation = new Documentation();
		$this->addresses = new Addresses();
		$this->projects = new Projects();
		$this->customers = new Customers();
		$this->helper = new PrintoutHelper();
		$this->employees = new \v3\Employees();
	}
	
	public function getData($schemaId, $documentId) {
		
		$templateData = [];
		
		$documentData = $this->documentation->getDocumentId($schemaId, $documentId);
		
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		
		
		array_unshift($documentData['children'], $main);
		
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		
		$templateData = $this->helper->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
		
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		
		if(!empty($projectData)) {
			$customerData = $this->getCustomerData($projectData['knr']);
		}
		
		if($customerData) {
			isset($customerData['name']) ? $templateData['customerName'] = $customerData['name'] : $templateData['customerName'] = ''; 
			
			isset($customerData['ort']) ? $templateData['customerPlace'] = $customerData['ort'] : $templateData['customerPlace'] = '';
			
			$templateData['customerAddress'] = '';
			
			if(isset($customerData['plz'])) {
				$templateData['customerAddress'] .= $customerData['plz'].' ';
			}
			
			if(isset($customerData['adresse1'])) {
				$templateData['customerAddress'] .= $customerData['adresse1'];
			}
			
			$templateData['customerAddress'] = trim($templateData['customerAddress']);
		}
		
		if($projectData) {
			$templateData['projectName'] = $projectData['project_name'];
			$templateData['projectDate'] = date('d.m.Y',strtotime($projectData['start_date']));
			$templateData['projectNumber'] = $projectData['ktr'];
		}
		
		$templateData['documentId'] = $documentId;
		
		$author = $this->employees->get($documentData['author'],'','','all','');
		
		$templateData['dcumentAuthor'] = $author[0]['firstName'].' '.$author[0]['lastName'];
		
		return $templateData;
	}
	
	private function getProjectData($projectNo) {
		$result = $this->projects->get($projectNo);
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		
		return end($projects);
	}	
	
	private function getCustomerData($customerNo) {
		$customerData = $this->customers->get($customerNo);
		return $customerData;
	}
}
