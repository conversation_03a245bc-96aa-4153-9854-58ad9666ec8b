<!DOCTYPE HTML>

<?php
	$data = $this->data;
		
		function renderCheckbox($checkboxTitle, $dbValue) {
			if(strtolower($dbValue) == 'ja' || $dbValue == '1' || $dbValue == 'true' || $dbValue== 'on') {
				return '<img src="'.WEB_ROOT_API.'vendor\printouts\checked.png?>"'.'alt="checked" width="auto" height="15%">'.$checkboxTitle;
				} else {
				return '<img src="'.WEB_ROOT_API.'vendor\printouts\unchecked.png?>"'.'alt="checked" width="auto" height="15%">'.$checkboxTitle;
				}
		}

?>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Fut Montagebericht</title>
</head>
	<body>
		<div align="center" class="title"><b><u><PERSON>agebericht und Abnahmebescheinigung</u></b> <?=str_repeat('&nbsp;', 3);?><b>#<?=$data['documentId'];?></b></div>
		<br><br><br>
		<table class="data">
			<tr>
                <td colspan="2">Auftraggeber: <?=$data['customerName'];?></td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></tr>
			<tr>
				<td align="left">Ort: <?=$data['customerPlace'];?></td>
				<td align="left">Strasse: <?=$data['customerAddress'];?></td>
			</tr>
			<tr><td colspan="2" style="border-top: 1px solid lightgray"><?=str_repeat('&nbsp;', 3);?></td></tr>
			<tr>
				<td colspan="2">Die Firma F&T Alu- Technik GmbH hat folgende Leistung(en) auftragsgemäß fertig gestellt.</td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></tr>
			<tr>
				<td colspan="2"><?=$data['Montagebericht und Abnahmebescheinigung']['Beschreibung'];?></td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></tr>
			<tr>
				<td>Tag der Fertigstellung:</td>
				<td align="left">Monteur-Name/Montagefirma</td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></tr>
			<tr>
				<td><?=date('d.m.Y',strtotime($data['Montagebericht und Abnahmebescheinigung']['Datum']));?></td>
				<td align="left"><?=$data['dcumentAuthor'];?></td>
			</tr>
            <tr><td colspan="2" style="border-top: 1px solid lightgray"><?=str_repeat('&nbsp;', 3);?></td></tr>
		</table>
		<br>
		<div align="left"><b><u>Zutreffendes bitte ankreuzen!</u></b></div>
		<br>
		<div align="left">Zum Zwecke der Abnahme find eine Prüfung der montierten Anlagen statt.</div>
		<br>
		<table class="checkboxes">
			<tr>
				<td><?=renderCheckbox('Fenster', $data['Montagebericht und Abnahmebescheinigung']['Abnahme']['Fenster']);?></td>
				<td><?=renderCheckbox('Wintergärten', $data['Montagebericht und Abnahmebescheinigung']['Abnahme']['Wintergärten']);?></td>
				<td><?=renderCheckbox('Terrassendach', $data['Montagebericht und Abnahmebescheinigung']['Abnahme']['Terassendach']);?></td>
				<td><?=renderCheckbox('Sonstiges', $data['Montagebericht und Abnahmebescheinigung']['Abnahme']['Sonstiges']);?></td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></td><td></td><td></td></tr>
			<tr>
				<td><b><u>Anwesend waren:</u></b></td>
				<td><?=renderCheckbox('Auftraggebber', $data['Montagebericht und Abnahmebescheinigung']['Anwesend waren']['Auftraggeber']);?></td>
				<td><?=renderCheckbox('Architekt',  $data['Montagebericht und Abnahmebescheinigung']['Anwesend waren']['Architekt']);?></td>
				<td><?=renderCheckbox('Monteur',  $data['Montagebericht und Abnahmebescheinigung']['Anwesend waren']['Monteur']);?></td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></td><td></td><td></td></tr>
			<tr>
				<td><?=renderCheckbox('Innen besehen', $data['Montagebericht und Abnahmebescheinigung']['Örtlichkeit']['Innen besehen']);?></td>
				<td><?=renderCheckbox('Außen besehen', $data['Montagebericht und Abnahmebescheinigung']['Örtlichkeit']['Außen besehen']);?></td>
				<td colspan="2"><?=renderCheckbox('komplette Bedienungskontrolle',  $data['Montagebericht und Abnahmebescheinigung']['Örtlichkeit']['Komplette Bedienungskontrolle']);?></td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></td><td></td><td></td></tr>
			<tr>
				<td class="ergebnis" colspan="4">
				<?php $string1 = 'Die montieren Anlagen werden als einwandfrei befunden und gelten somit nach § 640'; $string2 = 'sofern die VOB Vertragsbestandteil  ist, auch nach VOB Teil B, §12 als Abgenommen.'; ?>
					<b><u>Ergebnis:</u></b><?=str_repeat('&nbsp;', 3).$string1;?>
					<br>
					<b class="hiddenErgebnis"><u>Ergebnis:</u></b><?=str_repeat('&nbsp;', 3).$string2;?> 
					
				</td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></td><td></td><td></td></tr>
			<tr>
				<td colspan="4"><?=renderCheckbox('Die montieren Anlagen werden unter folgenden Vorbehalt abgenommen', $data['Montagebericht und Abnahmebescheinigung']['Die montieren Anlagen werden unter folgenden Vorbehalt abgenommen']);?></td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></td><td></td><td></td></tr>
			<tr>
				<td colspan="1" valign="top"></td>
					<td colspan="3" align="left">
						<?php if(file_exists($data['Montagebericht und Abnahmebescheinigung']['Position 1']['Fotos'])) { ?>
						<?php echo '<img src="'.$data['Montagebericht und Abnahmebescheinigung']['Position 1']['Fotos'].'" alt="" class="positionPhotos"'; ?>
				 	<?php } else {echo '';}?>
				 	</td>
				 	
			</tr>
			<tr>
				<td colspan="1" valign="top"></td>
					<td colspan="3" align="left">
						<?php if(file_exists($data['Montagebericht und Abnahmebescheinigung']['Position 2']['Fotos'])) { ?>
						<?php echo '<img src="'.$data['Montagebericht und Abnahmebescheinigung']['Position 2']['Fotos'].'" alt="" class="positionPhotos"'; ?>
				 	<?php } else {echo '';}?>
					</td>
			</tr>
			<tr><td><?=str_repeat('&nbsp;', 3);?></td><td></td><td></td><td></td></tr>
			<tr>
				<td colspan="4">Durch nachfolgende Unterschrift wird das Ergebnis als verbindlich anerkannt:</td>
			</tr>
		</table>
		<br><br>
		<table class="signatures">
			<tr>
				<td width="32%" align="center" valign="bottom"><?=date('d.m.Y',strtotime($data['Montagebericht und Abnahmebescheinigung']['Datum']));?></td>
				<td width="2%"></td>
				<td width="32%" align="center">
					<?php if(file_exists($data['Montagebericht und Abnahmebescheinigung']['Unterschriften']['Monteur Unterschrift'])) { ?>
						<?php echo '<img src="'.$data['Montagebericht und Abnahmebescheinigung']['Unterschriften']['Monteur Unterschrift'].'" alt="" class="positionPhotos"'; ?>
				 	<?php } else {echo '';}?>
					</td>
				<td width="2%"></td>
				<td width="32%" align="center">
					<?php if(file_exists($data['Montagebericht und Abnahmebescheinigung']['Unterschriften']['Auftraggeber Unterschrift'])) { ?>
						<?php echo '<img src="'.$data['Montagebericht und Abnahmebescheinigung']['Unterschriften']['Auftraggeber Unterschrift'].'" alt="" class="positionPhotos"'; ?>
				 	<?php } else {echo '';}?>
					</td>
			</tr>
			<tr>
				<td align="center">Ort/Datum</td>
				<td></td>
				<td align="center">Monteur/Firma</td>
				<td></td>
				<td align="center">Auftraggeber</td>
			</tr>
		</table>
		<br>
		<table class="footer">
			<tr>
				<th align="center" colspan="2">Dokumentation im Rahmen der WPK nach EN 1090-3</th>
			</tr>
			<tr>
				<td>
					Maße geprüft und Toleranzen eingehalten nach der: <br>
					<table class="innerCheckboxes">
						<tr>
							<td align="left"><?=renderCheckbox('Fertigung', $data['Montagebericht und Abnahmebescheinigung']['Dokumentation im Rahmen der WPK nach EN 1090-3']['Maße geprüft und Toleranzen eingehalten nach der']['Fertigung']);?></td>
							<td align="left"><?=renderCheckbox('Montage', $data['Montagebericht und Abnahmebescheinigung']['Dokumentation im Rahmen der WPK nach EN 1090-3']['Maße geprüft und Toleranzen eingehalten nach der']['Montage']);?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					Mit Hilfe:
					<table class="innerCheckboxes">
						<tr>
							<td align="left"><?=renderCheckbox('Arbeitsanweisung', $data['Montagebericht und Abnahmebescheinigung']['Dokumentation im Rahmen der WPK nach EN 1090-3']['Mit Hilfe']['Arbeitsanweisung']);?></td>
							<td align="left"><?=renderCheckbox('Fertigungsunterlagen', $data['Montagebericht und Abnahmebescheinigung']['Dokumentation im Rahmen der WPK nach EN 1090-3']['Mit Hilfe']['Fertigungsunterlagen']);?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td>
					<table class="innerCheckboxes">
						<tr>
							<td align="left"><?=date('d.m.Y',strtotime($data['Montagebericht und Abnahmebescheinigung']['Dokumentation im Rahmen der WPK nach EN 1090-3']['Datum']))?></td>
							<td align="left">
								<?php if(file_exists($data['Montagebericht und Abnahmebescheinigung']['Unterschriften']['Monteur Unterschrift'])) { ?>
									<?php echo '<img src="'.$data['Montagebericht und Abnahmebescheinigung']['Unterschriften']['Monteur Unterschrift'].'" alt="" class="positionPhotos"'; ?>
				 				<?php } else {echo '';}?>
								</td>
						</tr>
						<tr>
						<td align="left">Datum</td>
						<td align="left">Unterschrift</td>
					</tr>
					</table>
				</td>
			</tr>
		</table>
	</body>

</html>

<style>
	.data, .checkboxes, .signatures {width:100%; border: none; table-layout: fixed}
	
	.data ,th, td {
        border-collapse: collapse;
        padding: 0;
        mso-cellspacing: 0;
        horiz-align:left;
    }
    
    .footer ,.footer th, .footer td {
        width:60%;
        border-collapse: collapse;
        padding: 0;
        border: 1px solid black;
    }
    
    .footer td {padding-left: 15px;}
    .innerCheckboxes {border: none!important; width:100%;}
    .innerCheckboxes th, .innerCheckboxes td {border:none!important; padding-left: 15px;}
    .title {font-size:x-large;}
    .ergebnis {vertical-align:text-top;}
    .signatures {width:100%; height:auto;}
    .positionPhotos {width:50%; height:auto;}
    body {margin-left:20mm;}
    .hiddenErgebnis {visibility:hidden;}
    </style>