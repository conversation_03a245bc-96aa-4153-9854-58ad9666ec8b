<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'WorkingOrders.php';

class C_Ladeliste {

private $db;
private $documentation;
private $projects;
private $wos;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->wos = new WorkingOrders();
	}
	
	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		$templateData = [];
		$templateData['documentDate'] = date('d.m.Y',strtotime($documentData['documentCreatedOn']));
		$templateData['projectNo'] = $documentData['documentRelKey1'];
		
		$headlines = [];
			
		$parents = [];
		$children = [];
		$subchildren = [];
		
		foreach ($documentData['children'] as $k=>$v) {
			
			if($v['title'] == 'Bemerkungen') {
				$templateData['Bemerkungen'] = $v['reportedValue'];
			}
			
			if($v['title'] == 'Kennzeichnen') {
				$templateData['Kennzeichnen'] = $v['reportedValue'];
			}
			
			if($v['title'] == 'Entladung auf Baustelle') {
				foreach ($v['reportedValues'] as $keyReportedValue => $reportedValue) {
					$templateData['Entladung auf Baustelle'][$keyReportedValue] = $reportedValue;
				}
			}
			
			if($v['title'] == 'Datum') {
				$templateData['date'] = date('d.m.Y',strtotime($v['reportedValue']));
			}
			
			if($v['indentationLevel'] === 2 && ($v['title'] == 'Riegeldiagonale' || $v['title'] == 'Knotendiagonale'
			|| $v['title'] == 'Horizontaldiagonale' || $v['title'] == 'Podesttreppe') ) {
				$parents[$v['title']] = $v['id'];
			}
			
			if($v['indentationLevel'] === 3) {
				$children[$v['id']]['title'] = $v['title'];
				$children[$v['id']]['parentId'] = $v['parentId'];
			}
			
			if($v['indentationLevel'] === 4) {
				$subchildren[$v['id']]['title'] = $v['title'];
				$subchildren[$v['id']]['parentId'] = $v['parentId'];
				$subchildren[$v['id']]['reportedValue'] = $v['reportedValue'];
			}
		}
		
		
		$nestedData = [];
		
		foreach($subchildren as $id => $subchild) {
			foreach($children as $childId => $child) {
				foreach ($parents as $parent => $parentId) {
					if(($subchild['parentId'] == $childId) && ($child['parentId'] == $parentId)) {
						$nestedData[$parent][$child['title']][$subchild['title']] = eval('return '.$subchild['reportedValue'].';');
					}
				}
			}	
		}
		
		foreach ($documentData['children'] as $k=>$v) {
			if($v['type'] == 'headline') {
				if($v['indentationLevel'] == 2 && $v['title'] == 'Stiele') {
					$headlines[$v['id']] = $v['title'].' 2';
				} else {
					$headlines[$v['id']] = $v['title'];
				}
			}
		}
		
		foreach ($headlines as $i => $headline) {
			foreach ($documentData['children'] as $c => $child) {
				if($i == $child['parentId']) {
					$templateData[$headline][$child['title']] = eval('return '.$child['reportedValue'].';');
				}
			}
		}
		
		$templateData['nestedData'] = $nestedData;
		$templateData['projectName'] = $documentData['documentRelKey1'];
		//project data
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		
		if($projectData) {
			$templateData['projectName'] .= " - ".$projectData['project_name'];

			$templateData['projectManager'] = $projectData['technicalContactName'] ? $projectData['technicalContactName'] : '';
		}
		
		return $templateData;
		
	
	}	
	
	private function getProjectData($projectNo) {
		if ($target = getTunnelingTarget('GET', 'v1/projects?ktr='.$projectNo.'&year='.date('Y'))) {
			$result = getDataFromTunnel($target);
			return (array) $result;
		}
		else
		{
			$result = $this->projects->get($projectNo);
			$projects = [];
			foreach ($result as $k=>$v) {
				$projects[$v['year']] = $v;
			}
			ksort($projects);
			return end($projects);
		}
	}
}