<?php $data = $this->data; ?>

<?php foreach ($data['Leistungen'] as $key => $value) { ?>
    <div class="nextPages">
		<?php if (empty($value['Foto'])) {?>
			<!--closing the div tag only if there are no images. Otherwise it will be closed right after the first itteration of photos and closed after it. --!>
			<div class="page"></div>
	</div>
<?php } ?>
		<br>
			<?php foreach ($value['Foto'] as $fid => $url) { ?>
				<?php reset($array);
    				if ($fid === key($value['Foto'])) {
        				$className = 'demoClassName';
    				} else {		
    					//add this class only to the second and all the following images
    					$className = 'nextPages';
    				} ?>				
				<div align="center" class="<?=$className;?>">
					<div class="images">
						<?=$key.'</br>';?>
						<?php if (!empty($value['Leistungskatalog'][0]) && $value['Leistungskatalog'][0] != '') {
							echo $value['Leistungskatalog'][0].'</br>';
						} ?>
						<?php $separator = $value['Leistungstext'] ? ' / ' : '';?> 
						<?=$value['Leistungstext'].$separator."Örtlichkeit ".$value['Örtlichkeit']?>
					</div>
					<img src="<?=$url;?>" alt="image" class="image">
				<?php if ($fid !== key($value['Foto'])) {
					echo '<div class="page"></div>';
				} ?>
				</div>
				<?php if ($fid === key($value['Foto'])) {
						//closing the div tag of the first image. Purpose: to be on the same page with the rest of the Leistung 1 values 
        				echo '<div class="page"></div></div>';
    				} else {
    					echo '';
    				}?>
			<?php } ?> 
    <?php } ?>
    
    <!-- closing the first nextPages div if there are more than one photos for the Leistung --!>
    <?php if (empty(!$value['Foto']) || count($value['Foto']) == 1) {?>
			<?php if(count($data['Leistungen']) !== 1) { ?>
				<div class="page"></div>
			<?php } ?>
    	</div>
    <?php } ?>
</body>
</html>
