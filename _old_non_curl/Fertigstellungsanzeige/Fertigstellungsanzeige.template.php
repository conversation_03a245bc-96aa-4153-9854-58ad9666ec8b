<!DOCTYPE html>
<html>
<?php
$data = $this->data;
$nameOfSchema = $data['schemaName'];
require_once 'vendor/config/includes.php';

$data['firstPage']['Leistungen'] = [];
$data['secondPage']['Leistungen'] = [];
$data['thirdPage']['Leistungen'] = [];

$firstPageLimit = 3;
$secondPageLimit = 7;

foreach ($data['Leistungen'] as $key=>$value) {
	if((int)trim(str_replace( "Leistung", "", $key)) <= $firstPageLimit) {
		$data['firstPage']['Leistungen'][$key] = $value;
	} else if((int)trim(str_replace( "Leistung", "", $key)) > $firstPageLimit && (int)trim(str_replace( "Leistung", "", $key)) <= $secondPageLimit ) {
	    $data['secondPage']['Leistungen'][$key] = $value;
	} else {
	    $data['thirdPage']['Leistungen'][$key] = $value;
	}
}

/**
 * @param $leistungen
 */
function generateLeistungen($leistungen)
{
	foreach ($leistungen as $title => $value) {
		echo "<u>" . $title . "</u><br><br>";
		echo "<span style='margin-bottom: 4px'>Beschreibung der Leistungen:</span>";
		echo "<ul>";
		foreach ($value['Leistungskatalog'] as $performanceCatalogItem)
			echo "<li>" . $performanceCatalogItem . "</li>";
		if($value['Leistungstext'])
		    echo "<li>" . $value['Leistungstext'] . "</li>";
		echo "</ul>";
		echo "Beschreibung der Örtlichkeit: <br><ul style='margin-top: 4px'><li>" . $value['Örtlichkeit'] . "</li></ul>";
		if ($value['Foto'])
			echo "Foto im Anhang";
		echo "<br><br><br>";
	}
}

function generateDeclaration ($desiredActionKey) {
	if($desiredActionKey == 'Zustandsfeststellung'){
		echo "Unter Bezugnahme auf die von uns erbrachten oben erwähnten Teilleistungen erklären wir Ihnen hiermit unser Verlangen zur gemeinsamen Feststellung des Zustands derselben innerhalb der nächsten ";
	} elseif($desiredActionKey == 'Teilabnahme') {
		echo "Unter Bezugnahme auf die von uns erbrachten oben erwähnten Teilleistungen erklären wir Ihnen hiermit unser Verlangen zur Teilabnahme derselben innerhalb der nächsten ";
	} elseif($desiredActionKey == 'Abnahme') {
		echo "Unter Bezugnahme auf die von uns erbrachten oben erwähnten Leistungen erklären wir Ihnen hiermit unser Verlangen zur Abnahme derselben innerhalb der nächsten ";
	}
}

function generatePartnerEmails($emails) {
	echo '<div><br/></div><div><b>'.lang('distributor').'</b></div>';
	
	foreach ($emails as $key => $email) {
		echo '<div>'.'- '.$email.'</div>';  
	}
} 
?> 
<head>
		 <script>
        		function subst() {
				  var vars={};
				  var x=document.location.search.substring(1).split('&');
				  for(var i in x) {var z=x[i].split('=',2);vars[z[0]] = unescape(z[1]);}
				  var x=['frompage','topage','page','webpage','section','subsection','subsubsection'];
				  for(var i in x) {
					var y = document.getElementsByClassName(x[i]);
					var counter = 2;
					for(var j=0; j<y.length; ++j) {
					  y[j].textContent = 'Seite '+counter;
					  counter++;
					} 
				  }
				}
				
				function singlePageFit() {
					var fit = document.getElementsByClassName("fit");
					for (var i = 0; i < fit.length - 1; i++) {
						fit[i].classList.add("hidden");
					}
				}
         </script>
	
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Fertigstellungsanzeige</title>
    <style>
        .images {
            width: 80%;
            padding-top: 250px;
            margin: 0 auto!important;
        }
		
		.image {
			width: 50%;
			height:auto;
			margin-top:25%;
		}
		
		.pageBreak {
			page-break-before:always;
		}
		
        body {
            margin: 0;
            padding: 0;
            text-rendering: optimizeLegibility;
            font-family: "Times New Roman";
            width: 260mm;!important;
        }

        .firstPage {
            background-image: url(<?=WEB_ROOT_API.'vendor/printouts/Fertigstellungsanzeige/firstBackground.png'?>);
            background-repeat: no-repeat;
            background-size: contain;
            position: relative;
            page-break-after: always;
            padding-top: 48px;
            padding-left: 117px;
            padding-right: 210px;
            height: 35.85cm;
            overflow: hidden;
        }

        .secondPageLeistungen, .thirdPageLeistungen, .nextPages {
            background-image: url(<?=WEB_ROOT_API.'vendor/printouts/Fertigstellungsanzeige/secondBackground.png'?>);
            background-repeat: no-repeat;
            background-size: contain;
            page-break-after: always;
            position: relative;
            height: 35.85cm;
            overflow: hidden;
        }

        .nextPagesContentPadding {
            padding-top: 250px;
            padding-left: 100px;
            padding-right: 200px;
        }
        
        .page {
				z-index: 9999;		
				position: absolute;
				bottom: 0;
				right: 10%;
        }
        
         .firstPageNumber {
				z-index: 9999;		
				position: absolute;
				bottom: 0;
				right: 10%;
        }
        
        .hidden {display:none;}
    	.wrapper {margin-top:8%;}
    </style>
</head>
<body onload="subst(); singlePageFit()">
<div class="firstPage">
    <br><br><br><br><br><br><br><br><br><br><br>
    <div align="left" class="customerName"><?=$data['customerName'];?></div>
    <div align="left" class="customerAddress"><?=$data['customerAddress'];?></div>
    <br><br><br><br>
    <br><br>
    <table width="100%"> 
    	<tr>
			<td align="right"><?=$data[$nameOfSchema]['Fertigstellungsdatum'];?> </td>
		</tr>
    </table>
    <?php
        // only show this row if we have content to display
        if(!$data["internalProjectNo"])
            echo '<div align="left" class="internalProjectNo"><b>Bezug:</b> '.$data["internalProjectNo"].'</div>';
    ?>
    <div class="wrapper">
		<b>BV:</b> <?=$data['projectName'];?>
		<div align="left" class="documentId"><b>Fertigstellungsanzeige Nr. <?=$data['documentId'];?> / Verlangen nach <?=$data[$nameOfSchema]['Verlangen nach ...'];?></b> </div>
		<div class="anrede">
			<br>
			Sehr geehrte Damen und Herren,
			<br><br>
			auf dem Bauvorhaben "<?=$data['projectName'];?>" haben wir nachfolgend beschriebene Leistungen
			am <?=$data[$nameOfSchema]['Fertigstellungsdatum'];?> fertig gestellt und Ihnen zum Gebrauch übergeben:
			<br><br>
	
			<?php
				generateLeistungen($data['firstPage']['Leistungen']);
			?>
		</div>
	</div>
    <div class="fit">
		<?php
            $desiredActionKey = 'Verlangen nach ...';
            generateDeclaration($data[$nameOfSchema][$desiredActionKey]);
        ?>
        <p align="center"><b>
            <?=$data[$nameOfSchema]['gemeinsame Zustandsfeststellung in Werktagen'];?>
            <?php echo $data[$nameOfSchema]['gemeinsame Zustandsfeststellung in Werktagen']>1 ? 'Werktage' : 'Werktag'; ?>
        </b></p>
        Den genauen Termin teilen Sie uns bitte schnellstmöglich mit, wir stehen
        Ihnen hinsichtlich der <?=$data[$nameOfSchema][$desiredActionKey];?> innerhalb der genannten Frist
        jederzeit zur Verfügung.
        <br><br>
        Mit freundlichen Grüßen
        <?php
            // only show signature field if signature exists
            if($data[$nameOfSchema]['Unterschrift'])
            {
                echo "<br><br>";
                echo '<div><img src="'.$data[$nameOfSchema]['Unterschrift'].'" alt="signature" width="30%" height="auto"></div>';
            }
            else
                echo "<br><br>";
        ?>
        i.A. <?=$data['author'];?>
       		<?php if (count($data['partnerEmails']) <= 6 ) { ?>
				<?php generatePartnerEmails($data['partnerEmails']); ?>
			<?php } ?>
        
	</div>
</div>
<p style="page-break-before: always"></p>

<?php if(!empty( $data['secondPage']['Leistungen']) || count($data['firstPage']['Leistungen']) >= $firstPageLimit-1) { ?>
	<div class="secondPageLeistungen">
		<div class="nextPagesContentPadding">
<?php } ?>

  <?php
        generateLeistungen($data['secondPage']['Leistungen']);
    ?>
<?php if(!empty( $data['secondPage']['Leistungen']) || count($data['firstPage']['Leistungen']) >= $firstPageLimit-1) { ?>
			 <div class="fit">
				<?php
					$desiredActionKey = 'Verlangen nach ...';	
					 generateDeclaration($data[$nameOfSchema][$desiredActionKey]);
				?>
				<p align="center"><b>
					<?=$data[$nameOfSchema]['gemeinsame Zustandsfeststellung in Werktagen'];?>
					<?php echo $data[$nameOfSchema]['gemeinsame Zustandsfeststellung in Werktagen']>1 ? 'Werktage' : 'Werktag'; ?>
				</b></p>
				Den genauen Termin teilen Sie uns bitte schnellstmöglich mit, wir stehen
				Ihnen hinsichtlich der <?=$data[$nameOfSchema][$desiredActionKey];?> innerhalb der genannten Frist
				jederzeit zur Verfügung.
				<br><br>
				Mit freundlichen Grüßen
				<?php
					// only show signature field if signature exists
					if($data[$nameOfSchema]['Unterschrift'])
					{
						echo "<br><br>";
						echo '<div><img src="'.$data[$nameOfSchema]['Unterschrift'].'" alt="signature" width="30%" height="auto"></div>';
					}
					else
						echo "<br><br>";
				?>
			 i.A. <?=$data['author'];?>
				<?php if (count($data['partnerEmails']) <= 6 ) { ?>
					<?php generatePartnerEmails($data['partnerEmails']); ?>
				<?php } ?>
			</div>
			<div class="page"></div>
		</div>
	</div>
	<p style="page-break-before: always"></p>
<?php } ?>

<?php if(!empty( $data['thirdPage']['Leistungen']) || count($data['secondPage']['Leistungen']) >= $secondPageLimit-$firstPageLimit-1) { ?>
	<div class="thirdPageLeistungen">
		<div class="nextPagesContentPadding">
	
<?php } ?>

  <?php
        generateLeistungen($data['thirdPage']['Leistungen']);
        ?>
<?php if(!empty( $data['thirdPage']['Leistungen']) || count($data['secondPage']['Leistungen']) >= $secondPageLimit-$firstPageLimit-1) { ?>
			 <div class="fit">
					<?php
						$desiredActionKey = 'Verlangen nach ...';
						generateDeclaration($data[$nameOfSchema][$desiredActionKey]);
					?>
					<p align="center"><b>
						<?=$data[$nameOfSchema]['gemeinsame Zustandsfeststellung in Werktagen'];?>
						<?php echo $data[$nameOfSchema]['gemeinsame Zustandsfeststellung in Werktagen']>1 ? 'Werktage' : 'Werktag'; ?>
					</b></p>
					Den genauen Termin teilen Sie uns bitte schnellstmöglich mit, wir stehen
					Ihnen hinsichtlich der <?=$data[$nameOfSchema][$desiredActionKey];?> innerhalb der genannten Frist
					jederzeit zur Verfügung.
					<br><br>
					Mit freundlichen Grüßen
					<?php
						// only show signature field if signature exists
						if($data[$nameOfSchema]['Unterschrift'])
						{
							echo "<br><br>";
							echo '<div><img src="'.$data[$nameOfSchema]['Unterschrift'].'" alt="signature" width="30%" height="auto"></div>';
						}
						else
							echo "<br><br>";
					?>					
				 i.A. <?=$data['author'];?>
					 <?php if (count($data['partnerEmails']) <= 6 ) { ?>
						<?php generatePartnerEmails($data['partnerEmails']); ?>
					<?php } ?>
			</div>
			<div class="page"></div>
		</div>
	</div>
	<p style="page-break-before: always"></p>
<?php } ?>
<?php if (count($data['partnerEmails']) > 6 ) { ?>
	<div class="nextPages">
		<div class="nextPagesContentPadding">
						 <br/>
						 
						 <?php generatePartnerEmails($data['partnerEmails']); ?>
						 
			<div class="page"></div>
		</div>
	</div>
<?php } ?>
