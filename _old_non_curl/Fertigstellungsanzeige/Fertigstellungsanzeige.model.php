<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';
require_once ABS_PATH.'v3/Employees.php';
require_once ABS_PATH.'Partners.php';
require_once ABS_PATH.'Files.php';

class C_Fertigstellungsanzeige {
private $db;
private $documentation;
private $projects;
private $partners;
private $files;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->employees = new \v3\Employees();
		$this->partners = new Partners();
		$this->files = new Files();
	}

	
	public function getData($schemaId,$documentId) {
		
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		$documentData['children'] = array_merge([['id'=> $documentData['id'], 'title'=> $documentData['title'], 'type'=> $documentData['type']]],$documentData['children']);
		
		$parents = [];
		$templateData = [];
		
		foreach ($documentData['children'] as $k=> $v) {
			if($v['type'] == 'headline') {
				$parents[$v['id']] = $v['title'];
				unset($documentData['children'][$k]);
			}
		}

		// loop over all parents (=headlines)
		foreach ($parents as $parentIndex => $parentTitle) {
			// loop over all document children
			foreach ($documentData['children'] as $child) {
				// proceed if current child belongs to the parent
				if($child['parentId'] == $parentIndex) {
					// handle types photo and signaturefields
					if($child['type'] == 'photo' || $child['type'] == 'signatureField') {
						if (strpos($parentTitle, 'Leistung') !== false) {
							foreach ($child['reportedValues'] as $reportedIndex => $fid) {
								$templateData['Leistungen'][$parentTitle][$child['title']][$fid] = $this->files->getSingle($fid)['filepath'];							
							}
							
						} else {
							foreach ($child['reportedValues'] as $reportedIndex => $fid) {
								$templateData[$parentTitle][$child['title']][$fid] = $this->files->getSingle($fid)['filepath'];
							}
						}
					}
					// handle type date
					else if ($child['type'] == 'date'){
					    $templateData[$parentTitle][$child['title']] = date('d.m.Y',strtotime($child['reportedValue']));
					}
					// hande all other types
					else {
						// special handling for children with title containing "Leistung*"
						if (strpos($parentTitle, 'Leistung') !== false) {
							if (strpos($child['type'], 'combobox-multi') !== false) {
								$templateData['Leistungen'][$parentTitle][$child['title']] = $child['reportedValues'];
							} else
							{
								$templateData['Leistungen'][$parentTitle][$child['title']] = $child['reportedValue'];
							}
						} else {
							$templateData[$parentTitle][$child['title']] = $child['reportedValue'];
						}
						
					}
				}
			}
		}

		// fetch project from tunnel
		if($projectTarget = getTunnelingTarget('GET','projects/select/'.$documentData['documentRelKey1'].'/2020'))
		{
			/*
			$projectTarget = null;
			$projectTarget[] = 'http://*************:16000/scaffoldingAPI/index.php/projects/select/39448/2020';
			*/
			$project = getDataFromTunnel($projectTarget);
			$projectData = (array) $project;
		}
		$partners = $this->partners->get($documentData['documentRelKey1']);

		$templateData['partnerEmails'] = [];
		foreach ($partners as $partnerIndex => $partnerData) {
			$templateData['partnerEmails'][] = $partnerData['email'];
		}
		
		$templateData['projectName'] = $projectData['project_name'];
		$templateData['customerName'] = isset($projectData['customer_kname']) ? $projectData['customer_kname'] : '';
		$templateData['customerAddress'] = '';
		$templateData['internalProjectNo'] = $projectData['internalProjectNo'] ? $projectData['internalProjectNo'] : $projectData['info'];

		isset($projectDataData['customer_city']) ? $templateData['customerAddress'] = $projectData['customer_city'] : $templateData['customerAddress'] = '';

		// fetch assosciated partner
        $projectPartner = $projectData['partners'][0];
        if($projectPartner)
        {
            $partnersAPI = new Partners();
            $partner = $partnersAPI->getselect($projectPartner->customerNo,$projectPartner->partnerNo);
            $templateData['partnerEmail'] = $partner['email'];
        }

		// fetch customer from tunnel
		if($projectData['knr'] && $customerTarget = getTunnelingTarget('GET','addresses/'.$projectData['knr']))
		{
			/*
			$customerTarget = null;
			$customerTarget[] = 'http://*************:16000/scaffoldingAPI/index.php/addresses/778';
			*/
			$customerData = (array) getDataFromTunnel($customerTarget);
		}
		// address is split in 4 different fields
		$addressFields = array('ADR_ANSCHRIFT1','ADR_ANSCHRIFT2','ADR_ANSCHRIFT3','ADR_ANSCHRIFT4');
		foreach ($addressFields as $addressField) {
			if($customerData[$addressField])
				$customerNameCandidate .= $customerData[$addressField]."<br>";
		}
		if($customerNameCandidate)
			$templateData['customerName'] = $customerNameCandidate."<br>";
		else
			$templateData['customerName'] = $customerData['name'];

		$templateData['customerAddress'] = $customerData['address']."<br> ".$customerData['postCode']." ".$customerData['city'];

		$templateData['documentDate'] =  date('d.m.Y',strtotime($documentData['documentCreatedOn']));
        $templateData['author'] = $documentData['author'] ? $this->employees->getsingle($documentData['author'])['displayName'] : '';

        // compute localId
        // extract local id by schema from document
        $localIdBySchema = explode('-',$documentData['localIdBySchema'])[1];
        // extract base value from special schema, which stores them
		if ($documentData['documentRelType'])
        $baseValueDocuments = (new DocumentationDocument())->getByRelation($documentData['documentRelType'],$documentData['documentRelKey1'],null,29);
        // check for existance
        
        //fetch all document for that schema
            $allDocuments = $this->documentation->getSchemaDocuments($schemaId);
            
		//fetching these documents, related only to the project number
		foreach ($allDocuments as $key=>$documentInfo) {
			if($documentInfo['rel_key1'] != $documentData['documentRelKey1']) {
				unset($allDocuments[$key]);
			}
		}
            
		foreach ($allDocuments as $docIndex => $doc) {
			if(is_null($doc['originDocumentId']) || $doc['originDocumentId'] == '' ) {
				unset($allDocuments[$docIndex]);
			}
		}
        
        if($baseValueDocuments && $baseValueDocuments[0]) {
            $localIdBySchema += (int) $baseValueDocuments[0]['children'][0]['reportedValues'][0];
            $totalDocumentsWithOriginDocumentId = count($allDocuments); 
        	$localIdBySchema = $localIdBySchema - $totalDocumentsWithOriginDocumentId;
        }
            
        $localIdBySchema = $this->computeRunningNoOfDocumentation($documentData);
		$templateData['documentId'] =  $projectData['externalProjectNo']."-".$localIdBySchema;
		$templateData['projectInternalNo'] = $projectData['internalProjectNo'];
        $templateData['projectName'] = $projectData['project_name'];
        $templateData['schemaName'] = $documentData['title'];

        return $templateData;
	}

	/**
	 * Computes local id of document
	 * @param $documentData
	 * @return int|mixed|string
	 */
	public function computeRunningNoOfDocumentation($documentData)
	{
		// extract local id by schema from document
		$localIdBySchema = explode('-', $documentData['localIdBySchema'])[1];
		// extract base value from special schema, which stores them
		if ($documentData['documentRelType'])
			$baseValueDocuments = (new DocumentationDocument())->getByRelation($documentData['documentRelType'], $documentData['documentRelKey1'], null, 29);
		// check for existance
		if ($baseValueDocuments && $baseValueDocuments[0]) {
			$localIdBySchema += (int)$baseValueDocuments[0]['children'][0]['reportedValues'][0];
		}
		return $localIdBySchema;
	}
}