<?php

class C_GrundGerustBauTagebericht {

	private $projectNo;
	private $workingOrderNo;
	
	public function __construct($projectNo, $workingOrderNo) {
		$this->projectNo = $projectNo;
		$this->workingOrderNo = $workingOrderNo;
	}

	public function getData()
	{
		$projects = $this->getProjectCoreData();
		$workingOrder = $this->getWorkingOrderCoreData();
		$hours = $this->getHoursCoreData();
		$teamFromPlanning = $this->getTeamFromPlanning($workingOrder);
		$employeesFromPlanning = $teamFromPlanning['employees'];
		$vehiclesFromPlanning = $teamFromPlanning['vehicles'];
		$filesCount = $this->getFilesCount($workingOrder);
		$dynamicDocumentation = $this->getDynamicDocumentation($workingOrder);
		return $this->combineData($projects, $workingOrder,$hours, $vehiclesFromPlanning, $employeesFromPlanning, $filesCount,$dynamicDocumentation);
	}

	private function getDynamicDocumentation($workingOrder)
	{
		require_once ABS_PATH.'Documentation.php';
		$documentationAPI = new Documentation();
		return $documentationAPI->getDocumentationByKey('workingOrder',$workingOrder['ktr'],$workingOrder['aanr']);
	}

	private function getFilesCount($workingOrder)
	{
		require_once ABS_PATH.'Files.php';
		$filesAPI = new Files();
		$files = $filesAPI->get('ktr-aanr',$workingOrder['ktraanr']);

		return count($files);
	}
	private function getTeamFromPlanning($workingOrder)
	{
		require_once ABS_PATH.'Team.php';
		$teamAPI = new Team();
		$team = $teamAPI->getselect('wo',$workingOrder['ktr'],$workingOrder['aanr'],$workingOrder['plan_date']);
		$vehiclesFromTeam = $team['nonhuman_resources'];

		require_once ABS_PATH.'Vehicles.php';
		$vehiclesAPI = new Vehicles();
		$vehicles = array();
		foreach ($vehiclesFromTeam as $vehicle) {
			$vehicles[] = $vehiclesAPI->getselect_resource($vehicle);
		}
		$result = array();
		$result['vehicles'] = $vehicle;
		$result['employees'] = $team['human_resources'];
		return $result;
	}

	private function getHoursCoreData()
	{
		require_once ABS_PATH.'Hours.php';
		$hours = new Hours();
		return $hours->getall(0,null,null,'',$this->projectNo,$this->workingOrderNo,'mixed',0,'','employees');
	}
	private function getWorkingOrderCoreData()
	{
		require_once ABS_PATH.'WorkingOrders.php';
		$workingOrders = new WorkingOrders();
		return  $workingOrders->getselect($this->projectNo,$this->workingOrderNo);
	}

	private function getProjectCoreData()
	{
		require_once ABS_PATH.'Projects.php';
		$projects = new Projects();
		return $projects->get($this->projectNo)[0];
	}

	private function combineData($project, $workingOrder, $hours, $vehiclesFromPlanning, $employeesFromPlanning,$filesCount,$dynamicDocumentation)
	{
		$templateData = [];

		$templateData['customerName'] = $project['customer_kname'];
		$templateData['address'] = $project['customer_address'].", ".$project['customer_postcode']." ".$project['customer_city'];
		$templateData['projectNumber'] = $workingOrder['ktraanr'];
		$templateData['date'] = date('d.m.Y',strtotime($workingOrder['plan_date']));
		$templateData['projectName'] = $workingOrder['projectName'];
		$templateData['workingOrderName'] = $workingOrder['kurztext'];
		$templateData['vehicles'] = $vehiclesFromPlanning;
		$templateData['filesCount'] = $filesCount;
		$templateData['dynamicDocumentation'] = $dynamicDocumentation;

		$employeeList = new C_GrundGerustBauTageberichtEmployeeList($hours);
		$employeeList->sortByTeamPlanning($employeesFromPlanning);
		$templateData['employees'] = $employeeList;

		return $templateData;
	}

}

/**
 * Class C_GrundGerustBauTageberichtEmployeeList
 *
 * The EmployeeList contains all employees in the private $employees array
 * The employeeRows describe a single employee and they have an array of hours
 * Each "hour" is described as an Hours Entry
 */
class C_GrundGerustBauTageberichtEmployeeList
{
	private $employees = array();

	public function __construct($hours)
	{
		// popluate employees array from hours
		foreach ($hours as $hour) {
			$employeeNo = $hour['pnr'];
			/** @var C_GrundGerustBauTageberichtEmployeeRow $employee */
			$employee = $this->employees[$employeeNo];
			if (!in_array($employeeNo, array_keys($this->employees))) {
				$employeeName = $hour['employee']['displayName'];
				$isDriver = $hour['plannedDriver'] == $employeeNo;
				$employee = new C_GrundGerustBauTageberichtEmployeeRow($employeeNo, $employeeName,$isDriver);
				$this->employees[$employeeNo] = $employee;

			}
			$employee->addHours($hour);
		}

		// change types if needed based on employees hours-sequence
		foreach ($this->getEmployees() as $employee) {
			$employee->postProcessHours();
		}
	}

	/**
	 * @return C_GrundGerustBauTageberichtEmployeeRow[]
	 */
	public function getEmployees()
	{
		return $this->employees;
	}

	/**
	 * @return first element of employees array
	 */
	public function getFirst()
	{
		return $this->employees[array_keys($this->employees)[0]];
	}

	/**
	 * Loops over the employees to fetch the duration of a certain type at a certain index and sums it up.
	 * E.g. the 3rd occurence of WORK
	 *
	 * @param $index
	 * @param $type
	 * @return float
	 */
	public function getSumByIndexAndType($index, $type)
	{
		$sum = 0.0;
		foreach ($this->getEmployees() as $employee) {
			$sum += $employee->getSumByType($type, $index);
		}

		return $sum;
	}

	/**
	 * Loops over the employees to fetch the total duration by a certain type
	 * @param $type
	 * @return float
	 */
	public function getSumByType($type)
	{
		$sum = 0.0;
		foreach ($this->employees as $employee) {
			$sum += $employee->getSumByType($type);
		}
		return $sum;
	}

	public function sortByTeamPlanning($employeesFromPlanning)
	{
		$sortedEmployees = array();
		$isLeader = true;
		foreach ($employeesFromPlanning as $item) {
			if(array_key_exists($item,$this->employees))
			{
				$sortedEmployees[$item] = $this->employees[$item];
				$this->employees[$item]->setLeader($isLeader);
				$isLeader = false;
			}
		}
		$unplannedEmployeeNumbers = array_diff(array_keys($this->employees), array_keys($sortedEmployees));
		foreach ($unplannedEmployeeNumbers as $unplannedEmployeeNumber) {
			$sortedEmployees[$unplannedEmployeeNumber] = $this->employees[$unplannedEmployeeNumber];
			$this->getEmployees()[$unplannedEmployeeNumber]->setPreplanned(false);
		}
		$this->employees = $sortedEmployees;
	}
}
class C_GrundGerustBauTageberichtEmployeeRow{
	const wageTypeForLoad = '80.1';
	private $employeeNo = 0;
	private $employeeName = "";
	private $driver = false;
	private $coDriver = false;
	private $preplanned = true;
	private $leader = false;
	/** @var $hours C_GrundGerustBauTageBerichtHoursEntry[] */
	private $hours = array();

	public function __construct($employeeNo=0, $name='', $driver = false, $coDriver = false)
	{
		$this->employeeNo = $employeeNo;
		$this->employeeName = $name;
		$this->driver = $driver;
		$this->coDriver = $coDriver;
	}

	/**
	 * Parses hours from the API response and instantiates objects
	 * Sorts the hours by startTime using usort
	 * @param $hours
	 */
	public function addHours($hours)
	{
		if($hours['startDriveTo'])
		{
			$driveToConstructionSiteTime = new C_GrundGerustBauTageBerichtHoursEntry('DRIVETO',$hours['date'],$hours['startDriveTo'],$hours['endDriveTo']);
			$this->hours[] = $driveToConstructionSiteTime;
		}

		$type = ($hours['wageTypeKey'] == self::wageTypeForLoad)?'LOAD':'WORK';

		$workTime = new C_GrundGerustBauTageBerichtHoursEntry($type,$hours['date'],$hours['start'],$hours['end'],$hours['hours'],$hours['wageTypeKey']);
		$this->hours[] = $workTime;

		if($hours['startBreak'])
		{
			$breakTime = new C_GrundGerustBauTageBerichtHoursEntry('BREAK',$hours['date'],$hours['startBreak'],$hours['endBreak'],$hours['pause']);
			$this->hours[] = $breakTime;

			// TODO: BREAK has to split previous WORK into two HoursEntries
			if($breakTime->getStart() > $workTime->getStart() && $breakTime->getEnd() < $workTime->getEnd());
			{
				$extraWorkTime = new C_GrundGerustBauTageBerichtHoursEntry('WORK',$hours['date'],$breakTime->getEnd(),$workTime->getEnd(),0,$workTime->getTask(),true);
				if($extraWorkTime->getDuration() > 0)
				{
					$this->hours[] = $extraWorkTime;
					$workTime->setEnd($breakTime->getStart());
					$workTime->recalculateDuration();
				}
			}
		}
		if($hours['startDriveFrom'])
			{
			$driveBackFromConstructionSiteTime = new C_GrundGerustBauTageBerichtHoursEntry('DRIVEFROM',$hours['date'],$hours['startDriveFrom'],$hours['endDriveFrom']);
			$this->hours[] = $driveBackFromConstructionSiteTime;
		}
		usort($this->hours, function ($a,$b)
		{
			if($a->getStart() != $b->getStart())
				return ($a->getStart() < $b->getStart()) ? -1 : 1;
			else
				return ($a->getEnd() < $b->getEnd()) ? -1 : 1;
		}
		);
	}

	public function getDisplayString()
	{

		$suffix = $this->leader? '<img width="20px" src="'.WEB_ROOT_API.'vendor\printouts\worker.png">':"";
		return "$this->employeeName ($this->employeeNo) $suffix";
	}

	/**
	 * @param bool $leader
	 */
	public function setLeader($leader)
	{
		$this->leader = $leader;
	}

	public function getDriverString()
	{
		if($this->driver)
		{
			return "Fahrer";
		}
		else if ($this->coDriver)
		{
			return "Beifahrer";
		}
	}

	/**
	 * iterates over the hours to find the given entry with the passed type and index
	 *
	 * @param $index defines the index to be found
	 * @return mixed
	 */
	public function getTaskNameFromType($type, $index)
	{
		$i = 1;
		foreach ($this->hours as $hour) {
			if($hour->isOfType($type))
			{
				if($i === $index)
					return $hour->getTask();
				$i++;
			}
		}
		return "-";
	}

	/**
	 * Fetches the index of the type from the hours of the employees
	 *
	 * @param $index index of the element of interest
	 * @param $type of event (WORK, DRIVE, BREAK)
	 * @return mixed
	 */
	public function getTimeFromType($index,$type,$startOrEnd = 'start')
	{
		$i = 1;
		foreach ($this->hours as $hour)
		{
			if($hour->isOfType($type))
			{
				if($i === $index)
				{
					if($startOrEnd === 'start')
						return $hour->getStart()->format('H:i\h');
					if($startOrEnd === 'end')
						return $hour->getEnd()->format('H:i\h');
				}
				$i++;
			}
		}
		return "-";
	}

	/**
	 * Fetches the end time of the last hours-element
	 *
	 * @return mixed
	 */
	public function getEndTime()
	{
		return end($this->hours)->getEnd()->format('H:i\h');
	}

	/**
	 * aggregates the duration for that employee based on the type and the optional index
	 * @param $type
	 * @param null $index
	 * @return float
	 */
	public function getSumByType($type, $index = null)
	{
		$sum = 0.0;
		$i = 1;
		foreach ($this->hours as $hour) {
			if($hour->isOfType($type))
			{
				if($index === $i || is_null($index))
					$sum += $hour->getDuration();
				$i++;
			}
		}
		return round($sum,2);
	}

	private function getTypeCount($type)
	{
		$count = 0;
		foreach ($this->hours as $hour) {
			if($hour->isOfType($type))
				$count++;
		}
		return $count;
	}

	/**
	 * postProcesses the gathered hours
	 * - DRIVE is first entry
	 * - Empty WORK after BREAK
	 */
	public function postProcessHours()
	{
		// if the sequence starts with drive
		// first loading time has to become first work time
		if($this->hours[0]->isOfType('DRIVE'))
		{
			foreach ($this->hours as $hour) {
				if($hour->isOfType('LOAD'))
				{
					$hour->setType('WORK');
					continue;
				}
			}
		}

		// handle cases were the order of hours matters
		foreach ($this->hours as $hour) {
			$index = array_search($hour,$this->hours);
			// get predecessor
			$predecessorIndex = $index-1;
			$predecessor = ($this->hours[$predecessorIndex]) ? $this->hours[$predecessorIndex]:null;
			// drop WORK with 0 duration (caused by break splitting the hoursEntry)
			if($hour->getDuration() == 0 && $hour->isOfType('WORK') && $predecessor && $predecessor->isOfType('WORK') && $hour->getStart() == $predecessor->getEnd())
				unset($this->hours[$index]);
			// change DRIVEFROM to DRIVETO,
			// if there are 2 DRIVEFROM events
			// and the current DRIVEFROM is part of the first "half" of the sequence
			if($hour->isOfType('DRIVEFROM') && $this->getTypeCount('DRIVEFROM') > 1 && $index <= count($this->hours)/2)
			{
				$hour->setType('DRIVETO');
			}
			// change type of LOAD to WORK, if previous event is DRIVETO
			if($predecessor && $predecessor->isOfType('DRIVETO') && $hour->isOfType('LOAD'))
			{
				$hour->setType('WORK');
			}
			// unset duration of WORK if it equals a following BREAK
			if($hour->isOfType('BREAK') && $predecessor && $predecessor->isOfType('WORK') && $hour->getStart() == $predecessor->getStart() && $hour->getEnd() == $predecessor->getEnd())
			{
				$predecessor->setDuration(0);
			}
		}
		// reindex the hours
		$this->hours = array_values($this->hours);
	}

	/**
	 * @param bool $preplanned
	 */
	public function setPreplanned($preplanned)
	{
		$this->preplanned = $preplanned;
	}

	/**
	 * @return bool
	 */
	public function isPreplanned()
	{
		return $this->preplanned;
	}
}

class C_GrundGerustBauTageBerichtHoursEntry{
	const DRIVETYPES = ['DRIVETO', 'DRIVEFROM'];
	private $type;
	private $start;
	private $end;
	private $duration;
	private $task;

	public function __construct($type = "", $date= "",$start = "",$end= "",$duration=0.0,$task='',$startAndEndAsDateTime = false)
	{
		$this->type = $type;
		if(!$startAndEndAsDateTime)
		{
			$this->start = new DateTime("$date $start");
			$this->end = new DateTime("$date $end");
		}
		else
		{
			$this->start = $start;
			$this->end = $end;
		}
		$this->duration = floatval($duration);
		$this->task = $task;

		if($this->duration == 0.0)
		{
			self::recalculateDuration();
		}
	}

	/**
	 * @return string
	 */
	public function getStart()
	{
		return $this->start;
	}

	/**
	 * @return DateTime
	 */
	public function getEnd()
	{
		return $this->end;
	}

	/**
	 * @return float
	 */
	public function getDuration()
	{
		return $this->duration;
	}

	/**
	 * @return string
	 */
	public function getTask()
	{
		return $this->task;
	}

	public function setEnd($end)
	{
		$this->end = $end;
	}

	/**
	 * rather use recalculateDuration, since this is only used in a special data constellation
	 *
	 * @param $duration
	 */
	public function setDuration($duration)
	{
		$this->duration = $duration;
	}

	/**
	 * @param string $type
	 */
	public function setType($type)
	{
		$this->type = $type;
	}

	public function recalculateDuration()
	{
		$dateInterval = $this->end->diff($this->start);
		$this->duration = round(($dateInterval->h * 60 + $dateInterval->i) / 60,2);
	}
	/**
	 * checks type of the hoursEntry
	 * @param $type
	 * @return bool
	 */
	public function isOfType($type)
	{
		if($type === 'DRIVE')
			return in_array($this->type,self::DRIVETYPES);
		else
			return $this->type === $type;
	}

	public function __toString()
	{
		return $this->type." ".$this->start." - ".$this->end." - ".$this->task;
	}
}
