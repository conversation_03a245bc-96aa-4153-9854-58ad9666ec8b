<?php

use <PERSON>racast\Restler\RestException;

require_once ABS_PATH.'DirectoryOfServices.php';

class C_AutomatedInvoicing {
private $directoryOfServices;

	
	public function __construct() {
		$this->directoryOfServices = new DirectoryOfServices();
	}

	
	public function getData($customerNo, $principal, $invoiceDate) {
		$mainUrl = "https://api.baubuddy.de/scripts/customer_invoicing/run.php?";
		$principal = "principal=".$principal;
		$customer = '&customerNo='.$customerNo;
		$invoiceDate = '&invoiceDate='.$invoiceDate;
		$url = $mainUrl.$principal.$customer.$invoiceDate;
		$dosPrincipal = getallheaders()['Principal'];
		if (!$dosPrincipal)
			throw new RestException(404, "Missing service provider principal in the header.");
		$data = [];
		
		
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_HTTPGET, true);
		curl_setopt($ch, CURLOPT_VERBOSE, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array('Principal: '.$dosPrincipal));
		$response = curl_exec($ch);
		
		if (!$response) {
			curl_close($ch);
			$result = [];
		} else {
			curl_close($ch);
			$result = json_decode($response, true);
		}

		if ($result) {
			$sum = 0;
			$data['invoiceDate'] = '';
			$directoryOfServicesId = -1;
			foreach($result as $key=>$value){

				$sum+= $value['unitPrice'];

				if($data['invoiceDate'] == '') {
					if(isset($value['invoiceDate'])) {
						$data['invoiceDate'] = $value['invoiceDate'];
					}
				}

				if($directoryOfServicesId == -1) {
					$directoryOfServicesId = $value['DirectoryOfServicesId'];
				}
			}

			//Directory of services items
			$dOSItems = $this->directoryOfServices->getinfo($directoryOfServicesId);

			$data['items'] = $dOSItems;
			$data['total'] =  number_format($sum, 2, '.', '');
			$data['invoices'] = $result;
		}

		return $data; 
	} 
	
	
	
}
