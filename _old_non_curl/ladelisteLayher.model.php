<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'WorkingOrders.php';
require_once ABS_PATH.'printouts/printout.helper.php';

class C_LadelisteLayher {

private $db;
private $documentation;
private $projects;
private $wos;
private $helper;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->wos = new WorkingOrders();
		$this->helper = new PrintoutHelper();
	}
	
	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		
		array_unshift($documentData['children'], $main);
		
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		
		$templateData = $this->helper->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
		$templateData['documentId'] = $documentData['documentId'];
		$templateData['projectNo'] = $documentData['documentRelKey1'];
		$templateData['projectName'] = $documentData['documentRelKey1'];
		//project data
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		
		if($projectData) {
			$templateData['projectName'] .= " - ".$projectData['project_name'];

			$templateData['projectManager'] = $projectData['technicalContactName'] ? $projectData['technicalContactName'] : '';
		}
		
		$templateData['date'] = date('d.m.Y',strtotime($documentData['documentCreatedOn']));
		
		return $templateData;
	}	
	
	private function getProjectData($projectNo) {
		if ($target = getTunnelingTarget('GET', 'v1/projects?ktr='.$projectNo.'&year='.date('Y'))) {
			$result = getDataFromTunnel($target);
			return (array) $result;
		}
		else
		{
			$result = $this->projects->get($projectNo);
			$projects = [];
			foreach ($result as $k=>$v) {
				$projects[$v['year']] = $v;
			}
			ksort($projects);
			return end($projects);
		}
	}
	
	
	
	
}