<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Employees.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'printouts/printout.helper.php';
	
class C_Begehungsbericht {
	private $db;
	private $documentation;
	private $employees;
	private $projects;
	private $helper;
	private $documentationModel;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->employees = new Employees();
		$this->projects = new Projects();
		$this->helper = new PrintoutHelper();
		$this->documentationModel = new doc_schema_positions();
	}
	
	public function getData($schemaId, $documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		
		
		array_unshift($documentData['children'], $main);
		
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		
		$templateData = $this->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
		
		$templateData['documentId'] = $documentData['documentId'];
		
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		$templateData['projectName'] = $projectData['project_name'];
		
		//project address, zip and city
		$templateData['combinedAddress'] = '';
		isset($projectData['customer_address']) ? $templateData['combinedAddress'] = $projectData['customer_address'] : $templateData['address'] = ''; 
			
			if(!empty($projectData['customer_postcode'])) {
				$templateData['combinedAddress'] .= ' '.$projectData['customer_postcode'];
			}
		
		    if(!empty($projectData['customer_city'])) {
				$templateData['combinedAddress'] .= ', '.$projectData['customer_city'];
			}
			
			$templateData['combinedAddress'] = trim($templateData['combinedAddress']);
		
		$docAuthor = $this->employees->getselect_employee($documentData['author']);
		$templateData['documentAuthor'] = '';
		
		if (!empty($docAuthor['name'])) {
			$templateData['documentAuthor'] .= $docAuthor['name'];
		}
		
		if (!empty($docAuthor['vorname'])) {
			$templateData['documentAuthor'] .= ' '.$docAuthor['vorname'];
		}
		
		if (!empty($docAuthor['profession'])) {
			$templateData['documentAuthor'] .= ', '.$docAuthor['profession'];
		}
		
		return $templateData;
		
	}
	
	private function getProjectData($projectNo) {
		$result = $this->projects->get($projectNo);
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		
		return end($projects);
	}	
	
	private function getDeepValueFromField($keyFieldname, $valueFieldName, $node, $childrenFieldName) {		
		if ($node[$valueFieldName]) {
				return [$node[$keyFieldname] => $node[$valueFieldName]];
		} elseif($node[$childrenFieldName]) {
			foreach ( $node[$childrenFieldName] as $i =>$child) {
				$paths[$node[$keyFieldname]][$child[$keyFieldname]] = $this->getDeepValueFromField($keyFieldname, $valueFieldName, $this->setPathAsValue($child), $childrenFieldName)[$child[$keyFieldname]];
			}	
			return $paths;
		}
		return null;
	}
	
	private function setPathAsValue($child) {
		if(isset($child['filePath'])) {
			$child['reportedValue'] = $child['filePath'];
		}
		return $child;
	}

}
