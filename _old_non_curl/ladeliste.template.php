<html>
<?php $data = $this->data;

		function renderCheckbox($checkboxValue, $allValues) {
			if (in_array($checkboxValue, $allValues)) {
				return '<img src="'.WEB_ROOT_API.'vendor\printouts\checked.png?>"'.'alt="checked" width="auto" height="8%">'.$checkboxValue;
			} else {
				return '<img src="'.WEB_ROOT_API.'vendor\printouts\unchecked.png?>"'.'alt="unchecked" width="auto" height="8%">'.$checkboxValue;

			}
			
		}
		?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Ladeliste PERI</title>
	</head>
	<body>
		<table>
			<tr>
				<th width="33%" align="left"><img src="<?=WEB_ROOT_API.'vendor\printouts\ladelisteLogo.png'?>" alt="SchaeferGeruestbauLogo" class="companyLogo"></th>
				<th align="right">
					<table>
						<tr>
							<th colspan="2" align="center"><u>Entladung auf Baustelle</u></th>
						</tr>
						<tr>
							<td><?=renderCheckbox('Kran', $data['Entladung auf Baustelle']);?></td>
							<td><?=renderCheckbox('Stapler', $data['Entladung auf Baustelle']);?></td>
						</tr>
						<tr>
							<td><?=renderCheckbox('Teleskoplader', $data['Entladung auf Baustelle']);?></td>
							<td><?=renderCheckbox('Hand', $data['Entladung auf Baustelle']);?></td>
						</tr>
					</table>
				</th>
			</tr>
		</table>
		<div class="row keep-together break-after">
		  <div class="column leftColumn">
			<table class="documentData" cellspacing="0">
				<tr>
					<td width="30%">Datum:</td>
					<td width="70%"><?=$data['documentDate'];?></td>
				</tr>
				<tr>
					<td valign="top">Bauvorhaben:</td>
					<td><?=$data['projectName'];?></td>
				</tr>
				<tr>
					<td>Zuständiger BL:</td>
					<td><?=$data['projectManager'];?></td>
				</tr>
				<tr>
					<td>Kennzeichen:</td>
					<td><?=$data['Kennzeichnen'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="bohlen borders" cellspacing="0">
				<tr class="grey">
					<td colspan="5">Unterlagen</td>
					<td align="center">1,20</td>
					<td align="center">kurz</td>
				</tr>
				<tr>
					<td colspan="5"></td>
					<td class="red" align="center"><?=$data['Unterlagen']['1,20'];?></td>
					<td class="red" align="center"><?=$data['Unterlagen']['kurz'];?></td>
				</tr>
				<tr class="grey">
					<td>Bohlen</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
					<td align="center">3,50</td>
					<td align="center">4,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Bohlen']['1,50'];?></td>
					<td class="red" align="center"><?=$data['Bohlen']['2,00'];?></td>
					<td class="red" align="center"><?=$data['Bohlen']['2,50'];?></td>
					<td class="red" align="center"><?=$data['Bohlen']['3,00'];?></td>
					<td class="red" align="center"><?=$data['Bohlen']['3,50'];?></td>
					<td class="red" align="center"><?=$data['Bohlen']['4,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="spindeln borders" cellspacing="0">
				<tr class="grey">
					<td colspan="2" class="smallFont">Spindeln</td>
					<td align="center" class="smallFont">Lang</td>
					<td align="center" class="smallFont">Kurz</td>
					<td align="center" class="smallFont">Klipp</td>
					<td align="center" class="smallFont">Monster</td>
					<td align="center" class="smallFont">Rolle</td>
				</tr>
				<tr>
					<td colspan="2" align="center"></td>
					<td class="red" align="center"><?=$data['Spindeln']['lang'];?></td>
					<td class="red" align="center"><?=$data['Spindeln']['kurz'];?></td>
					<td class="red" align="center"><?=$data['Spindeln']['Kipp'];?></td>
					<td class="red" align="center"><?=$data['Spindeln']['Monster'];?></td>
					<td class="red" align="center"><?=$data['Spindeln']['Rolle'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="basisrahmen borders" cellspacing="0">
				<tr class="grey">
					<td colspan="5">Basisrahmen</td>
					<td align="center">50</td>
					<td align="center">100</td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder noRightBorder"></td>
					<td class="noLeftBorder noBottomBorder noRightBorder"></td>
					<td class="noLeftBorder noBottomBorder noRightBorder"></td>
					<td class="noLeftBorder noBottomBorder"></td>
					<td class="grey" align="center">W06</td>
					<td class="red" align="center"><?=$data['W06']['50'];?></td>
					<td class="red" align="center"><?=$data['W06']['100'];?></td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder noRightBorder"></td>
					<td class="noLeftBorder noBottomBorder noRightBorder"></td>
					<td class="noLeftBorder noBottomBorder noRightBorder"></td>
					<td class="noLeftBorder noBottomBorder"></td>
					<td class="grey" align="center">W09</td>
					<td class="red" align="center"><?=$data['W09']['50'];?></td>
					<td class="red" align="center"><?=$data['W09']['100'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="tunnelrahmen borders" cellspacing="0">
				<tr>
					<td class="grey" colspan="6" align="left">Tunnelrahmen</td>
					<td class="red"></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="stiele borders" cellspacing="0">
				<tr class="grey">
					<td align="left">Stiele</td>
					<td align="center" class="smallFont">Easy Stiel 200</td>
					<td align="center" class="smallFont">Basisstiel 124</td>
					<td align="center" class="smallFont">EVP-Pfosten</td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder"></td>
					<td class="red" align="center"><?=$data['Stiele']['Easy Stiehl 200'];?></td>
					<td class="red" align="center"><?=$data['Stiele']['Basisstiehl 124'];?></td>
					<td class="red" align="center"><?=$data['Stiele']['EVP-Pfosten'];?></td>
				</tr>
			</table>
		  
		  <p></p>
		  
		  <table class="borders smallerTable" cellspacing="0">
			  <tr>
				  <td rowspan="2" class="smallFont">Beläge</td>
				  <td class="grey" align="right">0,25</td>
				  <td class="noUpperBorder"></td>
				  <td class="grey" align="right">0,33</td>
				  <td class="noUpperBorder"></td>
				  <td class="grey" align="right">0,67</td>
			  </tr>
			  <tr>
				  <td class="grey" align="right">Stahl</td>
				  <td></td>
				  <td class="grey" align="right">Stahl</td>
				  <td></td>
				  <td class="grey" align="right">Alu</td>
			  </tr>
			  <tr>
				  <td align="right">0,50</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['0,50'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['0,50'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['0,50'];?></td>
			  </tr>
			  <tr>
				  <td align="right">0,67</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['0,67'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['0,67'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['0,67'];?></td>
			  </tr>
			  <tr>
				  <td align="right">0,75</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['0,75'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['0,75'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['0,75'];?></td>
			  </tr>
			  <tr>
				  <td align="right">1,00</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['1,00'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['1,00'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['1,00'];?></td>
			  </tr>
			  <tr>
				  <td align="right">1,25</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['1,25'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['1,25'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['1,25'];?></td>
			  </tr>
			  <tr>
				  <td align="right">1,50</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['1,50'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['1,50'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['1,50'];?></td>
			  </tr>
			  <tr>
				  <td align="right">1,75</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['1,75'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['1,75'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Alu']['1,75'];?></td>
			  </tr>
			  <tr>
				  <td align="right">2,00</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['2,00'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['2,00'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['2,00'];?></td>
			  </tr>
			  <tr>
				  <td align="right">2,50</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['2,50'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['2,50'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['2,50'];?></td>
			  </tr>
			  <tr>
				  <td align="right">3,00</td>
				  <td class="red" align="right"><?=$data['0,25 Stahl']['3,00'];?></td></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,33 Stahl']['3,00'];?></td>
				  <td></td>
				  <td class="red" align="right"><?=$data['0,67 Alu']['3,00'];?></td>
			  </tr>
		  </table>
		  
		  <p></p>
		  
		  <table class="borders" cellspacing="0">
				<tr class="grey">
					<td colspan="3" align="left">Durchstieg mit Leiter</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td colspan="3"></td>
					<td class="red" align="center"><?=$data['Durchstiege mit Leiter']['1,00'];?></td>
					<td class="red" align="center"><?=$data['Durchstiege mit Leiter']['2,00'];?></td>
					<td class="red" align="center"><?=$data['Durchstiege mit Leiter']['2,50'];?></td>
					<td class="red" align="center"><?=$data['Durchstiege mit Leiter']['3,00'];?></td>
				</tr>
			</table>
		  	
		  	<p></p>
		  	
			<table class="gelander borders" cellspacing="0">
				<tr class="grey">
					<td align="left" class="smallerFont">Geländer</td>
					<td align="center">0,67</td>
					<td align="center">1,00</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Geländer']['0,67'];?></td>
					<td class="red" align="center"><?=$data['Geländer']['1,00'];?></td>
					<td class="red" align="center"><?=$data['Geländer']['1,50'];?></td>
					<td class="red" align="center"><?=$data['Geländer']['2,00'];?></td>
					<td class="red" align="center"><?=$data['Geländer']['2,50'];?></td>
					<td class="red" align="center"><?=$data['Geländer']['3,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey" align="center" colspan="2">Bordblech</td>
					<td colspan="6" class="noUpperBorder noRightBorder"></td>
				</tr>
				<tr class="grey">
					<td align="center">0,50</td>
					<td align="center">0,67</td>
					<td align="center">0,75</td>
					<td align="center">1,00</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td align="center" class="red"><?=$data['Bordblech']['0,5'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['0,67'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['0,75'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['1,00'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['1,50'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['2,00'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['2,50'];?></td>
					<td align="center" class="red"><?=$data['Bordblech']['3,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td colspan="3" align="left">Diagonale</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td colspan="3"></td>
					<td class="red" align="center"><?=$data['Diagonale']['1,50']?></td>
					<td class="red" align="center"><?=$data['Diagonale']['2,00']?></td>
					<td class="red" align="center"><?=$data['Diagonale']['2,50']?></td>
					<td class="red" align="center"><?=$data['Diagonale']['3,00']?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders anker" cellspacing="0">
				<tr class="grey">
					<td align="left">Anker</td>
					<td align="center">0,20</td>
					<td align="center">0,30</td>
					<td align="center">0,65</td>
					<td align="center">1,00</td>
					<td align="center">1,20</td>
					<td align="center">1,50</td>
					<td align="center">1,70</td>
				</tr>
				<tr>
					<td align="left"></td>
					<td align="center" class="red"><?=$data['Anker']['0,20'];?></td>
					<td align="center" class="red"><?=$data['Anker']['0,30'];?></td>
					<td align="center" class="red"><?=$data['Anker']['0,65'];?></td>
					<td align="center" class="red"><?=$data['Anker']['1,00'];?></td>
					<td align="center" class="red"><?=$data['Anker']['1,20'];?></td>
					<td align="center" class="red"><?=$data['Anker']['1,50'];?></td>
					<td align="center" class="red"><?=$data['Anker']['1,70'];?></td>
				</tr>
				<tr class="grey">
					<td colspan="2" align="left" class="smallFont">Ringschrauben</td>
					<td align="center">120</td>
					<td align="center">160</td>
					<td align="center">230</td>
					<td align="center">300</td>
					<td align="center">350</td>
					<td align="center">400</td>
				</tr>
				<tr>
					<td colspan="2" align="left"></td>
					<td align="center" class="red"><?=$data['Ringschrauben']['120'];?></td>
					<td align="center" class="red"><?=$data['Ringschrauben']['160'];?></td>
					<td align="center" class="red"><?=$data['Ringschrauben']['230'];?></td>
					<td align="center" class="red"><?=$data['Ringschrauben']['300'];?></td>
					<td align="center" class="red"><?=$data['Ringschrauben']['350'];?></td>
					<td align="center" class="red"><?=$data['Ringschrauben']['400'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td colspan="3" align="left">Absperrung ("Affen")</td>
					<td align="center">0,33</td>
					<td align="center">0,67</td>
					<td align="center">0,75</td>
					<td align="center"Steckkonsolen>1,00</td>
				</tr>
				<tr>
					<td colspan="3"></td>
					<td class="red" align="center"><?=$data['Absperrung (\'Affen\')']['0,33'];?></td>
					<td class="red" align="center"><?=$data['Absperrung (\'Affen\')']['0,67'];?></td>
					<td class="red" align="center"><?=$data['Absperrung (\'Affen\')']['0,75'];?></td>
					<td class="red" align="center"><?=$data['Absperrung (\'Affen\')']['1,00'];?></td>
				</tr>
			</table>
		  </div>
		  
		  <div class="column rightColumn">
			<table class="borders" cellspacing="0">
				<tr>
					<td width="33%" class="noLeftBorder noUpperBorder"></td>
					<td class="grey" align="center">0,25</td>
					<td class="grey" align="center">0,33</td>
					<td class="grey" align="center">0,50</td>
					<td class="grey" align="center">0,67</td>
					<td class="grey" align="center">0,75</td>
					<td class="grey" align="center">1,00</td>
				</tr>
				<tr>
					<td width="33%" class="grey" align="left">Konsolen</td>
					<td class="red" align="center"><?=$data['0,25']['Konsolen'];?></td>
					<td class="red" align="center"><?=$data['0,33']['Konsolen'];?></td>
					<td class="red" align="center"><?=$data['0,5']['Konsolen'];?></td>
					<td class="red" align="center"><?=$data['0,67']['Konsolen'];?></td>
					<td class="red" align="center"><?=$data['0,75']['Konsolen'];?></td>
					<td class="red" align="center"><?=$data['1,00']['Konsolen'];?></td>
				</tr>
				<tr>
					<td class="grey" align="left">Steckkonsolen</td>
					<td class="red" align="center"><?=$data['0,25']['Steckkonsolen'];?></td>
					<td class="red" align="center"><?=$data['0,33']['Steckkonsolen'];?></td>
					<td class="noRightBorder"></td>
					<td class="noRightBorder"></td>
					<td class="noRightBorder"></td>
					<td class="noRightBorder"></td>
				</tr>
				<tr>
					<td colspan="3" class="grey" align="left">Konsolstreben</td>
					<td class="red" align="center"><?=$data['0,5']['Konsolstreben'];?></td>
					<td class="red" align="center"><?=$data['0,67']['Konsolstreben'];?></td>
					<td class="red" align="center"><?=$data['0,75']['Konsolstreben'];?></td>
					<td class="red" align="center"><?=$data['1,00']['Konsolstreben'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left">Rohre</td>
					<td align="center">0,50</td>
					<td align="center">1,00</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
					<td align="center">2,50</td>
				</tr>
				<tr>
					<td></td>
					<td align="center" class="red"><?=$data['Rohre']['0,50'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['1,00'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['1,50'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['2,00'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['2,50'];?></td>
				</tr>
				<tr class="grey">
					<td align="left">Rohre</td>
					<td align="center">3,00</td>
					<td align="center">3,50</td>
					<td align="center">4,00</td>
					<td align="center">4,50</td>
					<td align="center">5,00</td>
				</tr>
				<tr>
					<td></td>
					<td align="center" class="red"><?=$data['Rohre']['3,00'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['3,50'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['4,00'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['4,50'];?></td>
					<td align="center" class="red"><?=$data['Rohre']['5,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey">Distanzkupplungen</td>
					<td class="red"><?=$data['Distanzkupplungen / Riegelaufnahme']['Riegelaufnahme (doppelt)'];?></td>
				</tr>
				<tr>
					<td class="grey">Riegelaufnahme (einfach)</td>
					<td class="red"><?=$data['Distanzkupplungen / Riegelaufnahme']['Riegelaufnahme (einfach)'];?></td>
				</tr>
				<tr>
					<td class="grey">Riegelaufnahme (doppelt)</td>
					<td class="red"><?=$data['Distanzkupplungen / Riegelaufnahme']['Riegelaufnahme (doppelt)'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="center" class="smallFont">TK 180º</td>
					<td align="center" class="smallFont">TK 90º</td>
					<td align="center" class="smallFont">NK</td>
					<td align="center" class="smallFont">DK</td>
					<td align="center" class="smallFont">GK Rohr</td>
					<td align="center" class="smallFont">GK Teller</td>
				</tr>
				<tr>
					<td class="red" align="center"><?=$data['Kupplungen']['TK 180°'];?></td>
					<td class="red" align="center"><?=$data['Kupplungen']['TK 90°'];?></td>
					<td class="red" align="center"><?=$data['Kupplungen']['NK'];?></td>
					<td class="red" align="center"><?=$data['Kupplungen']['DK'];?></td>
					<td class="red" align="center"><?=$data['Kupplungen']['GK Rohr'];?></td>
					<td class="red" align="center"><?=$data['Kupplungen']['GK Teller'];?></td>
				</tr>
				<tr>
					<td colspan="2" class="grey">Zugfeste + Bolzen</td>
					<td class="red" align="center"><?=$data['Kupplungen']['Zugfeste + Bolzen'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey">Dachfangnetze (2 x10 m)</td>
					<td class="red"><?=$data['Dachfang + Zubehör']['Dachfangnetze (2 x 10m)'];?></td>
				</tr>
				<tr>
					<td class="grey">Blaue Bendel</td>
					<td class="red"><?=$data['Dachfang + Zubehör']['Blaue Bendel'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="50%">Doka-Träger</td>
					<td align="center">3,90</td>
					<td align="center">4,90</td>
					<td align="center">5,90</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Doka-Träger']['3,90'];?></td>
					<td class="red" align="center"><?=$data['Doka-Träger']['4,90'];?></td>
					<td class="red" align="center"><?=$data['Doka-Träger']['5,90'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="35%">Gitterträger (Stahl)</td>
					<td align="center">3-4</td>
					<td align="center">5,00</td>
					<td align="center">6,00</td>
					<td align="center">8,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Gitterträger (Stahl)']['3-4'];?></td>
					<td class="red" align="center"><?=$data['Gitterträger (Stahl)']['5,00'];?></td>
					<td class="red" align="center"><?=$data['Gitterträger (Stahl)']['6,00'];?></td>
					<td class="red" align="center"><?=$data['Gitterträger (Stahl)']['8,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="35%">Gitterträger (Alu)</td>
					<td align="center">3-4</td>
					<td align="center">5,00</td>
					<td align="center">6,00</td>
					<td align="center">8,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Gitterträger (Alu)']['3-4'];?></td>
					<td class="red" align="center"><?=$data['Gitterträger (Alu)']['5,00'];?></td>
					<td class="red" align="center"><?=$data['Gitterträger (Alu)']['6,00'];?></td>
					<td class="red" align="center"><?=$data['Gitterträger (Alu)']['8,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="35%">Schwerlast GT (750)</td>
					<td align="center">2,00</td>
					<td align="center">3,00</td>
					<td align="center">4,00</td>
					<td align="center">5,00</td>
					<td align="center">6,00</td>
					<td align="center">7,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Schwerlast GT (750)']['2,00'];?></td>
					<td class="red" align="center"><?=$data['Schwerlast GT (750)']['3,00'];?></td>
					<td class="red" align="center"><?=$data['Schwerlast GT (750)']['4,00'];?></td>
					<td class="red" align="center"><?=$data['Schwerlast GT (750)']['5,00'];?></td>
					<td class="red" align="center"><?=$data['Schwerlast GT (750)']['6,00'];?></td>
					<td class="red" align="center"><?=$data['Schwerlast GT (750)']['7,00'];?></td>
				</tr>
			</table>			
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey" width="35%">GT-Verbinder</td>
					<td class="red" width="15%" align="center"><?=$data['GT']['GT Verbinder'];?></td>
					<td class="grey" width="35%">GT-Wandschuhe</td>
					<td class="red" width="15%" align="center"><?=$data['GT']['GT Wandschuhe'];?></td>
				</tr>
				<tr>
					<td class="grey">GT-Schrauben</td>
					<td class="red" align="center"><?=$data['GT']['GT Schrauben'];?></td>
					<td class="noBottomBorder noRightBorder"></td>
					<td class="noBottomBorder noRightBorder"></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td>Staubschutznetz</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Staubschutznetz']['2,50'];?></td>
					<td class="red" align="center"><?=$data['Staubschutznetz']['3,00'];?></td>
				</tr>
				<tr class="grey">
					<td>Sandstrahlnetz</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Sandstrahlnetz']['2,50'];?></td>
					<td class="red" align="center"><?=$data['Sandstrahlnetz']['3,00'];?></td>
				</tr>
				<tr class="grey">
					<td>Folie</td>
					<td align="center">2,50</td>
					<td align="center">3,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Folie']['2,50'];?></td>
					<td class="red" align="center"><?=$data['Folie']['3,00'];?></td>
				</tr>
			</table>
		
			<p></p>
			
			<table class="borders smallerTable" cellspacing="0">
				<tr class="grey">
					<td align="left" width="50%">Kabelbinder</td>
					<td align="center">Dick</td>
					<td align="center">Dünn</td>
				</tr>
				<tr>
					<td></td>
					<td class="red" align="center"><?=$data['Kabelbinder']['Dick'];?></td>
					<td class="red" align="center"><?=$data['Kabelbinder']['Dünn'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable" cellspacing="0">
				<tr>
					<td class="grey">Spax</td>
					<td class="red"><?=$data['Spax, Nägel, Fallstecker']['Spax'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable" cellspacing="0">
				<tr>
					<td class="grey">Nägel</td>
					<td class="red"><?=$data['Spax, Nägel, Fallstecker']['Nägel'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable" cellspacing="0">
				<tr>
					<td class="grey">Fallstecker</td>
					<td class="red"><?=$data['Spax, Nägel, Fallstecker']['Fallstecker'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="orange">
					<td align="center">Achtung: Rückseite beachten!</td>
				</tr>
			</table>
		  </div>
		</div>
		
		<!-- Start of the second page-->
		<div class="row keep-together">
			<div class="column leftColumn">
				<table class="fullwidth">
					<tr><td class="orange" align="left">Flex</td></tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td class="noLeftBorder noUpperBorder" width="40%"></td>
						<td class="grey" align="center">0,50</td>
						<td class="grey" align="center">1,00</td>
						<td class="grey" align="center">1,50</td>
						<td class="grey" align="center">2,00</td>
						<td class="grey" align="center">2,50</td>
						<td class="grey" align="center">3,00</td>
						<td class="grey" align="center">3,50</td>
					</tr>
					<tr>
						<td class="grey">Stiele</td>
						<td class="red" align="center"><?=$data['Stiele 2']['0,50'];?></td>
						<td class="red" align="center"><?=$data['Stiele 2']['1,00'];?></td>
						<td class="red" align="center"><?=$data['Stiele 2']['1,50'];?></td>
						<td class="red" align="center"><?=$data['Stiele 2']['2,00'];?></td>
						<td class="red" align="center"><?=$data['Stiele 2']['2,50'];?></td>
						<td class="red" align="center"><?=$data['Stiele 2']['3,00'];?></td>
						<td class="red" align="center"><?=$data['Stiele 2']['3,50'];?></td>
					</tr>
						<tr>
						<td class="grey">Stiele ohne Zapfen</td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['0,50'];?></td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['1,00'];?></td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['1,50'];?></td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['2,00'];?></td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['2,50'];?></td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['3,00'];?></td>
						<td class="red" align="center"><?=$data['Stiele ohne Zapfen']['3,50'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr class="grey">
						<td colspan="12" align="left">Riegel</td>
					</tr>
					<tr class="grey">
						<td align="center" class="smallFont">0,25</td>
						<td align="center" class="smallFont">0,33</td>
						<td align="center" class="smallFont">0,50</td>
						<td align="center" class="smallFont">0,67</td>
						<td align="center" class="smallFont">0,75</td>
						<td align="center" class="smallFont">1,00</td>
						<td align="center" class="smallFont">1,25</td>
						<td align="center" class="smallFont">1,50</td>
						<td align="center" class="smallFont">1,75</td>
						<td align="center" class="smallFont">2,00</td>
						<td align="center" class="smallFont">2,50</td>
						<td align="center" class="smallFont">3,00</td>
					</tr>
						<tr class="red">
						<td align="center"><?=$data['Riegel']['0,25'];?></td>
						<td align="center"><?=$data['Riegel']['0,33'];?></td>
						<td align="center"><?=$data['Riegel']['0,50'];?></td>
						<td align="center"><?=$data['Riegel']['0,67'];?></td>
						<td align="center"><?=$data['Riegel']['0,75'];?></td>
						<td align="center"><?=$data['Riegel']['1,00'];?></td>
						<td align="center"><?=$data['Riegel']['1,25'];?></td>
						<td align="center"><?=$data['Riegel']['1,50'];?></td>
						<td align="center"><?=$data['Riegel']['1,75'];?></td>
						<td align="center"><?=$data['Riegel']['2,00'];?></td>
						<td align="center"><?=$data['Riegel']['2,50'];?></td>
						<td align="center"><?=$data['Riegel']['3,00'];?></td>
					</tr>
					<tr>
						<td colspan="7" class="grey" align="left">Riegel (verstärkt)</td>
						<td class="red" align="center"><?=$data['Riegel verstärkt']['1,50'];?></td>
						<td class="red" align="center"><?=$data['Riegel verstärkt']['1,75'];?></td>
						<td class="red" align="center"><?=$data['Riegel verstärkt']['2,00'];?></td>
						<td class="red" align="center"><?=$data['Riegel verstärkt']['2,50'];?></td>
						<td class="red" align="center"><?=$data['Riegel verstärkt']['3,00'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr class="grey">
						<td align="center" colspan="2" width="40%">Riegeldiagonale</td>
						<td align="center">0.50H</td>
						<td align="center">1.00H</td>
						<td align="center">1.50H</td>
						<td align="center">2.00H</td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">1,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['0,50H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,00H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,50H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['2,00H']['1,00'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">1,50</td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['0,50H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,00H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,50H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['2,00H']['1,50'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">2,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['0,50H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,00H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,50H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['2,00H']['2,00'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">2,50</td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['0,50H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,00H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,50H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['2,00H']['2,50'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder"></td>
						<td align="center" class="grey">1,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['0,50H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,00H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['1,50H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Riegeldiagonale']['2,00H']['3,00'];?></td>
					</tr>
					
					
					
					<tr class="grey">
						<td align="center" colspan="2" width="40%">Knotendiagonale</td>
						<td align="center">0.50H</td>
						<td align="center">1.00H</td>
						<td align="center">1.50H</td>
						<td align="center">2.00H</td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">1,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['0,50H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,00H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,50H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['2,00H']['1,00'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">1,50</td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['0,50H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,00H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,50H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['2,00H']['1,50'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">2,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['0,50H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,00H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,50H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['2,00H']['2,00'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">2,50</td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['0,50H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,00H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,50H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['2,00H']['2,50'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder"></td>
						<td align="center" class="grey">3,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['0,50H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,00H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['1,50H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Knotendiagonale']['2,00H']['3,00'];?></td>
					</tr>
					
					
					
					<tr class="grey">
						<td align="center" colspan="2" width="40%">Horizontaldiagonale</td>
						<td align="center">0.50H</td>
						<td align="center">1.00H</td>
						<td align="center">1.50H</td>
						<td align="center">2.00H</td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">1,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['0,50H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,00H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,50H']['1,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['2,00H']['1,00'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">1,50</td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['0,50H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,00H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,50H']['1,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['2,00H']['1,50'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">2,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['0,50H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,00H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,50H']['2,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['2,00H']['2,00'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">2,50</td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['0,50H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,00H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,50H']['2,50'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['2,00H']['2,50'];?></td>
					</tr>
					<tr>
						<td align="center" class="noLeftBorder noBottomBorder"></td>
						<td align="center" class="grey">3,00</td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['0,50H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,00H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['1,50H']['3,00'];?></td>
						<td align="center" class="red"><?=$data['nestedData']['Horizontaldiagonale']['2,00H']['3,00'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="fullwidth">
					<tr><td class="orange" align="left">Zubehör</td></tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td class="grey" width="75%">Durchstiegsleiter</td>
						<td class="red" width="25%" align="center"><?=$data['Zubehör']['Durchstiegsleiter'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td width="37%" class="grey">Pisser</td>
						<td width="13%" class="red" align="center"><?=$data['Zubehör']['Pisser'];?></td>
						<td width="37%" class="grey">Pisser + Riegelaufnahme</td>
						<td width="13%" class="red" align="center"><?=$data['Zubehör']['Pisser + Riegelaufnahme'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td width="37%" class="grey" align="center">Firmenschild klein</td>
						<td width="13%" class="red"><?=$data['Zubehör']['Firmenschild klein'];?></td>
						<td width="37%" class="grey" align="center">Firmenschild groß</td>
						<td width="13%" class="red"><?=$data['Zubehör']['Firmenschild groß'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders half">
					<tr>
						<td width="60%" class="grey">Bauschutztür</td>
						<td width=40%" class="red" align="center"><?=$data['Zubehör']['Bausschutztür'];?></td>					
					</tr>
					<tr>
						<td class="grey">Bauzaun</td>
						<td class="red" align="center"><?=$data['Zubehör']['Bauzaun'];?></td>					
					</tr>
					<tr>
						<td class="grey">Steine</td>
						<td class="red" align="center"><?=$data['Zubehör']['Steine'];?></td>					
					</tr>
				</table>
				
				<p></p>
				
				<div class="bemerkungenTitle"><b>Bemerkungen</b></div>
				
				<p></p>
				
				<div class="bemerkungen"><?=$data['Bemerkungen'];?></div>
			</div>
			<div class="column rightColumn">
			
			<table class="borders">
				<tr class="grey">
					<td width="33%" align="center">Anfänger</td>
					<td width="33%" align="center">Kurz (UVB 24)</td>
					<td align="center">Lang (UVB 49)</td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder"></td>
					<td class="red" align="center"><?=$data['Anfänger']['Kurz (UVB 24)'];?></td>
					<td class="red" align="center"><?=$data['Anfänger']['Kurz (UVB 49)'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td colspan="2" width="50%" align="left">Podesttreppe</td>
					<td width="17%" align="center">1,50</td>
					<td width="17%" align="center">2,50</td>
					<td width="16%" align="center">3,00</td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder"></td>
					<td align="center" class="grey">0,50 H</td>
					<td align="center" class="red"><?=$data['nestedData']['Podesttreppe']['1,50']['0,50H'];?></td>
					<td align="center" class="grey"></td>
					<td align="center" class="grey"></td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder"></td>
					<td align="center" class="grey">1,00 H</td>
					<td align="center" class="red"><?=$data['nestedData']['Podesttreppe']['1,50']['1,00H'];?></td>
					<td align="center" class="grey"></td>
					<td align="center" class="grey"></td>
				</tr>
				<tr>
					<td class="noLeftBorder noBottomBorder"></td>
					<td align="center" class="grey">2,00 H</td>
					<td align="center" class="grey"></td>
					<td align="center" class="red"><?=$data['nestedData']['Podesttreppe']['2,50']['2,00H'];?></td>
					<td align="center" class="red"><?=$data['nestedData']['Podesttreppe']['3,00']['2,00H'];?></td>
				</tr>
			</table>
			
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td width="40%" class="grey" align="left">Treppengeländer</td>
					<td width="15%" class="grey" align="center">2,50</td>
					<td width="15%" class="red" align="center"><?=$data['Treppengeländer']['2,50'];?></td>
					<td width="15%" class="grey" align="center">3,00</td>
					<td width="15%" class="red" align="center"><?=$data['Treppengeländer']['3,00'];?></td>
				</tr>
				<tr>
					<td width="40%" class="grey" align="left">Umlaufgeländer</td>
					<td width="15%" class="red" align="center"><?=$data['Zubehör']['Umlaufgeländer'];?></td>
					<td width="15%" class="noRightBorder noBottomBorder"></td>
					<td width="15%" class="noRightBorder noBottomBorder"></td>
					<td width="15%" class="noRightBorder noBottomBorder"></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders">
				<tr>
					<td width="40%" class="grey" align="left">Schaltafeln</td>
					<td width="15%" class="red" align="center"><?=$data['Zubehör']['Schaltafeln'];?></td>
					<td width="15%" class="noBottomBorder noRightBorder noUpperBorder"</td>
					<td width="15%" class="noBottomBorder noRightBorder noUpperBorder"</td>
					<td width="15%" class="noBottomBorder noRightBorder noUpperBorder"</td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td width="40%" class="grey" align="left">Gitterboxen</td>
					<td width="15%" class="red" align="center"><?=$data['Zubehör']['Gitterboxen'];?></td>
					<td width="30%" class="grey" align="center">Geländerboxen</td>
					<td width="15%" class="red" align="center"><?=$data['Zubehör']['Geländerboxen'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders">
				<tr>
					<td width="40%" class="grey" align="left">Eimer</td>
					<td width="15%" class="red" align="center"><?=$data['Zubehör']['Eimer'];?></td>
					<td width="15%" class="noBottomBorder noRightBorder noUpperBorder"</td>
					<td width="15%" class="noBottomBorder noRightBorder noUpperBorder"</td>
					<td width="15%" class="noBottomBorder noRightBorder noUpperBorder"</td>
				</tr>
			</table>
		  </div>
		</div>
		
	</body>
</html>

<style>
* 	{
  		box-sizing: border-box;
	}

	/* Create two equal columns that floats next to each other */
	.column {
	  float: left;
	  width: 50%;
	  padding: 10px;
	  height: 300px; /* Should be removed. Only for demonstration */
	}

	/* Clear floats after the columns */
	.row:after {
	  content: "";
	  display: table;
	  clear: both;
	}

	.companyLogo {padding: 10px; height:80%; width:auto;}
	.borders, .borders th, .borders td {border:1px solid black;}
	.borders {width:100%;  border-collapse: collapse;}
	.basisrahmen {width:100%;}
	.fullwidth {width:100%;}
	.grey {background-color:lightgrey;}
	.red {color:red;}
	table {table-layout:fixed!important;}
	.smallerTable {width:75%!important;}
	.half {width:50%!important;}
	.orange {background-color: orange;}
	.documentData {border-collapse: collapse; width:100%;}
	.noBottomBorder {border-bottom: 1px solid white!important;}
	.noRightBorder {border-right: 1px solid white!important;}
	.noLeftBorder {border-left: 1px solid white!important;}
	.noUpperBorder {border-top: 1px solid white !important;}
	.smallFont {font-size:small;}
	.smallerFont {font-size: 12px;}
	.keep-together {
    	page-break-inside: avoid;
	}
	
	.break-after {
    	page-break-after: always;
	}
	
	.bemerkungenTitle {font-size: 22px; font-family:Helvetica;}
	.bemerkungen {width:200%; border:1px solid black;}
	
</style>