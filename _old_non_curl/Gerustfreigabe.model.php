<?php

require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Partners.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';

class C_GerustfreigabeX
{
    private $db;
    private $documentation;
    private $partners;
    private $projects;
    private $customers;

    public function __construct() {
        $this->db = CommonData::getDbConnection();
        $this->documentation = new Documentation();
        $this->partners = new Partners();
        $this->projects = new Projects();
        $this->customers = new Customers();
    }

    public function getData($schemaId,$documentId)
    {
        $documentData = $this->documentation->getDocumentId($schemaId, $documentId);

        if ($documentData) {

            $modelData = [];

            $partners = $this->partners->get($documentData['documentRelKey1']);
            $modelData['firstPartner'] =
                $partners[0]['personalTitle'].
                $partners[0]['firstName'].
                $partners[0]['lastName'];

            $modelData['blankTitle'] = $documentData['title'];
            $modelData['projectData'] = $this->getProjectData($documentData['documentRelKey1']);

            if(!empty($projectData)) {
                $modelData['customerData'] = $this->getCustomerData($projectData['ktr']);
            }

            foreach ($documentData['children'] as $key => $value) {
                if ($value['type'] != 'headline') {
                    if ($value['type'] == 'signatureField') {
                        $modelData[$value['title']] = $value['filePath'];
                    } else {
                        $modelData[$value['title']] = $value['reportedValue'];
                    }
                }
            }
			$employeesAPI = new \v3\Employees();
			$modelData['Job-Ausführender'] = $this->resolveEmployeeDisplayName($modelData['Job-Ausführender'], $employeesAPI);
			$modelData['Job-Verantwortlicher'] = $this->resolveEmployeeDisplayName($modelData['Job-Verantwortlicher'], $employeesAPI);

			$modelData['companyLogo'] = $this->getLogoUrl();
            $modelData['dokumentNr'] = $documentId;

            return $modelData;

        } else {
                throw new RestException(400, "No such data for the provided parameters!");
        }
    }

    private function getProjectData($projectNo) {
        $result = $this->projects->get($projectNo);
        $projects = [];

        foreach ($result as $k=>$v) {
            $projects[$v['year']] = $v;
        }

        ksort($projects);

        return end($projects);
    }

    private function getCustomerData($customerNo) {
        $customerData = $this->customers->get($customerNo);
        return $customerData;
    }

    //Company logo
    private function getLogoUrl() {
        $stmt = $this->db->prepare("
			SELECT value FROM settings where name = 'logo'
		");
        $stmt->execute();

        $data = $stmt->fetch();

        return $data[0];
    }

	/**
	 * @param string $employeeNo
	 * @param \v3\Employees $employeesAPI
	 * @return string|null
	 * @throws \Luracast\Restler\RestException
	 */
	private function resolveEmployeeDisplayName($employeeNo, \v3\Employees $employeesAPI)
	{
		if ($employeeNo) {
			$employee = $employeesAPI->getsingle($employeeNo);
			return $employee['displayName'];
		}
		return $employeeNo;
	}
}

