<?php
	require_once ABS_PATH.'Hours.php';
	require_once ABS_PATH.'Documentation.php';
	require_once ABS_PATH.'Addresses.php';
	require_once ABS_PATH.'Projects.php';
	require_once ABS_PATH.'Customers.php';
	
class C_Abnahmeprotokoll {
	private $db;
	private $hours;
	private $documentation;
	private $addresses;
	private $projects;
	private $customers;
	
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->hours = new Hours();
		$this->documentation = new Documentation();
		$this->addresses = new Addresses();
		$this->projects = new Projects();
		$this->customers = new Customers();
	}
	
	public function getData($schemaId, $documentId) {
		
		$templateData = [];
		
		$documentData = $this->documentation->getDocumentId($schemaId, $documentId);
		
		$templateData['wo'] = $documentData['documentRelKey1'];
		
		if(!empty($documentData['documentRelKey2'])) {
			$templateData['wo'].= '-'.$documentData['documentRelKey2'];
		}
		$templateData['documentDate'] = date('d.m.Y',strtotime($documentData['documentCreatedOn']));
		$templateData['projectNo'] = $documentData['documentRelKey1'];
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		$templateData['customerName'] = $projectData['customer_name'];
		
		if(!empty($projectData['customer_postcode'])) {
			$templateData['plzOrt'] = $projectData['customer_postcode'].' ';
		}
		
		if(!empty($projectData['customer_city'])) {
			$templateData['plzOrt'] .= $projectData['customer_city'];
		}
		$templateData['plzOrt'] = trim($templateData['plzOrt']);
		
		if($templateData['plzOrt'] == ',') {
			$templateData['plzOrt'] = '';
		}
		
		if(!empty($projectData)) {
			$customerData = $this->getCustomerData($projectData['knr']);
		}
		
		if($customerData) {
			$templateData['customerName'] = $customerData['name'];
		}
		
		$templateData['address'] = !empty($projectData['customer_address']) ? $projectData['customer_address'] : $customerData['adresse1'].', '.$customerData['plz'].' '.$customerData['ort'];
		
		if($projectData) {
			$templateData['projectName'] = $projectData['project_name'];
			$templateData['projectDate'] = date('d.m.Y',strtotime($projectData['start_date']));
			$templateData['projectNumber'] = $projectData['ktr'];
		}
		
		if(!empty($documentData)) {
			foreach ($documentData['children'] as $k=>$v) {
				$ids[$v['id']] = $v;
			}
		
			foreach ($ids as $key => $value) {
				switch ($value['indentationLevel']) {
					case 4:
						if($value['type'] == 'date') {
							$templateData['Beanstandungen'][$ids[$ids[$ids[$value['parentId']]['parentId']]['title']]][$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = date('d.m.Y',strtotime(DateTimeTools::reformat_timestamp($value['reportedValue'])));
						} else if ($value['type'] == 'photo') {
                            $templateData['Beanstandungen'][$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['filePath'];
                        } else if($value['type'] == 'signatureField') {
							$templateData['Beanstandungen'][$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['filePath'];
						} else {
							$templateData['Beanstandungen'][$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['reportedValue'];
						}
						break;
					case 3:
						if($value['type'] == 'date') {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = date('d.m.Y',strtotime(DateTimeTools::reformat_timestamp($value['reportedValue'])));
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['filePath'];
						} else {
							$templateData[$ids[$ids[$value['parentId']]['parentId']]['title']][$ids[$value['parentId']]['title']][$ids[$value['id']]['title']] = $value['reportedValue'];
						}
						break;
					case 2:
						if($value['type'] == 'date') {
							$templateData[$ids[$value['parentId']]['title']][$value['title']] = date('d.m.Y',strtotime(DateTimeTools::reformat_timestamp($value['reportedValue']))); 
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$ids[$value['parentId']]['title']][$value['title']] = $value['filePath'];
						} else {
							$templateData[$ids[$value['parentId']]['title']][$value['title']] = $value['reportedValue'];
						}
						break;
					 default:
						if($value['type'] == 'date') {
							$templateData[$value['title']] = date('d.m.Y',strtotime(DateTimeTools::reformat_timestamp($value['reportedValue'])));
						} else if ($value['type'] == 'photo' || $value['type'] == 'signatureField') {
							$templateData[$value['title']] = $value['filePath'];
						} else {
							$templateData[$value['title']] = $value['reportedValue'];
						} 	
				}	
			}
		} else {
			return [];
		}
		return $templateData;		
	}
	
	private function getProjectData($projectNo) {
		$result = $this->projects->get($projectNo);
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		
		return end($projects);
	}	
	
	private function getCustomerData($customerNo) {
		$customerData = $this->customers->get($customerNo);
		return $customerData;
	}
}
