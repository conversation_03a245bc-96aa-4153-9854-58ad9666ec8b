<html>
<?php
$data = $this->data;


	function checkForEmptyValues($hersteller, $position, $artikelbezeichnug, $grund, $url) {
		if( (strlen($hersteller) == 0) && (strlen($position) == 0) && (strlen($artikelbezeichnug) == 0) && (strlen($grund) == 0) && (strlen($url) == 0) ) {
			return true;	
		} else {
			return false;
		}
	}
	
	/**
	 * Checks if a file exists based on an image url. Returns true if the file exists and false if it doesn't.
	 * @param string $url The url of the image
	 * @return bool
	 */
	function UR_exists($url){ 
 		  if (!$url) return;
 		  $headers=get_headers($url); 
   		  return stripos($headers[0],"200 OK")?true:false; 
	}

?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Abnahmeprotokoll</title>
	</head>
	<body>
	<table class="header" cellpadding="0" cellspacing="0">
			<tbody>
				<tr>
					<th><table><tr><td class="title" style="vertical-align:top;  width: 50%;">Abnahmeprotokoll</tdAbnahmeprotokoll></tr><tr><td align="left" class="title" style="font-size: medium; color: red;">Lt. Auftragsnummer zur Rechnung</td></tr></table></th>
					<th rowspan="4" align="right" width="50%"></th>
				</tr>
				<tr>
					<td></td>
					<td></td>
				</tr>
				<tr>
					<td>
						<div style="vertical-align:bottom"><?=$data['customerName'];?></div>
						<div class="top topBorder">Kundenname</div>
					</td>
					<td></td>
				</tr>
				<tr>
					<td>
						<div class="underlinedDiv" style="vertical-align:bottom"><?=$data['projectName'];?></div>
						<div class="top topBorder">Auftragsnummer</div>
					</td>
					<td></td>
				</tr>
		</tbody>
	</table>
	<br>
		<table class="lieferadresse" class="display-none">
			<tbody>
				<tr  class="hidden">
<!--                                        I added class hidden to address: Logo sowie Adresse oben rechts weglassen.
                    Link: https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/35-->
					<th colspan="4" class="subTitle" align="left">Lieferadresse</th>
				</tr>
				<tr  class="hidden">
<!--                                        I added class hidden to address: Logo sowie Adresse oben rechts weglassen.
                    Link: https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/issues/35-->
                    <td align="left" colspan="2" width="50%">
						<div class="underlinedDiv" <?php if(empty($data['address'])) {echo ' style="visibility:hidden;"';} ?>><?php echo !empty($data['address']) ? $data['address'] : str_repeat('&nbsp;', 2);?></div>
						<div class="top topBorder">Straße, Nr. </div>
					</td>
					<td colspan="2" width="50%">
						<div class="underlinedDiv" <?php if(empty($data['plzOrt'])) {echo ' style="visibility:hidden;"';} ?>><?php echo !empty($data['plzOrt']) ? $data['plzOrt'] : str_repeat('a', 1);?></div>
						<div class="top topBorder">PLZ, Ort</div>
					</td>
				</tr>
			</tbody>
		</table>
		<br>
		<table class="freeText" style="display:none">
			<tr>
				<th width="70%" align="left">Wurde die Ware vollständing und ohne Beanstandung geliefert</th>
<?php
    // checkbox is set if its "true" (or its derivates) or missing (default case)
    if(in_array($data['Wurde die Ware vollständing und ohne Beanstandung geliefert'], ['true',1,true,'on']) || !$data['Wurde die Ware vollständing und ohne Beanstandung geliefert']) { ?>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" class="checkbox">ja</th>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox">nein</th>
<?php } else { ?>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox">ja</th>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="unchecked" class="checkbox">nein</th>
<?php } ?>
			</tr>
		</table>
	<br>
		<div align="left" class="subTitle" <?php echo ($data['Wurde die Ware vollständing und ohne Beanstandung geliefert'] == 'true') ?  'style="display:none"' : '';?>><b>Beanstandungen</b></div>
<?php $counter = 1; ?>
<?php foreach ($data['Beanstandungen'] as $k => $v) { ?>
<?php foreach ($v as $index => $complaint) { ?>
<?php if (checkForEmptyValues($complaint['Hersteller'],$complaint['Position'],$complaint['Artikelbezeichnug'],$complaint['Grund'],$complaint['Foto']) == false) { ?>
			<table class="complaints" cellspacing="0" <?php echo ($data['Wurde die Ware vollständing und ohne Beanstandung geliefert'] == 'true') ?  'style="display:none"' : '';?>>
							<tr>
								<td align="left" width="5%">
									<div class="text">Nr.</div>
									<div align="left" class="text"><?=$counter;?></div>
								</td>
								<td align="left" width="15%">
									<div class="text">Hersteller</div>
									<div align="left" class="text"><?=$complaint['Hersteller'];?></div>
								</td>
								<td align="left" width="10%">
									<div class="text">Pos</div>
									<div align="left" class="text"><?=$complaint['Position'];?></div>
								</td>
								<td align="left" width="50%">
									<div class="text">Art.-Bezeichnung</div>
									<div align="left" class="text"><?=$complaint['Artikelbezeichnug'];?></div>
								</td>
								<td align="left" width="20%" style="vertical-align:top">
									<div class="text" style="vertical-align:top">Bestellt</div>
								</td>
							</tr>
							<tr>
								<td align="left" colspan="5">
									<table style="width: 100%;" class="grund">
										<tr>
											<th align="left" width="50%" style="vertical-align:top"><?=$complaint['Grund'];?></th>
											<?php if (UR_exists($complaint['Foto']) == true) { ?>	
												<th align="left" width="50%" style="vertical-align:top"><?php echo '<img src="'.$complaint['Foto'].'" alt="grund" class="grundPhoto grundSpan"' ;?></th>
											<?php } ?>
										</tr>
									</table>
								</td>
							</tr>
					</table>
		<br>
<?php $counter++ ;} ?>
<?php } ?>
<?php } ?>
		<br>
		<table class="freeText">
			<tr>
				<th width="70%" align="left">Sind Fußböden, Türen, Tapeten, Wände und Treppenhaus unbeschädigt</th>
<?php
    // checkbox is set if its "true" (or its derivates) or missing (default case)
    if(in_array($data['Sind Fußböden, Türen, Tapeten, Wände und Treppenhaus unbeschädigt'], ['true',true,1,'on']) || !$data['Sind Fußböden, Türen, Tapeten, Wände und Treppenhaus unbeschädigt']) { ?>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" class="checkbox">ja</th>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox">nein</th>
<?php } else { ?>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox">ja</th>
				<th width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="unchecked" class="checkbox">nein</th>
<?php } ?>
			</tr>
			<tr>
				<td colspan="3"><b>Eine nachträgliche Schadensmeldung wird nicht anerkannt</b></td>
				<td></td>
			</tr>
		</table>
		<br>
		<div align="left" class="subTitle" <?php echo ($data['Wurde die Ware vollständing und ohne Beanstandung geliefert'] == 'true') ?  'style="display:none"' : '';?>><b>Schadensmeldungen</b></div>
<table class="reports">
    <?php
    $schadensmeldungNullCounter = 0;
    foreach ($data['Schadensmeldungen'] as $schadensmeldung) {
    ?>
<tr>
	<th align="left" width="50%" valign="top">
        <?php
        if ($schadensmeldung === null) {
            $schadensmeldungNullCounter += 1;
            // print it if the last of the three Schadensmeldung is also empty
            if ($schadensmeldungNullCounter == count($data['Schadensmeldungen'])) {
                echo 'ohne';
            }
        }
        else {
           echo $schadensmeldung['Grund'];
        }
        ?>
    </th>
	<th align="left" width="50%">
	<?php if (UR_exists($schadensmeldung['Foto']) == true) { ?>
		<img src="<?=$schadensmeldung['Foto'];?>" alt="grund" class="grundPhoto">
	<?php } ?>
	</th>
</tr>
<?php } ?>
</table>
		<br>
		<div align="left" class="text">
			Der Kunde bestätigt mit seiner Unterschrift, dass - soweit nicht auf dem Abnahmeprotokoll schriftlich festgehalten wurde die
			gelieferten und montierten Küchenmöbel, Geräte, Spüle und Zubehör zum Zeitpunkt der Übergabe frei von Mängeln sind
			und ordnungsgemäß montiert bzw. angeschlossen wurden.
			<b>Die Spüle, das Kochfeld und die Arbeitsplatte sind ohne Beschädigung!</b>
		</div>
	<br>
		<table class="signatures">
			<tr>
				<th>
					<div align="left" class="underlined">
<?php 					if ($data['Datum']) { ?>
<?= 							$data['Datum'].','; ?>
<?php 					} ?>	
<?php if (UR_exists($data['Unterschrift Kunde']) == true) { ?>		
<?= 						'<img src="'.$data['Unterschrift Kunde'].'" alt="signature" class="signature">'; ?>	
<?php } ?>
					</div>
					<div class="top topBorder" align="left">Datum, Unterschrift Kunde</div>
				</th>
			</tr>
		</table>
		<br>
    <?php if($data['Bezahlt']) {?>
		<table class="payment">
			<tr>
				<th width="10%" align="left">Bezahlt</th>
<?php if($data['Bezahlt'] == 'Überweisung') { ?>
				<th width="25%" align="right"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" class="checkbox" style="display: block"></th>
				<th width="10%" align="left">Überweisung</th>
				<th width="25%" align="right"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox" style="display: block"></th>
				<th width="5%" align="left"> Bar</th>
<?php } else { ?>
				<th width="25%" align="right"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox" style="display: block"></th>
				<th width="10%" align="left">Überweisung</th>
				<th width="25%" align="right"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="unchecked" class="checkbox" style="display: block"></th>
				<th width="5%" align="left">Bar</th>
<?php } ?>
				
				<th width="15%"></th>
				<th width="10%" align="left"><?=$data['Betrag (in €)'];?><?= str_repeat('&nbsp;', 2);?>EUR</th>
			</tr>
		</table>
    <?php }?>
	<br>
    Weitere Informationen zur Zahlung:
    <?php
        if($data['weitere Informationen zur Zahlung'])
            echo $data['weitere Informationen zur Zahlung'];
        else
            echo "-";
    ?>
    <br>
		<table class="restarbeiten">
<?php foreach ($data['Restarbeiten'] as $i => $r) {
    if($r){
    ?>
		<tr>
			<td width="15%">Restarbeiten</td>
			<?php if(in_array($r['Restarbeiten'], ['true',1,'on',true])) { ?>
						<td width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="checked" class="checkbox">ja</td>
						<td width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="notChecked" class="checkbox">nein</td>		
					<?php }else { ?>
						<td width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\unchecked.png'?>" alt="checked" class="checkbox">ja</td>
						<td width="15%"><img src="<?=WEB_ROOT_API.'vendor\printouts\checked.png'?>" alt="notChecked" class="checkbox">nein</td>	
				  <?php	} ?>
					<td width="25%" align="right"><?= str_repeat('&nbsp;', 5);?>Zeitbedarf (in h): <?= str_repeat('&nbsp;', 5).$r['Zeitbedarf (in h)'];?> </td>
                    <td width="20%" align="right"><?= str_repeat('&nbsp;', 5);?>Anzahl Tischler: <?= str_repeat('&nbsp;', 5).$r['Anzahl Tischler'];?> </td>
				</tr>
<?php }} ?>
		</table>
		<br>
		<table class="signatures">
			<tr>
				<th>
					<div align="left" class="underlined">
<?php 					if (!empty($data['Datum'])) { ?>
<?php 							echo $data['Datum'].',';?> 
<?php 					} ?>	
<?php if (UR_exists($data['Unterschrift Monteur']) == true) { ?>	
<?= 						'<img src="'.$data['Unterschrift Monteur'].'" alt="signature" class="signature">'; ?>
<?php } ?>
					</div>
					<div class="top topBorder" align="left">Datum, Unterschrift Monteur</div>
				</th>
			</tr>
		</table>		
	</body>
</html>

<style>
    .hidden {visibility: hidden;}
	.lieferadresse, .reports, .freeText {width:100%;}
	.freeText, .reports {border: 1px solid red;}
	.complaints {width:100%; border-collapse:collapse;}
	.complaints th, .complaints td  {border: 1px solid red;}
	.top { vertical-align: text-top; font-size:xx-small;}
	.title {font-size:xx-large; color: red;}
	.subTitle {font-size:x-large; color: red;}
	.smallFont {font-size:x-small;}
	.restarbeiten, .payment {width:100%;}
	td {overflow:hidden;}	

	table, tr, td, th, tbody, thead, tfoot {
    	page-break-inside: avoid !important;
	}
    .grund td, .grund th {border:none!important;}
    .largeFont {font-size:x-large;}
    .checkbox { width:40%; height:auto;}
    .grundPhoto {margin-left: 5px; margin-bottom: 5px; margin-top:5px; height: 20%; width:auto;}
    .signature {width:40%;}
    .signatures {width:50%;}
 	.topBorder {border-top:1px solid red;}  
    .header {width:100%; display:block;}
    .grund {table-layout: fixed;}
   	 div {min-height: 1.25em; line-height: 1.25; vertical-align:bottom;}
   	
   	*  {margin: 0; padding: 0;}

</style>