<?php

require_once ABS_PATH.'Files.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Employees.php';
require_once ABS_PATH.'WorkingOrders.php';

class C_MediaPrint_Old {

    private $db;
    private $files;
    private $projects;
    private $employees;
    private $settings;
    private $targetTimeZone;
    private $workingOrder;

    public function __construct() {
        $this->db = CommonData::getDbConnection();
        $this->files = new Files();
        $this->projects = new Projects();
        $this->employees = new Employees();
        $this->settings = new Settings();
        $this->workingOrder = new WorkingOrders();
        // fetch timezone from settings
		$this->targetTimeZone = $this->settings->getTimeZoneIdentifier()['timezoneIdentifier'];
    }

    public function getData($projectNo) {

        $resultFiles = $this->files->get_files_data('ktr', $projectNo);

        foreach ($resultFiles as $key => &$value) {
            if (empty($resultFiles)) {
                return [];
            } else {
                $value['comments'] = $this->files->getCommentById($value['fid']);
                foreach ($value['comments'] as $k => &$v) {
                    $v['commentAuthor'] = $this->getEmployee($v['createdBy']);
                }
                $value['uploaderName'] = $this->getEmployee($value['uploader']);
                $value['showWorkingOrderNumber'] = true;
                $value['showUploader'] = true;
                $value['showUploadDate'] = true;
                $value['showCreationDate'] = true;
                $value['showComments'] = true;
            }
        }

        $result = [];
        $result['files'] = $resultFiles;

        return $result;
    }

    public function getFiles($data, $projectNo = 0, $workingNo = 0) {

        if (empty($data)) {
            $result = [];
        } else {

			$result['imagesPerPage'] = $data->imagesPerPage ?: 1;

            foreach ($data->images as $key => $value) {

                $file = $this->files->getSingle($value->fileId);
                // skip non-images
                if(strpos($file['mime_type'],'image/') === false)
                    continue;
                $result['files'][$value->fileId] = $file;

                // Perform timezone adjustments
				$result['files'][$value->fileId]['upload_time'] = DateTimeTools::utc_to_target_timezone($result['files'][$value->fileId]['upload_timeUTC'], $this->targetTimeZone);
				$result['files'][$value->fileId]['creation_time'] = DateTimeTools::utc_to_target_timezone($result['files'][$value->fileId]['creation_timeUTC'], $this->targetTimeZone);

                $result['files'][$value->fileId]['postDescription'] = $value->description;
                $result['files'][$value->fileId]['dbDescription'] = $result['files'][$value->fileId]['description'];

                $result['files'][$value->fileId]['uploaderName'] = $this->getEmployee($value->uploader);
                $result['files'][$value->fileId]['workingOrderNumber'] = $result['files'][$value->fileId]['rel_key'];

				$result['files'][$value->fileId]['showWorkingOrderNumber'] = isset($value->showWorkingOrderNumber) ? $value->showWorkingOrderNumber : true;
				$result['files'][$value->fileId]['showUploader'] = isset($value->showUploader) ? $value->showUploader : true;
				$result['files'][$value->fileId]['showUploadDate'] = isset($value->showUploadDate) ? $value->showUploadDate : true;
				$result['files'][$value->fileId]['showCreationDate'] = isset($value->showCreationDate) ? $value->showCreationDate : true;
				$result['files'][$value->fileId]['showComments'] = isset($value->showComments) ? $value->showComments : true;

				$result['files'][$value->fileId]['comments'] = $value->showComments ? $this->files->getCommentById($value->fileId) : [];
				foreach ($result['files'][$value->fileId]['comments'] as $k => &$v) {
					$v['commentAuthor'] = $this->getEmployee($v['createdBy']);
				}

            }
        }

        $result['projectData'] = $this->getProjectData($projectNo);

     if($workingNo!==0){
     	$resultWorkingOrder = $this->workingOrder->getselect($projectNo,$workingNo);
			$result['workingOrderData']['working_order'] = $workingNo;
			$result['workingOrderData']['kurz_text'] = $resultWorkingOrder['kurztext'];
     }
     else{
			$result['workingOrderData']['working_order'] = '';
			$result['workingOrderData']['kurz_text'] = '';
		}

        $result['companyLogo'] = $this->getLogoUrl();

        return $result;
    }

    private function getProjectData($projectNo) {

        $result = $this->projects->get($projectNo);
        $projects = [];

        if (empty($result)) {
            return [];
        } else {
            foreach ($result as $key => $value) {
                $projects[$value['year']] = $value;
            }
        }

        ksort($projects);

        return end($projects);
    }

    // TODO use v3/Employees, it already comes with the displayName property, so no need for manual concateniation here
    private function getEmployee($pnr) {

       $resultEmployee = $this->employees->getselect_employee((int)$pnr);
       if (empty($resultEmployee)) {
           return '';
       } else {
           return $resultEmployee['name'].' '.$resultEmployee['vorname'];
       }
    }

    //Company logo
    private function getLogoUrl() {
		$settingsApi = new Settings();
	    $logoUrl = $settingsApi->getcommon()['logo'];
		return $logoUrl;
    }

}