<?php
require_once ABS_PATH.'Documentation.php';

class C_ZahllistenAgregation {


private $documentation;
private $query;
private $db;

	
	public function __construct() {
		$this->documentation = new DocumentationModel();
		$this->query = '';
		$this->db = CommonData::getDbConnection();
	}
	
	public function getData($type,$per, $schemaId, $showDocuments) {
		
		$data['schemaPositions'] = $this->documentation->getAllNonHeadlines($schemaId);
		
		foreach ($data['schemaPositions'] as $k=>&$v) {
			$output_array = [];
			preg_match('/((?<=\[)\d+(?=\]))/', $v['title'], $output_array);
			$v['sortingIndex'] = $output_array[0];
			
		}
		
		$this->sort_array_of_array($data['schemaPositions'], 'sortingIndex');
		//resolve the number of documentations. If it's only 1 than the data will be fetched for one level below
		$type == 'principal' ? $documentsCount = '1' : $documentsCount = $this->getDocumentsCount($type, $per, $schemaId); 
		//this two types are with different behavior and were added later om the development
		
		$specialTypes = ['workingOrdersPerProject', 'documentsPerWorkingOrder', 'projectsPerCustomer'];
		
		if($showDocuments == true) {
			//all document for that very project number will be returned
			$data['documentsData'] =  $this->getDocumentsDataKtr($schemaId, $per);
		
		} else {
		
			//keep the already existing logic
			if(in_array($type, $specialTypes)) {
				if($type == 'workingOrdersPerProject') {
					$data['documentsData'] =  $this->documentation->getDocumentsData($type, $per, $schemaId);
				} else  if($type == 'documentsPerWorkingOrder'){
					$data['documentsData'] = $this->documentation->getDocumentsDataPerSingleWo($schemaId, $per);
				} else {
				//projectPerCustomer
					$data['documentsData'] = $this->documentation->getDocumentsData($type, $per, $schemaId);
				}
			} else {
				if((int)$documentsCount > 1) {
					$data['documentsData'] = $this->documentation->getDocumentsData($type, $per, $schemaId);	
				} else {
					switch ($type) {
						case 'customers' :
							$type = 'projects';
							//comma separated list of projects
							$perProjects = $this->getNumbersLevelBelow($type, $per, $schemaId);
							$data['documentsData'] =  $this->documentation->getDocumentsData($type, $perProjects, $schemaId);	
							break;
						case 'projects' :
							$type = 'workingOrders';
							$perWorkingOrders = $this->getNumbersLevelBelow($type, $per, $schemaId);
							$data['documentsData'] =  $this->documentation->getDocumentsData($type, $perWorkingOrders, $schemaId);
							break;
						case 'workingOrders' :
							//single working order. Documents should be selected for the single one.
							$type = 'singleWorkingOrder';
							$perWo = $this->getNumbersLevelBelow($type, $per, $schemaId);
							$data['documentsData'] = $this->documentation->getDocumentsDataPerSingleWo($schemaId, $perWo);
							break;
						default:
							$data['documentsData'] = $this->documentation->getDocumentsData($type, $per, $schemaId);		
					}
				}
			}
		}
		
		
		$data['schemaData'] = $this->getSchemaData($schemaId);
		
		$parents = [];
		
		foreach ($data['documentsData'] as $k=>$doc) {
			if(isset($doc['originDocumentId'])) {
				array_push($parents, $doc['originDocumentId']);
			}
		}
		
		$data['parents'] = array_unique($parents);
		
		//set type to documentsPerWorkingOrder because if showDocuments is true always all the selected documents should be displayed
		
		if($showDocuments == true) {
			$type = 'documentsPerWorkingOrder';
		}
		
		return $this->formatData($data, $type, $schemaId, $documentsCount, $showDocuments);
	}
	
	//here the schemaPositions and documentsData will be looped to get the final format of the data
	//the schemaPositions and documentsData have to be looped and combined in
	// followed format for working orders as example SchemaPosition => [woNumber=> value, woNumber2 => value]
	//and so on. The encode function in xlsFormat class didn't have implementation to handle such data structure and
	//some changes were implemented suitable for the task
	private function formatData($data, $type, $schemaId, $documentsCount, $showDocuments) {		
		$excelData = [];
		$documentIds = [];
		
		//all the descriptions are stored in a different array because of future merging in the function to achieve
		// Description key always first (first raw in the Excel sheet) and than the sorted positions
		if($showDocuments == true) {
			$descriptions = [];
			$descriptions['Zählliste']['Schema'] = '';
		    $descriptions['Zählliste']['Parent'] = '';
		}
		
		//prepare data for a cut off and debugging session
		 foreach ($data['schemaPositions'] as $k=>$v) {
			$total = 0;
			foreach ($data['documentsData'] as $index=>&$doc) {
						$documentIds[$doc['id']] = $doc['id'];
					if($doc['schemaPositionId'] == $v['id'] && !in_array($doc['id'], $data['parents'])) {
						if($type == 'workingOrders' || $type == 'workingOrdersPerProject') {
							$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							$excelData[$v['title']][$doc['wo']] += eval('return '.$doc['value'].';');
						} else if ($type == 'projects' || $type == 'projectsPerCustomer') {
							 $excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							 $excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							 $excelData[$v['title']][$doc['name']] += eval('return '.$doc['value'].';');
						} else if($type == 'customers') {
							$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							$excelData[$v['title']][$doc['customerName']] += eval('return '.$doc['value'].';');
						} else if ($type == 'singleWorkingOrder' || $type == 'documentsPerWorkingOrder') {
							$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							$excelData[$v['title']][$doc['id']] += eval('return '.$doc['value'].';');
							
							if(strpos($doc['positionTitle'], 'Beschreibung') !== false) {
								$descriptions['Zählliste'][$doc['id']] = $doc['value'];
							}
							
						}  else {
						
							//principal
							$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
						}
							$total += eval('return '.$doc['value'].';');
					} else {
						if($type == 'workingOrders' || $type == 'workingOrdersPerProject') {
							if(!isset($excelData[$v['title']]['Schema'])) {
								$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							}
							
							if(!isset($excelData[$v['title']]['Parent'])) {
								$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							}
							
							if(!isset($excelData[$v['title']][$doc['wo']])) {
								$excelData[$v['title']][$doc['wo']] = '';
							}
						} else if ($type == 'projects'|| $type == 'projectsPerCustomer') {
							if(!isset($excelData[$v['title']]['Schema'])) {
								$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							}
							
							if(!isset($excelData[$v['title']]['Parent'])) {
								$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							}
							
							if(!isset($excelData[$v['title']][$doc['name']]) && $doc['name'] != $data['schemaData'][0]['name']) {
								$excelData[$v['title']][$doc['name']] = '';
							}
						
						} else if($type == 'customers') {
							if(!isset($excelData[$v['title']]['Schema'])) {
								$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							}
							
							if(!isset($excelData[$v['title']]['Parent'])) {
								$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							}
							
							if(!isset($excelData[$v['title']][$doc['customerName']])) {
								$excelData[$v['title']][$doc['customerName']] = '';
							}				
						}	else  if ($type == 'principal'){
							if(!isset($excelData[$v['title']]['Schema'])) {
								$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
							}	
							
							if(!isset($excelData[$v['title']]['Parent'])) {
								$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
							}
								
						}   else {
								//single working order or documentsPerWorkingOrder
								if(!isset($excelData[$v['title']]['Schema'])) {
									$excelData[$v['title']]['Schema'] = $data['schemaPositions'][$k]['name'];
								}
								if(!isset($excelData[$v['title']]['Parent'])) {
									$excelData[$v['title']]['Parent'] = $data['schemaPositions'][$k]['parent'];
								}
								
								foreach ($documentIds as $docId) {
									if(!isset($excelData[$v['title']][$docId])) {
										$excelData[$v['title']][$docId] = 0;
									}
								}
								
							if(strpos($doc['positionTitle'], 'Beschreibung') !== false) {
								$descriptions['Zählliste'][$doc['id']] = $doc['value'];
							}
								
					}
				//this one is commented beacause in case of need af empty column before total as it was mentioned probably
				//$excelData[$v['title']][''] = '';
				}		
			
			 }
			 	$excelData[$v['title']]['Total'] = $total;
		}
		
		if(($type == 'projects') && $documentsCount == '1') {
			foreach ($excelData as $index=>&$v) {
				foreach ($v as $key => &$value) {
					if($key != 'Schema' && $key != 'Parent' && $key != 'Total' && $value === '') {
						$value = $excelData[$index]['Total'];
					}
				}
			}
		}
		
		foreach ($excelData as $index=>&$v) {
			foreach ($v as $key => &$value) {
				if($key != 'Schema' && $key != 'Parent' && $key != 'Total' && $value === '') {
					$value = 0;
				}
			}
		}
		
		if($descriptions) {
			$descriptions['Zählliste']['Total'] = '';
			return array_merge($descriptions,$excelData);
		} else {
			return $excelData;
		}
	}
	
	public function getPrincipalData() {
		
		$stmt = $this->db->prepare("
			SELECT name, adresse1, plz,ort, telefon FROM kunden WHERE knr = 1
		");
		$stmt->execute();
		
		$data = $stmt->fetch();
		
		return $data;
		
	}
	
	public function getSchemaData($schemaId) {
		
		$stmt = $this->db->prepare("
			SELECT createdOn, name FROM documentation_schema WHERE id = :schemaId
		");
		$stmt->execute(['schemaId'=>$schemaId]);
		
		$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
		
		return $data;
	}
	
	
	public function checkSchemaExistence($schemaId) {
		$stmt = $this->db->prepare( "SELECT TOP 1 id FROM documentation_schema WHERE id = :schemaId");
		$stmt->execute(['schemaId'=>$schemaId]);
		$data = $stmt->fetch();
		return $data;
	}
	
	private function getDocumentsCount($type, $per, $schemaId) {

		if(empty($per)) {
			switch ($type) {
				case 'workingOrders' :
				$stmt = $this->db->prepare ("SELECT COUNT(ktrAanr) FROM (SELECT DISTINCT CAST(rel_key1 as varchar)+'-'+CAST(rel_key2 as varchar) AS ktrAanr 
											FROM documentation_document WHERE rel_type = 'workingOrder' AND schemaId = :schemaId AND rel_key1 NOT IN (2,16,562)) wo");
				$stmt->execute(['schemaId'=>$schemaId]);
				$data = $stmt->fetch();
				return $data[0];
				break;
				
				case 'projects' :
				$stmt = $this->db->prepare ("SELECT COUNT(rel_key1) FROM (SELECT DISTINCT rel_key1 FROM documentation_document WHERE rel_type IN ('project', 'workingOrder') AND schemaId = :schemaId AND rel_key1 NOT IN (2,16,562)) pr");
				$stmt->execute(['schemaId'=>$schemaId]);
				$data = $stmt->fetch();
				return $data[0];
				break;
				
				//customers
				default:
				$stmt = $this->db->prepare ("SELECT COUNT(knr) FROM ktr WHERE ktr.ktr IN (SELECT rel_key1 FROM documentation_document WHERE documentation_document.schemaId = :schemaId AND rel_key1 NOT IN (2,16,562, 260167))");
				$stmt->execute(['schemaId'=>$schemaId]);
				$data = $stmt->fetch();
				return $data[0];
				break;	
			}
				
		} else {
				switch ($type) {
					case 'workingOrders' :
					
					$wos = explode(',', $per);
					$wos = $this->excludeSpecificWos($wos);
					$wosString = '';
					foreach ($wos as $k=>$v) {
						$wosString .= "'".$v."',";
					}
					if (count($wos) > 0) {
						$wosString = substr_replace($wosString, "", -1);
					}
					$whereWos = " AND CONCAT(CONVERT(NVARCHAR, documentation_document.rel_key1), '-', CONVERT(NVARCHAR, documentation_document.rel_key2)) IN (".$wosString.")";
					
					$stmt = $this->db->prepare ("SELECT COUNT(ktrAanr) FROM (SELECT DISTINCT CAST(rel_key1 as varchar)+'-'+CAST(rel_key2 as varchar) AS ktrAanr 
												FROM documentation_document WHERE rel_type = 'workingOrder' AND schemaId = :schemaId".$whereWos." ) wo");
					$stmt->execute(['schemaId'=>$schemaId]);
					$data = $stmt->fetch();
					return $data[0];
					break;
				
				case 'projects' :
					$per = $this->excludeSpecificProjects($per);
					$stmt = $this->db->prepare ("SELECT COUNT(rel_key1) FROM (SELECT DISTINCT rel_key1 FROM documentation_document 
												WHERE rel_type = 'project' AND schemaId = :schemaId AND rel_key1 IN (".$per.")) pr");
					$stmt->execute(['schemaId'=>$schemaId]);
					$data = $stmt->fetch();
					return $data[0];
					break;
				
				//customers
					default:
					//requirement to exclude these specific project numbers at all
					$excludeProjects = ' AND ktr.ktr NOT IN(2,16,562)';			
					$stmt = $this->db->prepare ("SELECT COUNT(documentation_document.id) FROM documentation_document 
												LEFT JOIN ktr ON documentation_document.rel_key1 = ktr.ktr
												WHERE ktr.ktr IN (SELECT rel_key1 FROM documentation_document WHERE documentation_document.schemaId = :schemaId)
												and ktr.knr IN (".$per.")".$excludeProjects);
					$stmt->execute(['schemaId'=>$schemaId]);
					$data = $stmt->fetch();
					return $data[0];
					break;	
			}
		}
	}
	
	private function getNumbersLevelBelow($type, $per, $schemaId) {
		
		if($type == 'projects') {
				empty($per) ? $where = '' : $where = " ktr.knr IN (".$per.") AND";
				$excludeProjects = ' AND documentation_document.rel_key1 NOT IN (2,16,562)';
				$stmt = $this->db->prepare( "SELECT rel_key1 FROM documentation_document LEFT JOIN ktr ON
											 documentation_document.rel_key1 = ktr.ktr WHERE ".$where." documentation_document.schemaId = :schemaId".$excludeProjects);
				$stmt->execute(['schemaId'=>$schemaId]);
				$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
				
				$result = '';
				foreach ($data as $k=>$v) {
					foreach ($v as $key=>$value) {
						$result.= $value.',';
					}
				}
				$result = rtrim($result, ',');
				return $result;
		} else if($type == 'workingOrders') {
				//workingOrders
				empty($per) ? $where = ' documentation_document.rel_key1 NOT IN (2,16,562)' : $where = " rel_key1 IN (".$per.") AND documentation_document.rel_key1 NOT IN (2,16,562)";
				
				$stmt = $this->db->prepare( "SELECT CAST(documentation_document.rel_key1 as varchar)+'-'+CAST(documentation_document.rel_key2 as varchar)  as ktrAanr
											 FROM documentation_document WHERE schemaId = :schemaId AND ".$where);
				$stmt->execute(['schemaId'=>$schemaId]);
				$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
				
				$result = '';
				foreach ($data as $k=>$v) {
					foreach ($v as $key=>$value) {
						$result.= $value.',';
					}
						
				}
				$result = rtrim($result, ',');
				return $result;
		} else {
		
			//a single working Order in the database
			$stmt = $this->db->prepare( "SELECT TOP 1 CAST(documentation_document.rel_key1 as varchar)+'-'+CAST(documentation_document.rel_key2 as varchar)  as ktrAanr
										FROM documentation_document where rel_type = 'workingOrder' AND schemaId = :schemaId AND documentation_document.rel_key1 NOT IN (2,16,562)");
				$stmt->execute(['schemaId'=>$schemaId]);
				$data = $stmt->fetch(PDO::FETCH_ASSOC);
				return $data['ktrAanr'];
		}
	}
	
	public function getExistingDocuments($schemaId) {
		$stmt = $this->db->prepare( "SELECT * FROM documentation_document WHERE schemaId = :schemaId");
		$stmt->execute(['schemaId'=>$schemaId]);
		$data = $stmt->fetchAll();
		return $data;
	}
	
	public function getHeaderData($type, $per) {
		$headerData = [];
		switch ($type) {
			case 'projectsPerCustomer' : 
				$headerData['customerData'] = $this->getCustomerData($per);
				break;
			case 'customers' : 
				$headerData['customerData'] = $this->getCustomerData($per);
				break;
			case 'workingOrdersPerProject' :
				$headerData['projectData'] = $this->getProjectData($per);
				if(!is_null($headerData['projectData']['customerNo'])) {
					$headerData['customerData'] = $this->getCustomerData($headerData['projectData']['customerNo']);
				} else {
					$headerData['customerData'] = [];	
				}
				break;
			case 'projects' :
				$headerData['projectData'] = $this->getProjectData($per);
				if(!is_null($headerData['projectData']['customerNo'])) {
					$headerData['customerData'] = $this->getCustomerData($headerData['projectData']['customerNo']);
				} else {
					$headerData['customerData'] = [];	
				}
				break;
			case 'documentsPerWorkingOrder' : 
				preg_match_all("/\d+/", $per, $matches);
				$ktr = $matches[0][0];
				$aanr = $matches[0][1];
				
				$headerData['workingOrderData'][0] = $aanr;
				$headerData['workingOrderData'][1] = $this->getWoName($ktr, $aanr);
				
				$headerData['projectData'] = $this->getProjectData($ktr);
				
				if(!is_null($headerData['projectData']['customerNo'])) {
					$headerData['customerData'] = $this->getCustomerData($headerData['projectData']['customerNo']);
				} else {
					$headerData['customerData'] = [];	
				}
				
				break;
			case 'workingOrders' : 
				$param = explode(',', $per)[0];
				$ktr = explode('-', $param)[0];
				$aanr = explode('-', $param)[1];
				$headerData['workingOrderData'] = $aanr;
				$headerData['projectData'] = $this->getProjectData($ktr);
				if(!is_null($headerData['projectData']['customerNo'])) {
					$headerData['customerData'] = $this->getCustomerData($headerData['projectData']['customerNo']);
				} else {
					$headerData['customerData'] = [];	
				}
				
				break;
			default:
			$headerData = [];
		}
	return $headerData;
	}
	
	private function getCustomerData($knr) {
		$adr = new AddressesDB();
		$data = $adr->get_single($knr);
		
		$customerString = '';
		if(isset($data['name']) && !empty(trim($data['name']))) {
			$customerString .= trim($data['name']);
		}
	
		if(isset($data['city']) && !empty(trim($data['city']))) {
			$customerString .= ', '.trim($data['city']);
		}
		
		if(isset($data['postcode']) && !empty(trim($data['postcode']))) {
			$customerString .= ' '.trim($data['postcode']);
		}
		
		if(isset($data['address']) && !empty(trim($data['address']))) {
			$customerString .= ', '.trim($data['address']);
		}
		
		if(isset($data['phoneNumber']) && !empty(trim($data['phoneNumber']))) {
			$customerString .= ', Telefon:'.trim($data['phoneNumber']);
		}
		
		if(isset($data['faxNumber']) && !empty(trim($data['faxNumber']))) {
			$customerString .= ', Fax: '.trim($data['faxNumber']);
		}
		return $customerString;
	}
	
	private function getProjectData($ktr) {
		$param = explode(',', $ktr)[0];
		
		require_once ABS_PATH.'class/db.helper.php';
		$dbHelper = new DB_Helper();
		$ktrFromTunnel = $dbHelper->dummyKtrTable('');
		$ktrTable = $ktrFromTunnel ? : "ktr";
		
		$stmt = $this->db->prepare( "SELECT TOP 1 jahr as year FROM $ktrTable where ktr = :param ORDER BY jahr DESC");
		$stmt->execute(['param'=>$param]);
		$data = $stmt->fetchAll();
		$projectYear =  $data[0]['year'];
		
		$year = is_null($projectYear) ? date("Y") : $projectYear;  
		$projectData = [];
		
		$projects = new Projects();
		$data = $projects->getselect($ktr, $year); 
		$projectData['header']  = trim($data['project_name']);
		$projectData['customerNo'] = $data['knr']; 
		
		return $projectData;
	}
	
	//exclude working orders of exact project numbers part of the requirement
	private function excludeSpecificWos($wos) {
		//projects to exclude
		$projects = [2,16,562];
		$result = [];
		foreach ($wos as $k=>$v) {
			$splitted = explode('-', $v);
			$ktr = $splitted[0];
			if(!in_array($ktr, $projects)) {
				array_push($result, $v);
			}
		}
		
		return $result;
	}
	
	public function getWoName($ktr,$aanr) {
		$stmt = $this->db->prepare( "SELECT TOP 1 kurztext from aauftrag where ktr = :ktr AND aanr = :aanr  ORDER BY erfdat DESC");
		$stmt->execute(['ktr'=>$ktr, 'aanr'=>$aanr]);
		
		$data = $stmt->fetch(PDO::FETCH_ASSOC);
		return $data['kurztext'];
	}
	
	//exclude exact project numbers  part of the requirement
	private function excludeSpecificProjects($projects) {
		$result = '';
		$ktrs = explode(',', $projects);
		$excludedProjects = $projects = [2,16,562];
			foreach ($ktrs as $k=>$v) {
				if(!in_array($v, $excludedProjects)) {
					$result .= "'".$v."',";
				}
			}
		return rtrim($result, ',');
	}
	
	protected function sort_array_of_array(&$array, $subfield) {
		$sortarray = array();
		foreach ($array as $key => $row)
		{
			$sortarray[$key] = $row[$subfield];
		}
	
		array_multisort($sortarray, SORT_ASC, $array);
	}
	
	//https://gitlab.baubuddy.de/BauBuddy/scaffoldingAPI/-/issues/12381
	// fetching the positions of all documents and their values if showDocuments is true and
	// a single ktr is passed in the comma separated parameter. Discription values for each document are added as well 
	
	private function getDocumentsDataKtr ($schemaId, $ktr) {
		$stmt = $this->db->prepare( "SELECT ddp.schemaPositionId, ddp.value, dsp.type, dd.rel_key1 as projectNo, knd.name as customerName, 
									    CASE WHEN originDocumentId IS NULL THEN
           CONCAT('#',dd.id) ELSE
               CONCAT ('#',dd.id,' (Kopie von #',dd.originDocumentId,')')
               END as id, dsp.title as positionTitle
										FROM documentation_document_positions as ddp
										LEFT JOIN documentation_document as dd on ddp.documentationId = dd.id
										LEFT JOIN documentation_schema_positions as dsp on ddp.schemaPositionId = dsp.id
										left join ktr as ktr ON dd.rel_key1 = ktr.ktr
										left join kunden as knd ON ktr.knr = knd.knr
										WHERE dd.rel_type IN ('workingOrder', 'project') 
										AND (dsp.type IN ('formula') or dsp.title = 'Beschreibung der Seite/Lage') 
										AND dsp.schemaId = :schemaId
										AND dd.rel_key1 = :ktr
										and dd.id NOT IN (SELECT dd.originDocumentId from documentation_document as dd where dd.originDocumentId IS NOT NULL)");
				$stmt->execute(['schemaId'=>$schemaId, 'ktr'=>$ktr]);
				$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
	
				return $data;
	}
}

