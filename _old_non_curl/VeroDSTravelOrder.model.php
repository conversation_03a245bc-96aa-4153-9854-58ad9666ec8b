<?php
require_once ABS_PATH.'Documentation.php';

class C_DSTravelOrder {

private $documentation;
private $country;

	public function __construct($country) {
		$this->documentation = new Documentation();
		$this->country = $country;
	}
	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		if($documentData) {
			$children = $documentData['children'];
	
			$printIndicedChildrenArray = array();
			$printIndexDeduction = 0;
			foreach($children as $child)
			{
				// printIndex from child
				$printIndex = $child['printIndex'];

				$type = $child['type'];
				$reportedValue = $child['reportedValue'];

				// to exclude e.g. headlines without a printIndex
				if(isset($printIndex)) {
					// deduct index e.g. for invoiceSelector
					$printIndex -= $printIndexDeduction;
					// for photo and signature the full data is needed
					if (in_array($type, ['photo', 'signatureField'])) {
						// for testing on local machine
						// $child['filePath'] = 'http://api.baubuddy.de/infomaterial/Dokumente_vero_test/DYNAMICDOCUMENTATION_37/Bilder/2019-06-12_09-26-32--289-37-5C84FFCE-07EE-4AED-B7C0-950257E55C76.jpg';
						$printIndicedChildrenArray[$printIndex] = $child;
					} else {
						if($type == 'date') {
							$reportedValue = new DateTime($reportedValue);
						}
						elseif($type == 'INVOICESELECTOR')
						{
							// unset reportedValue
							$reportedValue = "";
							foreach ($child['reportedValues'] as $singleReportedValue)
							{
								$parts = explode("-",$singleReportedValue);
								$reportedValue .= $parts[0].",";
							}
							// remove last comma
							$reportedValue = substr($reportedValue,0,-1);
							$printIndexDeduction += count($child['reportedValues'])-1;
						}

						// for the other values the value is sufficient, this allows quicker access from the HTML template
						$printIndicedChildrenArray[$printIndex] = $reportedValue;
					}
				}
			}
	
			// calculate difference between start and end in days
			$interval = date_diff($printIndicedChildrenArray[2],$printIndicedChildrenArray[3]);
			// +1 since the final day is included
			$dayInterval = $interval->days + 1;
			// get year for invoices call, later
			$year = $printIndicedChildrenArray[2]->format('Y');
			$printIndicedChildrenArray[] = $dayInterval;
			// format dates properly
			$printIndicedChildrenArray[2] = $printIndicedChildrenArray[2]->format('d.m.Y');
			$printIndicedChildrenArray[3] = $printIndicedChildrenArray[3]->format('d.m.Y');
	
			// calculate maximum allowed business expenses
			if($this->country == 'bulgaria')
			{
				$dailyExpenseAllowanceInLev = 40;
				$dailyExpenseAllowancePrint = '40,00';
			}
			else if($this->country == 'namibia')
			{
				// its 60 US$
				$rate = 1.75;
				$dailyExpenseAllowanceInLev = 60 * $rate;
				$dailyExpenseAllowancePrint = "60 US$ =  {$dailyExpenseAllowanceInLev} ";
			}
			$allowedBusinessExpenses = (float) $dayInterval * $dailyExpenseAllowanceInLev;
			$printIndicedChildrenArray[] = number_format($allowedBusinessExpenses,2);
	
			// fetch principals customerNo to have the name for proper display
			require_once ABS_PATH.'class/param01.class.php';
			$param01DB = new Param01DB();
			$companyInfo = $param01DB->get_settings_info();
			$printIndicedChildrenArray[] = $companyInfo['firma1'];
			$printIndicedChildrenArray[] = '<br>'.$companyInfo['firma2'];
	
			// fetch and handle invoices
			$invoiceIds = explode(',',$printIndicedChildrenArray[6]);
			require_once ABS_PATH.'Invoices.php';
			$invoicesApi = new Invoices();
			// set remainingBusinessExpenses for later counter-calculation
			$remainingBusinessExpenses = $allowedBusinessExpenses;
			$total = $allowedBusinessExpenses;
			
			require_once ABS_PATH.'Addresses.php';
			$addressesAPI = new Addresses();
						
				foreach ($invoiceIds as $invoiceId) {
					$invoice = $invoicesApi->get($year,trim($invoiceId));

					// get amount from invoice
					$invoiceAmount = ($invoice['brutto'] > 0)? $invoice['brutto'] : $invoice['netto'];
					// convert € to Lev
					if($invoice['dsymbol'] == '€')
						$invoiceAmount = $invoiceAmount*1.95583;
					//$invoiceAmount = number_format($invoiceAmount,2);
					// calculate remainingBusinessExpenses
					// to check wether it can be covered be businessExpenses or needs to get its own row in the print layout
					$remainingBusinessExpenses = $allowedBusinessExpenses - round($invoiceAmount,0,PHP_ROUND_HALF_DOWN);
					if($remainingBusinessExpenses < 0 || strpos($invoice['info'],'deductible') !== false)
					{
						// complete suppliers data if missing
						if(empty($invoice['kname']))
						{		
							$address = $addressesAPI->get($invoice['knr']);
							$invoice['kname'] = $address['searchName'];
							$invoice['kort'] = $address['city'];
						}
						// format date properly
						$invoice['rechdatum'] = date('d.m.Y',strtotime($invoice['rechdatum']));
						$invoice['amount'] = number_format($invoiceAmount,2);
						// add invoice to array
						$printIndicedChildrenArray['invoices'][] = $invoice;
						// add amount to total
						$total = $total + $invoiceAmount;
					}
					else
					{
						$allowedBusinessExpenses = $allowedBusinessExpenses - $invoiceAmount;
					}
				}
			$printIndicedChildrenArray[] = $total;
			$printIndicedChildrenArray['country'] = $this->country;
			$printIndicedChildrenArray['dailyExpenseAllowancePrint'] = $dailyExpenseAllowancePrint;
			return $printIndicedChildrenArray;
			} else {
				throw new RestException(400, "No such document id!");
			}		
	}
}
