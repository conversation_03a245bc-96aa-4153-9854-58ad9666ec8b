<html xmlns:v="urn:schemas-microsoft-com:vml"
xmlns:o="urn:schemas-microsoft-com:office:office"
xmlns:w="urn:schemas-microsoft-com:office:word"
xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
xmlns="http://www.w3.org/TR/REC-html40">

<?php $data = $this->data ?>

<head>
<meta http-equiv=Content-Type content="text/html; charset=utf-8">
<meta name=ProgId content=Word.Document>
<meta name=Generator content="Microsoft Word 15">
<meta name=Originator content="Microsoft Word 15">
<link rel=File-List href="veroPBExpenseRefund.fld/filelist.xml">
<title>Antrag auf Kostenerstattung </title>
<!--[if gte mso 9]><xml>
 <o:DocumentProperties>
  <o:Author>neumann</o:Author>
  <o:LastAuthor>Jan Lo<PERSON>kemper</o:LastAuthor>
  <o:Revision>2</o:Revision>
  <o:TotalTime>1</o:TotalTime>
  <o:Created>2019-07-15T09:06:00Z</o:Created>
  <o:LastSaved>2019-07-15T09:06:00Z</o:LastSaved>
  <o:Pages>1</o:Pages>
  <o:Words>218</o:Words>
  <o:Characters>1243</o:Characters>
  <o:Company>GD-EKM</o:Company>
  <o:Lines>10</o:Lines>
  <o:Paragraphs>2</o:Paragraphs>
  <o:CharactersWithSpaces>1459</o:CharactersWithSpaces>
  <o:Version>16.00</o:Version>
 </o:DocumentProperties>
 <o:OfficeDocumentSettings>
  <o:TargetScreenSize>800x600</o:TargetScreenSize>
 </o:OfficeDocumentSettings>
</xml><![endif]-->
<link rel=themeData href="veroPBExpenseRefund.fld/themedata.thmx">
<link rel=colorSchemeMapping
href="veroPBExpenseRefund.fld/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:SpellingState>Clean</w:SpellingState>
  <w:GrammarState>Clean</w:GrammarState>
  <w:TrackMoves>false</w:TrackMoves>
  <w:TrackFormatting/>
  <w:HyphenationZone>21</w:HyphenationZone>
  <w:PunctuationKerning/>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>DE</w:LidThemeOther>
  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>
  <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:UseWord2010TableStyleRules/>
   <w:DontGrowAutofit/>
   <w:DontUseIndentAsNumberingTabStop/>
   <w:FELineBreak11/>
   <w:WW11IndentRules/>
   <w:DontAutofitConstrainedTables/>
   <w:AutofitLikeWW11/>
   <w:HangulWidthLikeWW11/>
   <w:UseNormalStyleForList/>
   <w:DontVertAlignCellWithSp/>
   <w:DontBreakConstrainedForcedTables/>
   <w:DontVertAlignInTxbx/>
   <w:Word11KerningPairs/>
   <w:CachedColBalance/>
  </w:Compatibility>
  <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>
  <m:mathPr>
   <m:mathFont m:val="Cambria Math"/>
   <m:brkBin m:val="before"/>
   <m:brkBinSub m:val="&#45;-"/>
   <m:smallFrac m:val="off"/>
   <m:dispDef/>
   <m:lMargin m:val="0"/>
   <m:rMargin m:val="0"/>
   <m:defJc m:val="centerGroup"/>
   <m:wrapIndent m:val="1440"/>
   <m:intLim m:val="subSup"/>
   <m:naryLim m:val="undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState="false" DefUnhideWhenUsed="false"
  DefSemiHidden="false" DefQFormat="false" LatentStyleCount="376">
  <w:LsdException Locked="false" QFormat="true" Name="Normal"/>
  <w:LsdException Locked="false" QFormat="true" Name="heading 1"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 2"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 3"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 4"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 5"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 6"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 7"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 8"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="heading 9"/>
  <w:LsdException Locked="false" SemiHidden="true" UnhideWhenUsed="true"
   QFormat="true" Name="caption"/>
  <w:LsdException Locked="false" QFormat="true" Name="Title"/>
  <w:LsdException Locked="false" QFormat="true" Name="Subtitle"/>
  <w:LsdException Locked="false" QFormat="true" Name="Strong"/>
  <w:LsdException Locked="false" QFormat="true" Name="Emphasis"/>
  <w:LsdException Locked="false" Priority="99" SemiHidden="true"
   UnhideWhenUsed="true" Name="Placeholder Text"/>
  <w:LsdException Locked="false" Priority="99" QFormat="true" Name="No Spacing"/>
  <w:LsdException Locked="false" Priority="99" Name="Light Shading"/>
  <w:LsdException Locked="false" Priority="99" Name="Light List"/>
  <w:LsdException Locked="false" Priority="99" Name="Light Grid"/>
  <w:LsdException Locked="false" Priority="99" Name="Medium Shading 1"/>
  <w:LsdException Locked="false" Priority="99" Name="Medium Shading 2"/>
  <w:LsdException Locked="false" Priority="99" Name="Medium List 1"/>
  <w:LsdException Locked="false" Priority="99" Name="Medium List 2"/>
  <w:LsdException Locked="false" Priority="99" Name="Medium Grid 1"/>
  <w:LsdException Locked="false" Priority="1" QFormat="true"
   Name="Medium Grid 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Medium Grid 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Dark List"/>
  <w:LsdException Locked="false" Priority="62" Name="Colorful Shading"/>
  <w:LsdException Locked="false" Priority="63" Name="Colorful List"/>
  <w:LsdException Locked="false" Priority="64" Name="Colorful Grid"/>
  <w:LsdException Locked="false" Priority="65" Name="Light Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="66" Name="Light List Accent 1"/>
  <w:LsdException Locked="false" Priority="67" Name="Light Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Shading 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Shading 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="70" Name="Medium List 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="true"
   UnhideWhenUsed="true" Name="Revision"/>
  <w:LsdException Locked="false" Priority="72" QFormat="true"
   Name="List Paragraph"/>
  <w:LsdException Locked="false" Priority="73" QFormat="true" Name="Quote"/>
  <w:LsdException Locked="false" Priority="60" QFormat="true"
   Name="Intense Quote"/>
  <w:LsdException Locked="false" Priority="61" Name="Medium List 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="62" Name="Medium Grid 1 Accent 1"/>
  <w:LsdException Locked="false" Priority="63" Name="Medium Grid 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="64" Name="Medium Grid 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="65" Name="Dark List Accent 1"/>
  <w:LsdException Locked="false" Priority="99" Name="Colorful Shading Accent 1"/>
  <w:LsdException Locked="false" Priority="34" QFormat="true"
   Name="Colorful List Accent 1"/>
  <w:LsdException Locked="false" Priority="29" QFormat="true"
   Name="Colorful Grid Accent 1"/>
  <w:LsdException Locked="false" Priority="30" QFormat="true"
   Name="Light Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="66" Name="Light List Accent 2"/>
  <w:LsdException Locked="false" Priority="67" Name="Light Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Shading 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Shading 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="70" Name="Medium List 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="71" Name="Medium List 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="72" Name="Medium Grid 1 Accent 2"/>
  <w:LsdException Locked="false" Priority="73" Name="Medium Grid 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="60" Name="Medium Grid 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="61" Name="Dark List Accent 2"/>
  <w:LsdException Locked="false" Priority="62" Name="Colorful Shading Accent 2"/>
  <w:LsdException Locked="false" Priority="63" Name="Colorful List Accent 2"/>
  <w:LsdException Locked="false" Priority="64" Name="Colorful Grid Accent 2"/>
  <w:LsdException Locked="false" Priority="65" Name="Light Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="66" Name="Light List Accent 3"/>
  <w:LsdException Locked="false" Priority="67" Name="Light Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Shading 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Shading 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="70" Name="Medium List 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="71" Name="Medium List 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="72" Name="Medium Grid 1 Accent 3"/>
  <w:LsdException Locked="false" Priority="73" Name="Medium Grid 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="60" Name="Medium Grid 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="61" Name="Dark List Accent 3"/>
  <w:LsdException Locked="false" Priority="62" Name="Colorful Shading Accent 3"/>
  <w:LsdException Locked="false" Priority="63" Name="Colorful List Accent 3"/>
  <w:LsdException Locked="false" Priority="64" Name="Colorful Grid Accent 3"/>
  <w:LsdException Locked="false" Priority="65" Name="Light Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="66" Name="Light List Accent 4"/>
  <w:LsdException Locked="false" Priority="67" Name="Light Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Shading 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Shading 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="70" Name="Medium List 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="71" Name="Medium List 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="72" Name="Medium Grid 1 Accent 4"/>
  <w:LsdException Locked="false" Priority="73" Name="Medium Grid 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="60" Name="Medium Grid 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="61" Name="Dark List Accent 4"/>
  <w:LsdException Locked="false" Priority="62" Name="Colorful Shading Accent 4"/>
  <w:LsdException Locked="false" Priority="63" Name="Colorful List Accent 4"/>
  <w:LsdException Locked="false" Priority="64" Name="Colorful Grid Accent 4"/>
  <w:LsdException Locked="false" Priority="65" Name="Light Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="66" Name="Light List Accent 5"/>
  <w:LsdException Locked="false" Priority="67" Name="Light Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Shading 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Shading 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="70" Name="Medium List 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="71" Name="Medium List 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="72" Name="Medium Grid 1 Accent 5"/>
  <w:LsdException Locked="false" Priority="73" Name="Medium Grid 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="60" Name="Medium Grid 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="61" Name="Dark List Accent 5"/>
  <w:LsdException Locked="false" Priority="62" Name="Colorful Shading Accent 5"/>
  <w:LsdException Locked="false" Priority="63" Name="Colorful List Accent 5"/>
  <w:LsdException Locked="false" Priority="64" Name="Colorful Grid Accent 5"/>
  <w:LsdException Locked="false" Priority="65" Name="Light Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="66" Name="Light List Accent 6"/>
  <w:LsdException Locked="false" Priority="67" Name="Light Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="68" Name="Medium Shading 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="69" Name="Medium Shading 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="70" Name="Medium List 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="71" Name="Medium List 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="72" Name="Medium Grid 1 Accent 6"/>
  <w:LsdException Locked="false" Priority="73" Name="Medium Grid 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="60" Name="Medium Grid 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="61" Name="Dark List Accent 6"/>
  <w:LsdException Locked="false" Priority="62" Name="Colorful Shading Accent 6"/>
  <w:LsdException Locked="false" Priority="63" Name="Colorful List Accent 6"/>
  <w:LsdException Locked="false" Priority="64" Name="Colorful Grid Accent 6"/>
  <w:LsdException Locked="false" Priority="65" QFormat="true"
   Name="Subtle Emphasis"/>
  <w:LsdException Locked="false" Priority="66" QFormat="true"
   Name="Intense Emphasis"/>
  <w:LsdException Locked="false" Priority="67" QFormat="true"
   Name="Subtle Reference"/>
  <w:LsdException Locked="false" Priority="68" QFormat="true"
   Name="Intense Reference"/>
  <w:LsdException Locked="false" Priority="69" QFormat="true" Name="Book Title"/>
  <w:LsdException Locked="false" Priority="70" SemiHidden="true"
   UnhideWhenUsed="true" Name="Bibliography"/>
  <w:LsdException Locked="false" Priority="71" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="TOC Heading"/>
  <w:LsdException Locked="false" Priority="72" SemiHidden="true"
   UnhideWhenUsed="true" Name="Plain Table 1"/>
  <w:LsdException Locked="false" Priority="73" SemiHidden="true"
   UnhideWhenUsed="true" Name="Plain Table 2"/>
  <w:LsdException Locked="false" Priority="19" QFormat="true"
   Name="Plain Table 3"/>
  <w:LsdException Locked="false" Priority="21" QFormat="true"
   Name="Plain Table 4"/>
  <w:LsdException Locked="false" Priority="31" QFormat="true"
   Name="Plain Table 5"/>
  <w:LsdException Locked="false" Priority="32" QFormat="true"
   Name="Grid Table Light"/>
  <w:LsdException Locked="false" Priority="33" QFormat="true"
   Name="Grid Table 1 Light"/>
  <w:LsdException Locked="false" Priority="37" SemiHidden="true"
   UnhideWhenUsed="true" Name="Grid Table 2"/>
  <w:LsdException Locked="false" Priority="39" SemiHidden="true"
   UnhideWhenUsed="true" QFormat="true" Name="Grid Table 3"/>
  <w:LsdException Locked="false" Priority="41" Name="Grid Table 4"/>
  <w:LsdException Locked="false" Priority="42" Name="Grid Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="43" Name="Grid Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="44" Name="Grid Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="45"
   Name="Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="40" Name="Grid Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="49"
   Name="Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="50"
   Name="Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="49"
   Name="Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="50"
   Name="Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="49"
   Name="Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="50"
   Name="Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="49"
   Name="Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="50"
   Name="Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="49"
   Name="Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="50"
   Name="Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="52" Name="Grid Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="Grid Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="Grid Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="Grid Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="49"
   Name="Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="50"
   Name="Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="51" Name="List Table 1 Light"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark"/>
  <w:LsdException Locked="false" Priority="49" Name="List Table 6 Colorful"/>
  <w:LsdException Locked="false" Priority="50" Name="List Table 7 Colorful"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 1 Light Accent 1"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2 Accent 1"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3 Accent 1"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4 Accent 1"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark Accent 1"/>
  <w:LsdException Locked="false" Priority="49"
   Name="List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="50"
   Name="List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 1 Light Accent 2"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2 Accent 2"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3 Accent 2"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4 Accent 2"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark Accent 2"/>
  <w:LsdException Locked="false" Priority="49"
   Name="List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="50"
   Name="List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 1 Light Accent 3"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2 Accent 3"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3 Accent 3"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4 Accent 3"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark Accent 3"/>
  <w:LsdException Locked="false" Priority="49"
   Name="List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="50"
   Name="List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 1 Light Accent 4"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2 Accent 4"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3 Accent 4"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4 Accent 4"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark Accent 4"/>
  <w:LsdException Locked="false" Priority="49"
   Name="List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="50"
   Name="List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 1 Light Accent 5"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2 Accent 5"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3 Accent 5"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4 Accent 5"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark Accent 5"/>
  <w:LsdException Locked="false" Priority="49"
   Name="List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="50"
   Name="List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked="false" Priority="51"
   Name="List Table 1 Light Accent 6"/>
  <w:LsdException Locked="false" Priority="52" Name="List Table 2 Accent 6"/>
  <w:LsdException Locked="false" Priority="46" Name="List Table 3 Accent 6"/>
  <w:LsdException Locked="false" Priority="47" Name="List Table 4 Accent 6"/>
  <w:LsdException Locked="false" Priority="48" Name="List Table 5 Dark Accent 6"/>
  <w:LsdException Locked="false" Priority="49"
   Name="List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="50"
   Name="List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked="false" Priority="51" Name="Mention"/>
  <w:LsdException Locked="false" Priority="52" Name="Smart Hyperlink"/>
  <w:LsdException Locked="false" Priority="46" Name="Hashtag"/>
  <w:LsdException Locked="false" Priority="47" Name="Unresolved Mention"/>
  <w:LsdException Locked="false" Priority="48" Name="Smart Link"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:Wingdings;
	panose-1:5 0 0 0 0 0 0 0 0 0;
	mso-font-charset:2;
	mso-generic-font-family:decorative;
	mso-font-pitch:variable;
	mso-font-signature:0 268435456 0 0 -2147483648 0;}
@font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-536870145 1107305727 0 0 415 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-536859905 -1073697537 9 0 511 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin:0cm;
	margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-language:DE;}
p.MsoBodyText, li.MsoBodyText, div.MsoBodyText
	{mso-style-unhide:no;
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:6.0pt;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-fareast-language:DE;}
p.Briefkopfadresse, li.Briefkopfadresse, div.Briefkopfadresse
	{mso-style-name:Briefkopfadresse;
	mso-style-unhide:no;
	margin:0cm;
	margin-bottom:.0001pt;
	text-align:justify;
	line-height:12.0pt;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	mso-bidi-font-size:10.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-font-kerning:9.0pt;}
p.NameinAdresse, li.NameinAdresse, div.NameinAdresse
	{mso-style-name:"Name in Adresse";
	mso-style-unhide:no;
	mso-style-parent:Briefkopfadresse;
	mso-style-next:Briefkopfadresse;
	margin-top:11.0pt;
	margin-right:0cm;
	margin-bottom:0cm;
	margin-left:0cm;
	margin-bottom:.0001pt;
	text-align:justify;
	line-height:12.0pt;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	mso-bidi-font-size:10.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-font-kerning:9.0pt;}
p.Betreffzeile, li.Betreffzeile, div.Betreffzeile
	{mso-style-name:Betreffzeile;
	mso-style-unhide:no;
	mso-style-next:"Body Text";
	margin-top:0cm;
	margin-right:0cm;
	margin-bottom:9.0pt;
	margin-left:18.0pt;
	text-indent:-18.0pt;
	line-height:12.0pt;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Times New Roman",serif;
	mso-fareast-font-family:"Times New Roman";
	mso-font-kerning:9.0pt;
	font-weight:bold;
	mso-bidi-font-weight:normal;}
span.SpellE
	{mso-style-name:"";
	mso-spl-e:yes;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;}
@page WordSection1
	{size:595.3pt 841.9pt;
	margin:70.85pt 70.85pt 2.0cm 70.85pt;
	mso-header-margin:35.4pt;
	mso-footer-margin:35.4pt;
	mso-paper-source:0;}
div.WordSection1
	{page:WordSection1;}
 /* List Definitions */
 @list l0
	{mso-list-id:384528738;
	mso-list-type:hybrid;
	mso-list-template-ids:-108740768 67567617 67567619 67567621 67567617 67567619 67567621 67567617 67567619 67567621;}
@list l0:level1
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l0:level2
	{mso-level-number-format:bullet;
	mso-level-text:o;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:"Courier New";}
@list l0:level3
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Wingdings;}
@list l0:level4
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l0:level5
	{mso-level-number-format:bullet;
	mso-level-text:o;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:"Courier New";}
@list l0:level6
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Wingdings;}
@list l0:level7
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l0:level8
	{mso-level-number-format:bullet;
	mso-level-text:o;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:"Courier New";}
@list l0:level9
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Wingdings;}
@list l1
	{mso-list-id:1421172094;
	mso-list-type:hybrid;
	mso-list-template-ids:-181654850 67567617 1595441804 67567621 67567617 67567619 67567621 67567617 67567619 67567621;}
@list l1:level1
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l1:level2
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:"Courier New";
	mso-bidi-font-family:"Times New Roman";}
@list l1:level3
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Wingdings;}
@list l1:level4
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l1:level5
	{mso-level-number-format:bullet;
	mso-level-text:o;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:"Courier New";}
@list l1:level6
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Wingdings;}
@list l1:level7
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Symbol;}
@list l1:level8
	{mso-level-number-format:bullet;
	mso-level-text:o;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:"Courier New";}
@list l1:level9
	{mso-level-number-format:bullet;
	mso-level-text:;
	mso-level-tab-stop:none;
	mso-level-number-position:left;
	text-indent:-18.0pt;
	font-family:Wingdings;}
ol
	{margin-bottom:0cm;}
ul
	{margin-bottom:0cm;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-unhide:no;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-para-margin-bottom:.0001pt;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Times New Roman",serif;}
</style>
<![endif]--><!--[if gte mso 9]><xml>
 <o:shapedefaults v:ext="edit" spidmax="1026"/>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <o:shapelayout v:ext="edit">
  <o:idmap v:ext="edit" data="1"/>
 </o:shapelayout></xml><![endif]-->
</head>

<body lang=DE style='tab-interval:35.4pt'>

<div class=WordSection1>

<p class=Briefkopfadresse><b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt'>Antrag auf Kostenerstattung <o:p></o:p></span></b></p>

<p class=Briefkopfadresse><span style='font-size:14.0pt;mso-bidi-font-size:
10.0pt'><o:p>&nbsp;</o:p></span></p>

    <p class=Briefkopfadresse style='line-height:150%'><b style='mso-bidi-font-weight:
normal'><span style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:
150%'>Name, Vorname</span></b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt;line-height:150%'>:<span style='mso-tab-count:1'>       </span>
        <?php echo str_pad($this->data[2], 102, '.') ?><o:p></o:p></span></p>

    <p class=Briefkopfadresse style='line-height:150%'><b style='mso-bidi-font-weight:
normal'><span style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:
150%'>Straße</span></b><span style='font-size:12.0pt;mso-bidi-font-size:10.0pt;
line-height:150%'>:<span style='mso-spacerun:yes'>               </span><span
style='mso-tab-count:1'>        </span>
            <?php echo str_pad($this->data[3], 102, '.') ?><o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><b style='mso-bidi-font-weight:
normal'><span style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:
150%'>PLZ/ Wohnort</span></b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt;line-height:150%'>:<span style='mso-tab-count:1'>         </span>
        <?php echo str_pad($this->data[4], 102, '.') ?><o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span class=SpellE><b
style='mso-bidi-font-weight:normal'><span lang=IT style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%;mso-ansi-language:IT'>Telefon</span></b></span><b
style='mso-bidi-font-weight:normal'><span lang=IT style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%;mso-ansi-language:IT'>/ E-Mail</span></b><span
lang=IT style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:150%;
mso-ansi-language:IT'>:<span style='mso-tab-count:1'>        </span>
        <?php echo str_pad($this->data[5], 102, '.') ?><o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><b style='mso-bidi-font-weight:
normal'><span style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:
150%'>an die Firma</span></b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt;line-height:150%'>:<span style='mso-spacerun:yes'>  </span><span
style='mso-tab-count:1'>          </span>
        <?php echo $this->data[8] ?><o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'>Mir sind folgende Kosten
entstanden:<o:p></o:p></span></p>

<table class=MsoNormalTable border=1 cellspacing=0 cellpadding=1>
 <tr style='mso-yfti-irow:0;mso-yfti-firstrow:yes;height:16.0pt'>
     <td>
  <p class=MsoNormal><b style='mso-bidi-font-weight:normal'><span
  style='font-family:"Calibri",sans-serif;mso-bidi-font-family:"Times New Roman";
  color:black'>Nr.<o:p></o:p></span></b></p>
  </td>
     <td>
  <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'>Datum<o:p></o:p></span></b></p>
  </td>
     <td>
  <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'>Zulieferer<o:p></o:p></span></b></p>
  </td>
     <td>
  <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'>Kategorie<o:p></o:p></span></b></p>
  </td>
     <td>
  <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'>Preis €<o:p></o:p></span></b></p>
  </td>
     <td>
  <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'>Preis Lev<o:p></o:p></span></b></p>
  </td>
     <td>
         <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'>Link<o:p></o:p></span></b></p>
     </td>
 </tr>
    <?php foreach ($this->data['invoices'] as $invoice) {
        ?>
        <tr style='mso-yfti-irow:1;height:16.0pt'>
            <td>
                <p class=MsoNormal><span style='font-family:"Calibri",sans-serif;mso-bidi-font-family:
  "Times New Roman";color:black'><o:p>&nbsp;</o:p>
                    <?php echo $invoice['belegnr']; ?>
                    </span></p>
            </td>
            <td>
                <p class=MsoNormal><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'><o:p>&nbsp;</o:p>
                        <?php echo $invoice['rechdatum']; ?>
                        </span></p>
            </td>
            <td>
                <p class=MsoNormal><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'><o:p>&nbsp;</o:p>
                        <?php echo $invoice['kname']; ?>
                        </span></p>
            </td>
            <td>
                <p class=MsoNormal><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'><o:p>&nbsp;</o:p>
                        <?php echo $invoice['info']; ?>
                        </span></p>
            </td><td>
                <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black;text-align: right'><o:p>&nbsp;</o:p>
                        <?php
						if($invoice['dsymbol'] == 'lv')
							echo str_replace('.',',',number_format($invoice['brutto']/1.95583,2))." €";
						elseif($invoice['dsymbol'] == '€' || !$invoice['dsymbol'])
							echo str_replace('.',',',number_format($invoice['brutto'],2))." €";
                        else
							echo str_replace('.',',',number_format($invoice['brutto'],2))." ".$invoice['dsymbol'];
                        ?>
                        </span></b></p>
            </td>
            <td>
                <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'><o:p>&nbsp;</o:p>
                        <?php
                        if($invoice['dsymbol'] == 'lv')
							echo str_replace('.',',',number_format($invoice['brutto'],2))." ".$invoice['dsymbol'];
                        ?>
                        </span></b></p>
            </td>
            <td>
                <p class=MsoNormal><b><span style='font-family:"Calibri",sans-serif;
  mso-bidi-font-family:"Times New Roman";color:black'><o:p>&nbsp;</o:p>
                        <?php echo "<a href='".$invoice['file']['filepath']."'>
                            <div style='display: inline'>
                            Link
                            </div>
                            </a>
                            ";
                        ?>
                        </span></b></p>
            </td>
        </tr>
    <?php
        // closing the foreach loop
    }
    ?>
</table>

<p class=Briefkopfadresse style='line-height:150%'><b><span style='font-size:
12.0pt;mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></b></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse><b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt'>Summe Kosten:<span style='mso-tab-count:1'>        </span></span></b><span
style='font-size:12.0pt;mso-bidi-font-size:10.0pt'>
        <?php echo str_replace('.',',',number_format($this->data[10],2)) ?>
        <b>€<o:p></o:p></b></span></p>

<p class=Briefkopfadresse><b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt'><o:p>&nbsp;</o:p></span></b></p>

<p class=Briefkopfadresse><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt'>Belege für die Kosten sind dem Antrag beigefügt.<o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-family:
"Arial",sans-serif'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'>Hiermit bitte ich um die <b>Erstattung
</b>meiner Kosten in Höhe von:<span style='mso-spacerun:yes'>       
</span><?php echo str_replace('.',',',number_format($this->data[10],2)) ?>€.<o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><!--[if supportFields]><span
style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:150%'><span
style='mso-element:field-begin'></span><span style='mso-bookmark:Kontrollkästchen1'><span
style='mso-spacerun:yes'> </span>FORMCHECKBOX </span></span><![endif]--><span
style='mso-bookmark:Kontrollkästchen1'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><![if !supportNestedAnchors]><a
name=Kontrollkästchen1></a><![endif]><!--[if gte mso 9]><xml>
 <w:data>FFFFFFFF650400001E0011004B006F006E00740072006F006C006C006B00E400730074006300680065006E003100000000000000000000000000000000000000000000000000</w:data>
</xml><![endif]--></span></span><!--[if supportFields]><span style='mso-bookmark:
Kontrollkästchen1'></span><span style='mso-element:field-end'></span><![endif]--><span
style='mso-bookmark:Kontrollkästchen1'></span><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><span
                style='mso-spacerun:yes'>  </span><s>Ich bitte um Barauszahlung.</s><o:p></o:p></span></p>

<p class=Briefkopfadresse align=left style='text-align:left;line-height:150%'><!--[if supportFields]><span
style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:150%'><span
style='mso-element:field-begin'></span><span style='mso-bookmark:Kontrollkästchen2'><span
style='mso-spacerun:yes'> </span>FORMCHECKBOX </span></span><![endif]--><span
style='mso-bookmark:Kontrollkästchen2'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><![if !supportNestedAnchors]><a
name=Kontrollkästchen2></a><![endif]><!--[if gte mso 9]><xml>
 <w:data>FFFFFFFF650400001E0011004B006F006E00740072006F006C006C006B00E400730074006300680065006E003200000001000000000000000000000000000000000000000000</w:data>
</xml><![endif]--></span></span><!--[if supportFields]><span style='mso-bookmark:
Kontrollkästchen2'></span><span style='mso-element:field-end'></span><![endif]--><span
style='mso-bookmark:Kontrollkästchen2'></span><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><span
style='mso-spacerun:yes'>  </span>Ich bitte um die Überweisung auf mein Konto.<o:p></o:p></span></p>

<p class=Briefkopfadresse align=left style='text-align:left;line-height:150%'><span
style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><span style='mso-tab-count:1'>            </span><span
style='mso-spacerun:yes'> </span>Kto.-Nr. / IBAN:<span style='mso-tab-count:1'>        </span>
        <?php echo str_pad($this->data[12], 42, '.') ?><span
style='mso-spacerun:yes'>         </span>BLZ / BIC:<span style='mso-tab-count:1'>           </span>
        <?php echo str_pad($this->data[13], 42, '.') ?>
        <o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><span style='mso-tab-count:1'>            </span><span
style='mso-spacerun:yes'> </span>Bank:<span style='mso-tab-count:2'>             </span>
        <?php echo str_pad($this->data[11], 102, '.') ?><o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><b style='mso-bidi-font-weight:
normal'><span style='font-size:12.0pt;mso-bidi-font-size:10.0pt;line-height:
150%'>Ort/ Datum</span></b><span style='font-size:12.0pt;mso-bidi-font-size:
10.0pt;line-height:150%'>:<span style='mso-tab-count:2'>              </span>
        <?php echo str_pad($this->data[14].", ".$this->data[15]->format("d.m.Y"), 102, '.') ?><o:p></o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'><o:p>&nbsp;</o:p></span></p>

<p class=Briefkopfadresse style='line-height:150%'><span style='font-size:12.0pt;
mso-bidi-font-size:10.0pt;line-height:150%'>Unterschrift des Antragstellers/
der Antragstellerin:<span style='mso-spacerun:yes'>        </span><span
style='mso-spacerun:yes'> </span>______________________________<b><o:p></o:p></b></span></p>

</div>
<hr>
<div>
    <h1>Belegkopien</h1>
    <?php
	foreach ($this->data['invoices'] as $invoice) {
	    ?>
        <h4>
            <?php
                echo "Belegnummer: ".$invoice['belegnr']." (".$invoice['info'].")";
            ?>
        </h4>
        <?php
		    // pull image
            if($invoice['file']['mime_type'] != 'application/pdf')
            {
                if($invoice['file']['filepath'])
				    echo "<img src='".$invoice['file']['filepath']."' height='400px'>'";
                else
                    echo "Kein Belegbild vorhanden";
            }
            echo "<br><a href='".$invoice['file']['filepath']."'>
                        <div style='display: inline; font-size: 8pt'>".$invoice['file']['filepath']."
                        </div>
                      </a>";

        ?>
    <?php
        // closing foreach loop
    }
    ?>
</div>

</body>

</html>
