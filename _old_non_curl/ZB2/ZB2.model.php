<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'WorkingOrders.php';
require_once ABS_PATH.'Addresses.php';
require_once ABS_PATH.'v3/Employees.php';
require_once ABS_PATH.'Team.php';
require_once ABS_PATH.'Partners.php';
require_once ABS_PATH.'Settings.php';


class C_ZB2 {
private $db;
private $documentation;
private $projects;
private $workingOrders;
private $addresses;
private $employees;
private $teams;
private $partners;
private $settings;
private $helper;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->workingOrders = new WorkingOrders();
		$this->addresses = new AddressesDB();
		$this->employees = new \v3\Employees();
		$this->teams = new Team();
		$this->partners = new Partners();
		$this->settings = new Settings();
		$this->helper = new Helper();
	}

	public function getData($schemaId,$documentId) {
		//hardcoded data for layout development
		$positions = $this->documentation->getDocumentId($schemaId,$documentId)['children'];

		$positionsByPrintIndex = array();
		foreach ($positions as $position) {
			if($position['printText'])
				$positionsByPrintIndex[$position['printText']] = $position['reportedValue'];
		}
		$data = array();

		// mapping between element of template and the printText-Index of $positions
		// printText-Index is generated and UPDATED from the mapping Excel-File
		$data['Technikdaten VZ']['D.1 Fabrikmarke'] = 9;
		$data['Technikdaten VZ']['D.2 Typ (Klartext)'] = 6;
		$data['Technikdaten VZ']['D.2 Variante (Klartext)'] = 7;
		$data['Technikdaten VZ']['D.2 Version (Klartext)'] = 8;
		$data['Technikdaten VZ']['D.3 Handelsbezeichnung (en)'] = 10;
		$data['FahrzeugdatenVZ']['(2) Hersteller-Kurzbezeichnung'] = 2;
		$data['FahrzeugdatenVZ']['(2.2) Code zu Typ-Schlüsselnummer (D.2)'] = 3;
		$data['FahrzeugdatenVZ']['(2.2) Code zu Variante/Version (D.2)'] = 4;
		$data['FahrzeugdatenVZ']['E Fahrzeug - Identifizierungsnummer'] = '12345678901234567';
		$data['FahrzeugdatenVZ'][' Prüfziffer zur Fahrzeugidentifizierungsnummer'] = 6;
		$data['FahrzeugdatenVZ']['(2.2) Prüfziffer zum Code TYP/Variante/Version'] = 5;
		$data['FahrzeugdatenVZ']['J Fahrzeugklasse'] = 21;
		$data['Technikdaten VZ']['(4) Art des Aufbaus'] = 22;
		$data['FahrzeugdatenVZ']['(5) Bezeichnung der Fahrzeugklasse'] = 23;
		$data['FahrzeugdatenVZ']['(5) Bezeichnung der Art des Aufbaus'] = 24;
		$data['Technikdaten VZ']['R Farbe (Klartext)'] = 15;
		$data['FahrzeugdatenVZ']['(2) Hersteller-Kurzbezeichnung'] = 2;
		$data['Technikdaten VZ']['P.1 Hubraum in cm3'] = 26;
		$data['Technikdaten VZ']['P.2 Nennleistung in KW'] = 27;
		$data['Technikdaten VZ']['P.4 Nenndrehzahl bei min -1'] = 28;
		$data['Technikdaten VZ']['P.3 Kraftstoff oder Energiequelle'] = 19;
		$data['FahrzeugdatenVZ']['(11) Code zur Grundfarbe des Fahrzeugs (R)'] = 'A';
		$data['FahrzeugdatenVZ']['(11) Code zur Zweitfarbe des Fahrzeugs (R)'] = 'B';
		$data['Technikdaten VZ']['(6) Datum der Typgenehmigung'] = '24.08.2021';
		$data['Technikdaten VZ']['K Nummer der EG_x0002_Typgenehmigung oder BE'] = 11;
		
		//TO DO - CHECK THE TITLE AFTER JSON SCHEMA IS CREATED
		$data['FahrzeugdatenVZ']['Code zur Kraftstoffart / Energiequelle P.3'] = 17;


		$iterator = new RecursiveArrayIterator($data);
		$resursive = new RecursiveIteratorIterator($iterator,RecursiveIteratorIterator::SELF_FIRST);
		$parentKey = "";
		foreach ($resursive as $key => $value) {
			// store parentKey if iterator processes an array
			if(is_array($value))
			{
				$parentKey = $key;
				continue;
			}
			else
			{
				// we found a leaf, in this case
				// we overwrite the printText value by the real value from the document
				$data[$parentKey][$key] = $positionsByPrintIndex[$value];
			}
		}

		return $data;
	}
	
	
}