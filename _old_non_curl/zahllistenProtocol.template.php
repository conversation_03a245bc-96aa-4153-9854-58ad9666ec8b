<html>
<?php $data = $this->data;?> 

<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Protokoll</title>
	</head>
	<body>
		<?php include ABS_PATH.'printouts/zahllistenProtocolHeader.template.php';?>
		<br/>
		<div class="container">
		<table class="headerTable">
			<tr>
				<th align="left" class="bordered"><?=$data['schemaData']['title'];?></th>
				<th class="bolded"></th>	
			</tr>
			<tr>
				<td><b>BV: </b><?=$data['projectData']['projectName'].' ('.$data['projectData']['externalId'].')';?></td>
				<td align="right">
				<b>Zählauftragsnummer: </b>
<?php 			if($data['projectData']['externalId']) { ?>
					<?=trim($data['projectData']['externalId']).'-'.trim($data['projectData']['aanr']);?>
<?php 			} else {?>
					<?=trim($data['projectData']['aanr']);?>
<?php 			} ?>
				</td>
			</tr>
			<tr>
				<td align="left"><b>Kurztext: </b><?=$data['projectData']['woName'];?></td>
				<td align="right"><b>Datum: </b><?=$data['schemaData']['date'];?></td>
<?php foreach ($data['description'] as $k=>$v) { ?>
			<tr><td><?=$v?></td></tr>
<?php } ?>
			</tr>
		</table>
		<br/>
		<table class="mainTable" cellspacing="0" cellpadding="0">
			<tr class="bordered">
<?php 			for ($i = 0; $i < count($data['documents'])+2; $i++ )	{?>
<?php 				if($i == 3) { ?> 
						<th colspan="<?=count($data['documents'])?>">Zählliste</th>
<?php				} else { ?>				
						<th style="visibility: hidden;"></th>
<?php 				} ?>
<?php 			} ?>
			</tr>
			<tr>
				<td><b>Lieferant</b></td>
				<td><b>Kategorie</b></td>
				<td><b>Größe</b></td>

<?php 			foreach($data['documents'] as $k => $doc) {?>
					<td>
					    <b><?=$doc['localId'].' (#'.$doc['documentId'].')'?></b>
					    <br>
					    <?php 
					        if($doc['originDocumentId'])
					        { ?>
					            Nachfolger von #<?=$doc['originDocumentId']?>
					   <?php
					   // closing if
					        }
					        ?>
					</td>
<?php 			} ?>

				<td><b>Gesamt</b></td>
				<td><b>Nr.</b></td>
			</tr>
<?php $lastParentId = 0; ?>
<?php		foreach ($data['formulas'] as $k=>$schemaP) { ?>							
				<tr>
				<td><?=$data['suppliers'][$schemaP['supplierId']]?></td>
<?php 					if($lastParentId !== ($schemaP['parentId'])) { $lastParentId = $schemaP['parentId']?>
						<td rowspan="<?=array_count_values(array_column($data['formulas'], 'parentId'))[$lastParentId]?>">
							<?=$data['headlines'][$schemaP['parentId']]['title']?>
						</td>
<?php 					}  ?>
					<td align="right"><?=$schemaP['title']?></td>
<?php 		$sum = 0;	for ($i = 0; $i < count($data['documents']); $i++ ) {?>
<?php 					if(in_array($data['documents'][$i]['documentId'], $data['parents']) && (!is_null($data['parents']))) { ?>
                            <td align="right" class="excludeFromTotal"><?=$data['documents'][$i][$schemaP['id']]?></td>
<?php 					} else { ?>
							<td align="right"><?=$data['documents'][$i][$schemaP['id']]?></td>
<?php 						$sum += (float)$data['documents'][$i][$schemaP['id']]; ?>
<?php 					} ?>
<?php 				} ?>
					<td align="right" class="total"><?=$sum;?></td>
					<td align="right"><?=$schemaP['positionNumber'];?></td>
				</tr>
<?php 			}?>
			
		</table>
		<br/>
		<table class="signatures">
			<tr>
				<th width="33%">
					Aufnehmer: <?=$data['employeesAndImages'][0]['Name Zähler'].'/';?>
				    <img src="<?=$data['employeesAndImages'][0]['Unterschrift Zähler'];?>" alt="Signature">
				</th>
				<th width="33%"></th>
				<th width="33%">
					Aufschreiber: <?=$data['employeesAndImages'][0]['Name Aufschreiber'].'/';?>
					<img src="<?=$data['employeesAndImages'][0]['Unterschrift Aufschreiber'];?>" alt="Signature">
				</th>
			</tr>
		</table>
		
		<p style="page-break-before: always"></p>
<?php 		foreach ($data['serials'] as $docId => $parent) { ?>
<?php 			foreach ($parent as $p => $child) { ?>
					<table class="serialTable">
						<tr><th align="center"><?=$p;?></th></tr>
						<tr><td align="center"><?=$child['Seriennummer'];?></td></tr>
						<tr><td align="center"><img src="<?=$child['Foto Seriennummer']?>" alt="<?=$p;?>" width="100%" height="auto"></td></tr>
					</table>
					<p style="page-break-before: always"></p>
<?php 			} ?>				
<?php 		} ?>
		</div>
	</body>
</html>

<style>

.mainTable, .headerTable, .signatures {width: 100%;}

.mainTable tr, .mainTable td, .mainTable th {border: 1px solid black;}

.bordered {border: 1px solid black; } 

.bolded {
  font-weight: bold;
}

.mainTable th {font-size:large; !important;}
.mainTable td {font-size: small !important;}
.description {font-size: small !important;}
body {font-family:Helvetica;}

.container {height:100%;}
.excludeFromTotal {background-color: LightGrey;}
img {
    width: 60%;
    height: auto;
}

.total {padding-right: 5px;}

.serialTable {
	border-collapse:separate; 
	border-spacing:5em;
	table-layout:fixed;
    width:100%;
}

</style>
	
