  <html>
<?php
	$data = $this->data;
	
	 function renderCheckbox($value) {
			if (strtolower($value) == 'ja' || $value == '1' || $value == 'true') {
				 echo '<img src="'.WEB_ROOT_API.'vendor\printouts\checked.png?>"'.' alt="checked" width="auto" height="50px">';
			} else {
				echo '<img src="'.WEB_ROOT_API.'vendor\printouts\unchecked.png?>"'.' alt="unchecked" width="auto" height="50px">';
			}
		}
?>
	<head>
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<title>Begehungsbericht</title>
	</head>
	<body>
	 <div style="page-break-before:always;" class="break generalWrapper">
		<div class="container">
			<img src="<?=WEB_ROOT_API.'vendor\printouts\begehungsberichtLogo.png'?>" alt="Logo" class="logo">
			<div align="center" class="title"><b><?=$data['projectName'];?></b></div>
		</div>
		<br>
		<table class="bauvorhaben">
			<tr>
				<th width="20%">Bauvorhaben:</th>
				<th width="80%" align="left">Neubau des Naturwissenschaftshauses der GS Holweide</th>
			</tr>
			<tr>
				<td></td>
				<td align="left"><?=$data['combinedAddress'];?></td>
			</tr>
		</table>
		
		<table cellspacing="0" class="greyTable">
			<tr class="grey">
				<th width="30%"><br></th>
				<th width="30%"></th>
				<th width="20%"></th>
				<th width="20%"></th>
			</tr>
			<tr>
				<td class="grey top"><b>gesprochen mit:</b></td>
				<td class="paddingContent">
<?php 				foreach ($data['Begehungsbericht']['Anwesende Firmen'] as $k=>$v) { ?>
<?php 					if(!is_null($v)) {?>
							<?='<b>'.$v['Name'].' ('.$v['Firma'].', '.$v['Gewerk'].')'.'</b></br>'; ?>
<?php 					} ?>
<?php 				} ?>
				</td>
				<td class="grey top" align="center"><b>Datum:</b></td>
				<td align="right" class="top paddingContent">
					<b><?=$data['Begehungsbericht']['Datum'] ? date('d.m.Y', strtotime($data['Begehungsbericht']['Datum'])) : '';?></b>
				</td>
			</tr>
			<tr class="grey">
				<td><br></td>
				<td></td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td class="grey"><b>Begehung durch:</b></td>
				<td align="left" class="paddingContent"><b><?=$data['documentAuthor'];?></b></td>
				<td class="grey" align="center"><b>Protokoll Nr.</b></td>
				<td align="right" class="paddingContent"><b><?=$data['documentId'];?></b></td>
			</tr>
			<tr class="grey">
				<td><br></td>
				<td></td>
				<td></td>
				<td></td>
			</tr>
		</table>
		
		<div class="content" align="left">
			<b><i>Hinweis bezüglich Auswirkungen des Corona-Virus auf Baustellen</i></b><br>
			<br>
			Das neuartige Coronavirus (SARS-CoV-2) hat Deutschland erreicht, mehrere Fälle wurden im <br>
			Bundesgebiet bestätigt.
			<br>
			Coronaviren können beim Menschen Krankheiten verursachen, die von leichteren Erkältungskrankheiten <br>
			bis hin zu schwereren Krankheiten wie MERS (Middle East Respiratory Syndrome), SARS (Severe Acute <br>
			Respiratory Syndrome) und aktuell COVID-19 (Coronavirus Disease 2019) reichen. <br>
			Infektionen des Menschen mit Coronaviren verlaufen meist mild und ohne erkennbare Symptome. <br>
			Es können Atemwegserkrankungen mit Fieber, Husten, Atemnot und Atembeschwerden auftreten. <br>
			In schwereren Fällen kann eine Infektion eine Lungenentzündung, ein schweres akutes respiratorisches <br>
			Syndrom, ein Nierenversagen und sogar den Tod verursachen, meist bei Personen, deren Immunsystem <br>
			geschwächt ist. <br>
			<br>
			Um das Infektionsrisiko gering zu halten, werden für das Coronavirus dieselben Hygienemaßnahmen <br>
			empfohlen, die auch bei einer gewöhnlichen Influenza gelten. Denn wie bei der Influenza wird diese <br>
			ebenso über Tröpfcheninfektion von Mund und Nase übertragen. <br>
			<br>
			<b>Konkret bedeutet das:</b> Oft die Hände waschen und Desinfektionsmittel bei sich führen, außerdem <br>
			Händeschütteln und den Besuch großer Veranstaltungen oder Menschansammlungen meiden <br>
			<br>
			<b>
				Dies hat, nach unserer Auffassung auch Auswirkungen auf den Sicherheits- u. Gesundheits- <br>
				schutz auf Baustellen. Wir möchten Sie bitten, die beauftragten Firmen entsprechend zu <br>
				informieren und die Baustellen gemäß den Hinweisen entsprechend auszustatten. <br>
			</b>
			<br>
			Die Firmen sind gem. Arbeitsschutzgesetz verpflichtet, im Zuge ihrer Gefährdungsbeurteilung, die <br>
			Gefährdungen ihrer Mitarbeiter zu beurteilen und ggf. Schutzmaßnahmen festzulegen und diese <br>
			umzusetzen. Grundsätzlich kann für die Gefährdung „Coronavirus“ (biologische Einwirkung) nur mit Hilfe <br>
			der Schutzmaßnahme „mehrfaches Händewaschen“ (ggf. die Erstellung eines Hautschutzplanes) <br>
			entgegengewirkt werden. <br>
			<br>
			Dies hat zur Folge, dass jede Baustelle mit einer geeigneten Waschgelegenheit (siehe ASR-A4.1 Nr.: <br>
			3.11) ausgestattet werden muss! Diese Waschgelegenheit ist mit Mitteln zum Reinigen (z. B. Seife in <br>
			Seifenspendern) und Trocknen der Hände (z. B. Einmalhandtücher), sowie mit Desinfektionsmitteln <br>
			auszustatten. Eine Reinigung der Waschgelegenheit sollte aus hygienischen Gründen täglich erfolgen <br>
			<span class="smallFont">Quelle: teilweise aus „Veröffentlichung der BG Bau vom 31.01.2020“</span>
		</div>
		<br>
	 </div>
		
	<div style="page-break-before:always;" align="center"><b>Festgestellte sicherheitstechnische Mängel / sicherheitswidriges Verhalten</b></div>
		<table class="pageTable" cellspacing="0">
			<tr class="grey">
				<th align="center title" width="80%">
					Sicherheitstechnische Mängel / sicherheitswidriges Verhalten / <br>
					besprochene Punkte
				</th>
				<th align="center" width="20%">Erledigt</th>
			</tr>
<?php 				foreach ($data['Begehungsbericht']['Punkte ins Protokoll / Beanstandungen / Mängel'] as $key => $value) { ?>
<?php 					if(!is_null($value)) {?>
							<tr>
								<td class="top"> 
									<b><?=$value['Beschreibung']?><br></b>
									<img src="<?=$value['Fotos']?>" alt="<?=$key?>" class="logoRightColumn">
								</td>
								<td align="center"><?=renderCheckbox($value['Erledigt'])?></td>
							</tr>
<?php					 }?>
<?php }		 		?>
		</table>
	 <br>
	 <div align="left" style="page-break-before:always;" class="baustellenubersicht"><u>Baustellenübersicht:</u></div>
	 <br>
<?php $table = '<table class="imagesTable" cellspacing="0"><tbody class="baustelleFotos"><tr>'; ?>

<?php   foreach ($data['Begehungsbericht']['Baustellenübersicht Fotos'] as $key=>$value) { ?>
<?php      $table .= '<td align="center">'.'<img src="'.$value.'"alt="Logo" class="twoPerRow"'.'</td>'; ?>

<?php     if((str_replace('Foto ','',$key)) % 2 == 0) { ?>
<?php         $table .= "</tr><tr>"; ?>
<?php     } ?>
<?php }$table .= "</tr></tbody></table>";  echo $table;?>

</html>

<style>
.content {position:relative;}
.logo {width:100%; height:auto; margin-top: 0px; position:relative; z-index:9999;}
.title {font-size:26px;}
.bauvorhaben {width:100%; font-size:21px;}
.content {width:100%; font-size:12px;}
.smallFont {font-size:8px;}
.greyTable {border: 1px solid lightgrey; width:100%;}
.grey {background-color: lightgrey;}
.pageTable {width:100%;}
.pageTable td{border:1px solid black;}
.pageTable th {border:1px solid black;}
.twoPerRow {width:49%;}
.logoRightColumn {width:60%; height:auto;}
.top { vertical-align:top; padding:10px;}
.imagesTable {border:1px solid white!important; width:100%;}
.baustellenubersicht {font-size:x-large;}
tbody{
    width: 100%;
    display: table;
}

@media print {
  .break {page-break-after: always;}
}

* {
  overflow: visible !important;
}

.baustelleFotos {width: 100%; display: table;}

ul {
  margin: 0;
}
ul.dashed {
  list-style-type: none;
  font-size:18px;
}
ul.dashed > li {
  text-indent: -5px;
}
ul.dashed > li:before {
  content: "-";
  text-indent: -5px;
}
.paddingContent {padding:10px;}
.liTitle {font-size:18px;}

table { page-break-inside:auto }
    tr    { page-break-inside:avoid; page-break-after:auto }
    thead { display:table-header-group }
    tfoot { display:table-footer-group }
</style>



