<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';

class C_Abnahme {
private $db;
private $documentation;
private $projects;
private $customers;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->customers = new Customers();
	}

	
	public function getData($schemaId,$documentId) {
		
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		if(empty($documentData)) {
			throw new RestException(400, "Not existing schema or document or no data for this document!");
		}
		$templateData = [];
		
		
		foreach ($documentData['children'] as $k=>$v) {
			if($v['type'] == 'signatureField') {
				$templateData[$v['title']] = $v['filePath'];
			} else {
				$templateData[$v['title']] = date('d.m.Y',strtotime($v['reportedValue']));
			}
		}
		
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		
		if(!empty($projectData)) {
			$customerData = $this->getCustomerData($projectData['knr']);
		}
		
		if($customerData) {
			isset($customerData['name']) ? $templateData['customerName'] = $customerData['name'] : $templateData['customerName'] = ''; 
			
			isset($customerData['ort']) ? $templateData['customerPlace'] = $customerData['ort'] : $templateData['customerPlace'] = '';
			
			$templateData['customerAddress'] = '';
			
			if(isset($customerData['plz'])) {
				$templateData['customerAddress'] .= $customerData['plz'].' ';
			}
			
			if(isset($customerData['adresse1'])) {
				$templateData['customerAddress'] .= $customerData['adresse1'];
			}
			
			$templateData['customerAddress'] = trim($templateData['customerAddress']);
		}
		
		if($projectData) {
			$templateData['projectName'] = $projectData['project_name'];
			$templateData['projectDate'] = date('d.m.Y',strtotime($projectData['start_date']));
			$templateData['projectNumber'] = $projectData['ktr'];
		}
		
		return $templateData;
	}
	
	private function getProjectData($projectNo) {
		$result = $this->projects->get($projectNo);
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		
		return end($projects);
	}	
	
	private function getCustomerData($customerNo) {
		$customerData = $this->customers->get($customerNo);
		return $customerData;
	}
	
	
}