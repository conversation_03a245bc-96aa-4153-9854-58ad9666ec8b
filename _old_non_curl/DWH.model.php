<?php
	require_once ABS_PATH.'class/vehicles.class.php';
	require_once ABS_PATH.'class/ktr.class.php';
	require_once ABS_PATH.'class/stunden.class.php';
	require_once ABS_PATH.'class/workingorder.class.php';
	require_once ABS_PATH.'class/kolaa.class.php';
	require_once ABS_PATH.'class/addresses.class.php';
	require_once ABS_PATH.'v2/Documentation.php';
	
class C_DWH {

	private $db;
	
	private $resource;
	private $hours;
	private $documentation;
	private $workingOrder;
	private $kolaa;
	private $ktr;
	private $depr;
	private $address;
	
	public function __construct() {

		$this->db = CommonData::getDbConnection();
		$this->v = new Validator();
		$this->resource =  new VehiclesDB();
		$this->hours = new Hours();
		$this->documentation =  new v2\Documentation();
		$this->workingOrder = new WorkingOrderDB();
		$this->kolaa = new KolaaDB();
		$this->ktr = new KtrDB();
		$this->depr = new Deprecated();
		$this->address = new AddressesDB();
	}

	public function getData($ktr, $aanr, $personalNumbers, $woDateOrProjectYear, $from, $to, $woOrProjectHours) {
		
		
		if($woOrProjectHours == 'working order') {
			//wo date
			$date = empty($woDateOrProjectYear) ? $this->getWoDate($ktr, $aanr) : $woDateOrProjectYear.' 00:00:00.000';  
		
			if(empty($this->checkWoExistence($ktr, $aanr, $date))) {
				throw new RestException(400, "Not existing working order with this ktr, aanr and date!");
			}
		} else {
			//project hours
			
			//the case when hours for project are uased the 4th parameter is project Year
			$year = $woDateOrProjectYear == 0 ? $this->getProjectYear($ktr) : $woDateOrProjectYear;  
		
			if(empty($this->checkProjectExistence($ktr, $woDateOrProjectYear))) {
				throw new RestException(400, "Not existing project with this ktr and year!");
			}
		}
		
		
		$year = date('Y', strtotime($date));
		
		$data = [];
		
		if($woOrProjectHours == 'working order') {
			$all_inter_readable_wos = $this->ktr->get_internal_readable_ktr($year);
			$data['internal_readable_ktrs'] = [];
        
			foreach ($all_inter_readable_wos as $wo) {
				array_push($data['internal_readable_ktrs'], $wo->ktr.'-'.$wo->aanr);
			}
		}
		
		$dbNameApiNameResource = [
				'rnr'	    =>	'rnr',
				'gruppe'	=>	'gruppe',
				'kurzname'	=>	'kurzname',
				'langtext'	=>	'langtext',
				'info'		=>	'info',
				'sort'		=>	'sort',
				'lagerort'	=>	'lagerort',
				'lteartikel'=>	'lteartikel',
				'gb1'       =>	'businessUnit',
				'vondat'	=>	'vondat',
				'plandat'	=>	'plandat',
				'bisdat'	=>	'bisdat',
				'hu'	    =>	'hu',
				'asu'   	=>	'asu'
  		 ];
		
		
		$data['active_resources'] = $this->resource->select_resources('active', $dbNameApiNameResource);
		
		$resnr = !empty($personalNumbers) ? $personalNumbers : 0; 

		
		if($woOrProjectHours == 'project') {
			$aanr = 0;
		}
		
		$data['hours'] = $this->hours->getall($resnr, $from, $to, '', $ktr, $aanr, 'mixed', 0, '','employees','','');
		
		$data['documentation'] = $this->documentation->get($ktr, $aanr, '');
		$data['resources'] = $this->kolaa->get_workorder_resources($ktr, $aanr);
		$data['resources'] = $this->superUnique($data['resources'],'resnr');
		
		
		
        foreach ($data['resources'] as $key=>&$value) {
            foreach ($data['active_resources'] as $obj) {
                if($value['resnr'] == $obj['rnr']) {
                    $value['priceInformation'] = $obj['priceInformation'];
                }
            }
        }
        
		$knr = $this->getKnr($ktr);
		$data['customer_data'] = $this->address->get_single($knr);
		$data['customer_address'] = $data['customer_data']->address ? $data['customer_data']->address : '';
		
        $documentation_user_pnrs = [];

       $data['documentation_user_name'] =  $this->get_pnr_names($documentation_user_pnrs);
       
       foreach ($data['documentation_user_name'] as $key=>$value) {
            foreach ($data['documentation'] as $obj) {
                if($key == $obj->personalNo) {
                    $obj->documentation_user_name = $value;
                }
            }
        }
        
		if($ktr  !== 0 && $year !== 0) {
        
        $db_name_api_name = [
			/*'db_name'	=>	'api_name'*/
			'ktr'		=>	'ktr',
			'jahr'		=>	'year',
			'name'		=>	'project_name',
			'astatus'	=>	'status',
			'von'		=>	'start_date',
			'bis'		=>	'end_date',
			'gb1'		=>	'businessUnit',
			'bvanrede'	=>	'customer_anrede',
			'bvname1'	=>	'customer_name',
			'bvname2'	=>	'customer_address',
			'bvplz'		=>	'customer_postcode',
			'bvaptext'	=>	'contact_person',
			'bvort'		=>	'customer_city',
			'bvtelefon'	=>	'phone_number',
			'bvtelefax'	=>	'fax_number',
			'bvemail'	=>	'email',
			'bvmobil'	=>	'mobile',
			'knr'		=>	'knr',
			'externalCustomerId' => 'externalCustomerId',
			'externalProjectNo'	 => 'externalProjectNo',
			'debnum'	=>	'debnum',
			'kname'		=>	'customer_kname',
			'rvkenn'	=>	'is_frame_contract',
			'typ'	    =>	'typ',
			'appusage'	=>	'appusage',
			'bauleit'	=>	'technicalContactKey',
			'sab'		=>	'commercialContactKey',
			'awert'		=>	'estimatedProjectValue',
			'info'		=>	'info',
			'eh'		=>	'wageTypeUnit',
		];
        	$project_data = $this->ktr->select_ktr(array_filter(['ktr'=>$ktr, 'jahr'=>$year]), $db_name_api_name, false, '', false);
            $data['project_address'] = $project_data->customer_address;
        }
        

        $employees_pnrs = [];
		
        if($data['hours']) {
            foreach($data['hours'] as $key => $obj) {
                
                if($woOrProjectHours == 'project' && $obj['aanr'] != 0) {
                	unset($data['hours'][$key]);
                }
                
                $obj['start_date'] = $date;
                if(isset($obj['pnr'])){
                    array_push($employees_pnrs, $obj['pnr']);
                }
            }
        }
        
        $data['comments'] = $this->getComments($ktr, $aanr);
		// add comment from hours
		if($data['hours'][0]['comment'])
		{
			$hoursCommentParts = explode('|',$data['hours'][0]['comment']);
			$data['comments'][] = $hoursCommentParts[0];
		}

        $data['employees_names'] 	= $this->get_pnr_names($employees_pnrs);
        
        foreach ($data['employees_names'] as $empNO=>$employee) {
            foreach ($data['hours'] as $obj) {
                if($key == $obj->pnr) {
                    $obj['employee_name'] = $employee;
                }
            }
        }

        foreach ($data['hours'] as $hour) {
            $data['description'] = '';
            if(!isset($hour['name'])) {
                continue;
            } else {
                $data['description'] = $hour['name'];
                break;
            }
        }

        usort($data['hours'], function($firstParam, $secondParam){
            $sortedHours = $firstParam['start'] - $secondParam['start'];
            $sortedHours.= $firstParam['pnr'] - $secondParam['pnr'];
            return $sortedHours;
        });

        $data['timestamps'][$hour['pnr']] = [];
        $data['approvedHours'] = [];
		$data['hoursTotal'] = null;
        foreach ($data['hours'] as $hour) {
            if ($hour['origin'] !== 'hours') {
                continue;
            }
            
            if($woOrProjectHours == 'project' && $hour['aanr'] != 0) {
            	continue;
            }
            if (!isset($data['approvedHours'][$hour['pnr']])) {
                $data['timestamps'][$hour['pnr']] = [];
                $data['approvedHours'][$hour['pnr']] = [
                    'names' => $hour['employee_name'],
                    'total' => 0,
                    'break' => 0,
                    'drive' => 0,
                ];
            }
            if ($hour['startDriveTo']) {
                $data['timestamps'][$hour['pnr']][] = strtotime($hour['startDriveTo']);
            }
            if ($hour['endDriveFrom']) {
                $data['timestamps'][$hour['pnr']][] = strtotime($hour['endDriveFrom']);
            }
            $data['timestamps'][$hour['pnr']][] = strtotime($hour['start']);
            $data['timestamps'][$hour['pnr']][] = strtotime($hour['end']);
            $data['approvedHours'][$hour['pnr']]['total']+= (float)$hour['hours'];
            $data['approvedHours'][$hour['pnr']]['break']+= (float)$hour['pause'];
            $data['approvedHours'][$hour['pnr']]['drive']+= (float)$hour['reisestd'];
			// calculate over all total
            $data['hoursTotal'] += $hour['hours'];
        }

        $data['logoUrl'] = $this->getLogoUrl();

        // calculate truck/bus number
		$result = array();
		foreach ($data['resources'] as $rs) {
			$price = $rs['priceInformation'] > 0 ? ' - (' . number_format((float)$rs['priceInformation'], 2, '.', '') . ')' : '';
			$result[] = $rs['text'] . $price;
		}
		$data['truckBusNumber'] = implode(", ", $result);

        // calculate arrival time
		// TODO understand what exactly is happening here
		$startTimes = [];
		foreach ($data['hours'] as $hr) {
			if (is_null($hr['start'])) {
				continue;
			}
			array_push($startTimes, $hr['start']);
		}
		$data['arrivalTime'] = (in_array($data['hours'][0]['ktr'] . '-' . $data['hours'][0]['aanr'], $data['internal_readable_ktrs'])) ? '' : count($startTimes) ? min($startTimes) : '';

        // calculate driver
		$result = array();
		foreach ($data['hours'] as $hr) {
			if ($hr['startDriveTo'] !== null) {
				$result[] = $hr['employee_name'];
			}
		}
		$data['driver'] = implode('&nbsp;&nbsp;&nbsp;&nbsp;', $result);

		return $data;
	}
	
	private function superUnique($array,$key){
        $temp_array = [];
        foreach ($array as &$v) {
            if (!isset($temp_array[$v>$key]))
                $temp_array[$v->$key] =& $v;
        }
        $array = array_values($temp_array);
        return $array;
    }
    
    private function get_pnr_names($pnrs_array) {
		$names = [];
		
		if ($pnrs_array) {
			$result = $this->depr->postquick_help('get_pnr_names',['pnrs'=>$pnrs_array]);	
			foreach ($result as $person) {
				$names[$person['pnr']] = $person['vorname'].' '.$person['name'];
			}
		}
		
		return $names;
	}
    
    private function getKnr ($ktr) {
		$stmt = $this->db->prepare("
			SELECT knr FROM ktr WHERE ktr = :ktr
		");
		$stmt->execute(['ktr' => $ktr]);
		
		$data = $stmt->fetch();
		
		return $data[0];
	}
    
    private function getLogoUrl () {
		$stmt = $this->db->prepare("
			SELECT value FROM settings where name = 'logo'
		");
		$stmt->execute();
		
		$data = $stmt->fetch();
		
		return $data[0];
	}
    
    private function getComments($ktr, $aanr) {
		$stmt = $this->db->prepare("
			SELECT commentText FROM comments WHERE rel_type = 'workingOrders' AND rel_key = :ktr and rel_key2 = :aanr
		");
		$stmt->execute(['ktr' => $ktr, 'aanr'=> $aanr]);
		
		$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
		
		$result = [];
		
		foreach ($data as $k => $comment) {
			$result[$k] = $comment['commentText'];	
		}
		return $result;
	}
    
    private function getWoDate ($ktr, $aanr) {
    	$stmt = $this->db->prepare("
			SELECT plandat FROM aauftrag WHERE ktr = :ktr AND aanr = :aanr ORDER BY plandat DESC
		");
		$stmt->execute(['ktr' => $ktr, 'aanr' => $aanr]);
		
		$data = $stmt->fetch();
		
		return $data[0];
	}
	
	//used when the usage is of working order hours layout
	private function checkWoExistence($ktr, $aanr, $woDate) {
		
		$stmt = $this->db->prepare("
				SELECT TOP 1 * FROM 
			(SELECT ktraanr FROM aauftrag WHERE aauftrag.ktr = :ktr AND aauftrag.aanr = :aanr AND aauftrag.plandat = :woDate 
			 UNION ALL 
			 SELECT ktraanr FROM aauftrag_cyrillic WHERE aauftrag_cyrillic.ktr = :ktr2 AND aauftrag_cyrillic.aanr = :aanr2 AND aauftrag_cyrillic.plandat = :woDate2) as ktraanr
		");
		$stmt->execute(['ktr' => $ktr, 'aanr'=> $aanr, 'woDate'=> $woDate, 'ktr2' => $ktr, 'aanr2'=> $aanr, 'woDate2'=> $woDate]);
		
		$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
		return $data;
	}
	
	//both functions below are used when is a project hours layout
	private function checkProjectExistence($ktr, $projectYear) {
			$stmt = $this->db->prepare("SELECT TOP 1 ktr FROM (SELECT TOP 1 ktr FROM ktr WHERE ktr = :ktr and jahr = :projectYear 
										UNION ALL
										SELECT TOP 1 ktr FROM ktr_cyrillic WHERE ktr_cyrillic.ktr = :ktr2 and ktr_cyrillic.jahr = :projectYear2) as ktr
					");
			
			$stmt->execute(['ktr' => $ktr, 'projectYear' => (int)$projectYear, 'ktr2' => $ktr, 'projectYear2'=> (int)$projectYear]);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
			return $data;
		}
		
		private function getProjectYear($ktr) {
			$stmt = $this->db->prepare("SELECT TOP 1 jahr FROM (SELECT TOP 1 jahr FROM ktr WHERE ktr = :ktr  
										UNION ALL
										SELECT TOP 1 jahr FROM ktr_cyrillic WHERE ktr_cyrillic.ktr = :ktr2) 
										AS jahr
										ORDER BY jahr desc");
			
			$stmt->execute(['ktr' => $ktr, 'ktr2' => $ktr]);
			$data = $stmt->fetchAll(PDO::FETCH_ASSOC);
			return (int)$data[0]['jahr'];
		}
}