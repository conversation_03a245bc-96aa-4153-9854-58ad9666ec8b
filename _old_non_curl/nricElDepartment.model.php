<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';

class C_nricElDepartment {
private $db;
private $documentation;
private $projects;
private $customers;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->customers = new Customers();
	}

	
	public function getData($schemaId,$documentId) {
		
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		if(empty($documentData)) {
			throw new RestException(400, "Not existing schema or document or no data for this document!");
		}
		$templateData = [];
		
		$schemas = $this->documentation->getAllSchemas();
		$currentSchema = array_filter( $schemas, function ($e) use (&$schemaId) {
												  		  return $e['id'] == $schemaId;
												  });
														  
		$templateData['printText'] = reset($currentSchema)['printText'];
		
		foreach ($documentData['children'] as $k=>$v) {
			if($v['type'] == 'checkbox') {
				preg_match('/^\d+./',$v['title'],$matches);
				$templateData['checkboxes'][$matches[0]]['reportedValue'] = $v['reportedValue'];
				$templateData['checkboxes'][$matches[0]]['trimmedTitle'] = trim(preg_replace("/\d+./", "", $v['title'], 1));
			}
			if($v['type'] == 'string') {
				$templateData[$v['title']] = $v['reportedValue'];
			}
		}
		
		if(strtolower($templateData['checkboxes']['16.']['reportedValue']) == 'true') {
			//https://gitlab.baubuddy.de/BauBuddy/scaffoldingAPI/-/issues/11073
			foreach ($templateData['checkboxes'] as $key => &$value) {
				$value['reportedValue'] = true;
			}
		}
		
		return $templateData;
	}
}