<html>

<?php   $templateData = (array)$this->data;  ?>

<?php   function renderCheckBoxes($title, $value, $date, $kommentar) {
           if (strtolower($value) == 'ja' || $value == '1' || $value == 'true' || $value == 'on') {
                echo
                    '<tr>
                        <td>'.$title.'</td>'.
                        '<td class="center" >'.'<img src="'.WEB_ROOT_API.'vendor\printouts\checked2.jpg"'.' alt="checked"  height="25px" width="auto">'.'</td>'.
                        '<td class="center" >'.'<img src="'.WEB_ROOT_API.'vendor\printouts\notChecked2.jpg"'.' alt="Notchecked" height="25px" width="auto">'.'</td>'.
                        '<td class="center">'.$date.'</td>'.
                        '<td>'.$kommentar.'</td>
                    </tr>';

           }
           else  {
                echo
                    '<tr>
                        <td>'.$title.'</td>'.
                        '<td class="center" >'.'<img src="'.WEB_ROOT_API.'vendor\printouts\checked2.jpg"'.' alt="checked"  height="25px" width="auto">'.'</td>'.
                        '<td class="center" >'.'<img src="'.WEB_ROOT_API.'vendor\printouts\notChecked2.jpg"'.' alt="Notchecked" height="25px" width="auto">'.'</td>'.
                        '<td class="center">'.$date.'</td>'.
                        '<td>'.$kommentar.'</td>
                    </tr>';
           }
} ?>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>QualiListe</title>
    </head>
    <body>

        <table class="noBorders">
            <tr colspan="2">
                <td ><h3>Qualifikationen – Übersicht</h3></td>
            </tr>
            <tr><td></td></tr>
            <tr>
                <td>Name Mitarbeiter:</td><td> <?= $templateData['employeeData']['displayName']?></td>
            </tr>
            <tr>
                <td>Personalnummer:</td><td> <?= $templateData['employeeData']['employeeNo']?></td>
            </tr>
            <tr>
                <td>Region:</td><td> <?= $templateData['employeeData']['businessUnit']?></td>
            </tr>
        </table>

        <table class="content">
            <tr class="color">
                <td class="align-top">Auflistung <br />Qualifikationen;</td>
                <td class="center align-top">ja</td>
                <td class="center align-top">nein</td>
                <td class="center align-top">Gültig Bis</td>
                <td class="center align-top">Bemerkungen</td>
            </tr>
<?php
    foreach ($templateData['fields']['checkBoxes'] as $key => $value) {
        // drop the static qualifiziert part
        $keyPart = str_replace('qualifiziert?','',$key);
        renderCheckBoxes($keyPart, $value, $templateData['fields'][$keyPart.'gültig bis'], $templateData['fields'][$keyPart.'Kommentar']);
    }
    ?>

        </table>
    </body>
</html>

<style>
    .noBorders { margin-bottom: 40px; }
    .noBorders td{ padding-top: 20px}
    .content {
        width: 100%;
        border-collapse: collapse;
    }
    .content .align-top {
        vertical-align: top;
    }
    .content tr td{
        width: 20%;
        height: 30px;
        border: 1px solid black;
        vertical-align: bottom;
    }
    .color { background-color: #9d9d9d; }
    .center { text-align: center; vertical-align: middle}
</style>