<?php
require_once ABS_PATH.'Documentation.php';

class C_Neriso {
private $db;
private $documentation;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
	}
	
	public function getLatestProjectData($ktr) {
		// use dummy table if it exists
		require_once ABS_PATH.'class/db.helper.php';
		$dbHelper = new DB_Helper();
		$dummyKtrTable = $dbHelper->dummyKtrTable('');
		// fall back to ktr
		$ktrTable = $dummyKtrTable ? $dummyKtrTable : 'ktr';
		$stmt = $this->db->prepare("
			SELECT TOP 1 RTRIM(ktr.name) AS name, RTRIM(ktr.kname) AS kname FROM $ktrTable ktr 
			WHERE ktr.ktr = :ktr ORDER BY ktr.jahr DESC
		");
		$stmt->execute(['ktr' => $ktr]);
		$data = $stmt->fetch();
		return $data;
	}
	
	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		if($documentData) {
			$projectData = $this->getLatestProjectData($documentData['documentRelKey1']);
			$combinedData = $this->combineData($documentData, $projectData);
			// set default date, in case non of the two schema versions has it set
			if(!$combinedData['Datum des Besuches'] && !$combinedData['Datum des Besuchs'])
			{
				$datetime = new DateTime($documentData['documentCreatedOn'], new DateTimeZone('UTC'));
				$datetime->setTimezone(new DateTimeZone('Europe/Berlin'));
				$combinedData['Datum des Besuches'] = $datetime->format('Y-m-d H:i:s');
			}

			// fetch logo
			$settingsAPI = new Settings();
			$combinedData['logo'] = $settingsAPI->getcommon()['logo'];
			$result = $this->mapData($combinedData);

            $children = $documentData['children'];
            $result['fullWeather'] = [];

            foreach ($children as $child) {
                if ($child["title"] === "Wetter" && $child['type'] === 'combobox-multi') {
                    $result['fullWeather'] = $child["reportedValues"];
                    break;
                }
            }

            return $result;
		} else {
			throw new RestException(400, "No such document id!");
		}
	}
	
	private function combineData ($documentData, $projectData) {
		$templateData = [];
		$filesAPI = new Files();
		foreach ($documentData['children'] as $k=>$v) {
			if(isset($v['reportedValue'])) {
				if($v['type'] == 'photo') {
				    // check if photo position contains only a single picture
					if(count($v['reportedValues']) == 1)
				        $templateData['Foto'] = $v['filePath'];
					// else it contains multiple pictures, we have to append a counter to match the indexing in the NerisoProtocol.template L36ff
					else
                    {
                        // first picture
                        $templateData["Foto"] = $v['filePath'];
                        // subsequent pictures
                        for ($i = 1; $i < count($v['reportedValues']);$i++)
                        {
                            $fileId = $v['reportedValues'][$i];
                            $file = $filesAPI->getSingle($fileId);
                            $photoKey = "Foto ".($i+1);
                            $templateData[$photoKey] = $file['filepath'];
                        }
                    }
				} elseif ($v['type'] == 'signatureField'){
                    $templateData[$v['title']] = $v['filePath'];
                }
                else {
					$templateData[$v['title']] = $v['reportedValue'];
				}
				
			}
			$templateData['projectName'] = $projectData['name'];
			$templateData['kName'] = $projectData['kname'];
		}
		return $templateData;
	}

	/**
	 * Maps positions from the schema to the expected variables of the template
	 * @param $data
	 * @return mixed
	 */
	private function mapData($data)
	{
		// mandatory mapping
		$this->renameKey($data,'Feststellungen/Leistungsstand','Feststellungen',PHP_EOL);
		$this->renameKey($data,'Feststellungen/Leistungsstand','Leistungsstand',PHP_EOL.PHP_EOL);

		// check if extended mapping is requried by looking at that key
		if(!count(array_intersect(array('Temperatur (in °C)', 'Verhältnis', 'Datum des Besuchs', 'Unterschrift Auftraggeber/Vertreter'), array_keys($data))))
			return $data;
		$this->renameKey($data,'Temperatur','Temperatur (in °C)');
		$this->renameKey($data,'Wetter','Verhältnis');
		$this->renameKey($data,'Datum des Besuches','Datum des Besuchs');
		$this->renameKey($data,'Unterschrift Bauherr','Unterschrift Auftraggeber/Vertreter');
		return $data;
	}

	private function renameKey(&$data, $oldKey, $newKey, $seperator = '')
	{
		// apply seperator only if $data[$oldKey] is not an empty string
		$data[$oldKey] .= $data[$oldKey] ? $seperator : '';
		$data[$oldKey] .= $data[$newKey];
		unset($data[$newKey]);
	}
}
