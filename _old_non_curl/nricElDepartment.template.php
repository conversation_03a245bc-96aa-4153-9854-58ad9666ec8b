<html>
<?php $data = $this->data;


function printYesNo($value) {
	if (in_array(strtolower($value), ['ja','on','true']) || $value == '1') {
		 echo 
				 '<td align="center" width="20%">'.'Да'.'</td>';
		} else {
			echo '<td align="center" width="20%">'.'Не'.'</td>';
		}
}


?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>ФБ -РП - 2.55 - 08 - 14</title>
	</head>
	<body>
		<table cellspacing="0" class="main">
			<tbody>
                <tr>
                    <td colspan="3" align="center"><?=$data['printText']; ?></td>
                </tr>
				<tr>
					<td colspan="3" align="center"><b>"Operations that are performed at each shift a review (EP) of the PCSM"</b></td>
				</tr>
				<tr>
					<td align="center">№ по ред</td>
					<td colspan="2" align="center"><b>ОПИСАНИЕ НА ИЗВЪРШЕНИЯ ПРЕГЛЕД</b></td>
				</tr>
				
<?php 							foreach ($data['checkboxes'] as $k=>$v) { ?>
									<tr>
										<td align="center" width="10%"><?=$k;?></td>
										<td align="left" width="70%"><?=$v['trimmedTitle'];?></td>
										<?php printYesNo($v['reportedValue']);?>						
									</tr>
<?php 							} ?>
			</tbody>
		</table>
		<p></p>
		<div class="footer">
			<div align="left" class="comment"><b>Коментар:</b></div>
			<div align="left" class="comment"><?=$data['Коментар'];?></div>
		</div>
	</body>
</html>

<style>
	.main {border: 1px solid black; width:100%;}
	.main tr {border: 1px solid black;}
	.main th {border: 1px solid black;}
	.main td {border: 1px solid black;}
	.comment {border: 1px solid black; width:99%; text-overflow: clip; white-space: normal;  padding-left: 3px;}
	.printText {border: 2px solid black; text-align:center; font-weight:bold; border-bottom:0!important;}
</style>
