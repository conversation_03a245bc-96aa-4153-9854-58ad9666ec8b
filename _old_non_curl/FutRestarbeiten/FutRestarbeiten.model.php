<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';
require_once ABS_PATH.'printouts/printout.helper.php';

class C_FutRestarbeiten {
private $db;
private $documentation;
private $projects;
private $customers;
private $helper;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->customers = new Customers();
		$this->helper = new PrintoutHelper();
	}

	
	public function getData($schemaId,$documentId) {
		
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		
		
		array_unshift($documentData['children'], $main);
		
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		$templateData = [];
		$templateData = $this->helper->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
		
		
		if(empty($documentData)) {
			throw new RestException(400, "Not existing schema or document or no data for this document!");
		}
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		
		if(!empty($projectData)) {
			$customerData = $this->getCustomerData($projectData['knr']);
		}
		
		if($customerData) {
			isset($customerData['name']) ? $templateData['customerName'] = $customerData['name'] : $templateData['customerName'] = ''; 

			$templateData['customerAddress'] = '';
			if(isset($customerData['adresse1'])) {
				$templateData['customerAddress'] .= $customerData['adresse1'].", ";
			}

			if(isset($customerData['plz'])) {
				$templateData['customerAddress'] .= $customerData['plz'].' ';
			}

			$templateData['customerAddress'] .= isset($customerData['ort']) ? $customerData['ort'] : '';

			$templateData['customerAddress'] = trim($templateData['customerAddress']);
		}
		
		if($projectData) {
			$templateData['projectName'] = $projectData['project_name'];
			$templateData['projectDate'] = date('d.m.Y',strtotime($projectData['start_date']));
			$templateData['projectNumber'] = $projectData['ktr'];
		}

		$employeesApi = new v3\Employees();
		$employee =  $employeesApi->getsingle((int) $documentData['author']);
		$templateData['teamName'] = $employee['displayName'];
		return $templateData;
	}
	
	private function getProjectData($projectNo) {
		$result = $this->projects->get($projectNo);
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		
		return end($projects);
	}	
	
	private function getCustomerData($customerNo) {
		$customerData = $this->customers->get($customerNo);
		return $customerData;
	}
	
	
}