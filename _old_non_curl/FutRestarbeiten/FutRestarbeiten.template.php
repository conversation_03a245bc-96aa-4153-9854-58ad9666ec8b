
<html>
<?php $data = $this->data;?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Fut Restarbeiten</title>
	</head>
	<body>
		<div><b><u>TAGESBERICHT</u></b></div>
		<br>
		<table class="team">
			<tr>
				<th align="left" width="20%">Team:</th>
				<th align="left" width="80%"><?=$data['teamName'];?></th>
			</tr>
		</table>
		<br>
		<table class="project">
			<tr>
				<th align="left" width="20%">Datum:</th>
				<th align="left" width="80%"><?=date('d.m.Y',strtotime($data['Tagesbericht v2']['Datum']));?></th>
			</tr>
			<tr>
				<td align="left"><b>Kunde/Name:</b></td>
				<td align="left"><b><?=$data['customerName'];?></b></td>
			</tr>
			<tr>
				<td align="left"><b>Kunde/Ort:</b></td>
				<td align="left"><b><?=$data['customerAddress'];?></b></td>
			</tr>
		</table>
		<br>
		<table class="main">
			<tr>
				<th align="left" width="70%">Ausgeführte Arbeiten:</th>
				<th align="left" width="30%">Zeit</th>
			</tr>
			<?php foreach ($data['Tagesbericht v2']['Ausgeführte Arbeiten'] as $k=>$v) {
                if(!$v)
                    continue;
			?>
    			<tr class="item_row">
					<td><?=$v['Beschreibung'];?></td>
					<td><?=$v['Zeit in Stunden'];?></td>
                </tr>
            <?php  }?>
            <tr>
				<td align="left"><b>Restarbeiten:</b></td>
				<td align="left"></td>
			</tr>
			<?php foreach ($data['Tagesbericht v2']['Restarbeiten'] as $key=>$val) {
                if(!$val)
					continue;
			    ?>
    			<tr class="item_row">
					<td><?=$val['Beschreibung'];?></td>
					<td><?=$val['Zeit in Stunden'];?></td>
                </tr>
            <?php  }?>
		</table>
		
	</body>
</html>



<style>
	.project {width: 100%;}
	.team {width: 100%;}
	.main {width: 100%;}
	.project ,th, td {
        border: 1px solid black;
        border-collapse: collapse;
        table-layout: fixed;
    }
    
    .main ,th, td {
        border: 1px solid black;
        border-collapse: collapse;
        table-layout: fixed;
        word-break: break-all
    }
    
    .team ,th, td {
        border: 1px solid black;
        border-collapse: collapse;
        table-layout: fixed;
    }
    
    body {margin-left:20mm;}
</style>