<!DOCTYPE HTML>

<?php
    $headerData = $this->data;

    $companyLogo = $headerData['companyLogo'];
    $projectDate = $headerData['projectData']['start_date'];
    $projectExternalNo = $headerData['projectData']['externalProjectNo'];
    $projectManager = $headerData['projectData']['technicalContactKey'];
    $workingOrderNumber = $headerData['workingOrderData']['working_order'];
    $workingOrderDescription = $headerData['workingOrderData']['kurz_text'];
?>

<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <title>Media Print</title>
</head>
<body>

    <div class="header">
        <table>
            <tr>
                <td width="66%">
                    <b><?= lang('customer_name') ?>:</b> <?= $headerData['projectData']['customer_kname'] ?>

                    <br><b><?= lang('mail_checklist_documentation_th_externalProjectNo') ?>:</b> <?= $headerData['projectData']['ktr'] ?>

                    <!-- show workingOrder Number if exists -->
                    <?php if ($workingOrderNumber != '') { ?>
                        <br><b><?= lang('working_order_number') ?>:</b> <?= $workingOrderNumber ?>
                    <?php } ?>

                    <br><b><?= lang('project_description') ?>:</b> <?= $headerData['projectData']['project_name'] ?>

                    <!-- show workingOrder Description if exists -->
                    <?php if ($workingOrderDescription != '') { ?>
                        <br><b><?= lang('working_order_description') ?>:</b> <?= $workingOrderDescription ?>
                    <?php } ?>

                    <!-- show projectDate if exists -->
                    <?php if ($projectDate != '') { ?>
                        <br><b><?= lang('date') ?>:</b> <?= date('d.m.Y', strtotime($projectDate)) ?>
                    <?php } ?>

                </td>
                <!-- Logo-->
                <td width="33%" align="right">
					<?php
					if(method_exists('KeramikStein','selectCorrectLogo'))
					{
						echo KeramikStein::selectCorrectLogo($projectExternalNo, $projectManager);
					}
					else
						echo '<img src="'.$companyLogo.'" alt="logo" />';
					?>
                </td>
            </tr>
        </table>
    </div>

</body>
</html>

<style type="text/css" media="screen,print">

    @font-face {
        font-family: seguiemj, sans-serif;
        src: url('<?=ABS_PATH ?>printouts/seguiemj.ttf') format('truetype');
    }

    html, body {
        margin: 0;
    }

    .header {
        margin-left: 15px;
    }

    table td img {
        display: block;
        max-width: 100%;
        max-height: 100%;
        float: right;
        margin-top: 5px;
    }

    table { margin-left: 10px; }

</style>

