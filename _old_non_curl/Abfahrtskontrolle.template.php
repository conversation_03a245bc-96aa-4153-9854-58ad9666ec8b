<html>
<?php $data = $this->data;

function renderCheckboxRow($value) {
	if (strtolower($value) == 'ja' || $value == '1' || strtolower($value) == 'true') {
			echo 
				 '<td align="center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\checked2.jpg"'.'alt="checked"  height="20px" width="auto">'.'</td>'.
				 '<td align="center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\notChecked2.jpg"'.'alt="Notchecked" height="20px" width="auto">'.'</td>';
		} else {
			echo			
				 '<td align="center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\notChecked2.jpg"'.'alt="notChecked" height="20px" width="auto">'.'</td>'.
				 '<td align="center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\checked2.jpg"'.'alt="checked" height="20px" width="auto">'.'</td>';
		}
}


?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Abfahrtskontrolle</title>
	</head>
	<body>
		<table width="100%" cellspacing="0" border="1">
			
				<tr>
					<td colspan="2" class="title" width="75%" align="center">Abfahrtskontrolle für LKW im Lager</td>
					<td width="25%%"><img src="<?=WEB_ROOT_API.'vendor\printouts\schaferLogo.png'?>" alt="companyLogo" width="99%" height="auto"></td>
				</tr>
				<tr>
					<td class="subTitle contentPadding" width="35%">
						<u>Amtl. Kennzeichen:</u><br>
						GZ - <?=$data['Amtl. Kennzeichen'];?>
					</td>
					<td class="subTitle contentPadding" width="25%"> 
						<u>AUhrzeit der Kontrolle:</u><br>
						<?=$data['Uhrzeit der Kontrolle'].' Uhr';?>
					</td>
					<td class="subTitle contentPadding" width="40%">
						<u>Vor- u. Zuname des Fahrers in Druckbuchstaben:</u><br>
						<?=$data['Vor- u. Zuname des Fahrers in Druckbuchstaben'];?>
					</td>
				</tr>
				<tr>
					<td colspan="3" class="checklisteFont contentPadding">
						Gemäß § 36 der Unfallverhütungsvorschrift DGUV Vorschrift 70 bzw. BGV D 27 ist der Fahrzeugführer verpflichtet, vor Beginn jeder
						<br>
						Arbeitsschicht die Wirksamkeit der Betätigungs- und Sicherheitseinrichtungen zu prüfen und während der Arbsitsschicht den Zustand des
						<br>
						Fahrzeuges auf augenfällige Mängei hin zu beobachten.
						<br>
						Der Fahrzeugführer hat festgestellte Mängel dem zuständigen Aufsichtführenden, bei Wechsel des Fahrzeugführers auch dem Ablöser,
						<br>
						mitzuteilen. Bei Mängeln, die die Betriebssicherheit gefährden, hat der Fahrzeugführer den Betrieb einzustellen.
						<br>
						<p align="center" class="checkliste">Checkliste</p>
						<br>
						Vor Abfahrt des LKW aus dem Lager, ist die nachfolgende Checkliste abzuarbeiten, Hierdurch wird die Verpflichtung aus der Vorschrift
						<br>
						eingehalten und der Ist-Zustand des Fahrzeuges, die Ladungssicherung sowie die Vollständigkeit evt). benötigter Behelfsgegenstände
						<br>
						dokumentiert.
					</td>
				</tr>
	</table>
		
		<br>
		
		<div class="container">
			<div class="floatLeft">
				<table class="checkboxes" cellspacing="0">
						<tr>
							<th rowspan="2" align="left" width="70%" class="headers contentPadding">Zu prüfen sind</th>
							<th colspan="2" align="center" width="30%" class="subheaders contentPadding">ln Qrdnung</th>
						</tr>
						<tr class="headers">
							<th align="center" class="contentPadding">ja</th>
							<th align="center" class="contentPadding">nein</th>
						</tr>
						<tr>
							<td class="checkboxPadding">Abblendlicht</td>
							<?=renderCheckboxRow($data['Abblendlicht'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Blinker</td>
							<?=renderCheckboxRow($data['Blinker'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Nebelscheinwerfer</td>
							<?=renderCheckboxRow($data['Nebelscheinwerfer'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Umrissleuchten</td>
							<?=renderCheckboxRow($data['Umrissleuchten'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Reifenprofil (Mindestprofiltiefe)</td>
							<?=renderCheckboxRow($data['Reifenprofil (Mindestprofiltiefe)'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Außenspiegel</td>
							<?=renderCheckboxRow($data['Außenspiegel'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Sichtbarer Ölverlust</td>
							<?=renderCheckboxRow($data['Sichtbarer Ölverlust'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Ladungssicherung</td>
							<?=renderCheckboxRow($data['Ladungssicherung'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Pylone / Warnbake etc</td>
							<?=renderCheckboxRow($data['Pylone / Warnbake etc'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Verbandskasten</td>
							<?=renderCheckboxRow($data['Verbandskasten'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Warnwesten</td>
							<?=renderCheckboxRow($data['Warnwesten'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Tätigkeitsnachweis der vorausge-
								<br>gangenen 28 Kalendertage</td>
							<?=renderCheckboxRow($data['Tätigkeitsnachweis der vorausgegangenen 28 Kalendertage'])?>
						</tr>
				</table>
			</div>
			
			<div class="floatRight">
				<table cellspacing="0" class="checkboxes">
						<tr>
							<th rowspan="2" align="left" width="70%" class="headers contentPadding">Zu prüfen sind</th>
							<th colspan="2" align="center" width="30%" class="subheaders contentPadding">ln Qrdnung</th>
						</tr>
						<tr class="headers">
							<th align="center" class="contentPadding">ja</th>
							<th align="center" class="contentPadding">nein</th>
						</tr>
						<tr>
							<td class="checkboxPadding">Fernlicht</td>
							<?=renderCheckboxRow($data['Fernlicht'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Warnblinker</td>
							<?=renderCheckboxRow($data['Warnblinker'])?>
						</tr>
						<tr>
							<td>Kennzeichenbeleuchtung</td>
							<?=renderCheckboxRow($data['Kennzeichenbeleuchtung'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Luftdruck (Reifen)</td>
							<?=renderCheckboxRow($data['Luftdruck (Reifen)'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Reifenzustand (Beschädigungen)</td>
							<?=renderCheckboxRow($data['Reifenzustand (Beschädigungen)'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Windschutzscheibe</td>
							<?=renderCheckboxRow($data['Windschutzscheibe'])?>
						</tr>
						<tr>
							<td>Anhängerkupplung (nur Hängerbeir.)</td>
							<?=renderCheckboxRow($data['Anhängerkupplung (nur Hängerbeir.)'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Zurrgurte</td>
							<?=renderCheckboxRow($data['Zurrgurte'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Bremstest durchgeführt</td>
							<?=renderCheckboxRow($data['Bremstest durchgeführt'])?>
						</tr>
						<tr>
							<td>Unterlegkeile</td>
							<?=renderCheckboxRow($data['Unterlegkeile'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Unfalischäden</td>
							<?=renderCheckboxRow($data['Unfalischäden'])?>
						</tr>
						<tr>
							<td class="checkboxPadding">Ordnungsgemäße Funktion des
								<br>Kontrollgerätes gewährleistet</td>
							<?=renderCheckboxRow($data['Ordnungsgemäße Funktion des Kontrollgerätes gewährleistet'])?>
						</tr>
				</table>
			</div>
		</div>
		<br>
		<table class="notes" cellspacing="0">
			<tr>
				<td colspan="3" class="contentPadding">
					<p class="orange"><u>Sonstiges/Bemerkungen</u></p><br>
					<?=$data['Sonstiges/Bemerkungen'];?>
				</td>
			</tr>
			<tr>
				<td class="footer contentPadding" colspan="3">
					Die AbfahrtskDntrolle wurde durch den Fahrzeugführer nach den Vorgaben de; 
					Checkliste ordnungsgemäß vor Beginn der Fahrt durchgeführt. Die gemachten <br>
					Angaben entsprechen der Wahrheit.

				</td>
			</tr>
			<tr>
				<td align="center" class="ort contentPadding">
					<?=$data['Ort'];?><br>
					Ort:
				</td>
				<td align="center" class="date contentPadding">
					<?=$data['Datum'];?><br>
					Datum:
				</td>
				<td align="center" class="signature contentPadding">
					<img src="<?=$data['Unterschrift des Fahrzeugführers'];?>" alt="signature" class="signature"><br>
					Unterschrift des Fahrzeugführers:
				</td>
			</tr>
			
		</table>
		<span class="left orange footer">Dieses Dokument ist zur Beweissicherung beim Fuhrparkdisponenten nach der Fahrt abzugeben</span>
		<span class="right orange footer">Seite I von I</span>
		
	</body>
</html>

<style>
	body {font-family: Helvetica;}
	.title {font-size:x-large; font-weight:bold;}
	.subTitle {font-size: small;}
	.footer {font-size: x-small;}
	.checkliste {font-size:xx-large; font-weight:bold; color: orange;}
	.orange {color: orange;}
	.floatLeft { width: 45%; float: left;}
	.floatRight {width: 45%; float: right;}
	.container { overflow: hidden; }
	.headers {font-size: medium; font-weight:bold; color: orange;}
	.subheaders {font-size: small; font-weight:bold; color: orange;}
	.checkboxes tr th {border:1px solid black;}
	.checkboxes {border-collapse:collapse; width:100%;}
	.notes {border:1px solid black;}
	.notes tr {border:1px solid black;}
	.notes tr td {border:1px solid black;}
	.signature {height: auto; width: 40%;}
	.ort {border-right: 1px solid white !important;}
	.signature {border-left: 1px solid white !important;}
	.date {border-right: 1px solid white !important; border-left: 1px solid white !important;}
	.right{
    	float:right;
	}

	.left{
    	float:left;
	}
	
	.checklisteFont {font-size: 14px;}
	.contentPadding {padding-left: 10px; padding-right: 10px; padding-top: 10px; padding-bottom:10px;}
	.checkboxPadding {padding-top: 2px; padding-bottom:2px;}
</style>