<!-- universal template repeated on different layout --!>
<table class="projectData">
			<tr>
				<td></td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td width="20%" align="left"><b>Datum:</b></td>
				<td width="30%" align="left"><?=$data['days'][strtolower(date('l', strtotime($data['woOrProjectData']['start_date'])))]	.', '.date('d.m.Y',strtotime($data['woOrProjectData']['start_date']));?></td>
				<td width="20%"></td>
			</tr>
			<tr>
				<td align="left" valign="top"><b>Monteur:</b></td>
				<td colspan="2"><?=$data['teamMembers'];?></td>
			</tr>
			<tr>
				<td align="left" valign="top"><b>Projekt:</b></td>
				<td align="left"><?=$data['projectName'];?></td>
				<td></td>
			</tr>
			<tr>
				<td align="left"><b>Bauleiter:</b></td>
				<td align="left"><?=$data['projectManager'];?></td>
				<td></td>
			</tr>
			<tr>
				<td colspan="3"><?=str_repeat('&nbsp;', 1);?></td>
			</tr>
		</table>
		<div class="underlined"></div>
		<table class="baustelle">
			<tr>
				<td colspan="4"><?=str_repeat('&nbsp;', 1);?></td>
			</tr>
			<tr>
				<td width="20%" align="left" valign="top"><b>Baustelle:</b></td>
				<td width="30%" align="left" valign="top">
					<b><?=$data['projectAddress'];?></b>
				</td>
				<td width="20%" valign="top" align="right"><b>Auftraggeber:</b></td>
				<td width="30%" align="left" valign="top">
					<b><?=$data['customerData'][0]['name'];?></b><br>
                    <b><?=$data['customerData'][0]['address'];?></b>
				</td>
			</tr>
			<tr>
				<td></td>
				<td></td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td align="left" valign="top"><b>Ansprechpartner:</b></td>
				<td width="30%" align="left" valign="top"><?=$data['partners'][0]['personalTitle'].' '.$data['partners'][0]['firstName'].' '.$data['partners'][0]['lastName'];?></td>
				<td width="20%" valign="top" align="right"></td>
				<td width="30%" align="left" valign="top"><?=$data['partners'][1]['personalTitle'].' '.$data['partners'][1]['firstName'].' '.$data['partners'][1]['lastName'];?></td>
			</tr>
			<tr>
				<td align="left" valign="top"><b>Rufnummer:</b></td>
				<td width="30%" align="left" valign="top"><?=$data['partners'][0]['phone'];?></td>
				<td width="20%" valign="top" align="right"></td>
				<td width="30%" align="left" valign="top"><?=$data['partners'][1]['phone'];?></td>
			</tr>
			<tr>
				<td align="left" valign="top"><b>Mobil:</b></td>
				<td width="30%" align="left" valign="top"><?=$data['partners'][0]['cell'];?></td>
				<td width="20%" valign="top" align="right"></td>
				<td width="30%" align="left" valign="top"><?=$data['partners'][1]['cell'];?></td>
			</tr>
		</table>
		
		<br>

<style>
	body {font-family:Helvetica;}
	 .projectData, .baustelle {width:100%;}
	 .projectData, .baustelle {font-size:17px;}
	 .underlined {border-bottom:2px solid black;}
	 .headerDiv {background-color:lightgrey; padding: 5px;}
	  ul { margin-left: 10px; padding: 10px;}
	 .smallFont {font-size:11px;}
	 .noBullet {list-style:none;}
	 .checkboxesCombobox, .images, .signatures, .weather {width:100%; border-collapse:collapse;}
	 .title {font-size:large;}
	 .checkboxRow {border-bottom: 1px solid gray;}
	 .checkboxesCombobox, .images, .signatures, .weather {
    	page-break-inside: avoid;
	}
</style>

