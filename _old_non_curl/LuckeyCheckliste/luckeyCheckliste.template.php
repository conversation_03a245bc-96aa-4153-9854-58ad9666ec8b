<html>
<?php $data = $this->data;

function renderCheckBox($title, $value)
{
    if (strtolower($value) == 'ja' || (int)$value == 1 || $value == 'true' || $value == 'on') {
        echo
            '<tr class="checkboxRow">
                        <td align="left" valign="top" width="60%">'.$title.'</td>'.
            '<td align="right" valign="top" width="40%">' . '<img src="' . WEB_ROOT_API . 'vendor\printouts\LuckeyServiceWartungsarbeiten\checkedCropped.png"' . ' alt="checked"  height="25px" width="auto">' . '</td>';
    } else {
        echo
            '<tr class="checkboxRow">
                        <td align="left" valign="top" width="60%">'.$title.'</td>'.
            '<td align="right" valign="top" width="40%">' . '<img src="' . WEB_ROOT_API . 'vendor\printouts\LuckeyServiceWartungsarbeiten\uncheckedCropped.png"' . ' alt="Notchecked" height="25px" width="auto">' . '</td></tr>';
    }
}

function generateRow($title, $stringValue)
{
    echo '<tr><td align="left" valign="top" width="60%">' . $title . '</td><td align="left" valign="top" width="40%">' . $stringValue . '</td></tr>';
}

/**
 * Checks if a file exists based on an image url. Returns true if the file exists and false if it doesn't.
 * @param string $url The url of the image
 * @return bool
 */
function UR_exists($url)
{
    $headers = get_headers($url);
    return stripos($headers[0], "200 OK") ? true : false;
}

function generateImage($title, $filePath)
{
    if (UR_exists($filePath) == true) {
        echo '<tr><td align="left" valign="top" width="60%">' . $title . '</td><td align="right" valign="top" width="40%">' . '<img src="' . $filePath . '"' . ' alt="Foto" height="auto" width="75%">' . '</td></tr>';
    }
}

function showIfTicked($positions)
{
    $results = array(true, 1, "1", "on");
    if (count(array_intersect($positions, $results)) > 0) {
        return true;
    } else {
        return false;
    }
}

?>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Luckey Checkliste</title>
</head>
<body>
<?php require_once ABS_PATH . 'printouts/LuckeyCheckliste/luckeyReusableData.template.php'; ?>
<?php if (isset($data['woOrProjectData']['langtext']) && !empty($data['woOrProjectData']['langtext'])) { ?>
    <table class="checkboxesCombobox">
        <tr class="underlinedTop">
            <td colspan="2"><?= str_repeat('&nbsp;', 1); ?></td>
        </tr>
        <tr>
            <td align="left" colspan="2" class="title"><b>Auftragsbeschreibung</b></td>
        </tr>
        <tr>
            <td><?= $data['woOrProjectData']['langtext']; ?></td
        </tr>
    </table>
<?php } ?>
<br>
<?php 	if (!empty($data['Bautagesbericht v2']['Wetter'])) { ?>
    <table class="weather pageBreak">
        <tr>
            <td align="left" colspan="2" class="title"><b>Wetter</b></td>
        </tr>
        <?php generateRow('Temperatur (in °C)', $data['Bautagesbericht v2']['Wetter']['Temperatur (in °C)']); ?>
        <?php generateRow('Verhältnis', $data['Bautagesbericht v2']['Wetter']['Verhältnis']); ?>
        <?php generateRow('Wetterbedingte Einschränkung', $data['Bautagesbericht v2']['Wetter']['wetterbedingte Einschränkung']); ?>
    </table>
<?php } ?>
<br>
<table class="checkboxesCombobox">
    <?php if (!empty($data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Leistungsstand zur Auftragsbeschreibung'])) { ?>
        <tr>
            <td colspan="2" align="left" class="title"><b>Fassade / Fenster / Türen</b></td>
        </tr>
        <?php generateRow('Leistungsstand zur Auftragsbeschreibung', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Leistungsstand zur Auftragsbeschreibung']); ?>
    <?php } ?>
    <?php if (isPositionTicked($data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']) == true) { ?>
    <tr>
        <td colspan="2" align="left" class="subtitle"><b>Ausgeführte Arbeiten</b></td>
    </tr>
    <?php renderCheckbox('Gestellt und ausgerichtet', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Gestellt und ausgerichtet']); ?>
    <?php renderCheckbox('Befestigt', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Befestigt']); ?>
    <?php renderCheckbox('Automatiktürbereich (Verkabelung, Leibungsbleche, Schwelle)', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Automatiktürbereich (Verkabelung, Leibungsbleche, Schwelle)']); ?>
    <?php renderCheckbox('Verkabelung (falls erforderlich)', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Verkabelung (falls erforderlich)']); ?>
    <?php renderCheckbox('Anschlüsse', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Anschlüsse']); ?>
    <?php renderCheckbox('Verglasung', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Verglasung']); ?>
    <?php renderCheckbox('Dichtungen', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Dichtungen']); ?>
    <?php renderCheckbox('Druck- u. Deckschalen', $data['Bautagesbericht v2']['Fassade / Fenster / Türen']['Ausgeführte Arbeiten']['Druck- u. Deckschalen']); ?>
</table>
<?php } ?>
<br>
<table class="checkboxesCombobox">
    <?php if (!empty($data['Bautagesbericht v2']['Wintergarten / Überdachung']['Leistungsstand zur Auftragsbeschreibung'])) { ?>
        <tr>
            <td colspan="2" align="left" class="title"><b>Wintergarten / Überdachung</b></td>
        </tr>
        <?php generateRow('Leistungsstand zur Auftragsbeschreibung', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Leistungsstand zur Auftragsbeschreibung']); ?>
    <?php } ?>
    <?php if (isPositionTicked($data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']) == true) { ?>
        <tr>
            <td colspan="2" align="left" class="subtitle"><b>Ausgeführte Arbeiten</b></td>
        </tr>
        <?php renderCheckbox('Befestigt', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Wandanschluss']); ?>
        <?php renderCheckbox('Stützen, Rinnenprofil', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Stützen, Rinnenprofil']); ?>
        <?php renderCheckbox('Dachsparren', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Dachsparren']); ?>
        <?php renderCheckbox('Dach verglast', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Dach verglast']); ?>
        <?php renderCheckbox('Anschlüsse', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Anschlüsse']); ?>
        <?php renderCheckbox('Dach abgedichtet', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Dach abgedichtet']); ?>
        <?php renderCheckbox('Elemente montiert', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Elemente montiert']); ?>
        <?php renderCheckbox('Elemente verglast', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Elemente verglast']); ?>
        <?php renderCheckbox('Fußpunkt abgeklebt / gedämmt', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Fußpunkt abgeklebt / gedämmt']); ?>
        <?php renderCheckbox('Bleche / Kantungen angebracht', $data['Bautagesbericht v2']['Wintergarten / Überdachung']['Ausgeführte Arbeiten']['Bleche / Kantungen angebracht']); ?>
    <?php } ?>
</table>
<br>
<table class="checkboxesCombobox">
    <?php if (!empty($data['Bautagesbericht v2']['Markise / Beschattung']['Leistungsstand zur Auftragsbeschreibung'])) { ?>
        <tr>
            <td colspan="2" align="left" class="title"><b>Markise / Beschattung</b></td>
        </tr>
        <?php generateRow('Leistungsstand zur Auftragsbeschreibung', $data['Bautagesbericht v2']['Markise / Beschattung']['Leistungsstand zur Auftragsbeschreibung']); ?>
    <?php } ?>
    <?php if (isPositionTicked($data['Bautagesbericht v2']['Markise / Beschattung']['Ausgeführte Arbeiten']) == true) { ?>
        <tr>
            <td colspan="2" align="left" class="title"><b>Markise / Beschattung</b></td>
        </tr>
        <tr>
            <td colspan="2" align="left" class="title"><b>Ausgeführte Arbeiten</b></td>
        </tr>
        <?php renderCheckbox('Beschattung montiert', $data['Bautagesbericht v2']['Markise / Beschattung']['Ausgeführte Arbeiten']['Beschattung montiert']); ?>
        <?php renderCheckbox('Beschattung eingelernt', $data['Bautagesbericht v2']['Markise / Beschattung']['Ausgeführte Arbeiten']['Beschattung eingelernt']); ?>
    <?php } ?>
    <br>
</table>
<br><br>
<?php 	if (!empty($data['Bautagesbericht v2']['Glasgestelle freimelden'])) { ?>
    <div><b>Glasgestelle freimelden: </b><?= $data['Bautagesbericht v2']['Glasgestelle freimelden']; ?></div>
    <br>
<?php } ?>

<?php 	if (!empty($data['Bautagesbericht v2']['Bemerkungen / Hinweise'])) { ?>
    <div><b>Bemerkungen / Ergebnisse: </b><?= $data['Bautagesbericht v2']['Bemerkungen / Hinweise']; ?></div>
    <br>
<?php } ?>
<?php 		if (!empty($data['Bautagesbericht v2']['Fotos']['Fotos'])) { ?>
    <table class="images pageBreak">
        <?php generateImage('Fotos', $data['Bautagesbericht v2']['Fotos']['Fotos']); ?>
    </table>
<?php		} ?>
<br>
Die Arbeiten sind vollständig und vertragsgemäß erbracht worden.
<?php if (!empty($data['Bautagesbericht v2']['Abnahme'])) { ?>
    <br><br><br>
    <table class="signatures pageBreak" cellspacing="0">
        <tr>
            <th width="22%" align="left" valign="top" class="highlight"><?= date('d.m.Y',
                    strtotime(
                        $data['Bautagesbericht v2']['Abnahme']['Einsatzdatum'] ?
                            $data['Bautagesbericht v2']['Abnahme']['Einsatzdatum'] :
                            $data['woOrProjectData']['start_date']
                    )
                ); ?></th>
            <th width="4%"></th>
            <th width="33%" align="left" valign="top">
                <?php if (UR_exists($data['Bautagesbericht v2']['Abnahme']['Unterschrift Auftraggeber / Vertreter']) == true) { ?>
                    <img src="<?= $data['Bautagesbericht v2']['Abnahme']['Unterschrift Auftraggeber / Vertreter'] ?>"
                         alt="signature" class="logo" width="75%" height="auto">
                <?php } ?>
            </th>
            <th width="4%"></th>
            <th width="33%" align="left" valign="top">
                <?php if (UR_exists($data['Bautagesbericht v2']['Abnahme']['Unterschrift Monteur']) == true) { ?>
                    <img src="<?= $data['Bautagesbericht v2']['Abnahme']['Unterschrift Monteur'] ?>" alt="signature"
                         class="logo" width="75%" height="auto">
                <?php } ?>
            </th>
            <th width="4%"></th>
            <!--<th width="22%" align="left" valign="top">
							<?php if (UR_exists($data['Bautagesbericht v2']['Abnahme']['Unterschrift Bauleiter']) == true) { ?>
								<img src="<?=$data['Bautagesbericht v2']['Abnahme']['Unterschrift Bauleiter']?>" alt="signature" class="logo" width="75%" height="auto">
							<?php } ?>
						</th>-->
        </tr>
        <tr>
            <td class="smallFont highlight" align="left" valign="top"><b>Datum</b></td>
            <td></td>
            <td class="smallFont highlight" align="left" valign="top"><b>Unterschrift Auftraggeber / Vertreter</b></td>
            <td></td>
            <td class="smallFont highlight" align="left" valign="top"><b>Unterschrift Monteur</b></td>
            <!--<td></td>
            <td class="smallFont" align="left" valign="top"><b>Unterschrift Bauleiter</b></td>-->
        </tr>
        <tr>
            <td class="smallFont" align="left" valign="top"></td>
            <td></td>
            <td class="smallFont" align="left" valign="top">
                <b><?= '(' . $data['Bautagesbericht v2']['Abnahme']['Name Auftraggeber / Vertreter'] . ')'; ?></b></td>
            <td></td>
            <td class="smallFont" align="left" valign="top">
                <b><?= '(' . $data['Bautagesbericht v2']['Abnahme']['Name Monteur'] . ')'; ?></b></td>
            <!--<td></td>
						<td class="smallFont" align="left" valign="top"><b><?='('.$data['Bautagesbericht v2']['Abnahme']['Name Bauleiter'].')';?></b></td>-->
        </tr>
    </table>
<?php 		} ?>
</body>
</html>



<style>
    body {
        font-family: Helvetica;
    }

    .projectData, .baustelle {
        width: 100%;
    }

    .projectData, .baustelle {
        font-size: 17px;
    }

    .underlined {
        border-bottom: 2px solid black;
    }

    .headerDiv {
        background-color: lightgrey;
        padding: 5px;
    }

    ul {
        margin-left: 10px;
        padding: 10px;
    }

    .smallFont {
        font-size: 11px;
    }

    .noBullet {
        list-style: none;
    }

    .checkboxesCombobox, .images, .signatures, .weather {
        width: 100%;
        border-collapse: collapse;
    }

    .title {
        font-size: large;
    }

    .subtitle {
        font-size: large;
        color: gray
    }

    .checkboxRow {
        border-bottom: 1px solid gray;
    }

    .greyDevider {
        border-top: 1px solid grey;
    }

    .underlinedTop {
        border-top: 2px solid black;

        .pageBreak {
            page-break-inside: avoid;
            position: absolute;
        }

        @media print {
            .pageBreak {
                page-break-inside: avoid;
            }
        }


</style>