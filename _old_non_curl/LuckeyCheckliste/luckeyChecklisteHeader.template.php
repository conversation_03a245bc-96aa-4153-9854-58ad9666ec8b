<!DOCTYPE HTML>
<?php 
$headerData = $this->data;
	
	/**
	 * Checks if the company logo file exists based on an image url from the DB. Returns true if the file exists and false if it doesn't.
	 * @param string $url The url of the image
	 * @return bool
	 */
	function UR_logo_exists($url){ 
 		  $headers=get_headers($url); 
   		  return stripos($headers[0],"200 OK")?true:false; 
	} 
?> 
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Luckey header</title>
	</head>
	<body>
		<table class="headerTable">
			<tbody>
				<tr>
					<td width="60%" class="font" valign="top">
						<b>
							<?=trim(str_replace('v2', '', $headerData['schemaTitle']));?> <?=$headerData['woOrProjectData']['externalWorkingOrderNo']?>
					</b>
					</td>
					<td rowspan="2" width="40%" valign="top">
						<?php if (UR_logo_exists($headerData['logo']) == true) { ?>
							<img src="<?=$headerData['logo']?>" alt="companyLogo" width="99%" height="auto">
						<?php } ?>
					</td>
				</tr>
				<tr>
					<td></td>
					<td></td>
				</tr>
				<tr>
				<!-- external project number eventually--!>
					<td colspan="2" align="left">
						zu Projekt <?=$headerData['woOrProjectData']['externalProjectNo']?>
                    </td>
				</tr>
			</tbody>
		</table>
		<div class="underlined"></div>
		<br>
	</body>
</html>

<style>
	.headerTable {width:100%;}
	.font {font-size:22px;}
	 body {font-family:Helvetica;}
	.underlined {border-bottom:2px solid black;}
</style>