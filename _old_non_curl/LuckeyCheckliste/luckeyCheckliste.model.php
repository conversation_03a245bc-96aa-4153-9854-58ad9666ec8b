<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'WorkingOrders.php';
require_once ABS_PATH.'Addresses.php';
require_once ABS_PATH.'v3/Employees.php';
require_once ABS_PATH.'Team.php';
require_once ABS_PATH.'Partners.php';
require_once ABS_PATH.'Settings.php';
require_once ABS_PATH.'printouts/printout.helper.php';


class C_Luckey {
private $db;
private $documentation;
private $projects;
private $workingOrders;
private $addresses;
private $employees;
private $teams;
private $partners;
private $settings;
private $helper;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
		$this->workingOrders = new WorkingOrders();
		$this->addresses = new AddressesDB();
		$this->employees = new \v3\Employees();
		$this->teams = new Team();
		$this->partners = new Partners();
		$this->settings = new Settings();
		$this->helper = new PrintoutHelper();
	}

	public function getData($schemaId,$documentId) {
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];
		
		array_unshift($documentData['children'], $main);
		
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');
		
		$templateData = $this->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
		$projectData = $this->getProjectData($documentData['documentRelKey1']);
		
		if(isset($documentData['documentRelKey2']) && !empty( $documentData['documentRelKey2'])) {
			if ($target = getTunnelingTarget('GET', 'workingorders/select/'.$documentData['documentRelKey1'].'/'.$documentData['documentRelKey2'])) {
				$woOrProjectData = getDataFromTunnel($target, true);
				
				//return $woOrProjectData;
				// for Luckey it can happen that multiple workingOrders are returned here
				// due to the fact that one workingOrders from their database is split if they exceed one day of planning
				// however they hold the same information, just the date differs
				// in this case we have to take the first object of the array
				if(!$woOrProjectData['externalWorkingOrderNo'])
					$woOrProjectData = $woOrProjectData[0];
			}
			else {
				$woOrProjectData = $this->workingOrders->getselect($documentData['documentRelKey1'], $documentData['documentRelKey2']);
			}
			$templateData['teamMembers'] = $this->getEmployees($woOrProjectData['staff_preplanned']);
			$woOrProjectData['start_date'] = $woOrProjectData['date'];
			unset($woOrProjectData['date']);
			$templateData['woOrProjectData'] = $woOrProjectData;
		} else {
			if ($target = getTunnelingTarget('GET', 'projects/select/'.$documentData['documentRelKey1'].'/'.date('Y'))) {
				$woOrProjectData = getDataFromTunnel($target, true);
			}
			else {
				$woOrProjectData = $this->getProjectData($documentData['documentRelKey1']);
			}
			echo 'woOrProjectData:';
			$templateData['teamMembers'] = $this->getEmployees($woOrProjectData['staff_preplanned']);
			echo 'teamMembers:';
			$templateData['woOrProjectData'] = $woOrProjectData;
		}
		
		if(!empty($projectData)) {
			$customerData = $this->getCustomerData($projectData['knr']);
		}
		
		if($customerData) {
			$templateData['customerData'] = $customerData;
			
			isset($customerData['name']) ? $templateData['customerName'] = $customerData['name'] : $templateData['customerName'] = ''; 

			$templateData['customerAddress'] = '';
			if(isset($customerData['address'])) {
				$templateData['customerAddress'] .= $customerData['address'].", ";
			}

			if(isset($customerData['postCode'])) {
				$templateData['customerAddress'] .= $customerData['postCode'].' ';
			}

			$templateData['customerAddress'] .= isset($customerData['city']) ? $customerData['city'] : '';

			$templateData['customerAddress'] = trim($templateData['customerAddress']);
		}
		
		if($projectData) {
			$templateData['projectName'] = $projectData['project_name'];
			$templateData['projectDate'] = date('d.m.Y',strtotime($projectData['start_date']));
			$templateData['projectNumber'] = $projectData['ktr'];
			if(!is_array($projectData['technicalContactDisplayName']) && $projectData['technicalContactDisplayName'])
				$templateData['projectManager'] = $projectData['technicalContactDisplayName'];
			else
				$templateData['projectManager'] = '';
			$templateData['projectAddress'] = $projectData['customer_address'] ? $projectData['customer_address'] : '';
		}
		
		$templateData['partners'] = $this->partners->get($documentData['documentRelKey1']);
		$templateData['logo'] =  $this->settings->getcommon()['logo'];
		$templateData['schemaTitle'] = $documentData['title'];
		
		//use the german names of the days, but not changing the locale setings 
		$templateData['days'] = ['monday' => 'Montag', 'tuesday' => 'Dienstag', 'wednesday' => 'Mittwoch','thursday' => 'Donnerstag', 
								 'friday' => 'Freitag', 'saturday' => 'Samstag', 'sunday' => 'Sonntag'];
		
		
		return $templateData;
	}
	
		private function getDeepValueFromField($keyFieldname, $valueFieldName, $node, $childrenFieldName) {		
		if ($node[$valueFieldName]) {
				return [$node[$keyFieldname] => $node[$valueFieldName]];
		} elseif($node[$childrenFieldName]) {
			foreach ( $node[$childrenFieldName] as $i =>$child) {
				$paths[$node[$keyFieldname]][$child[$keyFieldname]] = $this->getDeepValueFromField($keyFieldname, $valueFieldName, $this->setPathAsValue($child), $childrenFieldName)[$child[$keyFieldname]];
			}	
			return $paths;
		}
		return null;
	}
	
	private function setPathAsValue($child) {
		if(isset($child['filePath'])) {
			$child['reportedValue'] = $child['filePath'];
		}
		return $child;
	}
	
	private function getProjectData($projectNo) {
		if ($target = getTunnelingTarget('GET', 'projects/select/'.$projectNo.'/'.date('Y'))) {
			$result = getDataFromTunnel($target, true);
			// we already have the desired object here, no need for further post-processing
			return $result;
		}
		else {
			$result = $this->projects->get($projectNo);
		}
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		
		return end($projects);
	}	
	
	private function getCustomerData($customerNo) {
		if ($target = getTunnelingTarget('GET', 'addresses/'.$customerNo)) {
			$customerData = getDataFromTunnel($target, true);
		}
		else {
			$customerData = $this->addresses->get_single($customerNo);
		}
		return $customerData;
	}	
	
	private function getEmployees($staffPreplanned) {
		$personalNumbers = implode(',', $staffPreplanned);

		if ($target = getTunnelingTarget('GET', 'v3/employees?employeeNumbers='.$personalNumbers)) {
			$data = getDataFromTunnel($target, true);
		}
		else {
			$data = $this->employees->get($personalNumbers);
		}
		
		$result = '';
		
		foreach ($data as $k=>$v) {
			$result.= $v['firstName'].' '.$v['lastName'].', ';
		}
		$result = rtrim($result, ', ');
		return $result;
	}
}