<!DOCTYPE HTML>
<?php $headerData = $this->data?>
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Protokoll für Begehungsbericht</title>
        <!-- taken from https://wkhtmltopdf.org/usage/wkhtmltopdf.txt -->
        <script>
            function subst() {
                var vars = {};
                var x = document.location.search.substring(1).split('&');
                
                for (var i in x) {
                    if (x.hasOwnProperty(i)) {
                        var z = x[i].split('=', 2);
                        vars[z[0]] = decodeURI(z[1]);
                    }
                    vars[z[0]] = unescape(z[1]);
                }
                
                var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
                for (var css_class in css_selector_classes) {
                    
                    if (css_selector_classes.hasOwnProperty(css_class)) {
                        var element = document.getElementsByClassName(css_selector_classes[css_class]);
                        for (var j = 0; j < element.length; ++j) {
                            element[j].textContent = vars[css_selector_classes[css_class]];
                        }
                    }
                     var y = document.getElementsByClassName(css_selector_classes[css_class]);
                     for (var jj = 0; jj < y.length; ++jj) y[jj].textContent = vars[css_selector_classes[css_class]];
                     
                     if (vars['page'] == 1) {
						  var el = document.getElementById("pager");
						  el.classList.add("pager-hidden");
    				}
                }      
            }
        </script>
	</head>
	<body onload="subst()" style="margin-left: 2.5cm" id="body">
	<div class="headerWrapper pager" id="pager" align="center">
		<table class="header">
			<tr>
				<td colspan="2"><b><?=$data['projectName'];?><?php echo !empty($data['projectName']) ?',' :'';?></b><?=$data['combinedAddress'];?></td>
			</tr>
			<tr>
				<td class="leftAlign">Sicherheits- und Gesundheitsschutzkoordination – Begehungsbericht </td>
				<td class="rightAlign">Seite <span class="page"></span></td>
			</tr>
		</table>
	</div>
	<br>
	</body>
</html>

<style>
.header {font-size:12px;}
.pager.pager-hidden {
  display: none;
  position: relative;	
}


</style>