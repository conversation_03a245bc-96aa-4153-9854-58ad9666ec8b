<html>
<?php $data = $this->data;

		function renderCheckBox($title, $value) {
           if (strtolower($value) == 'ja' || $value == '1' || $value == 'true' || $value == 'on') {
                echo
                    '<tr>
                        <td align="left" valign="top">'.$title.'</td>'.
                        '<td align="left" valign="top">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\LuckeyServiceWartungsarbeiten\checkedCropped.png"'.' alt="checked"  height="25px" width="auto">'.'</td>';
           }
           else  {
                echo
                    '<tr>
                        <td align="left" valign="top">'.$title.'</td>'.
                        '<td align="left" valign="top">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\LuckeyServiceWartungsarbeiten\uncheckedCropped.png"'.' alt="Notchecked" height="25px" width="auto">'.'</td></tr>';
           }
		}
		
		function generateRow ($title, $stringValue){
			echo '<tr><td align="left" valign="top">'.$title.'</td><td align="left" valign="top">'.$stringValue.'</td></tr>';
		} 
		
		function generateImage ($title, $filePath){
			if(file_exists($filePath)) {
				echo '<tr><td align="left" valign="top">'.$title.'</td><td align="right" valign="top">'.'<img src="'.$filePath.'"'.' alt="Foto" height="auto" width="75%">'.'</td></tr>';
			}
		} 
?>

	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Luckey Service Wartungsarbeiten</title>
	</head>
	<body>
	<?php require_once ABS_PATH.'printouts/LuckeyCheckliste/luckeyReusableData.template.php'; ?>
		<?php if(isset($data['woOrProjectData']['langtext']) && !empty($data['woOrProjectData']['langtext'])) { ?>
			<table class="checkboxesCombobox pageBreak">
				<tr class="greyDevider"><td align="left" colspan="2" class="title"><b>Auftragsbeschreibung</b></td></tr>
				<tr><td><?=$data['woOrProjectData']['langtext'];?></td</tr>
			</table>
		<?php } ?>
		<br>
		<div class="headerDiv" align="left"><b>Folgende Arbeiten müssen noch erledigt werden:</b></div>
		<div class="smallFont"><b>ggf. Rückseite nutzen!</b></div>
		<br>
		<div class="headerDiv" align="left"><b>Materialverbrauch bzw. Vorhaltung</b></div>
		<div class="smallFont"><b>ggf. zusätzliche Lieferscheine beachten</b></div>
		<br>
<?php 		if (!empty($data['Service- / Wartungsarbeiten'])) { ?>
				<table class="data pageBreak">
						<?php renderCheckbox('Mangel/Problem behoben',$data['Service- / Wartungsarbeiten']['Mangel/Problem behoben']); ?>
						<?php renderCheckbox('Funktionstest erfolgreich durchgeführt',$data['Service- / Wartungsarbeiten']['Funktionstest erfolgreich durchgeführt']); ?>
						<?php generateRow('Hinweise', $data['Service- / Wartungsarbeiten']['Hinweise']); ?>
						<?php generateImage('Foto vorher', $data['Service- / Wartungsarbeiten']['Foto vorher']); ?>
						<?php generateImage('Foto nachher', $data['Service- / Wartungsarbeiten']['Foto nachher']); ?>
				</table>
<?php 		}?>
		<br>
<?php 		if (isset($data['Service- / Wartungsarbeiten']['Zeit Anfahrt']) || isset($data['Service- / Wartungsarbeiten']['Arbeitsbeginn']) || isset($data['Service- / Wartungsarbeiten']['Arbeitsende']) || isset($data['Service- / Wartungsarbeiten']['Zeit Abfahrt'])) { ?>
				<table class="times pageBreak">
					<tr>
						<td width="20%" align="left"><b>Zeitangaben:</b></td>
						<td width="20%" align="left" class="smallFont"><?='Anfahrt: '.$data['Service- / Wartungsarbeiten']['Zeit Anfahrt'].' Std.';?></td>
						<td width="20%" align="left" class="smallFont"><?='Arbeitsbeginn: '.$data['Service- / Wartungsarbeiten']['Arbeitsbeginn'].' Std.';?></td>
						<td width="20%" align="left" class="smallFont"><?='Arbeitsende: '.$data['Service- / Wartungsarbeiten']['Arbeitsende'].' Std.';?></td>
						<td width="20%" align="left" class="smallFont"><?='Zeit Abfahrt: '.$data['Service- / Wartungsarbeiten']['Zeit Abfahrt'].' Std.';?></td>
					</tr>
				</table>
<?php 		} ?>
<?php 		if (!empty($data['Service- / Wartungsarbeiten']['Abnahme'])) { ?>		
				<br><br><br>
				<table class="signatures pageBreak" cellspacing="0">
					<tr>
						<th width="22%" align="left" valign="top"><?= date('d.m.Y',strtotime($data['Service- / Wartungsarbeiten']['Abnahme']['Einsatzdatum']));?></th>
						<th width="4%"></th>
						<th width="22%" align="left">
						<?php if(file_exists($data['Service- / Wartungsarbeiten']['Abnahme']['Unterschrift Auftraggeber / Vertreter'])) { ?>
							<img src="<?=$data['Service- / Wartungsarbeiten']['Abnahme']['Unterschrift Auftraggeber / Vertreter']?>" alt="signature" class="logo" width="75%" height="auto">
						<?php } ?>
						</th>
						<th width="4%"></th>
						<th width="22%" align="left">
							<?php if(file_exists($data['Service- / Wartungsarbeiten']['Abnahme']['Unterschrift Monteur'])) { ?>
								<img src="<?=$data['Service- / Wartungsarbeiten']['Abnahme']['Unterschrift Monteur']?>" alt="signature" class="logo" width="75%" height="auto">
							<?php } ?>
						</th>
						<th width="4%"></th>
						<th width="22%" align="left">
							<?php if(file_exists($data['Service- / Wartungsarbeiten']['Abnahme']['Unterschrift Bauleiter'])) { ?>
								<img src="<?=$data['Service- / Wartungsarbeiten']['Abnahme']['Unterschrift Bauleiter']?>" alt="signature" class="logo" width="75%" height="auto">
							<?php } ?>	
						</th>
					</tr>
					<tr>
						<td class="smallFont" align="left" valign="top"><b>Datum</b></td>
						<td></td>
						<td class="smallFont" align="left" valign="top"><b>Unterschrift Auftraggeber / Vertreter</b></td>
						<td></td>
						<td class="smallFont" align="left" valign="top"><b>Unterschrift Monteur</b></td>
						<td></td>
						<td class="smallFont" align="left" valign="top"><b>Unterschrift Bauleiter</b></td>
					</tr>
						<tr>
						<td class="smallFont" align="left" valign="top"></td>
						<td></td>
						<td class="smallFont" align="left" valign="top"><b><?='('.$data['Service- / Wartungsarbeiten']['Abnahme']['Name Auftraggeber / Vertreter'].')';?></b></td>
						<td></td>
						<td class="smallFont" align="left" valign="top"><b><?='('.$data['Service- / Wartungsarbeiten']['Abnahme']['Name Monteur'].')';?></b></td>
						<td></td>
						<td class="smallFont" align="left" valign="top"><b><?='('.$data['Service- / Wartungsarbeiten']['Abnahme']['Name Bauleiter'].')';?></b></td>
					</tr>	
				</table>
<?php 		} ?>
	</body>
</html>



<style>
	  body {font-family:Helvetica;}
	 .projectData, .baustelle, .data {width:100%;}
	 .projectData, .baustelle {font-size:17px;}
	 .underlined {border-bottom:2px solid black;}
	 .headerDiv {background-color:lightgrey; padding: 5px;}
	  ul { margin-left: 10px; padding: 10px;}
	 .smallFont {font-size:11px;}
	 .noBullet {list-style:none;}
	.signatures {border-collapse:collapse; width:100%;}
	.times {border-collapse:collapse; width:100%;}
	.greyDevider {border-top: 1px solid grey;}
	.pageBreak { page-break-inside: avoid; position:absolute; }
</style>