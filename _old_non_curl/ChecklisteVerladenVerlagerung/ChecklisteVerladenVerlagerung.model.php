<?php
require_once ABS_PATH.'Documentation.php';

class C_ChecklisteVerladenVerlagerung  {

    private $documentation;
    private $db;

    public function __construct() {
        $this->documentation = new DocumentationDocument();
        try {
            $this->db = CommonData::getDbConnection();
        } catch (Exception $e) {}
    }

    public function getData($schemaId, $documentId) {

        $data = $this->documentation->getById($schemaId, $documentId);
        $modelData = [];

        if ($data[0]['children']) {
            foreach ($data[0]['children'] as $key => $value) {
                if ($value['type'] !== 'headline') {
                    if ($value['type'] == 'checkbox') {
                        $modelData['checkboxes'][$value['title']] = $value['reportedValue'];
                    } else if ($value['type'] == 'string') {
                        $modelData['kommentar'] = $value['reportedValue'];
                    }
                }
            }
        }
        $modelData['row-title'] = $data[0]['title'];

        return $modelData;
    }
}