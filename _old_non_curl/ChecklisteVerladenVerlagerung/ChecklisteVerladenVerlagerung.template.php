<!DOCTYPE html>
<html>

<?php
    $templateData = $this->data;
    require_once 'vendor/config/includes.php';

    function renderCheckboxRow($value) {
        if (strtolower($value) == 'ja' || $value == '1' || strtolower($value) == 'true') {
            echo
                '<td style="width: 6%; padding: 5px; text-align: center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\checked2.jpg"' .' alt="checked"  height="20px" width="auto">'.'</td>'.
                '<td style="width: 6%; padding: 5px; text-align: center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\notChecked2.jpg"'.' alt="Notchecked" height="20px" width="auto">'.'</td>';
        } else {
            echo
                '<td style="width: 6%; padding: 5px; text-align: center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\notChecked2.jpg"'.' alt="notChecked" height="20px" width="auto">'.'</td>'.
                '<td style="width: 6%; padding: 5px; text-align: center">'.'<img src="'.WEB_ROOT_API.'vendor\printouts\checked2.jpg"'.' alt="checked" height="20px" width="auto">'.'</td>';
        }
    }

?>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>Checkliste Verladen/Verlagerung</title>
    </head>
    <body>
        <table class="headerTable">
            <tr>
                <td class="leftHeader">
                    Adresse der Baustelle:
                </td>
                <td class="middleHeader">
                    <div class="top-title">Verantwortlicher vor Ort (AvO)</div>
                    <div class="bottom-title">Vor- und Nachname in Druckbuchstaben</div>
                </td>
                <td class="logoHeader"><img src="<?=WEB_ROOT_API.'vendor\printouts\SchaeferGeruestbauLogo.png'?>" alt="SchaeferGeruestbauLogo" class="companyLogo"></td>
            </tr>
        </table>
        <br>
        <table class="checkboxes">
            <tr class="row">
                <td><b><?= $templateData['row-title']?></b></td>
                <td class="checkBox-title">Ja</td>
                <td class="checkBox-title">Nein</td>
            </tr>
            <?php foreach ($templateData['checkboxes'] as $key => $value) { ?>
            <tr>
                 <td style="padding-left: 2px"><?= $key ?></td>
                 <?php renderCheckboxRow($value)?>
            </tr>
            <?php } ?>
            <tr>
                <td colspan="3" style="height: 30px">
                    <?= $templateData['kommentar']?>
                </td>
            </tr>
        </table>
    </body>
</html>

<style>
    .headerTable ,th, td {
        width: 100%;
        border: 1px solid black;
        border-collapse: collapse;
        padding: 0;
        mso-cellspacing: 0;
    }
    .headerTable .leftHeader {
        width: 40%;
        height: 100px;
        text-align:left;
        vertical-align: top;
        text-decoration: underline;
        padding-left: 10px;
    }
    .headerTable .middleHeader { width: 35%; text-align:center; vertical-align: top; }
    .top-title { text-decoration: underline; margin-bottom: 30px; }
    .bottom-title {
        padding-top: 30px;
        margin-top: 10px;
        font-size: 15px;
    }
    .headerTable .logoHeader { width: 25%; text-align: center; }
    .companyLogo { width: 100%; height: 70%; }
    .checkboxes ,th, td {
        width: 100%;
        border: 1px solid black;
        border-collapse: collapse;
        padding: 0;
        mso-cellspacing: 0;
    }
    .row { height: 30px; }
    .row .checkBox-title {
        padding: 8px;
        text-align: center;
        font-weight: bold;
    }
    .row img {
        width: 30px;
        height: 30px;
        margin-top: 2px ;
    }
</style>