<html>
<?php $data = $this->data;?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Auftragszettel</title>
	</head>
	<body>
		<table class="header">
			<tr>
				<th align="left" width="13%" class="smallFont">HIT GmbH</th>
				<th align="left" width="13%" class="smallFont"> Tel.</th>
				<th align="left" width="20%" class="smallFont"> <EMAIL></th>
				<td width="14%"></td>
				<th rowspan="2" align="right" width="40%"><img src="<?=WEB_ROOT_API.'vendor\printouts\AuftragszettelLogo.jpg'?>" alt="HitSanitarLogo" class="logo"></th>
			</tr>
			<tr>
				<td align="left" class="smallFont"><b>Teutonenstr. 4 59067 Hamm</b></td>
				<td align="left" class="smallFont"><b>+49 (0)2381 9975400</b></td>
				<td align="left" class="smallFont"><b>www.hit-hamm.de</b></td>
				<td></td>
				<td></td>
			</tr>
		</table>
		<br>
		<table class="customer">
			<tr>
				<th colspan="2" align="left" class="biggerFont">Auftrag</th>
				<th></th>				
			</tr>
			<tr>
				<td><?=$data['woData']['ktraanr']?></td>
			</tr>
			<tr>
				<td align="left" class="grey bordered"><b>Kundendaten</b></td>
				<td></td>
			</tr>
			<tr>
				<td>Kundennummer: <?=$data['customerData']['customerNumber']?></td>
				<td><b>Arbeitsort:</b> <?=$data['projectAddress']?></td>
			</tr>
			<tr>
				<td>Name: <?=$data['customerData']['name'];?></td>
				<td></td>
			</tr>
			<tr>
                <td>Adresse: <?=$data['customerData']['fullAddress'];?></td>
				<td><b>Termin:</b></td>
			</tr>
			<tr>
				<td></td>
				<td>Datum: <?php echo $data['woData']['date'] ? date('d.m.Y',strtotime($data['woData']['date'])) : '';?></td>
			</tr>
			<tr>
				<td></td>
				<td>Uhrzeit: <?=$data['woData']['pvonzeit']?></td>
			</tr>
		</table>
		<br>
		<table>
			<tr class="grey"> 
				<th class="bordered rightBorder" align="left">Informationen zum Ansprechpartner</th>
				<th class="bordered" align="left">Autrag erteilt durch:</th>
			</tr>
			<tr>
				<td>Name: <?=$data['customerData']['displayName'];?></td>
				<td>Name: <?=$data['customerData']['name']?></td>
			</tr>
			<tr>
				<td>Telefon: <?=$data['customerData']['telefon'];?></td>
				<td>Telefon: <?=$data['customerData']['phoneNumber']?></td>
			</tr>
			<tr>
				<td>Mobil: <?=$data['customerData']['mobile'];?></td>
				<td>Angenommen am: <?php echo $data['woData']['eingangsDatum'] ? date('d.m.Y',strtotime($data['woData']['eingangsDatum'])) : '';?></td>
			</tr>
			<tr class="bordered grey"> 
				<td align="left" class="rightBorder"><b>Auftrag</b></td>
				<td align="left" class="bordered"><b>Projekt</b></td>
			</tr>
			<tr>
				<td><?= $data['woData']['auswtext'] ?></td>
				<td></td>
			</tr>
		</table>
		<br>
		<div class="bordered grey">
			<b>Fahrzeugpauschale:</b> 
			<input type="checkbox" name="yes" value="ja" <?php if (count($data['drivers'])!== 0) echo "checked='checked'"; ?>> <b>Ja</b>
			<input type="checkbox" name="no" value="nein" <?php if (count($data['drivers'])== 0) echo "checked='checked'"; ?>> <b>Nein</b>
			<?=  str_repeat('&nbsp;', 150); ?>
			<b>Anfahrt:*</b> <b> km</b>
		</div>
		<br>
		<div class="bordered grey"><b>Diagnose / Fehlerbeschreibung</b></div>
		<br>
		<div><?=$data['notes'];?></div>
		<br>
		<div class="bordered grey" align="left">
			<b>Arbeitsbericht / Benötigte Artikel</b>
		</div>
		<br>
		<table>
			<tr>
				<th width="15%" class="bordered noBold" align="left">Pos. Nr.</th>
				<th width="55%" class="bordered noBold" align="left">Bezeichnung</th>
				<th width="15%" class="bordered noBold" align="left">Menge</th>
				<th width="15%" class="bordered noBold" align="left">LS-Nr.</th>
			</tr>
<?php 		foreach ($data['oversizes'] as $k=>$v) { ?>
				<tr>
					<td class="bordered"><?=$v->itemNo?></td>
					<td class="bordered"><?=$v->description?></td>
					<td class="bordered"><?=$v->quantity?></td>
					<td class="bordered"><?=$v->serviceItemNo?></td>
				</tr>
<?php 		} ?>
		</table>
		<br>
			<div class="bordered grey">
				<b>Auftrag abgeschlossen</b> <input type="checkbox" name="auftrag" value="auftrag">
				<?=  str_repeat('&nbsp;', 50); ?>
				<b>Folgearbeiten erforderlich</b> 
				<input type="checkbox" name="folgearbeiten" value="folgearbeiten" 
				<?php if (strtolower($data['folgearbeiten']) == 'ja' || strtolower($data['folgearbeiten']) == true) echo "checked='checked'";?>>
			</div>
		<br>
		<table width="100%" class="pageBreak">
			<tr>
				<td width="50%" class="smallFont">Wir möchten Ihnen eine kompetente und fachgerechte Leistung bieten.</td>
				<td width="25%"></td>
				<td width="25%" align="right" class="lightGrey smallFont"></td>
			</tr>
			<tr>
				<td class="smallFont">Unser Mitarbeiter muss diesen Vordruck an Ort und Stelle komplett ausfüllen</td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td class="smallFont">und Ihnen eine Kopie aushändigen. Überprüfen Sie die Eintragungen und</td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td class="smallFont">die Rechnungsanschrift damit eine ordnungsgemäße Abrechnung erfolgen kann.</td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td class="smallFont">Mit Ihrer Unterschrift bestätigen Sie die Richtigkeit aller Eintragungen und nehmen die Leistung ab.</td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td></td>
				<td align="center"><img src="<?=$data['signatures']['monteurSignaturePath'];?>" alt="monteurSignature" class="signature"><br>Unterschrift Monteur</td>
				<td align="center"><img src="<?=$data['signatures']['customerSignaturePath'];?>" alt="customerSignature" class="signature"><br>Unterschrift Kunde</td>
			</tr>
		</table class="new-page">
		<?php
			if($data['Anlagendaten'])
			{
				echo "<hr><br></br>Anlagendaten<br><table>";
				foreach ($data['Anlagendaten'] as $datum) {
					if($datum->type == 'headline')
						echo "<tr><td class='bordered grey' colspan='3'>".$datum->title."</td></tr>";
					else
						echo "<tr><td class='bordered noBold'>".$datum->title."</td><td class='bordered noBold'>".$datum->reportedValue."</td><td class='bordered noBold'>".$datum->newReportedValue."</td>";
				}
				echo "</table>";
			}
		?>
    	<hr>
	</body>
</html>



<style>
	 table {width:100%;  border-collapse: collapse;}
	.bordered {border:1px solid black;}
	.grey {background-color:darkgrey;}
	.biggerFont {font-size:xx-large;}
	.rightBorder {border-right-style:hidden;}
	.noBold {font-weight:normal;}
	.logo {width:80%; height:auto;}
	.signature {height: auto; width: 100%;}
	.lightGrey {color:lightgray;}
	.smallFont {font-size:smaller;}
	html, body {
		height:100%; !important; 
		margin: 0 !important; 
		padding: 0 !important;
		<?php (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') ? 'overflow: hidden;' : ''; ?>
		font-family: Helvetica;
	 }
	 
	 .pageBreak {
		  page-break-inside: avoid;
	 }

	 @media print {
		 .new-page {
			 page-break-before: always;
		 }
	 }
</style>