<html>
<?php $data = (array)$this->data;
		
   function isChecked($string) {
		if (strtolower($string) == 'ja' || $string == '1' || $string == 'true') {
			return "checked='checked'";
		} else {
			return '';
		}
	}

		$einsatzVonTechnik = ['Aufzug 500 kg','Aufzug 1500 kg','Geda Seilwinde','Böcker-Aufzug','Kran (bauseits)','Teleskoplader','klein ','groß'];
		$zugang = ['Innenliegender Leitergang','Podesttreppe', 'Leiter'];
		$breitenKlasse = ['W 06', 'W 09'];
		$ankergrund = ['WDVS', 'Ziegel', 'Beton', 'Holz'];


?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Montageanweisung</title>
	</head>
	<body>
		<table class="headerTable" cellspacing="0" width="100%">
			<tr class="SmallFont">
				<td width="20%"><b>Verfasser</b></td>
				<td width="20%"><b>Prozessverantwortlicher</b></td>
				<td width="20%"><b>Freigegeben durch</b></td>
				<td rowspan="4" width="40%"><img src="<?=WEB_ROOT_API.'vendor\printouts\schaferLogo.png'?>" alt="companyLogo" width="99%" height="auto"></td>
			</tr>
			<tr class="SmallFont">
				<td>Claudia Brand</td>
				<td>Martin Wenrich </td>
				<td>Martin Schäfer</td>
			</tr>
			<tr class="SmallFont">
				<td><b>Veröffentlichungsdatum</b></td>
				<td><b>Nachweis Ablage </b></td>
				<td><b>Revision</b></td>
			</tr>
			<tr class="SmallFont">
				<td>01.10.2019</td>
				<td>Kundenakte</td>
				<td><?=$data['documentId']?></td>
			</tr>
		</table>
		<br><br><br>
		<table class="tableTitle">
			<tr>
				<td class="title">Montageanweisung</td>
				<td>durch Bauleiter:</td>
				<td><u>Bitte auswählen</u></td>
				<td align="right">Datum: <?=date('d.m.y',strtotime($data['documentDate']));?></td>
			</tr>
			<tr>
				<td>Gerüstsystem:</td>
				<td>
					<input type="checkbox" name="PERI" value="PERI" <?php if ($data['nonParents']['Gerüstsystem'] == 'PERI') echo "checked='checked'"; ?>>PERI
				</td>
				<td></td>
				<td>
					<input type="checkbox" name="Layher" value="Layher" <?php if ($data['nonParents']['Gerüstsystem'] == 'Layher') echo "checked='checked'"; ?>>Layher
				</td>
				<td></td>
			</tr>
		</table>
		
		<table class="mainContent">
			<tr>
				<td colspan="2"><b><?='Aufbautermin: '.date('d.m.Y',strtotime($data['nonParents']['Aufbautermin']));?></b></td>
				<td><b><?='Geplanter Abbautermin: '.date('d.m.Y',strtotime($data['nonParents']['Geplanter Abbautermin']));?></b></td>
				<td><b><?='Baustellenentfernung: '.$data['nonParents']['Baustellenentfernung'];?></b></td>
			</tr>
			<tr>
				<td colspan="2"><b><?='Mann-Stunden / Kalkulationszeit: '.$data['nonParents']['Mann-Stunden / Kalkulationszeit'];?></b></td>
				<td><b><?='Anfahrt: '.$data['nonParents']['Anfahrt'];?></b></td>
				<td><b><?='ø Trageweg: '.$data['nonParents']['ø Trageweg'];?></b></td>
			</tr>
			<tr>
				<td colspan="4">
					<b>Fahrzeug:</b>
					<input type="checkbox" name="Kran-LKW 18 m" value="Kran-LKW 18 m" <?php if ($data['nonParents']['Fahrzeug'] == 'Kran-LKW 18m') echo "checked='checked'"; ?>>Kran-LKW 18 m
					<input type="checkbox" name="System-LKW 19 m " value="System-LKW 19 m " <?php if ($data['nonParents']['Fahrzeug'] == 'System-LKW 19m ') echo "checked='checked'"; ?>>System-LKW 19 m 
					<input type="checkbox" name="System-LKW 14 m " value="System-LKW 14 m " <?php if ($data['nonParents']['Fahrzeug'] == 'System-LKW 14m ') echo "checked='checked'"; ?>>System-LKW 14 m 
					<input type="checkbox" name="Sattel" value="Sattel" <?php if ($data['nonParents']['Fahrzeug'] == 'Sattel') echo "checked='checked'"; ?>>Sattel
					<input type="checkbox" name="Solo 10 m" value="Solo 10 m" <?php if ($data['nonParents']['Fahrzeug'] == 'Solo 10m') echo "checked='checked'"; ?>>Solo 10 m
					<input type="checkbox" name="Pritsche" value="Pritsche" <?php if ($data['nonParents']['Fahrzeug'] == 'Pritsche') echo "checked='checked'"; ?>>Pritsche
					<input type="checkbox" name="3,5 t" value="3,5 t" <?php if ($data['nonParents']['Fahrzeug'] == '3,5t') echo "checked='checked'"; ?>>3,5 t
				</td>
			</tr>
			<tr class="grey">
				<td colspan="4"><b>Baustelle / Bauvorhaben:</b></td>
			</tr>
			<tr>
				<td colspan="4"><b>Anschrift (Straße, PLZ, Ort): <?=$data['nonParents']['Anschrift (Straße, PLZ, Ort)']?></b></td>
			</tr>
			<tr>
				<td colspan="4"><b>Auftraggeber: : <?=$data['nonParents']['Auftraggeber']?></b></td>
			</tr>
			<tr>
			    <td colspan="2" class="borderBottom"><b>Ansprechpartner Baustelle: <?=$data['parents']['Ansprechpartner Baustelle']['Name']?></b></td>
				<td><b>Tel: <?=$data['parents']['Ansprechpartner Baustelle']['Tel']?></b></td>
				<td></td>
			</tr>
			<tr>
				<td colspan="2" class="borderUp"></td>
				<td><b>Mail: <?=$data['parents']['Ansprechpartner Baustelle']['Mail']?></b></td>
				<td></td>
			</tr>
			<tr>
			    <td colspan="2" class="borderBottom"><b>Weisungsbefugt Projektleiter: <?=$data['parents']['Weisungsbefugt Projektleiter']['Name']?></b></td>
				<td><b>Tel: <?=$data['parents']['Weisungsbefugt Projektleiter']['Tel']?></b></td>
				<td></td>
			</tr>
			<tr>
				<td colspan="2" class="borderUp"></td>
				<td><b>Mail:</b> <b><?=$data['parents']['Weisungsbefugt Projektleiter']['Mail']?></b></td>
				<td></td>
			</tr>
			
			<tr>
			    <td colspan="2" class="borderBottom"><b>SiGeKo: <?=$data['parents']['SiGeKo']['Name']?></b></td>
				<td><b>Tel: <?=$data['parents']['SiGeKo']['Tel']?></b></td>
				<td></td>
			</tr>
			<tr>
				<td colspan="2" class="borderUp"></td>
				<td><b>Mail: <?=$data['parents']['SiGeKo']['Mail']?></b></td>
				<td></td>
			</tr>
			<tr class="grey">
				<td colspan="4"><b>Gebäudeeigenschaften:</b></td>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
						<tr>
							<td width="15%"><b>Bauzustand:</b></td>
							<td width="16%"><input type="checkbox" name="Bestandsgebäude" value="Bestandsgebäude" <?php if ($data['nonParents']['Bauzustand'] == 'Bestandsgebäude') echo "checked='checked'"; ?>>Bestandsgebäude </td>
							<td width="16%"><input type="checkbox" name="Neubau/Rohbau" value="Neubau/Rohbau" <?php if ($data['nonParents']['Bauzustand'] == 'Neubau/Rohbau') echo "checked='checked'"; ?>>Neubau/Rohbau </td>
							<td width="16%"></td>
							<td width="16%"></td>
							<td width="16%"></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
						<tr>
							<td width="15%"><b>Ankergrund:</b></td>
							<td width="16%"><input type="checkbox" name="WDVS" value="WDVS" <?php if ($data['nonParents']['Ankergrund'] == 'WDVS') echo "checked='checked'"; ?>>WDVS cm </td>
							<td width="16%"><input type="checkbox" name="Ziegel" value="Ziegel" <?php if ($data['nonParents']['Ankergrund'] == 'Ziegel') echo "checked='checked'"; ?>>Ziegel</td>
							<td width="16%"><input type="checkbox" name="Beton" value="Beton" <?php if ($data['nonParents']['Ankergrund'] == 'Beton') echo "checked='checked'"; ?>>Beton</td>
							<td width="16%"><input type="checkbox" name="Holz" value="Holz" <?php if ($data['nonParents']['Ankergrund'] == 'Holz') echo "checked='checked'"; ?>>Holz</td>
							<td width="16%"><input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if (!in_array($data['nonParents']['Ankergrund'],$ankergrund)) echo "checked='checked'"; ?>><?=$data['nonParents']['Ankergrund']?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td><b>Dachüberstand ALT:</b></td>
				<td><input type="checkbox" name="Giebel" value="Giebel" <?=isChecked($data['Dachüberstand ALT']['Giebel']['value'])?>>Giebel <?=$data['Dachüberstand ALT']['Giebel']['cm']?> cm </td>
				<td><input type="checkbox" name="Traufe" value="Traufe" <?=isChecked($data['Dachüberstand ALT']['Traufe']['value'])?>>Traufe <?=$data['Dachüberstand ALT']['Traufe']['cm']?> cm </td>
				<td></td>
			</tr>
			<tr>
				<td><b>Dachüberstände NEU:</b></td>
				<td><input type="checkbox" name="Giebel" value="Giebel" <?=isChecked($data['Dachüberstand NEU']['Giebel']['value'])?>>Giebel <?=$data['Dachüberstand NEU']['Giebel']['cm']?> cm </td>
				<td><input type="checkbox" name="Traufe" value="Traufe" <?=isChecked($data['Dachüberstand ALT']['Traufe']['value'])?>>Traufe <?=$data['Dachüberstand NEU']['Traufe']['cm']?>  cm </td>
				<td></td>
			</tr>
			<tr>
				<td><b>Besonderheit:</b></td>
				<td><input type="checkbox" name="Elektrische Freileitung" value="Elektrische Freileitung " <?php if ($data['nonParents']['Besonderheit'] == 'Elektrische Freileitung') echo "checked='checked'"; ?>>Elektrische Freileitung</td>
				<td><input type="checkbox" name="Öffentlicher Verkehrsraum (Sondernutzung)" value="Öffentlicher Verkehrsraum (Sondernutzung)" <?php if ($data['nonParents']['Besonderheit'] == 'Öffentlicher Verkehrsraum (Sondernutzung)') echo "checked='checked'"; ?>>Öffentlicher Verkehrsraum (Sondernutzung)</td>
				<td><input type="checkbox" name="Passantenschutz" value="Passantenschutz" <?php if ($data['nonParents']['Besonderheit'] == 'Passantenschutz') echo "checked='checked'"; ?>>Passantenschutz </td>
			</tr>
			<tr>
				<td></td>
				<td><input type="checkbox" name="Statik " value="Statik " <?php if ($data['nonParents']['Besonderheit'] == 'Statik ') echo "checked='checked'"; ?>>Statik</td>
				<td><input type="checkbox" name="bauseits WC" value="bauseits WC" <?php if ($data['nonParents']['Besonderheit'] == 'bauseits WC') echo "checked='checked'"; ?>>bauseits WC (Sondernutzung)</td>
				<td><input type="checkbox" name="Gefahrstoffe" value="Gefahrstoffe" <?php if ($data['nonParents']['Besonderheit'] == 'Gefahrstoffe') echo "checked='checked'"; ?>>Gefahrstoffe </td>
			</tr>
			<tr>
				<td></td>
				<td><input type="checkbox" name="tragfähige feste Fläche " value="tragfähige feste Fläche " <?php if ($data['nonParents']['Aufstandsfläche'] == 'Statik ') echo "checked='checked'"; ?>>tragfähige feste Fläche </td>
				<td colspan="2"><input type="checkbox" name="lastverteilende" value="lastverteilende" <?php if ($data['nonParents']['Aufstandsfläche'] == 'lastverteilende Unterlage (Maßnahmen dazu, unten aufführen)') echo "checked='checked'"; ?>> lastverteilende Unterlage (Maßnahmen dazu, unten aufführen)</td>
				
			</tr>
			<tr>
				<td colspan="4">Lastverteilende Maßnahme: <?=$data['nonParents']['lastverteilende Maßnahme']?></td>
			</tr>
			<tr class="grey">
				<td colspan="4"><b>Gerüsteigenschaften:</b></td>
			</tr>
			<tr>
				<td><b>Gerüstart:</b></td>
				<td><input type="checkbox" name="Hängegerüst" value="Hängegerüst" <?php if ($data['nonParents']['Gerüstart'] == 'Hängegerüst') echo "checked='checked'"; ?>>Hängegerüst</td>
				<td><input type="checkbox" name="Raumgerüst/Flächengerüst" value="Raumgerüst/Flächengerüst" <?php if ($data['nonParents']['Gerüstart'] == 'Raumgerüst/Flächengerüst') echo "checked='checked'"; ?>>Raumgerüst/Flächengerüst</td>
				<td><input type="checkbox" name="Treppenturm / Gerüsttreppe" value="Treppenturm / Gerüsttreppe" <?php if ($data['nonParents']['Gerüstart'] == 'Treppenturm / Gerüsttreppe') echo "checked='checked'"; ?>>Treppenturm / Gerüsttreppe</td>
			</tr>
			<tr>
				<td></td>
				<td><input type="checkbox" name="Fahrgerüst" value="Fahrgerüst" <?php if ($data['nonParents']['Gerüstart'] == 'Fahrgerüst') echo "checked='checked'"; ?>>Fahrgerüst</td>
				<td><input type="checkbox" name="Dachfanggerüst (Dacharbeiten)" value="Dachfanggerüst (Dacharbeiten)" <?php if ($data['nonParents']['Gerüstart'] == 'Dachfanggerüst (Dacharbeiten)') echo "checked='checked'"; ?>>Dachfanggerüst (Dacharbeiten)</td>
				<td><input type="checkbox" name="Fassadengerüst (Maler / Putzer)" value="Fassadengerüst (Maler / Putzer)" <?php if ($data['nonParents']['Gerüstart'] == 'Fassadengerüst (Maler / Putzer)') echo "checked='checked'"; ?>>Fassadengerüst (Maler / Putzer)</td>
			</tr>
			<tr>
				<td></td>
				<td><input type="checkbox" name="Sandwichfassade" value="Sandwichfassade" <?php if ($data['nonParents']['Gerüstart'] == 'Sandwichfassade') echo "checked='checked'"; ?>>Sandwichfassade</td>
				<td><input type="checkbox" name="Schutzdach" value="Schutzdach" <?php if ($data['nonParents']['Gerüstart'] == 'Schutzdach') echo "checked='checked'"; ?>>Schutzdach</td>
				<td><input type="checkbox" name="Sondergerüst" value="Sondergerüst" <?php if ($data['nonParents']['Gerüstart'] == 'Sondergerüst') echo "checked='checked'"; ?>>Sondergerüst</td>
			</tr>
			<tr>
				<td colspan="2" width="">
					<table>
						<tr>
							<td><b>Lastklasse (gleichmäßig verteilte Last):</b></td>
						</tr>
						<tr>
							<td>
								<input type="checkbox" name="1" value="1" <?php if ($data['nonParents']['Lastklasse'] == '1 (0,75 kN/m²)') echo "checked='checked'"; ?>>1 (0,75 kN/m²)  
								<input type="checkbox" name="2" value="2" <?php if ($data['nonParents']['Lastklasse'] == '2 (1,50 kN/m²)') echo "checked='checked'"; ?>>2 (1,50 kN/m²)  
								<input type="checkbox" name="3" value="3" <?php if ($data['nonParents']['Lastklasse'] == '3 (2,00 kN/m²)') echo "checked='checked'"; ?>>3 (2,00 kN/m²)  	
							</td>
						</tr>
						<tr>
							<td>
								<input type="checkbox" name="4" value="4" <?php if ($data['nonParents']['Lastklasse'] == '4 (3,00 kN/m²)') echo "checked='checked'"; ?>>4 (3,00 kN/m²)  
								<input type="checkbox" name="5" value="5" <?php if ($data['nonParents']['Lastklasse'] == '5 (4,50 kN/m²)') echo "checked='checked'"; ?>>5 (4,50 kN/m²)  
								<input type="checkbox" name="6" value="6" <?php if ($data['nonParents']['Lastklasse'] == '6 (6,00 kN/m²)') echo "checked='checked'"; ?>>6 (6,00 kN/m²)
							</td>  
						</tr>
					</table>
				</td>
				<td width="25%">
					<table>
						<tr>
							<td colspan="2"><b>Breitenklasse:</b></td>
						</tr>
						<tr>
							<td><input type="checkbox" name="W 06" value="W 06" <?php if ($data['nonParents']['Breitenklasse'] == 'W 06') echo "checked='checked'"; ?>>W 06</td>
							<td> <input type="checkbox" name="W 09" value="W 09" <?php if ($data['nonParents']['Breitenklasse'] == 'W 09') echo "checked='checked'"; ?>>W 09</td>
						</tr>
						<tr>
							<td colspan="2"> <input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if (!in_array($data['nonParents']['Breitenklasse'], $breitenKlasse)) echo "checked='checked'"; ?>><?=$data['nonParents']['Breitenklasse']?></td>
						</tr>
					</table>					
				</td>
				<td>
					<table>
						<tr>
							<td><b>Zugang nur für Gerüstersteller:</b></td>
							<td></td>
						</tr>
						<tr>
							<td>
								<input type="checkbox" name="Innenliegender" value="Innenliegender" <?php if ($data['nonParents']['Zugang nur für Gerüstersteller'] == 'Innenliegender Leitergang') echo "checked='checked'"; ?>>Innenliegender Leitergang
							</td>
							<td>
								<input type="checkbox" name="Podesttreppe" value="Podesttreppe" <?php if ($data['nonParents']['Zugang nur für Gerüstersteller'] == 'Podesttreppe') echo "checked='checked'"; ?>>Podesttreppe
							</td>
						</tr>
						<tr>
							<td><input type="checkbox" name="Leiter" value="Leiter" <?php if ($data['nonParents']['Zugang nur für Gerüstersteller'] == 'Leiter') echo "checked='checked'"; ?>>Leiter</td>
							<td><input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if (!in_array($data['nonParents']['Zugang nur für Gerüstersteller'],$zugang)) echo "checked='checked'"; ?>><?=$data['nonParents']['Zugang nur für Gerüstersteller']?></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
			    <td colspan="3">
			    	<b>Horizontaler Abstand vom Belag zum Gebäude (Wandabstand) in m: <?=$data['nonParents']['Horizontaler Abstand vom Belag zum Gebäude (Wandabstand) in m']?> m.</b>
					<br>
					<b>Horizontaler Abstand von der Traufe zum Seitenschutz/ Schutzwand: <?=$data['nonParents']['Horizontaler Abstand von der Traufe zum Seitenschutz/ Schutzwand']?> m.</b>
					<br>
					<b>Vertikaler Abstand von der <u>Bodenplatte</u> Erdgeschoss zum ersten Belag: <?=$data['nonParents']['Vertikaler Abstand von der Bodenplatte Erdgeschoss zum ersten Belag']?> m.</b>
					<br>
					<b>Vertikaler Abstand von der Traufe zum obersten Belag: <?=$data['nonParents']['Vertikaler Abstand von der Traufe zum obersten Belag']?> m.</b>
			    </td>
				<td>
					<table>
						<tr>
							<td colspan="2">
								<b>Bekleidung / Anbauteile: <?=$data['nonParents']['Bekleidung / Anbauteile']?></b>			
							</td>
						</tr>
						<tr>
							<td><input type="checkbox" name="Sandstrahlnetz" value="Sandstrahlnetz" <?php if ($data['nonParents']['Bekleidung / Anbauteile'] == 'Sandstrahlnetz') echo "checked='checked'"; ?>>Sandstrahlnetz</td>
							<td><input type="checkbox" name="Rupfennetz" value="Rupfennetz" <?php if ($data['nonParents']['Bekleidung / Anbauteile'] == 'Rupfennetz') echo "checked='checked'"; ?>>Rupfennetz</td>
						</tr>
						<tr>
							<td><input type="checkbox" name="(Gitter)Plane" value="Sandstrahlnetz" <?php if ($data['nonParents']['Bekleidung / Anbauteile'] == '(Gitter)Plane') echo "checked='checked'"; ?>>(Gitter)Plane</td>
							<td><input type="checkbox" name="Plane als B1" value="Plane als B1" <?php if ($data['nonParents']['Bekleidung / Anbauteile'] == 'Plane als B1') echo "checked='checked'"; ?>>Plane als B1</td>
						</tr>
						<tr>
							<td><input type="checkbox" name="Innengeländer" value="Innengeländer" <?php if ($data['nonParents']['Bekleidung / Anbauteile'] == 'Innengeländer') echo "checked='checked'"; ?>>Innengeländer</td>
							<td><input type="checkbox" name="Gitterträger" value="Gitterträger" <?php if ($data['nonParents']['Bekleidung / Anbauteile'] == 'Gitterträger') echo "checked='checked'"; ?>>Gitterträger</td>
						</tr>
					</table>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
						<tr>
							<td><b>Konsole innen: </b></td>
							<td width="14%"><input type="checkbox" name="0,19m" value="0,19m " <?php if ($data['nonParents']['Konsole innen'] == '0,19m') echo "checked='checked'"; ?>>0,19m</td>
							<td width="14%"><input type="checkbox" name="0,25m" value="0,25m" <?php if ($data['nonParents']['Konsole innen'] == '0,25m') echo "checked='checked'"; ?>>0,25m</td>
							<td width="14%"><input type="checkbox" name="0,30m" value="0,30m" <?php if ($data['nonParents']['Konsole innen'] == '0,30m') echo "checked='checked'"; ?>>0,30m</td>
							<td width="14%"><input type="checkbox" name="0,50m" value="0,50m" <?php if ($data['nonParents']['Konsole innen'] == '0,50m') echo "checked='checked'"; ?>>0,50m</td>
							<td width="14%"><input type="checkbox" name="0,70m" value="0,70m" <?php if ($data['nonParents']['Konsole innen'] == '0,70m') echo "checked='checked'"; ?>>0,70m</td>
							<td><input type="checkbox" name="1,00m" value="1,00m" <?php if ($data['nonParents']['Konsole innen'] == '1,00m') echo "checked='checked'"; ?>>1,00m</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
						<td><b>Konsole außen: </b></td>
						<td width="14%"><input type="checkbox" name="0,19m" value="0,19m " <?php if ($data['nonParents']['Konsole außen'] == '0,19m') echo "checked='checked'"; ?>>0,19m</td>
						<td width="14%"><input type="checkbox" name="0,25m" value="0,25m" <?php if ($data['nonParents']['Konsole außen'] == '0,25m') echo "checked='checked'"; ?>>0,25m</td>
						<td width="14%"><input type="checkbox" name="0,30m" value="0,30m" <?php if ($data['nonParents']['Konsole außen'] == '0,30m') echo "checked='checked'"; ?>>0,30m</td>
						<td width="14%"><input type="checkbox" name="0,50m" value="0,50m" <?php if ($data['nonParents']['Konsole außen'] == '0,50m') echo "checked='checked'"; ?>>0,50m</td>
						<td width="14%"><input type="checkbox" name="0,70m" value="0,70m" <?php if ($data['nonParents']['Konsole außen'] == '0,70m') echo "checked='checked'"; ?>>0,70m</td>
						<td><input type="checkbox" name="1,00m" value="1,00m" <?php if ($data['nonParents']['Konsole außen'] == '1,00m') echo "checked='checked'"; ?>>1,00m</td>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="4"><b>Verankerung: * Hinweis: ab 160 mm statischer Nachweis/ Innendiagonale verwenden (außer bei Netzen und Plane)</b></td>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
							<td width="16%"><input type="checkbox" name="Ringschrauben" value="Ringschrauben" <?php if ($data['nonParents']['Verankerung'] == 'Ringschrauben') echo "checked='checked'"; ?>>Ringschrauben</td>
							<td width="16%"><input type="checkbox" name="120 mm" value="120 mm" <?php if ($data['nonParents']['Verankerung'] == '120 mm') echo "checked='checked'"; ?>>120 mm</td>
							<td width="16%"><input type="checkbox" name="160 mm" value="160 mm" <?php if ($data['nonParents']['Verankerung'] == '160 mm') echo "checked='checked'"; ?>>160 mm</td>
							<td width="16%"><input type="checkbox" name="230 mm *" value="230 mm *" <?php if ($data['nonParents']['Verankerung'] == '230 mm *') echo "checked='checked'"; ?>>230 mm *</td>
							<td width="16%"><input type="checkbox" name="300 mm *" value="300 mm *" <?php if ($data['nonParents']['Verankerung'] == '300 mm *') echo "checked='checked'"; ?>>300 mm *</td>
							<td width="16%"><input type="checkbox" name="350 mm *" value="350 mm *" <?php if ($data['nonParents']['Verankerung'] == '350 mm *') echo "checked='checked'"; ?>>350 mm *</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
						<tr>
							<td width="16%"><input type="checkbox" name="Abstützung / Rohre" value="Abstützung / Rohre" <?php if ($data['nonParents']['Verankerung'] == 'Abstützung / Rohre') echo "checked='checked'"; ?>>Abstützung / Rohre</td>
							<td width="16%"><input type="checkbox" name="Abstütztürme" value="Abstütztürme" <?php if ($data['nonParents']['Verankerung'] == 'Abstütztürme') echo "checked='checked'"; ?>>Abstütztürme</td>
							<td width="16%"><input type="checkbox" name="V-Anker" value="V-Anker" <?php if ($data['nonParents']['Verankerung'] == 'V-Anker') echo "checked='checked'"; ?>>V-Anker</td>
							<td width="16%"><input type="checkbox" name="Gerüstkappen" value="Gerüstkappen" <?php if ($data['nonParents']['Verankerung'] == 'Gerüstkappen') echo "checked='checked'"; ?>>Gerüstkappen</td>
							<td width="16%"><input type="checkbox" name="Freistehen. Gerüst" value="Freistehen. Gerüst" <?php if ($data['nonParents']['Verankerung'] == 'Freistehen. Gerüst') echo "checked='checked'"; ?>>Freistehen. Gerüst</td>
							<td width="16%"><input type="checkbox" name="Innendiagonale" value="Innendiagonale" <?php if ($data['nonParents']['Verankerung'] == 'Innendiagonale') echo "checked='checked'"; ?>>Innendiagonale</td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="4">
					<table width="100%">
						<tr>
							<td width="16%">Verschluss:</td>
							<td width="16%"><input type="checkbox" name="durch" value="durch" <?php if ($data['nonParents']['Verschluss'] == 'durch Fa. Schäfer Gerüstbau') echo "checked='checked'"; ?>>durch Fa. Schäfer Gerüstbau</td>
							<td width="16%"><input type="checkbox" name="bauseits" value="bauseits" <?php if ($data['nonParents']['Verschluss'] == 'bauseits') echo "checked='checked'"; ?>>bauseits</td>
							<td width="16%"><input type="checkbox" name="Ankerlöcher bleiben offen" value="Ankerlöcher bleiben offen" <?php if ($data['nonParents']['Verschluss'] == 'Ankerlöcher bleiben offen') echo "checked='checked'"; ?>>Ankerlöcher bleiben offen</td>
							<td width="16%"><input type="checkbox" name="Keine Verankerung vorhanden" value="Keine Verankerung vorhanden" <?php if ($data['nonParents']['Verschluss'] == 'Keine Verankerung vorhanden') echo "checked='checked'"; ?>>Keine Verankerung vorhanden</td>
							<td width="16%"></td>
						</tr>
					</table>
				</td>
			</tr>
			<tr>
				<td colspan="4"><b>Einsatz von Technik: </b></td>
			</tr>
			<tr>
				<td><input type="checkbox" name="500kg" value="500kg" <?php if ($data['nonParents']['Einsatz von Technik'] == 'Aufzug 500 kg') echo "checked='checked'"; ?>>Aufzug 500 kg</td>
				<td><input type="checkbox" name="1500kg" value="1500kg" <?php if ($data['nonParents']['Einsatz von Technik'] == 'Aufzug 1500 kg') echo "checked='checked'"; ?>>Aufzug 1500 kg</td>
				<td><input type="checkbox" name="Geda Seilwinde" value="Geda Seilwinde" <?php if ($data['nonParents']['Einsatz von Technik'] == 'Geda Seilwinde') echo "checked='checked'"; ?>>Geda Seilwinde</td>
				<td>
					<input type="checkbox" name="Böcker-Aufzug" value="Böcker-Aufzug" <?php if ($data['nonParents']['Einsatz von Technik'] == 'Böcker-Aufzug') echo "checked='checked'"; ?>>Böcker-Aufzug
					<input type="checkbox" name="Kran (bauseits)" value="Kran (bauseits)" <?php if ($data['nonParents']['Einsatz von Technik'] == 'Kran (bauseits)') echo "checked='checked'"; ?>>Kran (bauseits)
				</td>
			</tr>
			<tr>
				<td><input type="checkbox" name="Teleskoplader" value="Teleskoplader" <?php if ($data['nonParents']['Einsatz von Technik'] == 'Teleskoplader') echo "checked='checked'"; ?>>Teleskoplader </td>
				<td><input type="checkbox" name="klein" value="klein" <?php if ($data['nonParents']['Einsatz von Technik'] == 'klein') echo "checked='checked'"; ?>>klein</td>
				<td><input type="checkbox" name="groß" value="groß" <?php if ($data['nonParents']['Einsatz von Technik'] == 'groß') echo "checked='checked'"; ?>>groß</td>
				<td><input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if (!in_array($data['nonParents']['Einsatz von Technik'], $einsatzVonTechnik)) echo "checked='checked'"; ?>><?=$data['nonParents']['Einsatz von Technik']?></td>
			</tr>
			<tr class="grey">
				<td colspan="4">
					<input type="checkbox" name="Werbung" value="Werbung" <?=isChecked($data['nonParents']['Werbung am Gerüst angebracht (Schriftzug lesbar, nicht verdeckt)'])?>>Werbung am Gerüst angebracht (Schriftzug lesbar, nicht verdeckt)
				</td>
			</tr>
			<tr class="grey">
				<td colspan="4">
					<b>Memo:</b>
				</td>
			</tr>
			<tr>
				<td colspan="4">
					<b><?=$data['nonParents']['Memo']?></b>
				</td>
			</tr>
			<tr>
				<td><b>Anlagen:</b></td>
				<td><input type="checkbox" name=" Konstruktionsvorschlag" value=" Konstruktionsvorschlag" <?php if ($data['nonParents']['Anlagen'] == ' Konstruktionsvorschlag') echo "checked='checked'"; ?>> Konstruktionsvorschlag</td>
				<td><input type="checkbox" name="Gebäude Pläne" value="Gebäude Pläne" <?php if ($data['nonParents']['Anlagen'] == 'Gebäude Pläne') echo "checked='checked'"; ?>>Gebäude Pläne</td>
				<td><input type="checkbox" name="Anfahrtsskizze" value="Anfahrtsskizze" <?php if ($data['nonParents']['Anlagen'] == 'Anfahrtsskizze') echo "checked='checked'"; ?>>Anfahrtsskizze</td>
			</tr>
			<tr>
				<td><input type="checkbox" name="Material" value="Material" <?php if ($data['nonParents']['Anlagen'] == 'Material- / Stückliste') echo "checked='checked'"; ?>>Material- / Stückliste</td>
				<td><input type="checkbox" name="AV" value="AV" <?php if ($data['nonParents']['Anlagen'] == 'A + V Layher/ PERI') echo "checked='checked'"; ?>>A + V Layher/ PERI</td>
				<td><input type="checkbox" name="Fotos" value="Fotos" <?php if ($data['nonParents']['Anlagen'] == 'Fotos') echo "checked='checked'"; ?>>Fotos</td>
				<td><input type="checkbox" name="Sonstiges" value="Sonstiges" <?php if ($data['nonParents']['Anlagen'] == 'Sonstiges') echo "checked='checked'"; ?>><?=$data['nonParents']['Anlagen']?></td>
			</tr>
		</table>
		<div>
			<b>Unterschrift Bauleiter:</b>
			
		</div>
	</body>
</html>

<style>
	.headerTable {width:100%; font-size: small!important;}
	.title {font-size: 14px;}
	 div {border: 1px solid black;}
	.mainContent {border: 1px solid black; width:100%; border-collapse:collapse; font-size: small!important;}
	.mainContent tr {border: 1px solid black; font-size:small!important;}
	.mainContent td {vertical-align: middle; font-size:small!important;}
	.grey {background-color:lightgrey;}
	.tableTitle {font-size:small!important;}
	body { font-family: Arial;}
}
	
</style>