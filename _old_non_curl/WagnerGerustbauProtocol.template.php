<html>
<?php $data = $this->data;
//NB: THE KEYS WHICH ARE USED ARE RELATED TO THE VALUES IN THE DB SO IF ANY CHANGE HAS TO BE IMPLEMENTED IN THE TEMPLATE
// AND THERE IS A CHANGE IN THE DB IN ANY OF THE FIELD NAMES RESPECTIVELY  A CHANGE IN THE TEMPLATE SHOULD BE IMPLEMENTED AS WELL 

	    function isChecked($string) {
	        if(in_array(strtolower($string),['ja','1','true','yes','on'])){
				return "checked='checked'";
			} else {
				return '';
			}
		}
?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Protokoll für Arbeits und Schutzgerüste</title>
	</head>
	<body>
		<table>
			<tr>

                <td align="right" rowspan="4" width="30%"><img width="100%" src="<?= $data['logo']?>" alt="Ihr Logo hier / Your Logo here"></td>
				<td class="title"><b>Protokoll für Arbeits- und Schutzgerüste</b></td>
                <td align="left" rowspan="4" width="150px"><img src="<?=$data['qrCodeUrl']?>"></td>
			</tr>
			<tr>
				<td><b><u>Gerüstersteller:</u></b></td>
			</tr>
			<tr>
				<td><?= $data['companyInfo']['name']?></td>
			</tr>
			<tr>
			    <?php if($data['companyInfo']['address']!= '') {$commaString = ', ';} else {$commaString = '';}; ?>
				<td>
                    <?= $data['companyInfo']['address'].$commaString.$data['companyInfo']['postcode']." ".$data['companyInfo']['city'] ?>
                    <br><?=$data['companyInfo']['phoneNumber'] ?>
                    <br><?=$data['companyInfo']['email'] ?>
                </td>

			</tr>
		</table>
		<table class="borderTable">
			<tr><td colspan="5">Baustelle:<?= str_repeat('&nbsp;', 2).$data['projectName']; ?></td></tr>
			<tr><td colspan="5">Auftraggeber:<?= str_repeat('&nbsp;', 2).$data['kName']; ?></td></tr>
			<tr><td colspan="5">Befähigte Person:<?=str_repeat('&nbsp;', 2).$data['Geprüft']['Name']?></td></tr>
			<tr>
				<td colspan="2">Arbeitsgerüst (DIN EN 12811) als</td>
				<td>
					<input type="checkbox" name="Fassadengerust" value="Fassadengerust" <?php if ($data['Arbeitsgerüst (DIN EN 12811)'] == 'Fassadengerüst') echo "checked='checked'"; ?>>Fassadengerüst
				<td>
					<input type="checkbox" name="Raumgerust" value="Raumgerüst" <?php if ($data['Arbeitsgerüst (DIN EN 12811)'] == 'Raumgerüst') echo "checked='checked'"; ?>>Raumgerüst
				</td>
				<td>
					<input type="checkbox" name="Fahrgerust" value="Fahrgerust" <?php if ($data['Arbeitsgerüst (DIN EN 12811)'] == 'Fahrgerüst') echo "checked='checked'"; ?>>Fahrgerüst
				</td>
			</tr>
			<tr>
				<td rowspan="2" colspan="2" valign="top">Schutzgerüst (DIN 4420) als</td>
				<td><input type="checkbox" name="Fanggerust" value="Fanggerust" <?php if ($data['Schutzgerüst (DIN 4420)'] == 'Fahrgerüst') echo "checked='checked'"; ?>>Fanggerüst</td>
				<td class="leftCell"><input type="checkbox" name="Dachfanggerust" value="Dachfanggerust" <?php if ($data['Schutzgerüst (DIN 4420)'] == 'Fanggerüst Dachfanggerüst') echo "checked='checked'"; ?>>Dachfanggerust</td>
				<td class="rightCell"><input type="checkbox" name="Schutzdach" value="Schutzdach" <?php if ($data['Schutzgerüst (DIN 4420)'] == 'Schutzdach') echo "checked='checked'"; ?>>Schutzdach</td>
			</tr>
			<tr>
				<td colspan="5">Sondergerüste: <?php if($data['Sondergerüste Beschreibung']) echo str_repeat('&nbsp;', 2).$data['Sondergerüste Beschreibung'];?></td>
			</tr>
			<tr>
				<td rowspan="2">Lastklasse</td>
				<td><input type="checkbox" name="2" value="2" <?php if (strtolower($data['Lastklasse']) === '2 (1,5 kn/m²)') echo "checked='checked'"; ?>>2 (1,5 kN/m²)</td>
				<td><input type="checkbox" name="3" value="3"  <?php if (strtolower($data['Lastklasse']) === '3 (2,0 kn/m²)') echo "checked='checked'"; ?>>3 (2,0 kN/m²)</td>
				<td><input type="checkbox" name="4" value="4" <?php if (strtolower($data['Lastklasse']) === '4 (3,0 kn/m²)') echo "checked='checked'"; ?>>4 (3,0 kN/m²)</td>
				<td><input type="checkbox" name="Sonstiges" value="Sontiges"
					<?php if (!in_array(strtolower($data['Lastklasse']), ['2 (1,5 kn/m²)', '3 (2,0 kn/m²)', '4 (3,0 kn/m²)']))
						echo "checked='checked'>Sonstiges.".str_repeat('&nbsp;', 2).$data['Lastklasse Sonstiges'];
					else echo '>Sonstiges.'; ?>
				</td>
			</tr>
			<tr></tr>
			<tr>
				<td></td>
				<td colspan="4">
					Die Summe der Verkehrslasten aller übereinanderliegenden Gerüstlagen in einem Gerüstfeld darf
					den vorgenannten Wert nicht überschreiten.
				</td>
			</tr>
			<tr>
				<td rowspan="2" valign="top">Breitenklasse</td>
				<td><input type="checkbox" name="W06" value="W06" <?php if ($data['Breitenklasse'] == 'W06') echo "checked='checked'"; ?>>W06</td>
				<td><input type="checkbox" name="W09" value="W09" <?php if ($data['Breitenklasse'] == 'W09') echo "checked='checked'"; ?>>W09</td>
				<td colspan="3"><input type="checkbox" name="Sonstiges" value="Sonstiges"
				<?php if (($data['Breitenklasse'] !== 'W06') && ($data['Breitenklasse'] !== 'W09')) echo "checked='checked'"; ?>>
					W <?php if (($data['Breitenklasse'] !== 'W06' && $data['Breitenklasse'] !== 'W09')) echo $data['Breitenklasse Sonstiges']; ?>
				</td>
			</tr>
			<tr>
				<td colspan="4">Nutzungsbeschränkung: <?=str_repeat('&nbsp;', 2).$data['Nutzungsbeschränkung']?></td>
			</tr>
			<tr>
				<td rowspan="2">Geprüft (durch befähigte Person des Gerüststellers)</td>
				<td colspan="2" rowspan="2">Datum:  <?=str_repeat('&nbsp;', 2).date('d.m.Y',strtotime(substr($data['Geprüft']['Datum'],0,10)));?></td>
				<td colspan="3" rowspan="2">Name / Unterschrift:  <?=str_repeat('&nbsp;', 2).$data['Geprüft']['Name']?></td>
			</tr>
		</table>
			<hr>
		<table class="fullwidth">
		<tr>
			<th>Bekleidung</th>
			<th>Konstruktion</th>
			<th>Verkehrssicherung</th>
			<th></th>
		</tr>
		<tr>
			<td><input type="checkbox" name="mitNetzen" value="mitNetzen" <?=isChecked($data['Bekleidung']['mit Netzen'])?>>mit Netzen</td>
			<td><input type="checkbox" name="Regelausfuhrung" value="Regelausfuhrung" <?=isChecked($data['Konstruktion']['mit Regelausführung'])?>>Regelausführung</td>
			<td><input type="checkbox" name="Genehmigung" value="Genehmigung" <?=isChecked($data['Verkehrssicherung']['Genehmigung'])?>>Genehmigung</td>
			<td><input type="checkbox" name="Halteverbot" value="Halteverbot" <?=isChecked($data['Verkehrssicherung']['Halteverbot'])?>>Halteverbot</td>
		</tr>
		<tr>
			<td><input type="checkbox" name="mitPlanen" value="mitPlanen" <?=isChecked($data['Bekleidung']['mit Planen'])?>>mit Planen</td>
            <?php
                // Handle typo in old documentation_schema_positions
			    $data['Konstruktion']['Statik im Einzelfall liegt vor'] = $data['Konstruktion']['Statik im Einzelfall liegt vor'] ? : $data['Konstruktion']['Statik im Einzefall liegt vor'];
            ?>
			<td><input type="checkbox" name="Statik" value="Statik" <?=isChecked($data['Konstruktion']['Statik im Einzelfall liegt vor'])?>>Statik im Einzelfall liegt vor</td>
			<td><input type="checkbox" name="Warnschilder" value="Warnschilder" <?=isChecked($data['Verkehrssicherung']['Warnschilder'])?>>Warnschilder</td>
			<td><input type="checkbox" name="Beleuchtung" value="Beleuchtung" <?=isChecked($data['Verkehrssicherung']['Beleuchtung'])?>>Beleuchtung</td>
		</tr>
		<tr>
			<td><input type="checkbox" name="sonstiges" value="sonstiges" <?=isChecked($data['Bekleidung']['Sonstiges'])?>>Sonstiges</td>
			<td colspan="3"></td>
		</tr>
		</table>
			<hr>
		<table class="lastTwo">
			<tr>
				<th>Prüfung der Gerüstbauteile </th>
				<th>Beläge</th>
				<th>Arbeits- und Betriebssicherheit</th>
			</tr>
			<tr>
				<td><input type="checkbox" name="Augenscheinlich" value="Augenscheinlich" <?=isChecked($data['Prüfung der Gerüstbauteile']['Augenscheinlich unbeschädigt'])?>>Augenscheinlich unbeschädigt</td>
				<td><input type="checkbox" name="Gerustbohlen" value="Gerustbohlen" <?=isChecked($data['Beläge']['Gerüstbohlen'])?>>Gerüstbohlen</td>
				<td><input type="checkbox" name="Seitenschutz" value="Seitenschutz" <?=isChecked($data['Arbeits- und Betriebssicherheit']['Seitenschutz'])?>>Seitenschutz</td>
			</tr>
			<tr>
				<td><input type="checkbox" name="Originalbauteile" value="Originalbauteile" <?=isChecked($data['Prüfung der Gerüstbauteile']['Originalbauteile nach Z und A+V'])?>>Originalbauteile nach Z und A+V</td>
				<td><input type="checkbox" name="Systembelage" value="Systembelage" <?=isChecked($data['Beläge']['Systembeläge'])?>>Systembeläge</td>
				<td><input type="checkbox" name="Wandabstand" value="Wandabstand" <?=isChecked($data['Arbeits- und Betriebssicherheit']['Wandabstand < 30cm'])?>>Wandabstand < 30cm</td>
			</tr>
			<tr>
				<td></td>
				<td></td>
				<td><input type="checkbox" name="Genehmigung" value="Genehmigung"<?=isChecked($data['Arbeits- und Betriebssicherheit']['Aufstiege, Zugänge - Abstand < 50m'])?>>Aufstiege, Zugänge - Abstand < 50m</td>
			</tr>
			<tr>
				<td></td>
				<td></td>
				<td>
					<input type="checkbox" name="Treppenturm" value="Treppenturm" <?=isChecked($data['Arbeits- und Betriebssicherheit']['Treppenturm'])?>>Treppenturm
					<input type="checkbox" name="Leiter" value="Leiter" <?=isChecked($data['Arbeits- und Betriebssicherheit']['Leiter'])?>>Leiter
				</td>
			</tr>
            <tr>
                <td></td>
                <td></td>
                <td><input type="checkbox" name="Eckausbildung" value="Eckausbildung" <?=isChecked($data['Arbeits- und Betriebssicherheit']['Eckausbildung'])?>>Eckausbildung</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td><input type="checkbox" name="Schutzwand" value="Schutzwand" <?=isChecked($data['Arbeits- und Betriebssicherheit']['Schutzwand im Dachfanggerüst'])?>>Schutzwand im Dachfanggerüst</td>
            </tr>
            <tr>
                <td></td>
                <td></td>
                <td><input type="checkbox" name="Innenliegender" value="Innenliegender" <?=isChecked($data['Arbeits- und Betriebssicherheit']['innenliegender Seitenschutz'])?>>innenliegender Seitenschutz</td>
            </tr>
		</table>
		<hr>
		<table class="lastTwo">
			<tr>
				<th>Standsicherheit</th>
				<th>Verankerungen</th>
				<th>Freigabe</th>
			</tr>
			<tr>
				<td><input type="checkbox" name="Tragfähigkeit" value="Tragfähigkeit" <?=isChecked($data['Standsicherheit']['Tragfähigkeit der Aufstandsfläche'])?>>Tragfähigkeit der Aufstandsfläche</td>
				<td><input type="checkbox" name="Verankerungsraster" value="Verankerungsraster" <?=isChecked($data['Verankerungen']['Verankerungsraster'])?>>Verankerungsraster</td>
                <td><input type="checkbox" name="Prufung" value="Prufung" <?=isChecked($data['Freigabe']['Prüfung ist abgeschlossen'])?>>Prüfung ist abgeschlossen </td>
			</tr>
			<tr>
				<td><input type="checkbox" name="Spindelauszugslänge" value="Spindelauszugslänge" <?=isChecked($data['Standsicherheit']['Spindelauszugslänge'])?>>Spindelauszugslänge</td>
				<td><input type="checkbox" name="Ankerprotokoll" value="Ankerprotokoll"  <?=isChecked($data['Verankerungen']['Ankerprotokoll vorhanden'])?>>Ankerprotokoll vorhanden</td>
                <td><input type="checkbox" name="Kennzeichnung" value="Kennzeichnung" <?=isChecked($data['Freigabe']['Kennzeichnung ist angebracht'])?>>Kennzeichnung ist angebracht</td>

			</tr>
			<tr>
				<td><input type="checkbox" name="Diagonalen" value="Diagonalen" <?=isChecked($data['Standsicherheit']['Diagonalen'])?>>Diagonalen</td>
				<td><input type="checkbox" name="Langsriegel" value="Langsriegel" <?=isChecked($data['Verankerungen']['bei Bekleidungen erhöhte Kräfte beachten'])?>>bei Bekleidungen erhöhte Kräfte beachten</td>
                <td><input type="checkbox" name="gerustFreigegeben" value="gerustFreigegeben" <?=isChecked($data['Freigabe']['Gerüst ist freigegeben'])?>>Gerüst ist freigegeben</td>
			</tr>
			<tr>
				<td></td>
				<td></td>
				<td></td>
			</tr>
			<tr>
				<td><input type="checkbox" name="Gittertrager" value="Gittertrager" <?=isChecked($data['Standsicherheit']['Gitterträger'])?>>Gitterträger</td>
				<td><input type="checkbox" name="Freistehend" value="Freistehend" <?=isChecked($data['Verankerungen']['Freistehend'])?>>Freistehend</td>
                <td><input type="checkbox" name="nichtFertig" value="nichtFertig" <?=isChecked($data['Freigabe']['Nicht fertig gestellter Bereich abgegrenzt und Verbotszeichen "Zutritt verboten" angebracht'])?>>Nicht fertig gestellter Bereich abgegrenzt und Verbotszeichen " Zutritt verboten" angebracht</td>
			</tr>
			<tr>
				<td><input type="checkbox" name="Fahrrolle" value="Fahrrolle" <?=isChecked($data['Standsicherheit']['Fahrrolle'])?>>Fahrrolle</td>
				<td></td>
                <td></td>
			</tr>
			<tr>
				<td>Verbreiterung</td>
				<td></td>
                <td></td>
			</tr>
			<tr>
				<td>
					<input type="checkbox" name="35" value="35er"  <?=isChecked($data['Standsicherheit']['Verbreiterung 35er'])?>>35er
					<input type="checkbox" name="70" value="70er" <?=isChecked($data['Standsicherheit']['Verbreiterung 70er'])?>>70er
				</td>
				<td></td>
			</tr>
		</table>
		<hr>
	<table>
		<tr>
			<td>Bemerkungen: <?=$data['Übergabe']['Bemerkungen'];?> <br>
			</td>
		</tr>
	</table>
		<div  class="smallFont"></div>
		<p style="margin-top:0; margin-bottom:0; line-height:.5"><br /></p>
        <span class="smallFont"><b><u>Am Gerüst dürfen keine eigenmächtigen Veränderungen vorgenommen werden</u></b> z. B. Entfernen von Verankerungen, Ausbau von Seitenschutzbauteilen, Festlegen von Montagepunkten für Schuttrutschen, <b><u>dies darf grundsätzlich nur der Gerüstersteller.</u></b></span>
		<p style="margin-top:0; margin-bottom:0; line-height:.5"><br /></p>
		<div class="bordered smallFont">Vor Benutzung ist das Gerüst durch den Gerüstbenutzer auf Betriebssicherheit zu prüfen.</div>
		<table width="100%">
			<tr>
				<th width="25%" class="smallFont" align="left"><?=$data['Übergabe']['Ort'].', '.date('d.m.Y',strtotime(substr($data['Übergabe']['Datum'],0,10)));?></th>
				<th><img src="<?=$data['Übergabe']['Unterschrift Auftraggeber']?>" align="left" class="signature" width="25%"></th>
				<th><img src="<?=$data['Übergabe']['Unterschrift Gerüstbenutzer']?>" align="left" class="signature" width="25%"></th>
                <th><img src="<?=$data['Übergabe']['Unterschrift Gerüstersteller']?>" align="left" class="signature" width="25%"></th>
			</tr>
			<tr>
				<td width="25%" class="smallFont">Ort, Datum</td>
				<td width="25%" class="smallFont">Unterschrift Auftraggeber</td>
				<td width="25%" class="smallFont">Unterschrift Gerüstbenutzer</td>
                <td width="25%" class="smallFont">Unterschrift Gerüstersteller</td>
			</tr>
			<tr>
				<td></td>
				<td align="left" class="signature smallFont" width="25%"><?=$data['Übergabe']['Name Auftraggeber']?></td>
				<td align="left" class="signature smallFont" width="25%"><?=$data['Übergabe']['Name Gerüstbenutzer']?></td>
                <td align="left" class="signature smallFont" width="25%"><?=$data['Übergabe']['Name Gerüstersteller']?></td>
			</tr>
		</table>
	</body>
</html>

<style>
    table {font-size: small;}
	.borderTable, .borderTable td, .borderTable th  {
		border: 1px solid black;
		border-collapse: collapse;
	}
	.bordered {border: 1px solid black;}
	.underlined {border-bottom: 1px solid black;}
	.fullwidth {width: 100%;}
	.lastTwo {
		margin: 0;
		padding: 0;
	}
	.lastTwo {width: 100%;}
	.lastTwo td, .lastTwo th {width: 33%;}
	.lastTwo th, .fullwidth th {text-align: left;}
	.title {font-size: 25px;}
	.signature {height:auto;}
	.smallFont {font-size:small;}
</style>