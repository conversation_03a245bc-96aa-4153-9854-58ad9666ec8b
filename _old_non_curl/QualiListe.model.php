<?php

require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'v3/Employees.php';

class C_QualiListe {

    private $db;
    private $documentation;
    private $employees;

    public function __construct() {

        $this->db = CommonData::getDbConnection();
        $this->documentation = new Documentation();
        $this->employees = new v3\Employees();
    }

    public function getData($schemaId, $documentId) {

        $documentData = $this->documentation->getDocumentId($schemaId, $documentId);

        if ($documentData) {

            $modelData = [];

            $modelData['documentId'] = $documentId;
            $modelData['documentDate'] = $documentData['documentCreatedOn'];
            $modelData['footerTitle'] = $documentData['title'];
            $modelData['employeeData'] = (array) $this->getEmployee($documentData['documentRelKey1']);

            $headlines = array();
            foreach ($documentData['children'] as $k=>$v) {

                if ($v['type'] != 'headline') {
                    $parentTitle = $headlines[$v['parentId']]['title'];
                    if ($v['type'] == 'checkbox') {
                        $modelData['fields']['checkBoxes'][$parentTitle." ".$v['title']] = $v['reportedValue'];
                    } elseif ($v['type'] == 'string') {
                        $modelData['fields'][$parentTitle." ".$v['title']] = $v['reportedValue'];
                    } elseif ($v['type'] == 'date' && $v['title'] == 'gültig bis') {
                        $modelData['fields'][$parentTitle." ".$v['title']] = date('d.m.Y' ,strtotime($v['reportedValue']));
                    }
                }
                else
                {
                    $headlines[$v['id']] = $v;
                }
            }

            return $modelData;
        } else {
            throw new RestException(400, "No such data for the provided parameters!");
        }
    }

    private function getEmployee($pnr) {

        // fetch data from tunnel
        if ($target = getTunnelingTarget('GET', 'v3/employees/'.$pnr)) {
            $resultEmployee = getDataFromTunnel($target);
        }

        if (empty($resultEmployee)) {
            return '';
        } else {
            return $resultEmployee;
        }
    }
}