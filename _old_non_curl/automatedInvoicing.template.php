<!DOCTYPE HTML>
<html>
<?php $data = $this->data;?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Automated invoicing</title>
	</head>
	<body>
	<table class="invoices">
        <thead>
        <tr>
            <th colspan="4" class="title">
                Nutzungsabrechnung
                <?=date('d.m.Y', strtotime("-1 month", strtotime($data['invoiceDate'])));?>
                -
				<?=date('d.m.Y', strtotime("-1 day", strtotime($data['invoiceDate'])));?>
            </th>
        </tr>
        <tr>
            <th width="15%" align="right">Personalnr.</th>
            <th width="30%" align="left">Name</th>
            <th width="30%" align="center">Paket</th>
            <th width="15%" align="right">Preis</th>
        </tr>
        </thead>
<?php 			foreach ($data['invoices'] as $i=>$inv) { ?>
<?='<tr>';?>
					<?='<td align="right">'.$inv['employeeNo'].'</td>'; ?>
					<?='<td align="left">'.$inv['employeeDisplayName'].'</td>'; ?>
					<?='<td align="center">'.trim(str_replace('Paket', '', $inv['shortDescription'])).'</td>'; ?>
					<?='<td align="right">'.number_format($inv['unitPrice'], 2, ',', '').' '.' &#8364;'.'</td>'; ?>
<?='</tr>';?>
<?php 			} ?>
			<tr>
				<td></td>
				<td></td>
				<td></td>
                <td align="right" class="border"><b><?=$data['total'];?> &#8364</b></td>
			</tr>			
		</table>
		<br><br>
		<table class="items">
			<tr>
				<th align="left" width="25%">Preisliste</th>
				<th width="65%"></th>
				<th width="15%"></th>
			</tr>
<?php 			foreach ($data['items'] as $key=>$item) { ?>
<?='<tr>';?>
					<?='<td></td>' ?>
					<?='<td align="left">'.$item['shortDescription'].'</td>'; ?>
					<?='<td align="left">'.number_format($item['unitPrice'], 2, ',', '').' '.$item['measuringUnitKey'] .'</td>'; ?>
<?='</tr>';?>
<?php 			} ?>
			<tr>
		</table>
	</body>
</html>



<style>
   .title {font-weight:bold; font-size:xx-large; padding-top:25px;}
   .invoices {width:100%;}
   .border {border:1px solid black;}
   
    .invoices thead { display: table-header-group }
	.invoices  tfoot { display: table-row-group }
	.invoices tr { page-break-inside: avoid }
</style>