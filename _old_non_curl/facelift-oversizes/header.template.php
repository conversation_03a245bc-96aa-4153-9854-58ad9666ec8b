<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <style>
        * {
            font-family: Arial, sans-serif;
        }

        .titleTable {
            width: 100%;
        }

        .headerTable {
            width: 100%;
            border: 1px solid black;
        }

        .spanner {
            width: 60%;
        }

        .logo {
            width: 150px;
        }

        .hidden {
            visibility: hidden;
        }

        .oversize-title {
            padding: 0;
            margin: 0;
        }

        #project-nr {
            font-size: 22px;
        }

    </style>
</head>
<body>
<?php
require_once ABS_PATH.'/printouts/printout.helper.php';
?>
<table class="titleTable">
    <tr>
        <td colspan="15" class="hidden">-</td>
    </tr>
    <tr>
        <td class="oversize-title" colspan="14"><h2>Aufmaß</h2></td>
        <td class="logo oversize-title" rowspan="2">
            <img src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/../ladeliste-facelift/img/peri-logo.png");?>"
                 alt="Company Logo" class="logo">
        </td>
    </tr>
    <tr>
        <td class="oversize-title" id="project-nr">
            <?php
            $oversizeNr  = $this->data['projectNo'];
            $projectTitle = $this->data['projectName'];
            echo $oversizeNr . "&nbsp&nbsp&nbsp" . $projectTitle;  ?>
            </td>
        <td colspan="13" class="oversize-title"></td>
    </tr>
    <tr>
        <td colspan="15" class="hidden">-</td>
    </tr>
</table>


</body>
</html>
