<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8">
    <style>
        * {
            font-family: Arial, sans-serif;
        }

        #main-tile {
            background-color: #f4f4f4;
            height: 100vh;
            width: 100%;
            top: 0;
        }

        th {
            text-align: left;
        }

        .images {
            max-height: 285px;
        }

        #uploads-overview {
            width: 100%;
        }

        #uploads-overview td {
            padding: 15px;
        }

        #border-line {
            border-top: 2px solid black;
        }

        #footer-line {
            width: 100%;
        }

        .titleTable {
            width: 100%;
        }

        .headerTable {
            width: 100%;
            border: 1px solid black;
        }

        .spanner {
            width: 60%;
        }

        .logo {
            width: 150px;
        }

        .hidden {
            visibility: hidden;
        }

        .oversize-title {
            padding: 0;
            margin: 0;
        }

        #project-nr {
            font-size: 22px;
        }


        .black-td {
            text-align: left;
            background-color: #333333;
            height: 50px;
            padding-left: 20px;
        }

        #styled-title {
            color: white;
        }


        /*        Data table headers*/
        .head-titles-oversizes-data-holder {
            background-color: #e9e9e9;
            text-align: left;
            hyphens: manual;
            font-size: 9px !important;
            vertical-align: text-top !important;
            padding-top: 5px;
        }

        .head-titles-oversizes-data-holder td:not(nth-child(1))
        {
            border-left: hidden;
        }

        .oversizes-header td {
            padding-left: 5px;
            font-size: 12px;
        }

        .oversizes-header td:not(:nth-child(1)) {
            border-left: hidden;
        }
        .oversizes-holder {
            width: 100%;
            border-collapse: collapse;
        }

        .oversizes-holder tr:nth-child(1) td {
            border-top: hidden;
        }

        .oversizes-holder td {
            border: 1px solid black;
            padding-top: 5px;
            padding-left: 5px;
            background: #ffffff;
            width: 100%;
            font-size: 10px;
        }

        .oversizes-header td:not(:nth-child(1)) {
            border-left: hidden;
        }

        .oversizes-holder td:not(:nth-child(1)):not(:nth-child(2)) {
            border-left: hidden;
        }

        .oversizes-holder td:not(:nth-child(1)) {
            vertical-align: top !important;
        }

        #mass, #preexisting-mass, #checkmark-sums {
            vertical-align: middle !important;
        }

        #checkmark {
            width: 10px;
        }

        .checkmark {
            width: 40px;
        }

        .oversizes-header {
            table-layout: fixed;
        }

        .oversizes-header {
            width: 100%;
            border-collapse: collapse;
        }

        .oversizes-header td {
            border: 1px solid black;
        }

        .oversizes-header th {
            border: 1px solid black;
            max-width: 6.67%;
            min-width: 6.67%;
        }
    </style>
</head>
<body>
<?php
require_once ABS_PATH.'/printouts/printout.helper.php';
$data = $this->data;
$pageTitle = $data['pageTitle'];
$overviewPictureTitle = $data['overviewPictureTitle'];
$overviewPicture = $data['overviewPicture'];
$overviewTitle = $data['overviewTitle'];
$overviewTitlePicture = $data['overviewTitlePicture'];
$buildingSideXYmatrix = $data['buildingSideXYmatrix'];
$buildingSideXYpicture = $data['buildingSideXYpicture'];
$buildingLayoutBreakDown = $data['buildingLayoutBreakDown'];
$buildingLayoutBreakDownPicture = $data['buildingLayoutBreakDownPicture'];
?>

<section id="main-tile">
    <table id="uploads-overview">
        <thead>
        <tr>
            <th colspan="14">
                <?php echo "$pageTitle";?>
            </th>
        <tr>
        </thead>
        <tr>
            <td><img class="images" src="<?php echo $overviewPicture;?>" alt="draufsicht">
                <section><?php echo $overviewPictureTitle;?></section>
            </td>
            <td><img class="images" src="<?php echo $overviewTitlePicture; ?>" alt="photo">
                <section><?php echo $overviewTitle; ?></section>
            </td>
        </tr>
        <tr>
            <td>
                <img class="images" src="<?php echo $buildingSideXYpicture; ?>" alt="matrix">
                <section><?php echo $buildingSideXYmatrix; ?> </section>

            </td>
            <td>
                <img class="images" src="<?php echo $buildingLayoutBreakDownPicture; ?>" alt="zerlegung">
                <section>
                    <?php echo $buildingLayoutBreakDown; ?>
                </section>
            </td>
        </tr>
    </table>
</section>
<?php
?>
<table id="footer-line" style="page-break-after: always">
    <tr>
        <td colspan="15" id="border-line"></td>
    </tr>
    <tr>
        <td colspan="15"></td>
    </tr>
</table>

<!-- Begin of Second page -->

<table class="oversizes-header">
    <tbody><tr>
    </tr></tbody><thead>
    <tr><th colspan="15" class="black-td">
            <section id="styled-title"><b><?php echo "$pageTitle";?></b></section>
        </th>
    </tr>
    <tr>
        <th class="head-titles-oversizes-data-holder">
            <b>Leistung</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Gerüst-<br>Pos</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Vorhaltung-<br>Pos</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Stk</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Länge</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Tiefe</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Höhe</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Massen</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Vorhaltebeginn</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>GHV bis<br>(abw. Mietb)</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Bisherige Verlängerung</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Vorhalte-<br>ende</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Vorhalte-<br>verlängerung</b>
        </th>
        <th class="head-titles-oversizes-data-holder">
            <b>Vorhalte-<br>massen</b>
        </th>
        <th class="head-titles-oversizes-data-holder checkmark-valign">
            <b>Checkmark &nbsp; &nbsp; </b> <img src="./img/check-white-nobackground.png" alt="Checkmark Example" id="checkmark">
        </th>
    </tr>
    </thead>

    <tbody>
    </tbody>
	<?php
	$oversizeItems = $this->data['oversizes'];
	$trAggregationCounter = 0;
	$cells = '';
	$ctr = 0;

	$aggregatedTable = '';
	/**
	 * @param $item
	 * @return string
	 */
	function getPositionRow($item)
	{
		$intValSubItemsAvailInfo = intval($item["subItemsAvailableInformation"]);
		$itemNo = $item["itemNo"]; # Gerüst-Pos.
		$itemNoRent = $item["itemNoRent"]; # Vorhaltungs-Pos
		$itemDescription = $item["description"]; # Description / Kurztext
		$objectDescription = $item["objectDdescription"]; # $item["objectDescription"]; # Object Description / currently empty in the json now
		$detailDescription = $item["detailDescription"]; # Detail Description / currently empty in the json now
		$quantityCount = $item['quantityCount']; # Stück
		$quantityLength = $item["quantityLength"]; # Länge
		$quantityDepth = $item["quantityDepth"]; # Tiefe
		$quantityHeight = $item["quantityHeight"]; # Höhe
		$quantity = $item["quantity"]; # Massen
		$mountingDate = $item['mountingDate']; # Vorhaltebeginn
		$includedRentalTimeEndDate = date_format(date_create($item["includedRentalTimeEndDate"]), 'd.m.Y'); # GVH bis (abw. Mietb)
		$rentalEndDate = date_format(date_create($item["rentalEndDate"]), 'd.m.Y'); #Vorhalte-ende: rentalEndDate
		$measuringUnitRental = $item["measuringUnitRental"];
		$itemType = $item['itemType'] ? : $item['subItemType'];
		$lengthening = "-"; # n/a neither in new.json, nor in integration_oversizes.json
		$quantityTotalAllSubItems = $item["quantityTotalAllSubItems"]; # ?
		$objectAxis = $item["objectAxis"];
		$rentalPeriod = $item["rentalPeriod"];
		$quantityRent = $item["quantityRent"];

		$cells =
			' <td class="tbody-td-oversizes-data-holder" style="word-wrap: break-word; border-right: hidden;">'
                . $itemNo . '
                     <section>
                        <i>' . $objectDescription . '</i>
                     </section>
                </td>
                <td class="tbody-td-oversizes-data-holder">' . $objectAxis . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $quantityCount . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $quantityLength . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $quantityDepth . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $quantityHeight . '</td>
                <td class="tbody-td-oversizes-data-holder bold">' . $quantity . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $mountingDate . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $includedRentalTimeEndDate . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $lengthening . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $rentalEndDate . '</td>
                <td class="tbody-td-oversizes-data-holder">' . $rentalPeriod . '</td>
                <td class="tbody-td-oversizes-data-holder bold">' . $quantityRent . '</td>
                <td class="tbody-td-oversizes-data-holder">
                    <img 
                    src="'. PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/img/unchecked.png') . '" 
                    alt="checkbox" class="checkmark">
                </td>
            </tr>';

        if($detailDescription)
		{
            $cells .= '<tr>
                            <td colspan="14" id="object-description">' . $detailDescription . '</td>
                        </tr> ';
		}
		return $cells;
	}

	/**
	 * @param $unitPriceRent
	 * @param $unitPrice
	 * @return string
	 */
	function getSumRow($unitPriceRent, $unitPrice): string
	{
		return '<tr>
            <td class="totalssum" id="sum-title" colspan="4">
                <b>Summe</b>
            </td>
            <td class="totalssum" id="mass" colspan="4">
                <b>' . $unitPriceRent . '</b>
            </td>
            <td class="totalssum" id="preexisting-mass" colspan="5">
                <b>' . $unitPrice . '</b>
            </td>
            <td class="totalssum" id="checkmark-sums">
                <img 
                src="'. PrintoutHelper::translateLocalPathToServerPath(__DIR__ . '/img/unchecked.png') . '"
                alt="checkbox" class="checkmark">
            </td>
        </tr>';
	}

	/**
	 * @param $rowSpan
	 * @param $itemDescription
	 * @param string $cells
	 * @param string $sumRows
	 * @return string
	 */
	function getFirstColumn($rowSpan, $itemDescription, string $cells, string $sumRows): string
	{
		return '<tr>
                <td class="rowpsan-elements" rowspan="' . $rowSpan .
			'" style="border-right: 1px solid black !important; word-wrap: break-word;">'
			. $itemDescription . '</td> ' . $cells . $sumRows . '<tr> </tr>';
	}

	if (is_array($oversizeItems) || is_object($oversizeItems)) {
		foreach ($oversizeItems as $item) {

			/*                $mountingDate = date_format(date_create($item["mountingDate"]), 'Y.m.d'); #n/a in new.json*/
            $itemType = $item['itemType'];

            // TODO check wether this works properly with sub-items
			if(is_array($item["subItems"]) && isset($item["subItems"]) && count($item["subItems"]) > 0){
				foreach ($item["subItems"] as $subItem) {
					$itemType = $subItem["itemType"];
					$itemDescription = $subItem["objectDescription"];

					if ($itemType == 'TS') {
						$rowSpan = 1 + ($trAggregationCounter * 2);
						$unitPrice = $subItem['unitPrice'];
						$unitPriceRent = $subItem['unitPriceRent'];
						$sumRows =
							getSumRow($unitPriceRent, $unitPrice);
						$aggregatedTable .= getFirstColumn($rowSpan, $itemDescription, $cells, $sumRows);
						$cells = '';

						$trAggregationCounter = 0;
					} else {
						$cells = getPositionRow($subItem);
						$trAggregationCounter += 1;
					}
				}
			}

			if ($itemType == 'TK') {
				$trAggregationCounter = 0;
				$cells = '';
                // will be later used in TS
                $itemDescription = $item['description'];
			} elseif ($itemType == 'POS') {
				$cells .= getPositionRow($item);
				$trAggregationCounter++;
			} elseif ($itemType == 'TS') {
				$rowSpan = 1 + ($trAggregationCounter * 2);
				$unitPrice = $item['unitPrice'];
				$unitPriceRent = $item['unitPriceRent'];
				$sumRows =
					getSumRow($unitPriceRent, $unitPrice);
				$aggregatedTable .= getFirstColumn($rowSpan, $itemDescription, $cells, $sumRows);
			}
		}
	}
	echo $aggregatedTable;
	?>
</table>

</body>

</html>



