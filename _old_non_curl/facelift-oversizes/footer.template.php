<!DOCTYPE html>
<html lang="de">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <!-- taken from https://wkhtmltopdf.org/usage/wkhtmltopdf.txt -->
    <script>
        function subst() {
            var vars = {};
            var query_strings_from_url = document.location.search.substring(1).split('&');
            for (var query_string in query_strings_from_url) {
                if (query_strings_from_url.hasOwnProperty(query_string)) {
                    var temp_var = query_strings_from_url[query_string].split('=', 2);
                    vars[temp_var[0]] = decodeURI(temp_var[1]);
                }
            }
            var css_selector_classes = ['page', 'frompage', 'topage', 'webpage', 'section', 'subsection', 'date', 'isodate', 'time', 'title', 'doctitle', 'sitepage', 'sitepages'];
            for (var css_class in css_selector_classes) {
                if (css_selector_classes.hasOwnProperty(css_class)) {
                    var element = document.getElementsByClassName(css_selector_classes[css_class]);
                    for (var j = 0; j < element.length; ++j) {
                        element[j].textContent = vars[css_selector_classes[css_class]];
                    }
                }
            }
            <!-- hide page numbering on first page -->
            <!-- TODO the footer space is still provided, leading to the destruction of the document -->

        }
    </script>
</head>
<style>
    * {
        font-family: Arial, sans-serif;
    }

    .footerTable {
        width: 100%;
        font-size: 12px;
    }

    .span {
        width: 95%;
    }

    .spanner {
        width: 15%;
    }

    .hidden {
        visibility: hidden;
    }

    .centerAlign {
        text-align: center;
        display: inline;
    }

    .borderline {
        border-top: 2px solid black;
    }

    .icons {
        width: 20px;
    }

</style>
</head>
<body onload="subst()">
<?php
require_once ABS_PATH.'/printouts/printout.helper.php';
$data = $this->data;
$companyName1 = $data['companyName1'];
$companyName2 = $data['companyName2'];
$phoneNumber = $data['phoneNumber'];
$faxNumber = $data['faxNumber'];
$email = $data['email'];
?>

<table class="footerTable">
    <tr>
        <td colspan="2" class="spanner">
			<?php
			echo $companyName1; ?>,
        </td>
        <td colspan="2" class="spanner">
			<?php
			echo $companyName2; ?>
        </td>
        <td colspan="2" class="spanner">
            <img
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/phone.jpg"); ?>"
                    alt="Phone Icon" class="icons">
			<?php
			echo $phoneNumber; ?>
        </td>
        <td colspan="2" class="spanner">
            <img
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/fax.jpg"); ?>"
                    alt="Icon" class="icons">
			<?php
			echo $faxNumber; ?>
        </td>
        <td colspan="2" class="spanner">
            <img
                    src="<?php echo PrintoutHelper::translateLocalPathToServerPath(__DIR__."/img/email.jpg"); ?>"
                    alt="Icon" class="icons">
			<?php
			echo $email; ?>
        </td>
        <td colspan="2" class="spanner"></td>
        <td colspan="2" class="span centerAlign">Seite <span class="page"></span>/<span class="topage"></span>
        </td>
    </tr>
</table>

</body>
</html>
