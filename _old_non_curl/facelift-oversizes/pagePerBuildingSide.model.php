<?php
require_once ABS_PATH . 'models/Projects/ProjectsSelector.php';
require_once ABS_PATH.'Documentation.php';

if (defined('ABS_PATH')) {
	require_once ABS_PATH . 'printouts/printout.helper.php';
	require_once ABS_PATH . 'printouts/PrintoutCurl.php';
} else {
	require_once '../printout.helper.php';
    require_once '../PrintoutCurl.php';
}

class PagePerBuildingSideModel
{
	public function getData($schemaId, $documentId)
	{
		// fetch Documentation
		$documentationAPI = new Documentation();
		$documentData = $documentationAPI->getDocumentId($schemaId,$documentId);
		$schemaName = $documentData['title'];
		$projectNo = $documentData['documentRelKey1'];
		$main = [];
		$main['id'] = $documentData['id'];
		$main['title'] = $documentData['title'];
		$main['type'] = $documentData['type'];
		$main['required'] = $documentData['required'];
		$main['reportedValues'] = [];

		array_unshift($documentData['children'], $main);

		$helper = new PrintoutHelper();
		$grouped = $helper->groupChildrenUnderParentRecursive($documentData['children'], 'id', 'parentId', 'children');

		$documentDataGrouped = $helper->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');

		$matrixId = $documentDataGrouped[$schemaName]['Aufmaßerfassung über Matrix'];
		// from DynDocu
		$nameOfSide = $documentDataGrouped[$schemaName]['Seite angeben'];

		$templateData['pageTitle'] = "1. Gebäudeseite: {$nameOfSide}";
		// Draufsicht from the project or from the DynDocu - or both?
		$templateData['overviewPictureTitle'] = "Draufsicht | Gebäudeseite: {$nameOfSide}";

		// resolve project
		$projectsSelector = new ProjectsSelector();
		$parameters['projectNo']['eq'] = $projectNo;
		$project = $projectsSelector->get($parameters)[0];
		$templateData['projectName'] = $project['projectName'];
		$templateData['projectNo'] = $project['projectNo'];

		// resolve current user
		$accessTokenData = \Auth\AuthServer::getAccessTokenData();
		$accessToken = $accessTokenData['access_token'];
		$employeeNo = $accessTokenData['user_id'];
		$employeesApi = new \v3\Employees();
		$employee = $employeesApi->getsingle($employeeNo);
		$email = $employee['email'];

		// instantiate files api
		$filesAPI = new Files();
		$files = $filesAPI->get('ktr',$projectNo);

		// Draufsicht exists in DynDocu
		if($documentDataGrouped[$schemaName]['Draufsicht - gewählte Seite'])
		{
			$overviewPicturePath = $documentDataGrouped[$schemaName]['Draufsicht - gewählte Seite'];
		}
		else
		{
			foreach($files as $file)
			{
				if($file['category'] == 'measurement')
				{
					$overviewPicturePath = $file['filepath'];
					break;
				}
			}
		}
		$templateData['overviewPicture'] = $this->translateFilePath($overviewPicturePath);
		// Erstes Foto from the project or from the DynDocu - or both?
		$templateData['overviewTitle'] = "Foto | Gebäudeseite: {$nameOfSide}";

		// Draufsicht exists in DynDocu
		if($documentDataGrouped[$schemaName]['Fotos von Seite'])
		{
			// TODO pick first available
			$titlePicturePath = $documentDataGrouped[$schemaName]['Fotos von Seite'];
		}
		else
		{
			foreach($files as $file)
			{
				if($file['category'] != 'measurement')
				{
					$titlePicturePath = $file['filepath'];
					break;
				}
			}
		}
		$templateData['overviewTitlePicture'] = $this->translateFilePath($titlePicturePath);

		$principal = ACTIVE_PRINCIPAL;

		// Matrix Rendering from Web
		$templateData['buildingSideXYmatrix'] = "Matrix | Gebäudeseite: {$nameOfSide}";
		$templateData['buildingSideXYpicture'] = "https://peri-quicksolve-wrapper.baubuddy.de/matrix/{$documentId}/{$matrixId}/{$email}/image";

		// Matrix Trapezoid Slicing
		$templateData['buildingLayoutBreakDown'] = "Zerlegung der Gebäudefläche gemäß VOB Regelwerk";
		$templateData['buildingLayoutBreakDownPicture'] = "https://peri-quicksolve-wrapper.baubuddy.de/matrix/{$projectNo}/2022/{$schemaId}/{$documentId}/{$principal}/{$accessToken}/trapezoid";

		// Matrix OversizeItems
		$oversizesFromWrapperUrl = "https://peri-quicksolve-wrapper.baubuddy.de/matrix/{$projectNo}/2022/{$schemaId}/{$documentId}/oversizes";
		$curl = new PrintoutCurl();
        $options['httpheader'][] = 'principal: '.$principal;
		$options['httpheader'][] = 'access-token: '.$accessToken;
		$json = $curl->_simple_call('GET', $oversizesFromWrapperUrl, null, $options);
		$oversizesFromWrapper = json_decode($json,true);
		$itemNoCounter = 1;
		foreach ($oversizesFromWrapper['positions'] as $oversizeItem)
		{
			if(!$oversizeItem['quantity'])
				continue;

			if($oversizeItem['description'] == 'Fassadengerüst')
			{
				// Matrix OversizeSubitems for Fassadengerüst

				// add Totals
			}
			// add additional (dummy fields) - positions
			$oversizeItem['itemNo'] = $itemNoCounter.'.00';
			$itemNoCounter++;
			$oversizeItem['itemNoRent'] = '1.01';
			$oversizeItem['quantityCount'] = 1;
			// add additional (dummy fields) - positions

			// TODO drop after testing
			$oversizeItemHead = $oversizeItem;
			$oversizeItemHead['itemType'] = 'TK';

			// TODO drop after testing
			$oversizeItemSum = $oversizeItem;
			$oversizeItemSum['itemType'] = 'TS';
			$oversizeItemSum['unitPrice'] = 6 * $oversizeItem['quantity'];
			$oversizeItemSum['unitPriceRent'] = 0.3 * $oversizeItem['quantity'];

			$templateData['oversizes'][] = $oversizeItemHead;
			$templateData['oversizes'][] = $oversizeItem;
			$templateData['oversizes'][] = $oversizeItemSum;
		}

		// add company details for footer
		$settingsApi = new Settings();
		$settings = $settingsApi->getinfo()['address'];
		$templateData['companyName1'] = $settings['title'];
		$templateData['companyName2'] = $settings['name'];
		$templateData['phoneNumber'] = $settings['phoneNumber'];
		$templateData['faxNumber'] = $settings['faxNumber'];
		$templateData['email'] = $settings['email'];

		return $templateData;
	}

	private function translateFilePath($filePath)
	{
		if(defined('WEB_ROOT_API'))
		{
			$targetBaseUrl = WEB_ROOT_API.'/infomaterial';
		}
		else
		{
			$targetBaseUrl = 'https://peri.baubuddy.de:8443/api-messe/infomaterial';
		}
		$relativePath = explode('infomaterial',$filePath)[1];
		return $targetBaseUrl.$relativePath;
	}
}
