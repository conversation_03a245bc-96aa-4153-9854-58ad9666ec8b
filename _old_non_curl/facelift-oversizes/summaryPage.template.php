<!DOCTYPE html>
<html lang="de">
<head>
    <style>
        /*
        Summary page styling start
        */
        .titleTable {
            width: 100%;
        }

        .headerTable {
            width: 100%;
            border: 1px solid black;
        }

        .spanner {
            width: 60%;
        }

        .logo {
            width: 150px;
        }

        .hidden {
            visibility: hidden;
        }

        .span-high {
            height: 100px;
        }


        /*
        Summary page styling end
        */

        * {
            font-family: Arial, sans-serif;
        }

        .scaffolding-title {
            width: 100%;
        }

        .scaffolding-title td:nth-child(2) {
            text-align: right;
            color: red;
            font-size: 11px;
        }

        .facade-scaffolding th {
            border: 1px solid black;
            border-collapse: collapse;
            border-spacing: 0;
            padding: 5px;
            height: 40px;
            font-size: 13px;
            text-align: left;
        }

        .facade-scaffolding th {
            background-color: #e9e9e9;
        }

        .facade-scaffolding td {
            border: 1px solid black;
            color: #5d6673;
            height: 40px;
        }

        .facade-scaffolding tr:nth-child(1) td:first-child {
            border-bottom: hidden;
        }

        .facade-scaffolding tr:nth-child(2) td:first-child {
            border-top: hidden;
            border-right: hidden;
        }

        .facade-scaffolding tr:nth-child(1) td {
            border-top: hidden;
        }

        .facade-scaffolding tr:nth-child(2) td:nth-child(2) {
            border-left: hidden;
        }

        .facade-scaffolding {
            width: 100%;
            margin: 0;
            padding: 0;
            border-collapse: collapse;
        }

        .facade-scaffolding td:nth-child(1) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(1) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding td:nth-child(2) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(2) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding td:nth-child(3) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(3) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding td:nth-child(4) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(4) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding td:nth-child(5) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(5) {
            max-width: 100px;
            min-width: 100px;
        }
        .facade-scaffolding td:nth-child(6) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(6) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding td:nth-child(7) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(7) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding td:nth-child(8) {
            max-width: 100px;
            min-width: 100px;
        }

        .facade-scaffolding th:nth-child(8) {
            max-width: 100px;
            min-width: 100px;
        }

        #checkmark {
            width: 10px;
        }

        .checkmark {
            width: 40px;
        }

        #border-line {
            border-top: 2px solid black;
        }

        #footer-line {
            width: 100%;
        }
    </style>
</head>

<body>
<?php
require_once("/Applications/XAMPP/htdocs/facelift-oversizes/header.template.php");
require_once("/Applications/XAMPP/htdocs/facelift-oversizes/functions.php");
?>
<!--
Comment the above 3 (require_once) lines to hide, uncomment to show the header on localhost for test purposes
-->
<!--
Summary page info sub-table
-->

<section class="hidden">
    <table>
        <tr>
            <td colspan="15">
            </td>
        </tr>
    </table>
</section>
<table class="headerTable">
    <tr>
        <td class="oversize-title"><b>Kunde</b></td>
        <td colspan="13" class="spanner"></td>
        <td><b>Bewertung zum</b></td>
        <td><b>Datum</b></td>
        <td><b>Nummer</b></td>
    </tr>
    <tr>
        <td class="oversize-title">
            <?php
            $clientNr  = "14008";
            $clientName = "Erzbistum Paderborn";
            echo $clientNr . "&nbsp&nbsp&nbsp" . $clientName;  ?>
        </td>
        <td colspan="13" class="spanner"></td>
        <td>11.05.2022</td>
        <td>30.06.2022</td>
        <td>2367-14</td>
    </tr>
</table>

<!--
End summary page info sub-table
-->
<section class="hidden span-high">
    <table>
        <tr>
            <td colspan="15">
                -
            </td>
        </tr>
    </table>
</section>
<!--
 the body of the Summary Page starts here...
-->
<section>
    <table class="scaffolding-title">
        <tr>
            <td colspan="7" style="border: none !important; color: black;">

            </td>

            <td style="border: none !important; color: black;"><b>Alles erledigt</b> <img src="./img/unchecked.png" alt="checkbox" class="checkmark"></td>
        </tr>
        <tr>
            <td colspan="7" style="border: none !important; color: black;">
                <h3>Fassadengerüst</h3>
            </td>

            <td style="border: none !important;">*Änderungen/Mehrleistung zum Vor-Aufmaß</td>
        </tr>
    </table>
</section>

<section>
    <table class="facade-scaffolding">
        <thead>
        <tr>
            <th>
                Leistungen
            </th>
            <th>
                Grundvorh.
            </th>
            <th>
                montiert & demontiert
            </th>
            <th>
                Vorhaltebeginn
            </th>
            <th>
                GHV bis
            </th>
            <th>
                Bish. Verlängerung
            </th>
            <th>
                Vorhalteende
            </th>
            <th>
                Vorhaltemassen
            </th>
        </tr>
        </thead>
    </table>
    <table class="facade-scaffolding">
        <tbody>
        <tr>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td></td>
            <td colspan="7"></td>
        </tr>
        </tbody>
    </table>
</section>
<!--
 the body of the Summary Page ends here...
-->

<section class="hidden span-high">
    <table>
        <tr>
            <td colspan="15">
                -
            </td>
        </tr>
    </table>
</section>
<!--Comment the above 3 lines to hide, uncomment to show the footer on localhost for test purposes
-->

<table id="footer-line">
    <tr>
        <td colspan="15" id="border-line"></td>
    </tr>
    <tr>
        <td colspan="15"></td>
    </tr>
</table>
<?php
require_once("/Applications/XAMPP/htdocs/facelift-oversizes/footer.template.php");
?>

</body>
</html>