<html>
<?php
	$data = $this->data;
?>
	<head>
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<title>Abnahme</title>
	</head>
	<body>
		<table>
				<tr>
					<td align="left" class="smallFont" width="50%">Beglau Wärmepumpen GmbH • Cambser Str. 2a • 19067 Rampe </td>
					<td align="right" width="50%"><img src="<?=WEB_ROOT_API.'vendor\printouts\abnahmeLogo.png'?>" alt="companyLogo" class="companyLogo"</td>
				</tr>
			</table>
			<?=str_repeat('<br>',1);?>
		<div align="center" class="header">Abnahme-/Inbetriebnahme-Protokoll mit</div>
		<div align="center" class="header">Fertigstellungsanzeige und Fachunternehmerklärung</div>
		<p></p>
		<table cellspacing="0" border="0" width="100%">
			<tr>
				<td width="33%" align="left" class="mainContent" valign="top"><u>Auftragnehmer:</u></td>
				<td width="67%" class="mainContent" align="left">
						BEGLAU Wärmepumpen GmbH <br>
						Cambser Str. 2 a <br>
						19067 Rampe
				</td>
			</tr>
		</table>
		<p></p>
		<table cellspacing="0" border="0" width="100%">
			<tr>
				<td width="33%" align="left" class="mainContent" valign="top"><u>Auftraggeber:</u></td>
				<td width="67%" class="mainContent" align="left">
					<?=$data['customerName'];?> <br>
					<?=$data['customerAddress'];?> <br>
					<?=$data['customerPlace'];?> 
				</td>
			</tr>
		</table>
			<?=str_repeat('<br>',1);?>
		<table cellspacing="0" border="0" width="100%">
			<tr>
				<td width="33%" align="left" class="mainContent" valign="top"><u>Objekt:</u></td>
				<td width="67%" class="mainContent" align="left">
					<?=$data['projectDate'];?> <br>
					<?=$data['projectNumber'].' '.$data['projectName'];?> 
				</td>
			</tr>
		</table>
		<?=str_repeat('<br>',2);?>
		<div align="left" class="mainContent">Der Auftragnehmer hat die im o.g. Objekt installierte </div>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>Heizungs- und Warmwasseranlage und</div>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>- Be-/Entwässerungsanlage</div>
		<?=str_repeat('<br>',2);?>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>1. nach den Festlegungen bzw. Vorgaben des Auftraggebers/Bauherr nach dem</div>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>bestätigten Angebot und den bestätigten Nachträgen</b></div>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>2. und nach den in der Verdingungsordnung für Bauleistung unter der VOB Teil C</div>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>aufgeführten Technischen Regeln der DIN 18380 Heizungs- und</div>
		<div align="left" class="mainContent"><?=str_repeat('&nbsp;', 5);?>Warmwasseranlagen und DIN 18381 Trinkwasser-, Entwässerungsanlagen errichtet.</div>
		<?=str_repeat('<br>',3);?>
		<div align="left" class="mainContent">Ein Wartungsvertrag wurde angeboten und die Dokumentation der Wärmepumpe wurde</div>
		<div align="left" class="mainContent">übergeben.</div>
		<?=str_repeat('<br>',2);?>
		<table class="signitures" cellspacing="0" width="100%" border="1">
			<tr>
				<td align="center">
					<?=$data['Datum'].' / ';?>
					<img src="<?=$data['Unterschrift Auftraggeber']?>" alt="signature1" class="signatures">
					<br>
					Datum / Unterschrift <br>
				</td>
				<td align="center">
					<?=$data['Datum'].' / ';?>
					<img src="<?=$data['Unterschrift Bauleiter']?>" alt="signature2" class="signatures">
					<br>
					Datum / Unterschrift <br>
				</td>
				<td align="center">
					<?=$data['Datum'].' / ';?>
					<img src="<?=$data['Unterschrift Mitarbeiter Beglau-WP']?>" alt="signature3" class="signatures">
					<br>
					Datum / Unterschrift <br>
				</td>
			</tr>
			<tr>
				<td align="center">Auftraggeber</td>
				<td align="center">Bauleiter</td>
				<td align="center">Mitarbeiter Beglau-WP</td>
			</tr>
		</table>
		<?=str_repeat('<br>',2);?>
	</body>
</html>

<style>
	.header {font-weight:bold; font-size:x-large;}
	.mainContent {font-size:large;}
	.signatures {width:33%; height:auto;}
	.smallFont {font-size:small;}
	.largeFont{font-size:large; font-weight:bold;}
	.companyLogo {width:75%; height:auto;}
	.smallFont {font-size:small;}
</style>


