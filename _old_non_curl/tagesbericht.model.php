<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Customers.php';
require_once ABS_PATH.'Projects.php';
class C_Tagesbericht {

private $documentation;
private $db;
private $customers;
private $projects;
	
	public function __construct() {
		$this->documentation = new DocumentationDocument();
		$this->db = CommonData::getDbConnection();
		$this->customers = new Customers();
		$this->projects = new Projects();
	}
	public function getData($schemaId,$documentId) {
		
		$data = $this->documentation->getById($schemaId, $documentId);
		$templateData = [];
		$templateData['documentDate'] = $data[0]['documentCreatedOn'];
		$templateData['id'] = $documentId;
		
		$projectData = $this->getProjectData(((int)$data[0]['documentRelKey1']))[0];
		
		$templateData['customerName'] = $this->getCustomerName($projectData['knr']);
		
		if(isset($projectData['technicalContactDisplayName'])) {
			$templateData['technicalContactDisplayName'] = $projectData['technicalContactDisplayName'];
		} else {
			$templateData['technicalContactDisplayName'] = ' - ';
		}
		
		$templateData['projectName'] = $projectData['externalProjectNo']. " ".trim($projectData['project_name']);
		//set total count of workers and hours for each Regie
		$reggies = [];
		$regieIds = [];
		$availableRegies = ['Regie 1', 'Regie 2', 'Regie 3'];

		foreach ($data[0]['children'] as $v) {
			if ($v['type'] == 'signatureField') {
				$templateData[$v['title']] = $v['filePath'];
			} else if ($v['type'] != 'headline') {
				if($v['title'] === 'Datum des Besuches')
				{
					$date = $v['reportedValue'];
					// check wether timezone exists
					if(strlen($date) == 25)
					{
						// cut it off
						$v['reportedValue'] = substr($date,0,19);
					}
				}
				// assign value to title
				$templateData[$v['title']] = $v['reportedValue'];
			} else {
				if (in_array($v['title'], $availableRegies)) {
					$regieIds[$v['id']] = $v['title'];
				}
			}
			
			foreach ($regieIds as $id => $r) {
				if($id == $v['parentId'] && ($v['title']=='Beschreibung' || $v['title']=='Stunden' || $v['title']=='Anzahl Mitarbeiter')) {
					$reggies[$r][$v['title']] = $v['reportedValue'];
				}
			}
		}
		
		foreach ($reggies as $n => &$reggie) {
			$reggies[$n]['Mitarbeitern+Stunden'] = round(((float)$reggies[$n]['Stunden'] * (float)$reggies[$n]['Anzahl Mitarbeiter']), 2);
		}

		// handle empty Reggies
		if(!$reggies)
			$reggies[0]['Beschreibung'] = " - Keine Regiearbeiten - ";

		$templateData['Reggies'] = $reggies;
		return $templateData;
	}
	
	public function checkDocumentExistence($schemaId, $documentId) {
		$document = $this->documentation->getById($schemaId, $documentId);
		return $document;
	}
	
	private function getProjectData($ktr) {
		$ktrProjects = $this->projects->get($ktr,0,0,0,'','','',false,'','','','','all');
		
		if(count($ktrProjects) === 1) {
			
			return $ktrProjects;
		} else {
			$sortedProjects = [];
	
			foreach ($ktrProjects as $k=>$v) {
				$sortedProjects[$v['year']] = $v;
			}
			
			ksort($sortedProjects);
			return end($sortedProjects);
		}
	}

	
	private function getCustomerName($knr) {
		$customerData = $this->customers->getselect($knr);
		return $customerData['name'];
	}

}
