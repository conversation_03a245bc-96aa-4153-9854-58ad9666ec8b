<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <meta name="description" content="Source code by <PERSON><PERSON><PERSON>">
    <meta name="author" content="<PERSON><PERSON><PERSON>!">
    <style>
        * {
            font-family: Arial,Helvetica Neue,Helvetica,sans-serif;
        }

        tbody {
            height: 100% !important;
        }

        .display-block {
            display: block;
        }

        .display-inline {
            display: inline;
        }

        .customer-name-p {
            width: 100%;
        }

        .customer-table-cell {
            height: 100% !important;
            width: 40% !important;
            padding: 0px !important;
            margin: 0px !important;
            vertical-align: top;
        }

        .project-table-cell {
            width: 30% !important;
            padding: 0px !important;
            margin: 0px !important;
            vertical-align: top;
        }

        .bb-parts {
            height: 90px;
        }

        .img-section {
            width: 100px;
            height: 100px;
            padding: 0 !important;
            margin: 0 !important;
        }

        .logo {
            height: auto;
            width: 100%;
        }

        .eight-pt {
            font-size: 8pt;
        }

        .display-inline {
            display: inline;
        }

        .padding-top {
            padding-top: 5%;
        }

        .fourteen-pt {
            font-size: 14pt;
        }

        .not-bold {
            font-weight: normal;
        }

        .logo-td-borders {
            border-top: 1px solid white !important;
            border-bottom: 1px solid white !important;
            border-right: 1px solid white !important;
        }

        .site-info-table {
            width: 100%;
        }

        .drop-all-borders {
            border-top: 0px !important;
            border-bottom: 0px !important;
            border-right: 0px !important;
            border-left: 0px !important;
        }

        .display-none {
            visibility: hidden;
        }

        table.load-list-table {
            border-collapse: collapse;
            width: 100%;
        }
        table.load-list-table td, table.load-list-table th {
            border: 1px solid black;
        }

        table.load-list-table th {
            background-color: #E6E6E6 !important;
        }

        table.load-list-table tbody td {
            font-size: 13px;
        }

        #load-list-1st-cell-table {
            width: 100%;
        }

        #load-list-1st-cell-table td {
            border: 1px solid white;
            border-collapse: collapse;
        }

        #company-logo-cell {
            width: 30%;
            height: 100%;
        }

        #customer-table {
            height: 100% !important;
            width: 100% !important;
            padding: 0px;
            margin: 0px;
        }

        #project-table {
            height: 100%;
        }

        #project-name, #customer-name {
            font-size: 20px;
        }

        .padding-left {
            padding-left: 1%;
        }

        .color-gray {
            background-color: #e6e6e6;
        }

        .color-white {
            background-color: #fff;
        }
    </style>
</head>
<body>
<?php
$sumTotalWeight = 0;

$jsonData = $this->data;
$projectName = $jsonData['project']['projectName'];
$imgMatches = glob(__DIR__."/img/*.jpg");

function takeArticleNrFromImage($artNr, $imgMatches) {
	foreach($imgMatches as $articleImageFile) {
		$imgNr = explode("_", basename($articleImageFile))[0];
		if ($artNr == $imgNr) {
			// TODO is there a better way switch to the external path?
			$fullFilePath = str_replace('/var/www/html','https://'.$_SERVER['SERVER_NAME'],$articleImageFile);
			return $fullFilePath;
		}
	}
}
?>

<table class="load-list-table">

    <tbody>
    <tr>
        <td class="color-gray project-table-cell" colspan="3">
            <table id="project-table">
                <tr>
                    <td class="drop-all-borders ">Projekt </td>
                </tr>
                <tr>
                    <td id="project-name" class="drop-all-borders"> <?php echo $projectName; ?>
                    </td>
                </tr>
                <tr>
                    <td class="drop-all-borders">
                        <section class="display-none">-</section>
                    </td>
                </tr>
            </table>
        </td>
        <td colspan="3" class="color-white customer-table-cell">
            <table id="customer-table">
                <tr>
                    <td class="drop-all-borders">Kunde</td>
                </tr>
                <tr>
                    <td id="customer-name" class="drop-all-borders"><p class="display-inline customer-name-p"> <?php echo $jsonData['project']['customerName']; ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <td class="drop-all-borders">
                    </td>
                </tr>
            </table>
        </td>
        <td id="company-logo-cell" class="logo-td-borders" colspan="3">
            <section class="logo">
                <img class="logo" src="<?= str_replace('/var/www/html','https://'.$_SERVER['SERVER_NAME'],__DIR__.'/img/peri_logo.png') ?>" alt="Company logo">
            </section>
        </td>
    </tr>
</table>
<section class="display-none">-</section>
<!--
####  ####
#### Metadata for the Lade Liste Starts Here ####
####  ####
Link to see original:
https://gitlab.baubuddy.de/BauBuddy/api-printouts/uploads/dc412fc228007c39784287f88480a01e/image.png
-->
<table class="site-info-table">
    <tr>
        <td colspan="2" style="border-bottom: 1px solid black">
			<?php
			echo $jsonData['project']['address'] . $jsonData['project']['zipCode'] . $jsonData['project']['city'];
			?>
        </td>
        <td style="border-bottom: 1px solid black">
			<?php
			echo "#".$jsonData['document']['id'];
			?>
        </td>
    </tr>
    <tr style="font-size: 8pt">
        <td colspan="2">
            <!--Baustellenadresse-->
			<?php
			echo $jsonData['project']['projectSiteAddressTitle'];
			?>
        </td>
        <td>
            <!--Checklisten-Nr.-->
			<?php
			echo $jsonData['strings']['checklistIdTitle'];
			?>
        </td>
    </tr>
    <tr>
        <td style="border-bottom: 1px solid black">
			<?php
			# echo $jsonData['project']['projectManager']['displayName'];
			echo '<p class="fourteen-pt">' . $jsonData['project']['projectManager'] . '</p>';

			?>
        </td>
        <td style="border-bottom: 1px solid black">
			<?php
			# echo $jsonData['document']['author']['displayName'];
			echo '<p class="fourteen-pt">' . $jsonData['project']['siteManager'] . '</p>';

			?>
        </td>
        <td style="border-bottom: 1px solid black">
			<?php
			echo '<p class="fourteen-pt">' . $jsonData['document']['creationDate'] . '</p>';
			#echo $jsonData['document']['creationDate']->format('d.m.Y H:i\h');
			?>
        </td>
    </tr>
    <tr style="font-size: 8pt">
        <td>
            <!--Bauleitung-->
			<?php
			echo $jsonData['project']['projectManagerTitle'];
			?>
        </td>
        <td>
            <!--erstellt von-->
			<?php
			echo $jsonData['strings']['authorTitle'];
			?>
        </td>
        <td>
            <!--erstellt am-->
			<?php
			echo $jsonData['strings']['creationDateTitle'];
			?>
        </td>
    </tr>
	<?php
	if($jsonData['strings']['sectionTitle'])
	{
		?>
        <tr>
            <td colspan="3" style="border-bottom: 1px solid black">
				<?php
				echo $jsonData['project']['subProjectName'];
				?>
            </td>
        </tr>
        <tr style="font-size: 8pt">
            <td>
				<?php
				# TODO: Ask what is the isHinweisschreiben function?
				#if(isHinweisschreiben($jsonData['schema']['positions']))
				#    echo "<td colspan='3' style='border-bottom: 1px solid black'>";
				#else
				#    echo "<td colspan='3'>";
				// Bereich
				echo $jsonData['strings']['sectionTitle']
				?>
                <br><br>
            </td>
        </tr>
		<?php
		// closing if statement
	}
	?>
    <!--
	####  ####
	#### Metadata for the Lade Liste Ends Here ####
	####  ####

	-->
    <section class="display-none">-</section>
    <table class="load-list-table no-borders">
        <thead>
        <tr>
            <th colspan="4">
                <section class="display-none">-</section>
            </th>
            <th colspan="1">
                <p class="display-inline padding-top">Maximum
                <section class="not-bold eight-pt display-block">Pcs.</section>
                </p>
            </th>
            <th colspan="1">
                <p class="display-inline padding-top">Correction
                <section class="not-bold eight-pt display-block">Pcs.</section>
                </p>
            </th>
            <th colspan="1">Sum</th>
            <th colspan="2">Weight</th>
        </tr>
        </thead>

		<?php

		$jsonDataArrayLength = count($jsonData['schema']['positions'] );

		$positions = $jsonData['schema']['positions'];

		$tableIterator = 0;

		foreach ($positions as $position) {

			$quantity = $jsonData['schema']['positions'][$tableIterator]['quantity'];
			$corrections = $jsonData['schema']['positions'][$tableIterator]['corrections'];
			$sum = $jsonData['schema']['positions'][$tableIterator]['sum'];
			$directoryOfServiceCompoundKey = $jsonData['schema']['positions'][$tableIterator]['directoryOfServiceCompoundKey'];
			$itemGroupId = $jsonData['schema']['positions'][$tableIterator]['directoryOfServiceItem']['itemGroupId'];
			$shortDescription = $jsonData['schema']['positions'][$tableIterator]['directoryOfServiceItem']['shortDescription'];
			$serviceItemNo = $jsonData['schema']['positions'][$tableIterator]['directoryOfServiceItem']['serviceItemNo'];
			$articleImageNr = $jsonData['schema']['positions'][$tableIterator]['directoryOfServiceItem']['externalId'];
			$weight = $jsonData['schema']['positions'][$tableIterator]['directoryOfServiceItem']['Weight'];
			$totalWeight = $jsonData['schema']['positions'][$tableIterator]['totalWeight'];

			$tableIterator += 1;
			# $currentArtFileName = takeArticleNrFromImage($articleImageNr, $imgMatches);

			echo
				'
    <tr>
        <td colspan="4">
            <table id="load-list-1st-cell-table">
            <tr>
                <td rowspan="3">
                ' .
				'<section class="img-section"> 
                <img class="bb-parts" src="'
				.
				takeArticleNrFromImage($articleImageNr, $imgMatches)  . '" alt="An inventory article with number: ' . $articleImageNr . '"'
				# TODO: we have to clear exactly how
				# the images will be placed here.
				. '</section>'
				. '
                </td>
                <td>
                ' .
				$shortDescription
				. '
                </td>
            </tr>
            <tr>
                <td>
                ' .
				'Article number 
              ' . $serviceItemNo
				. '
                </td>
            </tr>     
            <tr>
                <td>
                ' .
				$weight . ' kg'
				. ' 
                </td>
            </tr>
            </table>
        </td>
        <td class="padding-left" colspan="1">' . $quantity . '</td>
        <td class="padding-left" colspan="1">' . $corrections . '</td>
        <td class="padding-left" colspan="1">' . $sum . '</td>
        <td class="padding-left" colspan="2">' . $totalWeight . ' kg' . '</td>
        
    </tr>
';
			$sumTotalWeight += $totalWeight;
		}
		?>

        <tr><td colspan="8"></td><td class="padding-left">Total weight: <?php echo $sumTotalWeight; ?> kg</td></tr>
        <tbody>

        </tbody>

    </table>


</table>
</body>
</html>