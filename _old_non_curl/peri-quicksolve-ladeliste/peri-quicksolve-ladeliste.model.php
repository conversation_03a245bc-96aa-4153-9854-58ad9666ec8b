<?php

class PeriQuicksolveLadelisteModel
{
	public function getData($projectNo, $workingOrderNo)
	{
		$templateData = array();
		// resolve Logo
		$settingsAPI = new Settings();
		$templateData['companyLogoUrl'] = $settingsAPI->getcommon()['logo'];
		$settingsDB = new \SettingsDB();

		// instantiate API node
		$addressesAPI = new Addresses();
		// resolve own company
		$principalCompanyNo = $settingsDB->get_settings(['principalCompanyNo'])['principalCompanyNo'];
		if ($principalCompanyNo) {
			$addressesAPI = new Addresses();
			$companyDetails = $addressesAPI->get($principalCompanyNo);
			$templateData['companyDetails'] = $companyDetails;
		}

		// resolve project
		require_once ABS_PATH . 'models/Projects/ProjectsSelector.php';
		$projectsSelector = new ProjectsSelector();
		$parameters['projectNo']['eq'] = $projectNo;
		$project = $projectsSelector->get($parameters)[0];
		$templateData['project']['projectName'] = $project['projectName'];
		$templateData['project']['address'] = $project['projectSiteAddress'];
		$templateData['project']['zipCode'] = $project['projectSiteZipCode'];
		$templateData['project']['city'] = $project['projectSiteCity'];
		$templateData['project']['customerNo'] = $project['customerNo'];

		// resolve customer
		$customer = $addressesAPI->get($project['customerNo']);
		$templateData['project']['customerName'] = $customer['name'];
		$templateData['project']['customerContactKey'] = "";


		// resolve workingOrder
		/*
		require_once ABS_PATH . 'models/WorkingOrders/WorkingOrdersSelector.php';
		$workingOrdersAPI = new \WorkingOrdersSelector();
		$q = new \SelectorQueryParams();
		$q->filter = ['projectNo' => ['eq' => $projectNo]];
		$q->filter['workingOrderNo'] = ['eq' => $workingOrderNo];
		$workingOrders = $workingOrdersAPI->get([], $q);
		if ($workingOrders[0]) {
			$workingOrder = $workingOrders[0];
		}
		$templateData['project']['subProjectName'] = $workingOrder['taskName'];
		*/
		#die('hi');
		// resolve employee - where from (technicalContact
		require_once ABS_PATH . 'v3/Employees.php';
		$employeesApi = new \v3\Employees();
		$commercialContact = $employeesApi->get($project['commercialContactKey'])[0];
		$technicalContact = $employeesApi->get($project['technicalContactKey'])[0];
		$templateData['project']['projectManager'] = $commercialContact['displayName'] ? : '';
		$templateData['project']['siteManager'] = $technicalContact['displayName'] ? : '';
		$templateData['project']['projectSiteAddressTitle'] = 'Straße';
		$templateData['project']['projectManagerTitle'] = 'Kaufm. Ansprechpartner';
		$templateData['project']['siteManagerTitle'] = 'Techn. Ansprechpartner';

		// fetch oversizeItems
		require_once ABS_PATH . 'Oversizes.php';
		$oversizesAPI = new Oversizes();
		$_GET['fields'] = 'billingServiceSheetNo,billingServiceSheetVersionNo,evaluationDate';
		$_GET['filter']['projectNo']['eq'] = $projectNo;
		$_GET['filter']['workingOrderNo']['eq'] = $workingOrderNo;
		$oversizes = $oversizesAPI->getAllHeadDataOversizesByFilter('ALL', 'positions', false, true);
		$oversize = $oversizes[0];
		$templateData['document']['id'] = $oversize['billingServiceSheetNo'];
		$oversizeAuthor = $employeesApi->get($oversize['positions'][0]['createdBy'])[0];
		$templateData['document']['author'] = $oversizeAuthor['displayName'];
		$templateData['document']['creationDate'] = $oversize['evaluationDate'];

		// strings
		$templateData['strings']['checklistIdTitle'] = 'Ladeliste Nr.';
		$templateData['strings']['authorTitle'] = 'Erstellt von';
		$templateData['strings']['creationDateTitle'] = 'Erstellt am';
		$templateData['strings']['sectionTitle'] = 'Tätigkeit';

		require_once  ABS_PATH . 'DirectoryOfServices.php';
		$directoryOfServiceAPI = new DirectoryOfServices();
		$positions = array();
		foreach ($oversize['positions'] as $oversizeItem) {
			$position = array();
			$position['quantity'] = $oversizeItem['quantity'];
			$position['corrections'] = 0;
			$position['sum'] = $oversizeItem['quantity'];
			// resolve directoryOfServiceItem
			$directoryOfServiceItem = null;
			$directoryOfServiceItems = $directoryOfServiceAPI->getInfoByCompoundKey($oversizeItem['directoryOfServiceCompoundKey']);
			$directoryOfServiceItem = $directoryOfServiceItems[0];
			$position['directoryOfServiceItem'] = $directoryOfServiceItem;
			$position['totalWeight'] = $position['quantity'] * $directoryOfServiceItem['Weight'];
			$positions[] = $position;
		}

		$templateData['schema']['positions'] = $positions;

		return $templateData;
	}
}