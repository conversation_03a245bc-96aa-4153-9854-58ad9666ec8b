<html>
<?php $data = $this->data;?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Ladeliste PERI</title>
	</head>
	<body>
		<table>
			<tr>
				<th width="33%" align="left"><img src="<?=WEB_ROOT_API.'vendor\printouts\ladelisteLayherLogo.png'?>" alt="LadelisteLogo" class="companyLogo"></th>
			</tr>
		</table>
		<div class="row keep-together break-after">
		  <div class="column leftColumn">
			<table class="documentData" cellspacing="0">
				<tr>
					<td width="30%">Datum:</td>
					<td width="70%"><?=$data['date'];?></td>
				</tr>
				<tr>
					<td>Bauvorhaben:</td>
					<td><?=$data['projectName'];?></td>
				</tr>
				<tr>
					<td>Zuständiger BL:</td>
					<td><?=$data['projectManager'];?></td>
				</tr>
				<tr>
					<td>Kennzeichen:</td>
					<td><?=$data['Kennzeichnen'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="bohlen borders" cellspacing="0">
				<tr class="grey">
					<td width="50%">Unterlagen</td>
					<td align="center" width="25%">1,20</td>
					<td align="center" width="25%">kurz</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Unterlagen']['1,20'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Unterlagen']['kurz'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="spindeln borders" cellspacing="0">
				<tr class="grey smallFont">
					<td colspan="2">Spindeln</td>
					<td align="center">Lang</td>
					<td align="center">Kurz</td>
					<td align="center">Klipp</td>
					<td align="center">Monster</td>
					<td align="center">Rolle</td>
				</tr>
				<tr>
					<td colspan="2" align="center"></td>
					<td class="red"><?=$data['Ladeliste Layher']['Spindeln']['lang'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Spindeln']['kurz'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Spindeln']['Kipp'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Spindeln']['Monster'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Spindeln']['Rolle'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="rahmen borders" cellspacing="0">
				<tr class="grey">
					<td width="50%">Rahmen 0,70</td>
					<td width="12.5%" align="center">0,66</td>
					<td width="12.5%" align="center">1,00</td>
					<td align="center" width="12.5%">1,50</td>
					<td align="center" width="12.5%">2,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 0,70']['0,66'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 0,70']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 0,70']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 0,70']['2,00'];?></td>
				</tr>
				<tr>
					<td class="grey" align="left">Offene Rahmen 0,70</td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 0,70']['0,66'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 0,70']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 0,70']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 0,70']['2,00'];?></td>
				</tr>
				<tr>
					<td align="left" class="grey">Rahmen 1,00</td>
					<td align="center">0,66</td>
					<td align="center">1,00</td>
					<td align="center">1,50</td>
					<td align="center">2,00</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 1,00']['0,66'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 1,00']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 1,00']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rahmen 1,00']['2,00'];?></td>
				</tr>
				<tr>
					<td align="left" class="grey">Offene Rahmen 1,00</td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 1,00']['0,66'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 1,00']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 1,00']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Offene Rahmen 1,00']['2,00'];?></td>
				</tr>
			</table>
			
			
			<p></p>
			
			<table class="tunnelrahmen borders" cellspacing="0">
				<tr>
					<td class="grey smallFont" align="left" width="23%">Kragrahmen</td>
					<td class="red" width="10%"><?=$data['Ladeliste Layher']['Kragrahmen'];?></td>
					<td class="grey smallFont" align="left" width="23%">Tunnelrahmen</td>
					<td class="red" width="10%"><?=$data['Ladeliste Layher']['Tunnelrahmen'];?></td>
					<td class="grey smallFont" align="left" width="23%">Traufrahmen</td>
					<td class="red" width="10%"><?=$data['Ladeliste Layher']['Traufrahmen'];?></td>
				</tr>
			</table>

		  <p></p>
		  
		  <table class="borders smallerTable" cellspacing="0">
			  <tr>
				  <td rowspan="2" class="smallFont grey">Beläge</td>
				  <td class="grey" align="center">0,19</td>
				  <td class="noUpperBorder"></td>
				  <td class="grey" align="center">0,32</td>
				  <td class="noUpperBorder"></td>
				  <td class="grey" align="center">0,60</td>
			  </tr>
			  <tr>
				  <td class="grey" align="center">Stahl</td>
				  <td></td>
				  <td class="grey" align="center">Stahl</td>
				  <td></td>
				  <td class="grey" align="center">Alu</td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">0,73</td>
				  <td class="red smallFont"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['0,73'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['0,73'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['0,73'];?></td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">1,00</td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['1,00'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['1,00'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['1,00'];?></td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">1,50</td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['1,50'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['1,50'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['1,50'];?></td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">2,00</td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['2,00'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['2,00'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['2,00'];?></td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">2,50</td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['2,50'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['2,50'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['2,50'];?></td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">3,00</td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['3,00'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['3,00'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['3,00'];?></td>
			  </tr>
			  <tr>
				  <td align="right" class="grey">4,14</td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,19 Stahl']['4,14'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,32 Stahl']['4,14'];?></td>
				  <td></td>
				  <td class="red"><?=$data['Ladeliste Layher']['Beläge']['0,60 Alu']['4,14'];?></td>
			  </tr>
		  </table>
		  
		  <p></p>
		  
		  <table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left" width="55%">Durchstieg mit Leiter</td>
					<td align="center"  width="15%">2,00</td>
					<td align="center"  width="15%">2,50</td>
					<td align="center"  width="15%">3,00</td>
				</tr>
				<tr>
					<td class="red"></td>
					<td class="red"><?=$data['Ladeliste Layher']['Durchstieg mit Leiter']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Durchstieg mit Leiter']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Durchstieg mit Leiter']['3,00'];?></td>
				</tr>
			</table>
		  	
		  	<p></p>
		  	
			<table class="gelander borders" cellspacing="0">
				<tr class="grey">
					<td align="left" class="smallFont">Geländer</td>
					<td align="center" class="smallFont">0,70</td>
					<td align="center" class="smallFont">1,00</td>
					<td align="center" class="smallFont">1,50</td>
					<td align="center" class="smallFont">2,00</td>
					<td align="center" class="smallFont">2,50</td>
					<td align="center" class="smallFont">3,00</td>
					<td align="center" class="smallFont">4,14</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['3,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer']['4,14'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="gelander borders" cellspacing="0">
				<tr class="grey">
					<td align="left" class="smallFont">Bordbrett</td>
					<td align="center" class="smallFont">0,70</td>
					<td align="center" class="smallFont">1,00</td>
					<td align="center" class="smallFont">1,50</td>
					<td align="center" class="smallFont">2,00</td>
					<td align="center" class="smallFont">2,50</td>
					<td align="center" class="smallFont">3,00</td>
					<td align="center" class="smallFont">4,14</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['3,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bordbrett']['4,14'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable" cellspacing="0">
				<tr>
					<td class="grey" width="52%">Stirnbordbertt</td>
					<td class="grey" width="12%">0,70</td>
					<td width="12%" class="red"><?=$data['Ladeliste Layher']['Stirnbordbertt']['0,70'];?></td>
					<td class="grey" width="12%">1,09</td>
					<td width="12%" class="red"><?=$data['Ladeliste Layher']['Stirnbordbertt']['1,09'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left" width="55%">Diagonale</td>
					<td align="center"  width="15%">2,00</td>
					<td align="center"  width="15%">2,50</td>
					<td align="center"  width="15%">3,00</td>
				</tr>
				<tr>
					<td class="red"></td>
					<td class="red"><?=$data['Ladeliste Layher']['Diagonale']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Diagonale']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Diagonale']['3,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left">Anker</td>
					<td colspan="2" align="center">
						<span class="alignleft">WDVS</span>
						<span class="alignright">Anker</span>
					</td>
					<td align="center">0,30</td>
					<td align="center">0,65</td>
					<td align="center">1,00</td>
				</tr>
				<tr>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['Anker']['WDVS Anker'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Anker']['0,30'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Anker']['0,65'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Anker']['1,00'];?></td>
				</tr>
				<tr class="grey">
					<td colspan="2" align="center" class="smallFont">
					 	<span class="alignleft">Ringschrauben</span>
						<span class="alignright">120</span>
					</td>
					<td align="center" class="smallFont">160</td>
					<td align="center" class="smallFont">230</td>
					<td align="center" class="smallFont">300</td>
					<td align="center" class="smallFont">350</td>
				</tr>
				<tr>
					<td colspan="2" class="red" align="right"><?=$data['Ladeliste Layher']['Ringschrauben']['120'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Ringschrauben']['160'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Ringschrauben']['230'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Ringschrauben']['300'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Ringschrauben']['350'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left" width="70%">Absperrung</td>
					<td align="center" width="20%">1,00</td>
					<td align="center" width="10%">0,70</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Absperrung']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Absperrung']['0,70'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left" width="50%">Zwischenriegel</td>
					<td align="center" width="25%">schraubbar</td>
					<td align="center" width="25%">steckbar</td>
				</tr>
				<tr>
					<td align="left" class="grey">0,70</td>
					<td class="red"><?=$data['Ladeliste Layher']['Zwischenriegel']['schraubbar']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Zwischenriegel']['steckbar']['0,70'];?></td>
				</tr>
				<tr>
					<td align="left" class="grey">1,00</td>
					<td  class="red"><?=$data['Ladeliste Layher']['Zwischenriegel']['schraubbar']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Zwischenriegel']['steckbar']['1,00'];?></td>
				</tr>
			</table>
		  </div>
		  
		  <div class="column rightColumn">
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="40%"></td>
					<td width="12%" align="center">0,19</td>
					<td width="12%" align="center">0,30</td>
					<td width="12%" align="center">0,50</td>
					<td width="12%" align="center">0,70</td>
					<td width="12%" align="center">1,00</td>
				</tr>
				<tr>
					<td class="grey">Konsolen</td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,19']['Konsolen'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,30']['Konsolen'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,50']['Konsolen'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,70']['Konsolen'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['1,00']['Konsolen'];?></td>
				</tr>
				<tr>
					<td class="grey" align="left">Seckkonsole</td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,19']['Steckkonsolen'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,30']['Steckkonsolen'];?></td>
					<td class="noData"></td>
					<td class="noData"></td>
					<td class="noData"></td>
				</tr>
				<tr>
					<td class="grey" align="left">Konsolstreben</td>
					<td class="noData"></td>
					<td class="noData"></td>
					<td class="noData"></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,70']['Konsolenstreben'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['1,00']['Konsolenstreben'];?></td>				
				</tr>
				<tr>
					<td class="grey" align="left">Belagssicherung</td>
					<td class="noData"></td>
					<td class="noData"></td>
					<td class="noData"></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['0,70']['Belagsicherung'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Konsolen']['1,00']['Belagsicherung'];?></td>	
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td align="left class="smallFont""> 
						<span class="alignleft">Rohre</span>
						<span class="alignright">0,50</span>
					</td>
					<td align="center" class="smallFont">1,00</td>
					<td align="center" class="smallFont">1,50</td>
					<td align="center" class="smallFont">2,00</td>
					<td align="center" class="smallFont">2,50</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['0,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['2,50'];?></td>
				</tr>
				<tr class="grey">
					<td align="left" class="smallFont">
						<span class="alignleft">Rohre</span>
						<span class="alignright">3,00</span>
					</td>
					<td align="center" class="smallFont">3,50</td>
					<td align="center" class="smallFont">4,00</td>
					<td align="center" class="smallFont" >5,00</td>
					<td align="center" class="smallFont">6,00</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['3,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['3,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['4,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['5,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Rohre']['6,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td colspan="3" class="grey smallFont">U-Distanzkupplung</td>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['U-Distanzkupplung'];?></td>
				</tr>
				<tr>
					<td colspan="3" class="grey smallFont">Doppelkeilkopfkupplung</td>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['Doppelkeilkopfkupplung'];?></td>
				</tr>
				<tr>
					<td colspan="3" class="grey smallFont">Knotenblechkupplung</td>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['Knotenblechkupplung'];?></td>
				</tr>
				<tr>
					<td colspan="3" class="grey smallFont">Gitterträgerwinkelkupplung</td>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['Gitterträgerwinkelkupplung'];?></td>
				</tr>
				<tr>
					<td colspan="3" class="grey smallFont">Bordbrettkupplung</td>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['Bordbrettkupplung'];?></td>
				</tr>
				<tr class="grey">
					<td align="center" class="smallFont">Kupplung</td>
					<td align="center" class="smallFont">Teller</td>
					<td align="center" class="smallFont">GT</td>
					<td align="center" class="smallFont">NK</td>
					<td align="center" class="smallFont">DK</td>
					<td align="center" class="smallFont">Geländer</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Kupplung'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Teller K'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['GT'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['NK'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['DK'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländer'];?></td>
				</tr>
				<tr>
					<td class="grey" colspan="2">Zugfeste + Bolzen</td>
					<td class="red"><?=$data['Ladeliste Layher']['Zugfeste + Bolzen'];?></td>
					<td class="noBottomBorder noRightBorder"></td>
					<td class="noBottomBorder noRightBorder"></td>
					<td class="noBottomBorder noRightBorder"></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="40%"> 
						<span class="alignleft">Bohlen</span>
						<span class="alignright">1,00</span>
					</td>
					<td width="12%">1,50</td>
					<td width="12%">2,00</td>
					<td width="12%">2,50</td>
					<td width="12%">3,00</td>
					<td width="12%">3,50</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Bohlen']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bohlen']['1,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bohlen']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bohlen']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bohlen']['3,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Bohlen']['3,50'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="30%"></td>
					<td width="20%" align="center">1,00</td>
					<td width="20%" align="center">0,70</td>
					<td width="20%" align="center">Stummel-L</td>
				</tr>
				<tr>
					<td class="grey" class="smallFont">Geländerstütze</td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländerstütze']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländerstütze']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Geländerstütze']['Stummel-L'];?></td>
				</tr>
				<tr>
					<td class="grey smallFont">Innengeländerhalter</td>
					<td class="noData"></td>
					<td class="red"><?=$data['Ladeliste Layher']['Innengeländerhalter']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Innengeländerhalter']['Stummel-L'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey smallFont" width="28%">Dachfangnetze</td>
					<td class="grey bigFont" align="center" width="18%"><b>Blau</b></td>
					<td width="18%" class="red"><?=$data['Ladeliste Layher']['Dachfangschutz']['Dachfangnetze']['Blau'];?></td>
					<td class="grey bigFont" align="center" width="18%"><b>Rot</b></td>
					<td width="18%" class="red"><?=$data['Ladeliste Layher']['Dachfangschutz']['Dachfangnetze']['Rot'];?></td>
				</tr>
				<tr>
					<td colspan="2" class="grey">Blau Bendel</td>
					<td class="red"><?=$data['Ladeliste Layher']['Blau Bendel'];?></td>
					<td colspan="2"></td>
				</tr>
				<tr class="grey">
					<td colspan="2"></td>
					<td>mit Loch</td>
					<td colspan="2">normal</td>
				</tr>
				<tr>
					<td colspan="2" class="grey">Schutzgitterstütze</td>
					<td class="red"><?=$data['Ladeliste Layher']['Schutzgitterstütze']['mit Loch'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Schutzgitterstütze']['normal'];?></td>
				</tr>
				
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td width="30%" class="grey smallFont">Schutzdachträger</td>
					<td align="center" class="grey" width="15%"></td>
					<td width="55%" class="red"><?=$data['nonHeadlines']['Schutzdachträger'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="50%"> 
						<span class="alignleft">Doka-Träger</span>
						<span class="alignright">3,90</span>
					</td>
					<td align="center">4,90</td>
					<td align="center">5,90</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Doka - Träger']['3,90'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Doka - Träger']['4,90'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Doka - Träger']['5,90'];?></td>
				</tr>
			</table>			
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="33%">
						<span class="alignleft">GT - Stahl</span>
						<span class="alignright">3-4</span>
					</td>
					<td align="center">6,00</td>
					<td align="center">5,14</td>
					<td align="center">6,14</td>
					<td align="center">7,71</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT - Stahl']['3-4'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT - Stahl']['6,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT - Stahl']['5,14'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT - Stahl']['6,14'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT - Stahl']['7,71'];?></td>
				</tr>
			</table>	
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="33%"> 
						<span class="alignleft">GT - Alu</span>
						<span class="alignright">3-4</span>
					</td>
					<td align="center">6,00</td>
					<td align="center">5,14</td>
					<td align="center">6,14</td>
					<td align="center">8,00</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT Alu']['3-4'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT Alu']['6,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT Alu']['5,14'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT Alu']['6,14'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT Alu']['8,00'];?></td>
				</tr>
			</table>
		
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr class="grey">
					<td width="80%">
						<span class="alignleft">GT-U-Riegel</span>
						<span class="alignright">0,70</span>
					</td>
					<td align="center">1,00</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT-U-Riegel']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT-U-Riegel']['1,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey" width="40%">GT Verbinder</td>
					<td width="20%" class="red"><?=$data['Ladeliste Layher']['GT Verbinder'];?></td>
					<td class="grey" width="40%">GT Wandschuhe</td>
					<td width="20%" class="red"><?=$data['Ladeliste Layher']['GT Wandschuhe'];?></td>
				</tr>
				<tr>
					<td class="grey">GT Schrauben</td>
					<td class="red"><?=$data['Ladeliste Layher']['GT Schrauben'];?></td>
					<td class="noData"></td>
					<td class="noData"></td>
				</tr>
				<tr class="grey">
					<td colspan="3">
						<span class="alignleft">GT Auflagerriegel</span>
						<span class="alignright">0,70</span>
					</td>
					<td align="center">1,00</td>
				</tr>
				<tr>
					<td colspan="3" class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT-Auflagerriegel']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Gitterträger']['GT-Auflagerriegel']['1,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey" width="80%">
						<span class="alignleft">Staubschutznetz</span>
						<span class="alignright">2,50</span>
					</td>
					<td class="grey" align="center">3,00</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Staubschutznetz']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Staubschutznetz']['3,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey" width="80%">
						<span class="alignleft">Sandstrahlnetze</span>
						<span class="alignright">2,50</span>
					 </td>
					<td class="grey" align="center">3,00</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Sandstrahlnetze']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Sandstrahlnetze']['3,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey" width="80%">
						<span class="alignleft">Folie</span>
						<span class="alignright">2,50</span>
					</td>
					<td class="grey" align="center">3,00</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Folie']['2,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Folie']['3,00'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="fullwidth">
					<tr><td class="orange" align="left">Achtung Rückseite beachten</td></tr>
				</table>
		  </div>
		</div>
		
		<!-- Start of the second page-->
		<div class="row keep-together">
			<div class="column leftColumn">
				<table class="fullwidth">
					<tr><td class="orange" align="left">Allround</td></tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr class="grey">
						<td align="left" width="25%">Stiele</td>
						<td align="center">0,50</td>
						<td align="center">1,00</td>
						<td align="center">1,50</td>
						<td align="center">2,00</td>
						<td align="center">2,50</td>
						<td align="center">3,00</td>
						<td align="center">4,00</td>
					</tr>
					<tr>
						<td width="20%"></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['0,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['1,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['1,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['2,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['2,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['3,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele']['4,00'];?></td>
					</tr>
					<tr>
						<td width="20%" class="grey">Stiele ohne Z.</td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['0,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['1,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['1,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['2,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['2,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['3,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Stiele ohne Z.']['4,00'];?></td>
					</tr>
					
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr class="grey">
						<td align="left" width="25%">U-Riegel</td>
						<td align="center">0,30</td>
						<td align="center">0,70</td>
						<td align="center">1,00</td>
						<td align="center">1,50</td>
						<td align="center">2,00</td>
						<td align="center">2,50</td>
						<td align="center">3,00</td>
					</tr>
					<tr>
						<td></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['0,30'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['0,70'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['1,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['1,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['2,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['2,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['U-Riegel']['3,00'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td align="center" width="25%" class="grey smallFont">
							<span class="alignleft">Diagonale</span>
							<span class="alignright">0,73</span>
						</td>
						<td align="center" class="grey smallFont">1,09</td>
						<td align="center" class="grey smallFont">1,57</td>
						<td align="center" class="grey smallFont">2,00</td>
						<td align="center" class="grey smallFont">2,57</td>
						<td align="center" class="grey smallFont">3,07</td>
						<td class="noBottomBorder noRightBorder noUpperBorder"></td>
						<td class="noBottomBorder noRightBorder noUpperBorder"></td>
					</tr>
					<tr>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Diagonale']['0,73'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Diagonale']['1,09'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Diagonale']['1,57'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Diagonale']['2,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Diagonale']['2,57'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Diagonale']['3,07'];?></td>
						<td class="noBottomBorder noRightBorder"></td>
						<td class="noBottomBorder noRightBorder"></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr class="grey">
						<td align="left" width="25%">Riegel</td>
						<td align="center">0,30</td>
						<td align="center">0,70</td>
						<td align="center">1,00</td>
						<td align="center">1,50</td>
						<td align="center">2,00</td>
						<td align="center">2,50</td>
						<td align="center">3,00</td>
					</tr>
					<tr>
						<td></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['0,30'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['0,70'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['1,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['1,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['2,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['2,50'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Riegel']['3,00'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td align="center" width="25%" class="grey smallFont">Bordbretter</td>
						<td class="grey">0,70</td>
						<td align="center" class="grey smallFont">1,09</td>
						<td align="center" class="grey smallFont">1,57</td>
						<td align="center" class="grey smallFont">2,00</td>
						<td align="center" class="grey smallFont">2,57</td>
						<td align="center" class="grey smallFont">3,07</td>
						<td class="noBottomBorder noRightBorder noUpperBorder"></td>
						<td class="noBottomBorder noRightBorder noUpperBorder"></td>
					</tr>
					<tr>
						<td></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Bordbretter']['0,70'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Bordbretter']['1,09'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Bordbretter']['1,57'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Bordbretter']['2,00'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Bordbretter']['2,57'];?></td>
						<td class="red"><?=$data['Ladeliste Layher']['Allround']['Bordbretter']['3,07'];?></td>
						<td class="noBottomBorder noRightBorder"></td>
						<td class="noBottomBorder noRightBorder"></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="fullwidth">
					<tr><td class="orange" align="left">Zubehör</td></tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
					<tr>
						<td width="40%" class="grey" align="left">Durchstiegsleiter</td>
						<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Durchstiegsleiter'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders steigleiter">
					<tr>
						<td width="40%" class="grey">
							<span class="alignleft">Steigleiter</span>
							<span class="alignright">3,00</span>
						</td>
						<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Steigleiter']['3,00'];?></td>
						<td width="40%" class="grey">
							<span class="alignleft">Steigleiter</span>
							<span class="alignright">4,00</span>
						</td>
						<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Steigleiter']['4,00'];?></td>				
					</tr>
					<tr>
						<td class="grey smallFont" colspan="2">Rohrverbinder für U-Profil</td>
						<td class="grey smallFont" colspan="2">Pisser</td>
					</tr>
					<tr>
						<td colspan="2" class="red" align="center"><?=$data['Ladeliste Layher']['Zubehör']['Rohrverbinder für U-Profil'];?></td>
						<td colspan="2" class="red" align="center"><?=$data['Ladeliste Layher']['Zubehör']['Pisser'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders steigleiter">
					<tr class="grey">
						<td width="40%">Bretter</td>
<?php 						foreach ($data['Ladeliste Layher']['Zubehör']['Bretter'] as $k=>$v) { ?>
<?php							echo '<td align="center">'.$k.'</td>' ?>
<?php 						} ?>
					</tr>
					<tr>
					<td></td>
<?php 						foreach ($data['Ladeliste Layher']['Zubehör']['Bretter'] as $k=>$v) { ?>
<?php							echo '<td class="red">'.$v.'</td>' ?>
<?php 						} ?>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders steigleiter">
					<tr>
						<td width="40%" class="grey">Firmenschild klein</td>
						<td width="20%" class="red" align="center"><?=$data['Ladeliste Layher']['Zubehör']['Firmenschild']['klein'];?></td>
						<td width="40%" class="grey">Firmenschild groß</td>
						<td width="20%" class="red" align="center"><?=$data['Ladeliste Layher']['Zubehör']['Firmenschild']['groß'];?></td>
					</tr>
				</table>
				
				<p></p>
				
				<table class="borders" cellspacing="0">
				<tr>
					<td width="75%" align="left" class="grey">Bauschutztür</td>
					<td width="25%" class="red"><?=$data['Ladeliste Layher']['Zubehör']['Bauschutztür'];?></td>
				</tr>
				<tr>
					<td width="75%" align="left" class="grey">Bauzaun</td>
					<td width="25%" class="red"><?=$data['Ladeliste Layher']['Zubehör']['Bauzaun'];?></td>
				</tr>
				<tr>
					<td width="75%" align="left" class="grey">Stein</td>
					<td width="25%" class="red"><?=$data['Ladeliste Layher']['Zubehör']['Stein'];?></td>
				</tr>
			</table>
				<p></p>
				
				<div class="bemerkungenTitle"><b>Bemerkungen</b></div>
				
				<p></p>
				
				<div class="bemerkungen"><?=$data['Ladeliste Layher']['Bemerkungen'];?></div>
				
			</div>
			<div class="column rightColumn">
			
			<table class="borders steigleiter">
				<tr class="grey">
					<td width="65%" align="center">
						<span class="alignleft">Anfänger</span>
						<span class="alignright">kurz</span>
					</td>
					<td width="35%" align="center">lang</td>
					
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Anfänger']['kurz'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Anfänger']['lang'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders steigleiter">
				<tr class="grey">
					<td width="50%" align="left">Konsolen</td>
					<td align="center">0,30</td>
					<td align="center">0,50</td>
					<td align="center">0,70</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Konsolen']['0,30'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Konsolen']['0,50'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Konsolen']['0,70'];?></td>
				</tr>
			</table>

			<p></p>
			
			<table class="borders steigleiter">
				<tr class="grey">
					<td width="50%" align="left"> 
						<span class="alignleft">Belagsicherung</span>
						<span class="alignright">0,70</span>
					</td>
					<td align="center">1,00</td>
					<td align="center">1,50</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Belagsicherung']['0,70'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Belagsicherung']['1,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Belagsicherung']['1,50'];?></td>
				</tr>
				<tr class="grey">
					<td align="left">
						<span class="alignleft">Belagsicherung</span>
						<span class="alignright">2,00</span>
					</td>
					<td align="center">2,57</td>
					<td align="center">3,07</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Belagsicherung']['2,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Belagsicherung']['2,57'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Belagsicherung']['3,07'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders">
				<tr>
					<td width="80%" class="grey">
						<span class="alignleft">Podesttreppe</span>
						<span class="alignright">3,00</span>
					</td>
					<td width="20%" class="grey" align="center">2,50</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Podesttreppe']['3,00'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Podesttreppe']['2,50'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders" cellspacing="0">
				<tr>
					<td class="grey smallFont" align="left" width="28%">Treppengeländer</td>
					<td align="center" class="grey">2,57</td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Treppengeländer']['2,57'];?></td>
					<td align="center" class="grey">3,07</td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Treppengeländer']['3,07'];?></td>
				</tr>
				<tr>
					<td class="grey smallFont" align="left">Treppe steckbar</td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Treppe steckbar'];?></td>
					<td colspan="3" class="noRightBorder noBottomBorder"></td>
				</tr>
				<tr>
					<td class="grey smallFont" align="left">Treppenpfosten</td>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Treppenpfosten'];?></td>
					<td colspan="3" class="noRightBorder"></td>
				</tr>
				<tr>
					<td class="grey smallFont" align="center">Anfang-</td>
					<td colspan="2" class="grey smallFont" align="center">End-</td>
					<td colspan="2" class="grey smallFont" align="center">Zwischenteil</td>
				</tr>
				<tr>
					<td class="red"><?=$data['Ladeliste Layher']['Allround']['Anfang-'];?></td>
					<td colspan="2" class="red" align="center"><?=$data['Ladeliste Layher']['Allround']['End-'];?></td>
					<td colspan="2" class="red" align="center"><?=$data['Ladeliste Layher']['Allround']['Zwischenteil'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable">
				<tr>
					<td align="left" class="grey" width="70%">Schaltafeln</td>
					<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Schaltafeln'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable">
				<tr>
					<td align="left" class="grey" width="70%">Kabelbinder</td>
					<td align="center" class="grey">dick</td>
					<td align="center" class="grey">dünn</td>
				</tr>
				<tr>
					<td></td>
					<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Kabelbinder']['dick'];?></td>
					<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Kabelbinder']['dünn'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable">
				<tr>
					<td align="left" class="grey" width="70%">Spax</td>
					<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Spax'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable">
				<tr>
					<td align="left" class="grey" width="70%">Nägel</td>
					<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Nägel'];?></td>
				</tr>
			</table>
			
			<p></p>
			
			<table class="borders smallerTable">
				<tr>
					<td align="left" class="grey" width="70%">Fallstecker</td>
					<td class="red"><?=$data['Ladeliste Layher']['Zubehör']['Fallstecker'];?></td>
				</tr>
			</table>
		  </div>
		</div>
		
	</body>
</html>

<style>
* 	{
  		box-sizing: border-box;
	}

	/* Create two equal columns that floats next to each other */
	.column {
	  float: left;
	  width: 50%;
	  padding: 10px;
	  height: 300px; /* Should be removed. Only for demonstration */
	}

	/* Clear floats after the columns */
	.row:after {
	  content: "";
	  display: table;
	  clear: both;
	}
	
	.noData {background-color:lightgrey;}
	.companyLogo {padding: 10px; height:80%; width:auto;}
	.borders, .borders th, .borders td {border:1px solid black;}
	.borders {width:100%;  border-collapse: collapse;}
	.basisrahmen {width:100%;}
	.fullwidth {width:100%;}
	.grey {background-color:lightgrey;}
	.red {color:red; text-align:right!important;}
	table {table-layout:fixed!important;}
	.smallerTable {width:75%!important;}
	.half {width:50%!important;}
	.orange {background-color: orange;}
	.documentData {border-collapse: collapse; width:100%;}
	.noBottomBorder {border-bottom: 1px solid white!important;}
	.noRightBorder {border-right: 1px solid white!important;}
	.noLeftBorder {border-left: 1px solid white!important;}
	.noUpperBorder {border-top: 1px solid white !important;}
	.smallFont {font-size:small;}
	.smallerFont {font-size: 12px;}
	.keep-together {
    	page-break-inside: avoid;
	}
	
	.break-after {
    	page-break-after: always;
	}
	
	.bemerkungenTitle {font-size: 22px; font-family:Helvetica;}
	.bemerkungen {width:200%; border:1px solid black;}
	.bigFont {font-size:large;}
	
	.steigleiter {width:90%;}
	
	.bemerkungenTitle {font-size: 22px; font-family:Helvetica;}
	.bemerkungen {width:200%; border:1px solid black;}
	
	.alignleft {
		float: left;
	}
	
	.alignright {
		float: right;
	}
	
	  p{
        margin-top: 5px;
        margin-bottom: 5px;
        margin-left: 5px;
        margin-right: 5px;
   }
	
</style>