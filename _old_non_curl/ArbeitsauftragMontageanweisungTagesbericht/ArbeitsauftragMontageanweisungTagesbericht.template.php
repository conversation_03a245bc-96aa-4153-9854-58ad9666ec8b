<html>

<?php
	$data = $this->data;
	
	function renderBoolCheckbox($checkboxTitle, $dbValue) {
	    if(is_array($dbValue))
	        $dbValue = $dbValue[0];
		if(strtolower($dbValue) == 'ja' || $dbValue == '1' || $dbValue == 'true' || $dbValue== 'on') {
			return '<img src="'.WEB_ROOT_API.'vendor\printouts\ArbeitsauftragMontageanweisungTagesbericht\checkedCropped.png?>"'.'alt="checked" width="auto" height="10%">'.PHP_EOL.$checkboxTitle;
		} else {
			return '<img src="'.WEB_ROOT_API.'vendor\printouts\ArbeitsauftragMontageanweisungTagesbericht\uncheckedCropped.png?>"'.'alt="checked" width="auto" height="10%">'.PHP_EOL.$checkboxTitle;
		}
	}
	
	function renderCheckbox($checkboxTitle, $dbValue) {
		if(
		        $dbValue === true
            ||
		        (!is_array($dbValue) && strtolower($checkboxTitle) == strtolower($dbValue))
            ||
                (is_array($dbValue) && in_array($checkboxTitle,$dbValue))
        ) {
			return '<img src="'.WEB_ROOT_API.'vendor\printouts\ArbeitsauftragMontageanweisungTagesbericht\checkedCropped.png?>"'.'alt="checked" width="auto" height="10%">'.PHP_EOL.$checkboxTitle;
		} else {
			return '<img src="'.WEB_ROOT_API.'vendor\printouts\ArbeitsauftragMontageanweisungTagesbericht\uncheckedCropped.png?>"'.'alt="checked" width="auto" height="10%">'.PHP_EOL.$checkboxTitle;
		}
	}
	
	function renderKonsolenCheckbox($checkboxTitle, $dbValue) {
		if(strtolower($checkboxTitle) == strtolower($dbValue)) {
			return '<img src="'.WEB_ROOT_API.'vendor\printouts\ArbeitsauftragMontageanweisungTagesbericht\checkedCropped.png?>"'.'alt="checked" width="auto" height="10%">'.PHP_EOL.$checkboxTitle;
		} else {
			return '<img src="'.WEB_ROOT_API.'vendor\printouts\ArbeitsauftragMontageanweisungTagesbericht\uncheckedCropped.png?>"'.'alt="checked" width="auto" height="10%">'.PHP_EOL.$checkboxTitle;
		}
	}
	
?>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
		<title>Arbeitsauftrag/Montageanweisung + Tagesbericht</title>
	</head>
	<body>
		<table class="title bigFont">
			<tr>
				<th width="50%">Arbeitsauftrag/Montageanweisung</th>
				<th width="30%">Gerüstbau Bröder</th>
				<th width="20%">Rang:</th>
			</tr>
		</table>
		
		<br>
		
		<table class="topData">
			<tbody>
				<tr>
					<td width="60%" align="left">
						Bauleiter: 
						<?=$data['projectManager'];?>
					</td>
					<td width="40%" align="left"> 
						Datum: <?=date('d.m.Y', strtotime($data['woOrProjectData']['start_date']));?>
					</td>
				</tr>
				<tr>
					<td align="left">Kolonnenführer: <?=$data['woTeamLeader'];?></td>
					<td align="left">
						Fahrzeug:	
						<?php $strResources = ''; foreach ($data['resources'] as $kRes=>$vRes) { ?>
						<?php $strResources .= trim($vRes['kurzname']).',' ?>
						<?php } $strResources = rtrim($strResources, ','); ?>
						<?= $strResources; ?>
					</td>
				</tr>
				<tr>
					<td align="left">
						Kolonne
						<?php $strTeam = ''; foreach ($data['woOrProjectData']['team'] as $k=>$v) { ?>
						<?php $strTeam .= trim($v['firstName'].' '.$v['lastName']).',' ?>
						<?php } $strTeam = rtrim($strTeam, ','); ?>
						<?= $strTeam; ?>
					</td>
					<td align="left">Fahrer: <?=$data['teamDriver'];?></td>
				</tr>
				<tr>
					<td colspan="2" align="left">Bauvorhaben: <?=$data['woOrProjectData']['projectName'].' '.$data['projectAddress'];?></td>
				</tr>
				<tr>
					<td align="left">Auftraggeber: <?=$data['customerDate']['displayName'];?></td>
					<td align="left">Bauleiter/Polier (AG)</td>
				</tr>
				<tr>
					<td colspan="2">geplante Arbeiten
						<?=renderBoolCheckbox('Rohbau', $data['Rohbau']);?>
						<?=renderBoolCheckbox('Hausstellung', $data['Hausstellung']);?>
						<?=renderBoolCheckbox('Dach', $data['Dach']);?>
						<?=renderBoolCheckbox('Fassade', $data['Fassade']);?>
						<?=renderBoolCheckbox('Sonstiges', $data['Sonstiges']);?>
					</td>
				</tr>
			</tbody>
		</table>
		<br>
		<table class="mainData">
			<tbody>
				<tr>
					<td align="left" width="50%">
						Datum: <?=date('d.m.Y H:i',strtotime($data['Arbeitsauftrag/Montageanweisung']['createdOn']));?> Uhr
					</td>
					<td colspan="3" align="left">
						Maßnahme: <?=$data['woOrProjectData']['kurztext']?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">
						<?=renderCheckbox('Schutzgerüst', $data['Arbeitsauftrag/Montageanweisung']['Gerüst']);?>
						<?=renderCheckbox('Arbeitsgerüst', $data['Arbeitsauftrag/Montageanweisung']['Gerüst']);?>
						<?=renderCheckbox('Fassadengerüst', $data['Arbeitsauftrag/Montageanweisung']['Gerüst']);?>
						<?=renderCheckbox('Fanggerüst', $data['Arbeitsauftrag/Montageanweisung']['Gerüst']);?>
						<?=renderCheckbox('Dachfanggerüst', $data['Arbeitsauftrag/Montageanweisung']['Gerüst']);?>
						<?=renderCheckbox('Sondergerüst', $data['Arbeitsauftrag/Montageanweisung']['Gerüst']);?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">
						Aufbau nach:
						<?=renderCheckbox('Regelausführung', $data['Arbeitsauftrag/Montageanweisung']['Aufbau nach']);?>
						<?=renderCheckbox('statischer Nachweis', $data['Arbeitsauftrag/Montageanweisung']['Aufbau nach']);?>
						<?=renderCheckbox('Sonstiges', $data['Arbeitsauftrag/Montageanweisung']['Aufbau nach']);?>
                        <?= $data['Arbeitsauftrag/Montageanweisung']['Aufbau nach->Sonstiges']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						Lastklasse
						<?=renderCheckbox('3 (2 kN/m2)', $data['Arbeitsauftrag/Montageanweisung']['Lastklasse']);?>
						<?=renderCheckbox('4 (3 kN/m2)', $data['Arbeitsauftrag/Montageanweisung']['Lastklasse']);?>
						<?=renderCheckbox('Sonstiges', $data['Arbeitsauftrag/Montageanweisung']['Lastklasse']);?>
						<?= $data['Arbeitsauftrag/Montageanweisung']['Lastklasse->Sonstiges']?>
						<br>
                        Breitenklasse
						<?=renderCheckbox('W06 (SL70)', $data['Arbeitsauftrag/Montageanweisung']['Breitenklasse']);?>
						<?=renderCheckbox('W09 (SL100)', $data['Arbeitsauftrag/Montageanweisung']['Breitenklasse']);?>
						<?=renderCheckbox('Modul', $data['Arbeitsauftrag/Montageanweisung']['Breitenklasse']);?>
						<?=renderCheckbox('Sonstiges', $data['Arbeitsauftrag/Montageanweisung']['Breitenklasse']);?>
						<?= $data['Arbeitsauftrag/Montageanweisung']['Breitenklasse->Sonstiges']?>
					</td>
					<td colspan="2" rowspan="16" align="center" valign="top">
						<!-- Skizze/Seitenbeschreibung --!>
						Skizze/Seitenbeschreibung
						<?php if($data['Arbeitsauftrag/Montageanweisung']['Skizze']) { ?>
							<img src="<?=$data['Arbeitsauftrag/Montageanweisung']['Skizze']?>" alt="skizze" class="skizze">
						<?php } ?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						Aufstandsfläche:
						<?=renderCheckbox('gerade', $data['Arbeitsauftrag/Montageanweisung']['Aufstandsfläche']);?>
						<?=renderCheckbox('schräg', $data['Arbeitsauftrag/Montageanweisung']['Aufstandsfläche']);?>
						<?=renderCheckbox('Sonstiges', $data['Arbeitsauftrag/Montageanweisung']['Aufstandsfläche']);?>
						<?= $data['Arbeitsauftrag/Montageanweisung']['Aufstandsfläche->Sonstiges']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						Wandabstand gemessen von:<?= $data['Arbeitsauftrag/Montageanweisung']['Wandabstand gemessen von'][0] ?>
                        <br>
						<?=renderCheckbox('30 cm', $data['Arbeitsauftrag/Montageanweisung']['Wandabstand']);?>
						<?=renderCheckbox('Sonstiges', $data['Arbeitsauftrag/Montageanweisung']['Wandabstand']);?>
						<?= $data['Arbeitsauftrag/Montageanweisung']['Wandabstand->Sonstiges']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						Standhöhe: <?= $data['Arbeitsauftrag/Montageanweisung']['Standhöhe'][0]?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						Zugang
						<?=renderCheckbox('Leiter', $data['Arbeitsauftrag/Montageanweisung']['Zugang']['Zugang']);?>
						<?=renderCheckbox('Leitergang', $data['Arbeitsauftrag/Montageanweisung']['Zugang']['Zugang']);?>
						<?=renderCheckbox('Treppe', $data['Arbeitsauftrag/Montageanweisung']['Zugang']['Zugang']);?>
                        Seite <?= $data['Arbeitsauftrag/Montageanweisung']['Zugang']['Seite'][0]?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?php
                        $checked = is_array($data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']);
                        echo renderCheckbox('Kons.',
                            $checked);
                        if($checked)
							echo $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Tiefe (in cm)'][0]." cm";
                        ?>
						<?=renderCheckbox('mit Horn', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Konsole']);?>
						<?=renderCheckbox('ohne Horn', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Konsole']);?>
						<?=renderCheckbox('innen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Konsole']);?>
						<?=renderCheckbox('aussen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Konsole']);?>
                        <br>
						<?=renderCheckbox('zum Hängen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Konsole']);?>
						<?=renderCheckbox('zum Schrauben', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Konsole']);?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?='Seite: '. $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 1']['Seite'][0]?>
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<?php
						$checked = is_array($data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']);
						echo renderCheckbox('Kons.',
							$checked);
						if($checked)
							echo $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Tiefe (in cm)'][0]." cm";
                        ?>
						<?=renderCheckbox('mit Horn', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Konsole']);?>
						<?=renderCheckbox('ohne Horn', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Konsole']);?>
						<?=renderCheckbox('innen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Konsole']);?>
						<?=renderCheckbox('aussen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Konsole']);?>
                        <br>
						<?=renderCheckbox('zum Hängen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Konsole']);?>
						<?=renderCheckbox('zum Schrauben', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Konsole']);?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?='Seite: '. $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 2']['Seite'][0]?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?php
						$checked = is_array($data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']);
						echo renderCheckbox('Kons.',
							$checked);
						if($checked)
							echo $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Tiefe (in cm)'][0]." cm";
						?>
						<?=renderCheckbox('mit Horn', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Konsole']);?>
						<?=renderCheckbox('ohne Horn', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Konsole']);?>
						<?=renderCheckbox('innen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Konsole']);?>
						<?=renderCheckbox('aussen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Konsole']);?>
                        <br>
						<?=renderCheckbox('zum Hängen', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Konsole']);?>
						<?=renderCheckbox('zum Schrauben', $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Konsole']);?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?='Seite: '. $data['Arbeitsauftrag/Montageanweisung']['Konsolen']['Konsole 3']['Seite'][0]?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?=renderBoolCheckbox('Konsolrahmen', $data['Arbeitsauftrag/Montageanweisung']['Zusatzteile']['Konsolrahmen']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['Konsolrahmen->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?=renderBoolCheckbox('Versatzrahmen', $data['Arbeitsauftrag/Montageanweisung']['Zusatzteile']['Versatzrahmen']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['Versatzrahmen->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?=renderBoolCheckbox('Innengeländer', $data['Arbeitsauftrag/Montageanweisung']['Zusatzteile']['Innengeländer']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['Innengeländer->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?=renderBoolCheckbox('4-Geländer', $data['Arbeitsauftrag/Montageanweisung']['Zusatzteile']['4-Geländer']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['4-Geländer->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="2" align="left">
						<?=renderBoolCheckbox('DDSN', $data['Arbeitsauftrag/Montageanweisung']['Zusatzteile']['Dachdeckerschutznetz (DDSN)']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['Dachdeckerschutznetz (DDSN)->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">
						<?=renderBoolCheckbox('Verankerung', $data['Arbeitsauftrag/Montageanweisung']['Verankerung']['Verankerung']);?>
                        <?=renderBoolCheckbox('WDVS-Anker', $data['Arbeitsauftrag/Montageanweisung']['Verankerung']['WDVS-Anker']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['WDVS-Anker->Seite']?>
						<?=renderBoolCheckbox('Abstützen', $data['Arbeitsauftrag/Montageanweisung']['Verankerung']['Abstützen']);?>
                        - Seite <?= $data['Arbeitsauftrag/Montageanweisung']['Abstützen->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="3" align="left">
						<?=renderBoolCheckbox('Gitterträger', $data['Arbeitsauftrag/Montageanweisung']['Gitterträger / Brücke']['Gitterträger']);?>
                        Länge m: <?= $data['Arbeitsauftrag/Montageanweisung']['Gitterträger->Länge m']?>
                        - Seite: <?= $data['Arbeitsauftrag/Montageanweisung']['Gitterträger->Seite']?>
					</td>
					<td align="left">
						<?=renderBoolCheckbox('Brücke', $data['Arbeitsauftrag/Montageanweisung']['Gitterträger / Brücke']['Brücke']);?>
                        - Seite: <?= $data['Arbeitsauftrag/Montageanweisung']['Brücke->Seite']?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">
						<?=renderBoolCheckbox('Plane', $data['Arbeitsauftrag/Montageanweisung']['Bekleidung']['Plane']);?>
						<?=renderBoolCheckbox('Netz', $data['Arbeitsauftrag/Montageanweisung']['Bekleidung']['Netz']);?>
						Sonstiges: <?=$data['Arbeitsauftrag/Montageanweisung']['Bekleidung']['Sonstiges'][0];?>
						- Seite : <?=$data['Arbeitsauftrag/Montageanweisung']['Bekleidung']['Seite'][0];?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">
						<?=renderBoolCheckbox('öffentl. Verkehsraum', $data['Arbeitsauftrag/Montageanweisung']['Verkehrssicherung']['öffentl. Verkehsraum']);?>
						<?=renderBoolCheckbox('Straße', $data['Arbeitsauftrag/Montageanweisung']['Verkehrssicherung']['Straße']);?>
						<?=renderBoolCheckbox('Gehweg', $data['Arbeitsauftrag/Montageanweisung']['Verkehrssicherung']['Gehweg']);?>
						<?=renderBoolCheckbox('Sperrung durch Bröder / AG', $data['Arbeitsauftrag/Montageanweisung']['Verkehrssicherung']['Sperrung durch Bröder / AG']);?>
						<?=renderBoolCheckbox('Warnbake/Blinklicht', $data['Arbeitsauftrag/Montageanweisung']['Verkehrssicherung']['Warnbake/Blinklicht']);?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">
							<?=renderBoolCheckbox('Helm', $data['Arbeitsauftrag/Montageanweisung']['Arbeitssicherheit']['Helm']);?>
							<?=renderBoolCheckbox('PSAgA', $data['Arbeitsauftrag/Montageanweisung']['Arbeitssicherheit']['PSAgA']);?>
							<?=renderBoolCheckbox('MSG(im Aufstiegsfeld)', $data['Arbeitsauftrag/Montageanweisung']['Arbeitssicherheit']['MSG (im Aufstiegsfeld)']);?>
							<?=renderBoolCheckbox('MSG (komplett)', $data['Arbeitsauftrag/Montageanweisung']['Arbeitssicherheit']['MSG (komplett)']);?>
							<?=renderBoolCheckbox('Warnweste', $data['Arbeitsauftrag/Montageanweisung']['Arbeitssicherheit']['Warnweste']);?>
							<?=renderBoolCheckbox('Schutzbrille', $data['Arbeitsauftrag/Montageanweisung']['Arbeitssicherheit']['Schutzbrille']);?>
					</td>
				</tr>
				<tr>
					<td colspan="4" align="left">Bemerkungen: <?=$data['Arbeitsauftrag/Montageanweisung']['Bemerkungen']['Bemerkungen'][0];?></td>
				</tr>
			</tbody>
		</table>
			<br>
			<table class="bottomData">
				<tr>
					<td align="left"><?=renderBoolCheckbox('Stundenarbeiten', isset($data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']));?></td>
					<td align="right">Datum/Uhrzeit(von-bis):
						<?=$data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['von'].'-'.$data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['bis'];?>
					</td>
				</tr>
				<tr>
					<td align="left" colspan="2" class="paddingLeft">welche Arbeiten: <?=$data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['Welche Arbeiten?'][0];?></td>
				</tr>
				<tr>
					<td align="left" class="paddingLeft">wer beauftragt: <?=$data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['Wer hat es beauftragt?'][0];?></td>
					<td align="right" class="paddingRight">
						Unterschrift Auftraggeber 
						<?php if(file_exists($data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['Unterschrift Auftraggeber'])) { ?>
							<img src="<?=$data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['Unterschrift Auftraggeber']?>" alt="photo" class="image">
						<?php } ?>
						</td>
				</tr>
				<tr>
					<td align="left" class="paddingLeft">
					Anzahl Monteure:
						<?php if(file_exists($data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['Anzahl Monteure'])) { ?>
							<img src="<?=$data['Arbeitsauftrag/Montageanweisung']['Stundenlohnarbeiten']['Anzahl Monteure']?>" alt="photo" class="image">
						<?php } ?>
					</td>
					<td align="right" class="paddingRight">Zeiten anerkannt</td>
				</tr>
			</table>
		<br>
		<div class="pageTwo">
			<div class="bigFont" align="center"><b>Gerüstbau Bröder</b></div>
			<br>
			<table class="secondPageTable">
				<tbody>
					<tr>
						<td colspan="2" rowspan="2" align="left" class="paddingLeft"  width="30%">BV: <?=$data['woOrProjectData']['project_name'];?></td>
						<td colspan="3" align="center" width="40%"><b>Tagesbericht</b></td>
						<td rowspan="2" align="left" class="paddingLeft width="30%">AG: <?=$data['customerData']['name'];?></td>
					</tr>
					<tr>
						<td colspan="2" align="center" class="paddingLeft">Projekt-Nr.: <?=$data['woOrProjectData']['ktr'];?></td>
						<td align="left" class="paddingLeft">Nr. 1</td>
					</tr>
					<tr>
						<td align="center" width="20%">WANN</td>
						<td align="center" width="20%">WER</td>
						<td align="center" width="20%">KFZ</td>
						<td align="center" width="20%">VON-BIS</td>
						<td colspan="2" align="center" width="20%">WAS (genaue Beschreibung mit Seiten)</td>
					</tr>
					<?php foreach ($data['secondPageDocuments'] as $k => $v) { ?>
					<tr>
						<td style="border-bottom: 1px solid lightgrey; border-right: 1px solid lightgrey"><?=date('d.m.Y',strtotime($v['date']));?></td>
						<td valign="top" style="border-bottom: 1px solid lightgrey; border-right: 1px solid lightgrey">
							<?php foreach ($v['team'] as $iTeam=>$employee) { ?>
							<?= $employee['displayName'].'</br>' ?>
							<?php } ?>
						</td>
						<td valign="top" style="border-bottom: 1px solid lightgrey; border-right: 1px solid lightgrey">
							<?php foreach ($v['resources'] as $iRes=>$res) { ?>
							<?=trim($res['kurzname']).'</br>' ?>
							<?php } ?>
						</td>
						<td align="left" style="border-bottom: 1px solid lightgrey; border-right: 1px solid lightgrey"><?=$v['from'].'-'.$v['to'];?></td>
						<td colspan="2" align="left" style="border-bottom: 1px solid lightgrey"><?=$v['Tagesbericht']['Bemerkungen'];?></td>
					</tr>
					<tr>
						<td style="border-right: 1px solid lightgrey">KF: <?= $v['team'][0]['displayName'] ?></td>
						<td style="border-right: 1px solid lightgrey">Fotos:
						<?php if (file_exists($v['Tagesbericht']['Photos'])) { ?>
							<img src="<?=$v['Tagesbericht']['Photos']?>" alt="photo" class="image"></td>
						<?php } ?>
						<td align="left" style="border-right: 1px solid lightgrey">Aufmaß: <?=renderBoolCheckbox('',$v['Tagesbericht']['Aufmaß']);?></td>
						<td align="left" style="border-right: 1px solid lightgrey">Freigabe: <?=renderBoolCheckbox('',$v['Tagesbericht']['Freigabe']);?></td>
						<td colspan="2" align="left">	
							Unterschrift: 
							<?php if (file_exists($v['Tagesbericht']['Kolonnenführer'])) { ?>
								<img src="<?=$v['Tagesbericht']['Kolonnenführer']?>" alt="photo" class="image">
							<?php } ?>
						</td>
					</tr>
					<?php } ?>
				</tbody>
			</table>
		</div>
	</body>
</html>

<style>
	.title {width:100%;}
	.topData td {border: 1px solid lightgrey; padding-left:5px;}
	.mainData td {border: 1px solid lightgrey; padding-left:5px;}
	.bottomData {border: 1px solid black;}
	.secondPageTable {width:100%; border-collapse:collapse;}
	.secondPageTable td {border: 1px solid black;}
	.mainData td {border: 1px solid lightgrey;}
	.topData, .mainData {border: 1px solid black!important;}
	.topData, .mainData, .bottomData {border-collapse: collapse; width:100%;}
	.bigFont {font-size:large;}
	.pageTwo {
    	page-break-before: always;
	}
	.paddingLeft {padding-left: 5px;}
	.paddingRight {padding-right: 5px;}
	.image {height:auto; width:50%;	}	
	.skizze {height:auto; width:75%;	}
	body {font-family:Helvetica;}
</style>
