<?php
require_once ABS_PATH.'Hours.php';
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Addresses.php';
require_once ABS_PATH.'Projects.php';
require_once ABS_PATH.'Customers.php';
require_once ABS_PATH.'v3/Employees.php';
require_once ABS_PATH.'WorkingOrders.php';
require_once ABS_PATH.'Vehicles.php';
require_once ABS_PATH.'Team.php';
require_once ABS_PATH.'printouts/printout.helper.php';
	
class C_ArbeitsauftragMontageanweisungTagesbericht {
	private $db;
	private $hours;
	private $documentation;
	private $addresses;
	private $projects;
	private $customers;
	private $helper;
	private $employees;
	private $workingOrders;
	private $resources;
	private $team;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->hours = new Hours();
		$this->documentation = new Documentation();
		$this->addresses = new Addresses();
		$this->projects = new Projects();
		$this->customers = new Customers();
		$this->helper = new PrintoutHelper();
		$this->employees = new \v3\Employees();
		$this->workingOrders = new WorkingOrders();
		$this->resources = new Vehicles();
		$this->team = new Team();
	}
	
	public function getData($schemaIdOne, $documentIdOne, $schemaIdTwo) {
		$templateData = [];
		$documentOne = $this->documentation->getDocumentId($schemaIdOne,$documentIdOne);
		$main = [];
		$main['id'] = $documentOne['id'];
		$main['title'] = $documentOne['title'];
		$main['type'] = $documentOne['type'];
		$main['required'] = $documentOne['required'];
		$main['reportedValues'] = [];
		array_unshift($documentOne['children'], $main);
		$grouped = $this->helper->groupChildrenUnderParentRecursive($documentOne['children'], 'id', 'parentId', 'children');
		$documentOneData = $this->helper->getDeepValueFromField('title', 'reportedValues', $grouped[0], 'children');
		$documentOneData['Arbeitsauftrag/Montageanweisung']['createdOn'] = $documentOne['documentCreatedOn'];
		// process displayInside of first indentationLevel
		$this->processDisplayInside($grouped[0]['children'], $documentOneData);
		// process Zusatzteile
		$this->processDisplayInside($grouped[0]['children'][9]['children'], $documentOneData);
		// process Verankerung
		$this->processDisplayInside($grouped[0]['children'][10]['children'], $documentOneData);
		// process Gitterträger
		$this->processDisplayInside($grouped[0]['children'][11]['children'], $documentOneData);
		$templateData = $documentOneData;
		//get all documents for the second and subsequent pages of this project
		$documents = $this->documentation->getDocumentationByKey('project',$documentOne['documentRelKey1'],null,$schemaIdTwo);
		
		$secondPageDocs = [];
		foreach ($documents as $doc) {
			$v['id'] = $doc['documentId'];
			$main = [];
			$main['id'] = $doc['id'];
			$main['title'] = $doc['title'];
			$main['type'] = $doc['type'];
			$main['required'] = $doc['required'];
			$main['reportedValues'] = [];
			array_unshift($doc['children'], $main);
			$grouped = $this->helper->groupChildrenUnderParentRecursive($doc['children'], 'id', 'parentId', 'children');
			$secondPageDocs[$v['id']] = $this->helper->getDeepValueFromField('title', 'reportedValue', $grouped[0], 'children');
			$secondPageDocs[$v['id']]['date'] = date('d.m.Y', strtotime($doc['documentCreatedOn']));
			
			
			if(isset($doc['documentRelKey2']) && !empty( $doc['documentRelKey2'])) {
				if ($target = getTunnelingTarget('GET', 'workingorders/select/'.$doc['documentRelKey1'].'/'.$doc['documentRelKey2'])) {
					$woOrProjectData = getDataFromTunnel($target, true);
				}
				else {
				
				$woOrProjectData = $this->workingOrders->getselect($doc['documentRelKey1'], $doc['documentRelKey2']);
				}
			
				if(empty($woOrProjectData['staff_preplanned'])) {
					$secondPageDocs[$v['id']]['team'] = [];
				} else {
					$personalNumbers = implode(',', $woOrProjectData['staff_preplanned']);
					$secondPageDocs[$v['id']]['team'] = $this->employees->get($personalNumbers);
				}
				
				$secondPageDocs[$v['id']]['resources'] = [];
				foreach ($woOrProjectData['resources_non_hr']as $i => $resNo) {
					$secondPageDocs[$v['id']]['resources'][$resNo] = $this->resources->getselect_resource($resNo);
				}
				
				$secondPageDocs[$v['id']]['from'] = $woOrProjectData['start_time'];
				$secondPageDocs[$v['id']]['to'] = $woOrProjectData['end_time'];
				$secondPageDocs[$v['id']]['description'] = $woOrProjectData['langtext'];
			
			} else {
				if ($target = getTunnelingTarget('GET', 'projects/select/'.$doc['documentRelKey1'].'/'.date('Y'))) {
					$woOrProjectData = getDataFromTunnel($target, true);
				}
				else {
					$woOrProjectData = $this->getProjectData($doc['documentRelKey1']);
				}
				
				if(empty($woOrProjectData['staff_preplanned'])) {
						$secondPageDocs[$v['id']]['team']= [];
					} else {
						$personalNumbers = implode(',', $woOrProjectData['staff_preplanned']);
					    $secondPageDocs[$v['id']]['team'] = $this->employees->get($personalNumbers);
				}
				
				$secondPageDocs[$v['id']]['resources'] = [];
				foreach ($woOrProjectData['resources_non_hr']as $i => $resNo) {
					$secondPageDocs[$v['id']]['resources'][$resNo] = $this->resources->getselect_resource($resNo);
				}
				
				$secondPageDocs[$v['id']]['from'] = $woOrProjectData['start_time'];
				$secondPageDocs[$v['id']]['to'] = $woOrProjectData['end_time'];
				$secondPageDocs[$v['id']]['description'] = $woOrProjectData['langtext'];
			}
		}
		
		ksort($secondPageDocs);
	
		$templateData['secondPageDocuments'] = $secondPageDocs;
			
		if(is_null($documentOne['documentRelKey2'])) {
			$templateData['woOrProjectData'] = $this->getProjectData($documentOne['documentRelKey1']);
		} else {
			$templateData['woOrProjectData'] = $this->workingOrders->getselect($documentOne['documentRelKey1'], $documentOne['documentRelKey2']);
		}
		
		//fetching the team of the first document working order
	
		$employeeNumbers = implode(',', $templateData['woOrProjectData']['staff_preplanned']);
		$templateData['woOrProjectData']['team'] = $this->employees->get($employeeNumbers);
		
		$woTeamLeader = $this->employees->get($templateData['woOrProjectData']['staff_preplanned'][0])[0];
		$templateData['woTeamLeader'] = trim($woTeamLeader['firstName'].' '.$woTeamLeader['lastName']);
		$templateData['customerData'] = $this->getCustomerData($templateData['woOrProjectData']['knr']);
		$projectData = $this->getProjectData($documentOne['documentRelKey1']);
		$templateData['projectManager'] = $projectData['technicalContactName'] ? $projectData['technicalContactName'] : '';
		
		// populate projectAddress from WO Data
		$templateData['projectAddress'] = '';
		
		if($templateData['woOrProjectData']['bvname']) {
			$templateData['projectAddress'] .= $templateData['woOrProjectData']['bvname'].', ';
		}
		
		if($templateData['woOrProjectData']['bvplz']) {
			$templateData['projectAddress'] .= $templateData['woOrProjectData']['bvplz'].', ';
		}

		if($templateData['woOrProjectData']['bvort']) {
			$templateData['projectAddress'] .= $templateData['woOrProjectData']['bvort'].', ';
		}
		rtrim($templateData['projectAddress'], ',');
		
		foreach ($templateData['woOrProjectData']['resources_non_hr']as $resIndex => $resNo) {
			$templateData['resources'][$resNo] = $this->resources->getselect_resource($resNo);
		}
		
		//resolve driver
		// TODO I think the driver should resolved for the second page docs, not for the first
		if($documentOne['documentRelKey1'] && $documentOne['documentRelKey2'])
			$team = $this->team->getwoteams($documentOne['documentRelKey1'],$documentOne['documentRelKey2']);
		
		if($team['driver']) {
			$driver = $this->employees->get(team['driver']);
			$templateData['teamDriver'] = trim($driver['firstName'.' '.$driver['lastName']]);
		} else {
			$templateData['teamDriver'] = '';
		}
		
		return $templateData;
	}
	
	private function getProjectData($projectNo) {
		$result = $this->projects->get($projectNo);
		$projects = [];
		
		foreach ($result as $k=>$v) {
			$projects[$v['year']] = $v;
		}
		
		ksort($projects);
		return end($projects);
	}	
	
	private function getCustomerData($customerNo) {
		$customerData = $this->customers->get($customerNo);
		return $customerData;
	}

	/**
	 * @param $children
	 * @param array $documentOneData
	 * @return array
	 */
	private function processDisplayInside($children, array &$documentOneData)
	{
		foreach ($children as $key => $displayInsidePosition) {
			// skip if position does not contain displayInside information
			if (!$displayInsidePosition['displayInside'])
				continue;

			// find parent of displayInsidePosition
			// this only works for the case, that
			// * there is a single displayInside-position
			// * immediately following the position
			$position = $children[$key - 1];

			// extra case for the one exception we have
			if($displayInsidePosition['id'] == 79)
				$position = $children[$key - 2];

			if ($position['id'] == $displayInsidePosition['displayInside']) {
				// compose title
				$title = $position['title'] . "->" . $displayInsidePosition['title'];
				// add to result array
				$documentOneData['Arbeitsauftrag/Montageanweisung'][$title] = $displayInsidePosition['reportedValue'];
			}
		}
	}
}
