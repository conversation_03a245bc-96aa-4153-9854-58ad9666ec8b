<html>
<?php
	$data = $this->data;
?>
	<head>
			<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
			<title>Bautagebuch</title>
	</head>
	<body>
		<div class="title">BAUTAGEBUCH</div>
		<div class="row">
		  <div class="leftColumn">
			  <table class="project" cellspacing="0">
				  <tr class="blueBackground">
					  <th colspan="2" align="left">Projekt</th>
				  </tr>
				  <tr class="whiteBackground">
                      <td align="left">Projekt-Nr.:</td><td> <?=$data['projectNo'];?></td>
				  </tr>
				  <tr class="whiteBackground">
					  <td align="left">Blatt-Nr.:</td><td> <?=$data['documentId'];?></td>
				  </tr>
				  <tr class="whiteBackground">
					  <td align="left">Datum:</td><td><?=$data['documentDate'];?></td>
				  </tr>
				  <tr class="whiteBackground">
					  <td align="left">Bauleiter:</td><td><?=$data['Bauleiter'];?></td>
				  </tr>
				   <tr class="whiteBackground">
					  <td align="left">Wetter:</td><td><?=$data['Temperatur'];?> °C / <?=$data['Wetter'];?></td>
				  </tr>
			  </table>
			  
			   <p></p>
		  
		   <table class="project" cellspacing="0">
				  <tr class="blueBackground">
					  <th align="left">Besonderheiten / Mängel / Zeitverzögerungen /Änderungen</th>
				  </tr>
				  <tr class="whiteBackground">
					  <td><?=$data['Besonderheiten / Mängel / Zeitverzögerungen / Änderungen'];?></td>
				  </tr>
			  </table>
			  
		  </div>
		  
		 <div class="rightColumn">
			 <table class="company" cellspacing="0">
			 	<tr>
				 	<th align="right" width="100%"><img src="<?=WEB_ROOT_API.'vendor\printouts\astecLogo.png'?>" alt="companyLogo" class="companyLogo"</th>
				 </tr>
				 <tr class="whiteBackground">
					 <td align="right"><b>astec Klima-Systeme GmbH</b></td>
				 </tr>
				 <tr class="whiteBackground">
					 <td align="right">Chromstraße 50 | 33415 Verl</td>
				 </tr>
				 <tr class="whiteBackground">
					 <td align="right">Telefon 0 52 46 . 92 76-0&nbsp;</td>
				 </tr>
				 <tr>
					 <td></td>
				 </tr>
				 <tr class="whiteBackground">
					 <td align="right">Telefax 0 52 46 . 92 76-49</td>
				 </tr>
				<tr class="whiteBackground">
					 <td align="right"><EMAIL></td>
				 </tr>
				 <tr class="whiteBackground">
					 <td align="right">https://www.astec-klimasysteme.de</td>
				 </tr>
			  </table>
		  </div>
		</div>
		
		<p></p>
		
		<table class="leistungen" cellspacing="0">
			  <tr class="blueBackground">
				  <th align="left">Ausgeführte Leistungen</th>
			  </tr>
			  <tr class="whiteBackground">
				  <td><?=$data['Ausgeführte Leistungen'];?></td>
			  </tr>
		  </table>
		  
		  <p></p>
		  
		  <div align="left" class="whiteBackground mitarbeiter">Mitarbeiter</div>
		  
		  <p></p>
		  
		  <table class="employees" cellspacing="0">
			  <tr class="blueBackground">
				  <th align="center">Eingesetzter Mitarbeiter</th>
				  <th align="center">Datum</th>
				  <th align="center">Von/Bis</th>
				  <th align="center">Von/Bis</th>
				  <th align="center">Von/Bis</th>
				  <th align="center">Gesamt</th>
				  <th Gesamt>Typ</th>
			  </tr>
<?php 			foreach ($data as $k=>$v) {
                // the minimum ("empty") value contains three blank arrays, hence we can skip rendering the row
                if(count($v) <= 3) continue;
                    if (strpos($k, 'Mitarbeiter') !== false) {		 ?>
						<tr class="whiteBackground">
							<td align="center"><?=$v['Name'];?></td>
							<td align="center"><?=$v['Date'];?></td>
							<td align="center"><?=$v['Von/Bis 1']['Von'].'-'.$v['Von/Bis 1']['Bis'];?></td>
							<td align="center"><?=$v['Von/Bis 2']['Von'].'-'.$v['Von/Bis 2']['Bis'];?></td>
							<td align="center"><?=$v['Von/Bis 3']['Von'].'-'.$v['Von/Bis 3']['Bis'];?></td>
							<td align="center"><?=$v['Total'];?></td>
							<td align="center"><?=$v['Typ'];?></td>
						</tr>
<?php 				 unset($data[$k]);}?>
<?php 			} ?>
		</table>
		
		<p></p>
		
		<div class="row">
		  <div class="leftColumn">
			    <table class="supplies" cellspacing="0">
			 	<tr class="blueBackground">
				 	<th align="center">Material/ Maschinen</th>
				 	<th align="center">Menge/ Einheit</th>
				 </tr>
<?php 			foreach ($data as $key=>$value) {
                // do not generate rows if there is no content
                if(!$value) continue;
                    if (strpos($key, 'Material/Maschinen') !== false) {?>
						<tr class="whiteBackground">
							<td align="center"><?=$value['Name'];?></td>
							<td align="center"><?=$value['Menge'].' '.$value['Einheit'];?></td>
						</tr>
<?php 				unset($data[$k]);}?>
<?php 			} ?>
			  </table>
		  </div>
		  
		 <div class="rightColumn">
			 <table class="supplies" cellspacing="0">
			 	<tr class="blueBackground">
				 	<th align="center">Anlieferungen</th>
				 	<th align="center">Menge/ Einheit</th>
				 </tr>
<?php			 foreach ($data as $i=>$a) {
                    // do not generate rows if there is no content
                    if(!$a) continue;
                        if (strpos($i, 'Anlieferungen') !== false) {
    ?>
						<tr class="whiteBackground">
							<td align="center"><?=$a['Name'];?></td>
							<td align="center"><?=$a['Menge'].' '.$a['Einheit'];?></td>
						</tr>
<?php 			 }?>
<?php } ?>
			  </table>
		  </div>
		</div>
		
		<p></p>
        <?php
        if(!$data['Unterschrift'])
        {
        ?>
		<table class="signatures" cellspacing="0">
			<tr>
				<th align="left" width="50%">Unterschrift</th>
				<th width="50%"><img src="<?=$data['Unterschrift'];?>" alt="bauleiterSignature" class="signature"></th>
			</tr>
		</table>
    <?php
        // closing bracket for if
        }
        ?>
	</body>
</html>

<style>
	.leftColumn {
	  float: left;
	  width: 47%;
	  margin-left:20px;
	  
	}
	
	.rightColumn {
	  float: right;
	  width: 47%;
	  margin-left:20px;
	}


	.row:after {
	  content: "";
	  display: table;
	  clear: both;
	}
	
	.title {font-family:Helvetica; color: #0f6ab4; font-size:xx-large; margin-left:20px;}
	.blueBackground {background-color:#0f6ab4;  color:white}
	.whiteBackground {color:#0f6ab4;}
	.project, .employees, .supplies, .employees {border: 1px solid #0f6ab4;  width:100%; display:table;}
	.project td, .leistungen td {border: 1px solid #0f6ab4;}
	.company, .leistungen, .employees {width:100%;}
	.employees td, .employees th {border: 1px solid #6fa5d2;}
	.supplies td, .supplies th {border: 1px solid #6fa5d2;}
	.leistungen, .employees, .mitarbeiter {margin-left:20px; width:98%!important;}
	.signatures th {border: 1px solid #0f6ab4; color:#0f6ab4}
	.signatures { margin-left:20px; width:98%; border: 1px solid #6fa5d2;}
	.signature {height: auto; width: 80%;}
	body {overflow-x: hidden; }
</style>


