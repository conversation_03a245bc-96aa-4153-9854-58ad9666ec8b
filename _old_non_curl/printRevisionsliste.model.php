<?php
require_once ABS_PATH.'Documentation.php';

class C_Revisionsliste {

private $documentation;
private $db;
	
	public function __construct() {
		$this->documentation = new DocumentationDocument();
		$this->db = CommonData::getDbConnection();
	}
	public function getData($schemaId,$documentId) {
		
		$data = $this->documentation->getById($schemaId, $documentId);
		$templateData = [];
		
		if($data[0]['children']) {
			foreach ($data[0]['children'] as $k=>$v) {
				if($v['type'] !== 'headline') {
					if($v['type'] == 'string') {
						if($v['title'] == 'Bemerkung') {
							$templateData['bemerkungen'][$v['displayInside']] = $v['reportedValue'];
						} else {
							$templateData[$v['title']] = $v['reportedValue'];
						}
					}
					
					if($v['type'] == 'date') {
						$templateData[$v['title']] = $v['reportedValue'];
					}
					
					if($v['type'] == 'checkbox') {
						$templateData['checkboxes'][$v['title']] = $v['reportedValue'];
					}
					
					if($v['type'] == 'signatureField') {
						$templateData[$v['title']] = $v['filePath'];
					}
				}		
			}	
		}

		if($templateData['bemerkungen']) {
			foreach ($templateData['bemerkungen'] as $key => $value) {
				foreach ($data[0]['children'] as $index => $child) {
					if($key == $child['id']) {
						$templateData['notes'][$child['title']] = $value;
					}
				}
			}
			unset($templateData['bemerkungen']);
		}		
		return $templateData;
	}
	
	public function checkDocumentExistence($schemaId, $documentId) {
		$stmt = $this->db->prepare( "SELECT TOP 1 * FROM documentation_document  WHERE schemaId = :schemaId AND id = :documentId");
		$stmt->execute(['schemaId'=>$schemaId, 'documentId' => $documentId]);
		$data = $stmt->fetch();
		return $data;
	}
}
