<?php
require_once ABS_PATH.'Documentation.php';
require_once ABS_PATH.'Projects.php';

class C_KurzfristigeBaustellen {
private $db;
private $documentation;
private $projects;
	
	public function __construct() {
		$this->db = CommonData::getDbConnection();
		$this->documentation = new Documentation();
		$this->projects = new Projects();
	}

	
	public function getData($schemaId,$documentId) {
		
		$documentData = $this->documentation->getDocumentId($schemaId,$documentId);
		
		if(empty($documentData)) {
			throw new RestException(404, "No data for document ID = {$documentId} and schema ID {$schemaId}.");
		}
		
		$templateData = [];
		
		foreach ($documentData['children'] as $k=>$v) {
			if(isset($v['reportedValue'])) {
				if($v['type'] == 'signatureField') {
					$templateData[$v['title']] = $v['filePath'];
				} else {
					$templateData[$v['title']] = $v['reportedValue'];
						
				}
			}
		}
		
		$projects = $this->projects->get($documentData['documentRelKey1'],0,0,0,'','','',false, '',  '', '', '', 'all');
		$keys = array_column($projects, 'year');
		array_multisort($keys, SORT_DESC, $projects);
		
		$projectData = $projects[0];
		
		$templateData['projectName'] = $projectData['project_name'];
		$templateData['from'] = isset($projectData['start_date']) ? date("d.m.Y", strtotime($projectData['start_date'])) : '';
		$templateData['to'] = isset($projectData['end_date']) ? date("d.m.Y", strtotime($projectData['end_date'])) : '';
		$templateData['Datum'] = date("d.m.Y", strtotime($templateData['Datum']));
		$templateData['fromTo'] = '';
		
		($templateData['from'] != '') ? $templateData['fromTo'].= 'Von: '.$templateData['from'] : '';
		($templateData['to'] != '') ? $templateData['fromTo'].= ' Bis: '.$templateData['to'] : ''; 
		
		$templateData['projectManager'] = isset($projectData['technicalContactDisplayName']) ? $projectData['technicalContactDisplayName'] : '';
		$templateData['customerName'] = isset($projectData['customer_kname']) ? $projectData['customer_kname'] : '';
	
		return $templateData;
		
	}
}