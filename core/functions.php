<?php

require_once __DIR__ . '/Request.php';
require_once __DIR__ . '/Response.php';
require_once __DIR__ . '/OutputType.php';
require_once __DIR__ . '/OutputTypeResult.php';

function die_with_response_code(int $code = Response::SERVER_ERROR, string|null $message = null): never
{
    http_response_code($code);
    die($message);
}

function is_post_request(): bool
{
    return Request::method() === 'POST';
}

function current_url(): string
{
    if (!isset($_SERVER['REQUEST_URI'], $_SERVER['SCRIPT_NAME'])) {
        return '';
    }

    $path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

    if (str_starts_with($path, $_SERVER['SCRIPT_NAME'])) {
        $path = substr($path, strlen($_SERVER['SCRIPT_NAME']));
    }

    return trim($path, '/');
}

function parse_route_and_type(string $uri): OutputTypeResult
{
    if (str_contains($uri, '.')) {
        $lastDotPosition = strrpos($uri, '.');
        $route = substr($uri, 0, $lastDotPosition);
        $extension = strtolower(substr($uri, $lastDotPosition + 1));

        if (in_array($extension, ['pdf', 'json', 'html'])) {
            $type = $extension === 'pdf' ? OutputType::Pdf : ($extension === 'json' ? OutputType::Json : OutputType::Html);
            return new OutputTypeResult($route, $type);
        } else {
            die_with_response_code(Response::BAD_REQUEST, "Unknown extension: $extension");
        }
    }

    return new OutputTypeResult($uri, OutputType::Pdf);
}