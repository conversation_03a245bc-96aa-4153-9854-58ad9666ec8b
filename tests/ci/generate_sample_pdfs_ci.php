<?php
// use this path since this script is run with root cwd
$baseDir = realpath('api-routes');
if ($baseDir === false) {
    echo "Error: api-routes directory not found\n";
    exit(1);
}

$iterator = new RecursiveIteratorIterator(
    new RecursiveDirectoryIterator(
        $baseDir,
        FilesystemIterator::SKIP_DOTS | FilesystemIterator::CURRENT_AS_PATHNAME
    ),
    RecursiveIteratorIterator::LEAVES_ONLY
);

$all_tests = [];
foreach ($iterator as $path) {
    if (is_file($path) && fnmatch('*_ci.php', basename($path))) {
        $all_tests[] = $path;
    }
}

$total = count($all_tests);
$passed_count = 0;
$failed_tests = []; // Will store simple route names for the summary
$failure_details = []; // Will store the full output for failed tests

echo "Running $total tests...\n\n";

foreach ($all_tests as $i => $path) {
    $route = str_replace($baseDir . '/', '', $path);
    $test_num = $i + 1;

    // Use printf for aligned, clean output
    printf("[%s/%s] Running %s ... ", str_pad((string)$test_num, strlen((string)$total), ' ', STR_PAD_LEFT), $total, $route);

    $start_time = microtime(true);

    // increase memory limit because if API throws huge response body, it can exceed the limit since we capture the result
    // of every failing test
    $command = 'php -d memory_limit=512M -d xdebug.mode=off ' . escapeshellarg($path) . ' 2>&1';
    exec($command, $output_lines, $exit_code);

    $end_time = microtime(true);
    $execution_time = $end_time - $start_time;
    $time_formatted = sprintf("%.2f", $execution_time);

    if ($exit_code === 0) {
        // Success: Print a clean, single line
        echo "✅ PASSED ({$time_formatted}s)\n";
        $passed_count++;
    } else {
        // Failure: Print a failed status and STORE the details for later.
        echo "❌ FAILED\n";
        $failed_tests[] = $route;
        $failure_details[] = [
            'route' => $route,
            'time' => $time_formatted,
            'exit_code' => $exit_code,
            'output' => implode("\n", $output_lines)
        ];
    }
}

// ==================== FINAL REPORTING ====================

// First, print the failure details if there are any
if (!empty($failure_details)) {
    echo "\n\n━━━━━━━━━━━━━━━━━ 🚨 FAILURE DETAILS 🚨 ━━━━━━━━━━━━━━━━━\n\n";
    foreach ($failure_details as $failure) {
        echo "-------------------- [FAILED] ---------------------\n";
        echo "File:      {$failure['route']}\n";
        echo "Time:      {$failure['time']}s\n";
        echo "Exit Code: {$failure['exit_code']}\n";
        echo "----------- [CAPTURED OUTPUT] -----------\n";
        echo $failure['output'] . "\n";
        echo "---------------------------------------------------\n\n";
    }
}

// Second, print the final summary
echo "==================== TEST SUMMARY ====================\n";
echo "Total tests: $total\n";
echo "Passed:      $passed_count\n";
echo "Failed:      " . count($failed_tests) . "\n";

if (!empty($failed_tests)) {
    echo "\nFailed tests list:\n";
    foreach ($failed_tests as $failed_test) {
        echo " - $failed_test\n";
    }
    exit(1); // Exit with a non-zero code to fail the CI job
}

echo "\nAll tests passed successfully! 🎉\n";
exit(0);