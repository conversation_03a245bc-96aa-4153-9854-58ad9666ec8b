<?php

namespace PrintoutHelper;

use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use PrintoutHelper;

require_once __DIR__ . '/../../printout.helper.php';

class ConvertDecimalToTimeTest extends TestCase
{
    #[DataProvider('provideDecimalToTime')]
    public function testConvertDecimalToTime(float|string $input, string $expected): void
    {
        $this->assertSame($expected, PrintoutHelper::convertDecimalToTime($input));
    }

    /**
     * @return array<string, array{float|string, string}>
     */
    public static function provideDecimalToTime(): array
    {
        return [
            'zero' => [0.0, '00:00'],
            'integer hours' => [5.0, '05:00'],
            'half hour' => [1.5, '01:30'],
            'quarter hour' => [1.25, '01:15'],
            'three quarters' => [2.75, '02:45'],
            'round up minutes to next hour edge' => [1.999, '02:00'],
            'comma decimal separator string' => ['2,5', '02:30'],
            'dot decimal separator string' => ['3.75', '03:45'],
            'rounding minutes 59.9 -> 60' => [4.998, '05:00'],
            'large hours' => [12.1, '12:06'],
            'more than a day still formats' => [27.5, '27:30'],
        ];
    }
}
