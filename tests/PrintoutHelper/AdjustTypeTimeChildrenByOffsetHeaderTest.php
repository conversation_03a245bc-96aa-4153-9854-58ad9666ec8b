<?php

namespace PrintoutHelper;

use DateTime;
use DateTimeZone;
use PHPUnit\Framework\TestCase;
use PrintoutHelper;

require_once __DIR__ . '/../../printout.helper.php';

class AdjustTypeTimeChildrenByOffsetHeaderTest extends TestCase
{
    public function testAdjustTypeTimeChildrenWithOffsetHeader(): void
    {
        $headers = [
            // -1 hour offset
            'X-BauBuddy-UTC-Offset' => '-1'
        ];
        $children = [
            ['type' => 'time', 'reportedValue' => '10:00', 'reportedValues' => ['10:00']],
            ['type' => 'string', 'reportedValue' => 'some text'],
        ];
        $expectedChildren = [
            ['type' => 'time', 'reportedValue' => '09:00', 'reportedValues' => ['09:00']],
            ['type' => 'string', 'reportedValue' => 'some text'],
        ];
        $adjustedChildren = PrintoutHelper::adjustTypeTimeChildrenByOffsetHeader($children, $headers);
        $this->assertEquals($expectedChildren, $adjustedChildren);
    }

    public function testAdjustTypeTimeChildrenWithoutOffsetHeader(): void
    {
        // Calculate expected offset for Europe/Berlin dynamically
        $berlinTimeZone = new DateTimeZone('Europe/Berlin');
        /** @noinspection PhpUnhandledExceptionInspection can never throw an exception because of 'now' */
        $now = new DateTime('now', $berlinTimeZone);
        $offsetInMinutes = $berlinTimeZone->getOffset($now) / 60; // Offset in minutes

        $children = [
            ['type' => 'time', 'reportedValue' => '10:00', 'reportedValues' => ['10:00']],
            ['type' => 'string', 'reportedValue' => 'some text'],
        ];

        // Manually calculate expected time based on Berlin offset
        $reportedMinutes = 10 * 60; // 10:00 in minutes
        $adjustedMinutes = $reportedMinutes + $offsetInMinutes;
        $dailyMinutes = ($adjustedMinutes + 1440) % 1440; // Ensure it wraps around 24 hours

        $expectedHours = floor($dailyMinutes / 60);
        $expectedMinutes = $dailyMinutes % 60;
        $expectedTime = sprintf('%02d:%02d', $expectedHours, $expectedMinutes);

        $expectedChildren = [
            ['type' => 'time', 'reportedValue' => $expectedTime, 'reportedValues' => [$expectedTime]],
            ['type' => 'string', 'reportedValue' => 'some text'],
        ];

        $adjustedChildren = PrintoutHelper::adjustTypeTimeChildrenByOffsetHeader($children, headers: []);
        $this->assertEquals($expectedChildren, $adjustedChildren);
    }
}
