<?php

namespace PrintoutHelper;

use NumberLocale;
use PHPUnit\Framework\TestCase;
use PrintoutHelper;

require_once __DIR__ . '/../../printout.helper.php';

class FormatNumberLocaleTest extends TestCase
{
    private static function invokeFormat(float $quantity): string
    {
        return PrintoutHelper::formatNumberLocale($quantity, 2, NumberLocale::DE);
    }

    public function testFormatsIntegerWithoutDecimalsAndUsesComma(): void
    {
        $this->assertSame('5', self::invokeFormat(5.0));
    }

    public function testRoundsToTwoDecimals(): void
    {
        $this->assertSame('1,23', self::invokeFormat(1.234));
        $this->assertSame('1,24', self::invokeFormat(1.235));
        $this->assertSame('0,1', self::invokeFormat(0.095));
        $this->assertSame('0,1', self::invokeFormat(0.096));
    }

    public function testTrailingZerosAreTrimmed(): void
    {
        $this->assertSame('2,5', self::invokeFormat(2.50));
        $this->assertSame('3', self::invokeFormat(3.00));
    }

    public function testLargeNumbersGermanLocaleWithGrouping(): void
    {
        // grouping enabled by default in helper: German uses dot as thousands and comma as decimal
        $this->assertSame('1.234.567,89', self::invokeFormat(1234567.889));
    }

    public function testNegativeNumbers(): void
    {
        $this->assertSame('-4,57', self::invokeFormat(-4.567));
    }

    public function testVerySmallNumbersRoundToZero(): void
    {
        $this->assertSame('0', self::invokeFormat(0.0049));
        $this->assertSame('0,01', self::invokeFormat(0.005));
    }

    public function testLargeIntegersHaveGrouping(): void
    {
        $this->assertSame('1.000.000', self::invokeFormat(1000000.0));
        $this->assertSame('999.999,99', self::invokeFormat(999999.994));
    }

    public function testOneDecimalAfterTrimming(): void
    {
        $this->assertSame('7,1', self::invokeFormat(7.10));
        $this->assertSame('7,01', self::invokeFormat(7.01));
    }

    public function testNegativeSmallNumbersRounding(): void
    {
        $this->assertSame('0', self::invokeFormat(-0.0049));
        $this->assertSame('-0,01', self::invokeFormat(-0.005));
        $this->assertSame('-0,1', self::invokeFormat(-0.096));
        $this->assertSame('-0,1', self::invokeFormat(-0.095));
    }

    public function testEnglishLocaleBehavior(): void
    {
        $this->assertSame('1,234,567.89', PrintoutHelper::formatNumberLocale(1234567.889, 2, NumberLocale::EN));
        $this->assertSame('2.5', PrintoutHelper::formatNumberLocale(2.50, 2, NumberLocale::EN));
        $this->assertSame('-4.57', PrintoutHelper::formatNumberLocale(-4.567, 2, NumberLocale::EN));
        $this->assertSame('0.01', PrintoutHelper::formatNumberLocale(0.005, 2, NumberLocale::EN));
    }
}
