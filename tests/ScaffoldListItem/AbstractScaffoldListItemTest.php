<?php

namespace ScaffoldListItem;

use AbstractScaffoldListItem;
use PHPUnit\Framework\TestCase;

require_once __DIR__ . '/../../api-routes/ScaffoldListItem/abstractScaffoldListItem.php';

// Concrete implementation of AbstractScaffoldListItem for testing
class ConcreteScaffoldListItem extends AbstractScaffoldListItem
{
    /**
     * @param array<string, mixed> $scaffoldListItem
     * @return string
     */
    public function prepareOrderNumberPublic(array $scaffoldListItem): string
    {
        return $this->prepareOrderNumber($scaffoldListItem);
    }

    protected function getTranslationFilePath(): string
    {
        // empty for test since the code is not tested
        return "";
    }
}

class AbstractScaffoldListItemTest extends TestCase
{
    private ConcreteScaffoldListItem $scaffoldListItem;

    protected function setUp(): void
    {
        $this->scaffoldListItem = new ConcreteScaffoldListItem();
    }

    public function testPrepareOrderNumberWithWoPrefix(): void
    {
        $item = ['orderNumber' => 'WO12345'];
        $this->assertEquals('12345', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberWithoutWoPrefix(): void
    {
        $item = ['orderNumber' => '12345'];
        $this->assertEquals('', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberWithPo(): void
    {
        $item = ['orderNumber' => 'PO12/12070450/**********'];
        $this->assertEquals('14719637', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberWithLeadingSpaces(): void
    {
        $item = ['orderNumber' => '  WO12345'];
        $this->assertEquals('12345', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberWithTrailingSpaces(): void
    {
        $item = ['orderNumber' => 'WO12345  '];
        $this->assertEquals('12345', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberWithSpacesAround(): void
    {
        $item = ['orderNumber' => '  WO12345  '];
        $this->assertEquals('12345', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberEmpty(): void
    {
        $item = ['orderNumber' => ''];
        $this->assertEquals('', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberOnlySpaces(): void
    {
        $item = ['orderNumber' => '   '];
        $this->assertEquals('', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberNull(): void
    {
        $item = []; // Simulating 'orderNumber' key not present
        $this->assertEquals('', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberIncorrectFormat(): void
    {
        $item = ['orderNumber' => 'W12345'];
        $this->assertEquals('', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }

    public function testPrepareOrderNumberWithNonNumericPart(): void
    {
        $item = ['orderNumber' => 'FOOBAR'];
        $this->assertEquals('', $this->scaffoldListItem->prepareOrderNumberPublic($item));
    }
}