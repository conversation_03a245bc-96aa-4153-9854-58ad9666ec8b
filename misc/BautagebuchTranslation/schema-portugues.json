{"name": "Diário de construção", "description": "O diário de construção pode ser preenchido de manhã e à noite", "status": "active", "createdBy": "365", "printText": null, "createdOn": "2023-12-01T08:47:19Z", "editedBy": null, "editedOn": "2023-12-01T05:51:59Z", "applicableEntities": [], "printOptions": ["36"], "positions": [{"id": -1, "title": "Informações do funcionário", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "Motivação no trabalho (subjetiva)", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["😄", "😊", "🙂", "😐", "😟", "😫", "😭"], "values": [{"value": "😄", "colorCode": null}, {"value": "😊", "colorCode": null}, {"value": "🙂", "colorCode": null}, {"value": "😐", "colorCode": null}, {"value": "😟", "colorCode": null}, {"value": "😫", "colorCode": null}, {"value": "😭", "colorCode": null}], "visible": true}, {"id": -3, "title": "Status de Planejamento", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -4, "parentId": -3, "title": "Montagem conforme o plano e acordo", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -5, "displayInside": -4, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -6, "title": "Condições Ambientais", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -7, "parentId": -6, "title": "Condições Meteorológicas", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Neblina", "Vento Forte", "Tempestade", "Seco", "<PERSON><PERSON>", "Chuva Torrencial", "<PERSON>eve", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Neblina", "colorCode": null}, {"value": "Vento Forte", "colorCode": null}, {"value": "Tempestade", "colorCode": null}, {"value": "Seco", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "Chuva Torrencial", "colorCode": null}, {"value": "<PERSON>eve", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -8, "displayInside": -7, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -9, "parentId": -6, "title": "Temperatura", "type": "float", "required": "true", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -10, "title": "Condições de Instalação", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -11, "parentId": -10, "title": "Base de Instalação conforme discutido", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -12, "displayInside": -11, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -13, "parentId": -10, "title": "Terreno com Capacidade de Carga", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -14, "displayInside": -13, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "Condições do Espaço", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -16, "parentId": -15, "title": "Liberação para Construção", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -17, "displayInside": -16, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "Rotas de Transporte / Vias de Acesso", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -19, "parentId": -18, "title": "Caminho médio de transporte (em m)", "type": "float", "required": "true", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -20, "parentId": -18, "title": "Dificuldades", "type": "combobox-multi", "isRequiredSignature": "0", "value": ["Caminho não pavimentado", "Caminho através de área verde", "Escada estreita", "Corredor estreito"], "values": [{"value": "Caminho não pavimentado", "colorCode": null}, {"value": "Caminho através de área verde", "colorCode": null}, {"value": "Escada estreita", "colorCode": null}, {"value": "Corredor estreito", "colorCode": null}], "visible": true}, {"id": -21, "displayInside": -20, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "Informações sobre Materiais", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -23, "parentId": -22, "title": "Material entregue?", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não", "Entrega atrasada"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não", "colorCode": null}, {"value": "Entrega atrasada", "colorCode": null}], "visible": true}, {"id": -24, "parentId": -22, "title": "Condição do material de andaime na entrega", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON>", "Defeituoso", "<PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Defeituoso", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -25, "displayInside": -24, "title": "Condição do material de andaime", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -26, "parentId": -22, "title": "Material residual presente", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -27, "parentId": -22, "title": "Fotos do material residual", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -28, "parentId": -22, "title": "Grau de pré-seleção (material de andaime)", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -29, "displayInside": -28, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "Informações do dispositivo", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -31, "parentId": -30, "title": "Auxílios conforme ordem de trabalho", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -32, "parentId": -30, "title": "Falha do dispositivo", "type": "combobox-multi", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Carregadeira telescópica", "Elevador de obra", "Guindaste automotivo", "G<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Carregadeira telescópica", "colorCode": null}, {"value": "Elevador de obra", "colorCode": null}, {"value": "Guindaste automotivo", "colorCode": null}, {"value": "G<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -33, "title": "Interrupções", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -34, "parentId": -33, "title": "Descrição da interrupção", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -35, "displayInside": -34, "title": "Foto", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -36, "parentId": -33, "title": "Atraso devido à interrupção (em h)", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -37, "title": "Serviços de montagem", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -38, "parentId": -37, "title": "<PERSON><PERSON><PERSON> de fachada (em m²)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -39, "parentId": -37, "title": "Console (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -40, "parentId": -37, "title": "Viga de aço (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -41, "parentId": -37, "title": "Torres de escada (em unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -42, "parentId": -37, "title": "Proteção de telhadista (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -43, "parentId": -37, "title": "Guarda-corpo interno (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -44, "parentId": -37, "title": "Plataforma de remoção (em m³ ou unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -45, "parentId": -37, "title": "Andaime de espaço (em m³)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -46, "parentId": -37, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -47, "parentId": -37, "title": "Medição gráfica", "type": "graphicalMeasurement", "isRequiredSignature": "0", "visible": true}, {"id": -48, "parentId": -37, "title": "Outros", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -49, "title": "Serviços de desmontagem", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -50, "parentId": -49, "title": "<PERSON><PERSON><PERSON> de fachada (em m²)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -51, "parentId": -49, "title": "Console (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -52, "parentId": -49, "title": "Viga de aço (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -53, "parentId": -49, "title": "Torres de escada (em unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -54, "parentId": -49, "title": "Proteção de telhadista (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -55, "parentId": -49, "title": "Guarda-corpo interno (em metros lineares)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -56, "parentId": -49, "title": "Plataforma de remoção (em m³ ou unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -57, "parentId": -49, "title": "Andaime de espaço (em m³)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -58, "parentId": -49, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -59, "parentId": -49, "title": "Medição gráfica", "type": "graphicalMeasurement", "isRequiredSignature": "0", "visible": true}, {"id": -60, "parentId": -49, "title": "Outros", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -61, "title": "Trabal<PERSON> por hora", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -62, "parentId": -61, "title": "Regime conforme LV", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -63, "parentId": -61, "title": "Reparo", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -64, "parentId": -61, "title": "<PERSON><PERSON>", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -65, "parentId": -61, "title": "Poluição", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -66, "parentId": -61, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}], "group_ids": []}