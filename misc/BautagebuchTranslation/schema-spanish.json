{"name": "Diario de construcción", "description": "El diario de construcción se puede completar por la mañana y por la noche.", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2023-12-01T08:47:19Z", "editedBy": null, "editedOn": "2023-12-01T05:51:59Z", "applicableEntities": [], "printOptions": ["36"], "positions": [{"id": -1, "title": "Información del empleado", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "Motivación laboral (subjetiva)", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["😄", "😊", "🙂", "😐", "😟", "😫", "😭"], "values": [{"value": "😄", "colorCode": null}, {"value": "😊", "colorCode": null}, {"value": "🙂", "colorCode": null}, {"value": "😐", "colorCode": null}, {"value": "😟", "colorCode": null}, {"value": "😫", "colorCode": null}, {"value": "😭", "colorCode": null}], "visible": true}, {"id": -3, "title": "Estado de planificación", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -4, "parentId": -3, "title": "Montaje según plan y acuerdo", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -5, "title": "Comentario", "type": "string", "displayInside": -4, "isRequiredSignature": "0", "visible": true}, {"id": -6, "title": "Condiciones ambientales", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -7, "parentId": -6, "title": "Condiciones meteorológicas", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["Soleado", "Parcialmente nublado", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Viento fuerte", "Tormenta", "Seco", "<PERSON><PERSON><PERSON>", "Chubasco", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Soleado", "colorCode": null}, {"value": "Parcialmente nublado", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Viento fuerte", "colorCode": null}, {"value": "Tormenta", "colorCode": null}, {"value": "Seco", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Chubasco", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -8, "title": "Foto", "type": "photo", "displayInside": -7, "isRequiredSignature": "0", "visible": true}, {"id": -9, "parentId": -6, "title": "Temperatura", "type": "float", "required": "true", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -10, "title": "Condiciones de instalación", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -11, "parentId": -10, "title": "Base de instalación, según lo acordado", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -12, "title": "Foto", "type": "photo", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -13, "parentId": -10, "title": "Terreno soportable", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "Foto", "type": "photo", "displayInside": -13, "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "Condiciones del espacio", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -16, "parentId": -15, "title": "Libertad de construcción", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "Foto", "type": "photo", "displayInside": -16, "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "Rutas de transporte / tráfico", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -19, "parentId": -18, "title": "Distancia media de transporte (en m)", "type": "float", "required": "true", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -20, "parentId": -18, "title": "Dificultades", "type": "combobox-multi", "isRequiredSignature": "0", "value": ["Camino sin pavimentar", "Camino sobre césped", "Escalera estrecha", "<PERSON><PERSON><PERSON>"], "values": [{"value": "Camino sin pavimentar", "colorCode": null}, {"value": "Camino sobre césped", "colorCode": null}, {"value": "Escalera estrecha", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -21, "title": "Foto", "type": "photo", "displayInside": -20, "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "Información del material", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -23, "parentId": -22, "title": "¿Material entregado?", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["Sí", "No", "Entrega tardía"], "values": [{"value": "Sí", "colorCode": null}, {"value": "No", "colorCode": null}, {"value": "Entrega tardía", "colorCode": null}], "visible": true}, {"id": -24, "parentId": -22, "title": "Estado del material de andamio a la entrega", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["Funcional", "Defectuoso", "<PERSON><PERSON>"], "values": [{"value": "Funcional", "colorCode": null}, {"value": "Defectuoso", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -25, "title": "Estado del material de andamio", "type": "photo", "displayInside": -24, "isRequiredSignature": "0", "visible": true}, {"id": -26, "parentId": -22, "title": "Material sobrante disponible", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -27, "parentId": -22, "title": "Fotos de material sobrante", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -28, "parentId": -22, "title": "Grado de preclasificación (material de andamio)", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["Alto", "Medio", "Bajo (cajas de malla)"], "values": [{"value": "Alto", "colorCode": null}, {"value": "Medio", "colorCode": null}, {"value": "Bajo (cajas de malla)", "colorCode": null}], "visible": true}, {"id": -29, "title": "Foto", "type": "photo", "displayInside": -28, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "Información del dispositivo", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -31, "parentId": -30, "title": "Herramientas según orden de trabajo", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -32, "parentId": -30, "title": "Fallo del dispositivo", "type": "combobox-multi", "isRequiredSignature": "0", "value": ["Transpaleta", "<PERSON><PERSON><PERSON> elevadora", "Manipulador telescópico", "Elevador de construcción", "Grúa automotriz", "Grúa", "Polipasto"], "values": [{"value": "Transpaleta", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON> elevadora", "colorCode": null}, {"value": "Manipulador telescópico", "colorCode": null}, {"value": "Elevador de construcción", "colorCode": null}, {"value": "Grúa automotriz", "colorCode": null}, {"value": "Grúa", "colorCode": null}, {"value": "Polipasto", "colorCode": null}], "visible": true}, {"id": -33, "title": "Fall<PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -34, "parentId": -33, "title": "Descripción del fallo", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -35, "title": "Foto", "type": "photo", "displayInside": -34, "isRequiredSignature": "0", "visible": true}, {"id": -36, "parentId": -33, "title": "<PERSON><PERSON><PERSON> de<PERSON> al fallo (en h)", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -37, "title": "<PERSON><PERSON><PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -38, "parentId": -37, "title": "Andamio de fachada (en m2)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -39, "parentId": -37, "title": "Consola (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -40, "parentId": -37, "title": "Vigas en celosía (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -41, "parentId": -37, "title": "Torre de escalera (en unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -42, "parentId": -37, "title": "Protección de tejado (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -43, "parentId": -37, "title": "Barandilla interior (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -44, "parentId": -37, "title": "Plataforma de descarga (en m3 o unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -45, "parentId": -37, "title": "Andamio de espacio (en m3)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -46, "parentId": -37, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -47, "parentId": -37, "title": "Medición gráfica", "type": "graphicalMeasurement", "isRequiredSignature": "0", "visible": true}, {"id": -48, "parentId": -37, "title": "<PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -49, "title": "<PERSON><PERSON><PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -50, "parentId": -49, "title": "Andamio de fachada (en m2)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -51, "parentId": -49, "title": "Consola (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -52, "parentId": -49, "title": "Vigas en celosía (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -53, "parentId": -49, "title": "Torre de escalera (en unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -54, "parentId": -49, "title": "Protección de tejado (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -55, "parentId": -49, "title": "Barandilla interior (en m lineales)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -56, "parentId": -49, "title": "Plataforma de descarga (en m3 o unidades)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -57, "parentId": -49, "title": "Andamio de espacio (en m3)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -58, "parentId": -49, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -59, "parentId": -49, "title": "Medición gráfica", "type": "graphicalMeasurement", "isRequiredSignature": "0", "visible": true}, {"id": -60, "parentId": -49, "title": "<PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -61, "title": "Trabajos por hora", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -62, "parentId": -61, "title": "Trabajo según especificaciones del contrato", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -63, "parentId": -61, "title": "Reparación", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -64, "parentId": -61, "title": "<PERSON><PERSON>", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -65, "parentId": -61, "title": "Contaminación", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -66, "parentId": -61, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}]}