{"name": "Dziennik budowy", "description": "Dziennik budowy można wypełniać rano i wieczorem", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2023-12-01T08:47:19Z", "editedBy": null, "editedOn": "2023-12-01T05:51:59Z", "applicableEntities": [], "printOptions": ["36"], "positions": [{"id": -1, "title": "Informacje o pracownikach", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "Motywacja do pracy (subiektywna)", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["😄", "😊", "🙂", "😐", "😟", "😫", "😭"], "values": [{"value": "😄", "colorCode": null}, {"value": "😊", "colorCode": null}, {"value": "🙂", "colorCode": null}, {"value": "😐", "colorCode": null}, {"value": "😟", "colorCode": null}, {"value": "😫", "colorCode": null}, {"value": "😭", "colorCode": null}], "visible": true}, {"id": -3, "title": "<PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -4, "parentId": -3, "title": "Montaż zgodnie z planem i umową", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -5, "title": "komentarz", "type": "string", "displayInside": -4, "isRequiredSignature": "0", "visible": true}, {"id": -6, "title": "Warunki środowiskowe", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -7, "parentId": -6, "title": "pogoda", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["Słoneczny", "Lek<PERSON>", "Mętny", "Mgła", "Silny wiatr", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Deszcz", "ul<PERSON>a", "Śnieg", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "values": [{"value": "Słoneczny", "colorCode": null}, {"value": "Lek<PERSON>", "colorCode": null}, {"value": "Mętny", "colorCode": null}, {"value": "Mgła", "colorCode": null}, {"value": "Silny wiatr", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "Deszcz", "colorCode": null}, {"value": "ul<PERSON>a", "colorCode": null}, {"value": "Śnieg", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -8, "title": "Zdjęcie", "type": "photo", "displayInside": -7, "isRequiredSignature": "0", "visible": true}, {"id": -9, "parentId": -6, "title": "Temperatura", "type": "float", "required": "true", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -10, "title": "Warunki ustawienia", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -11, "parentId": -10, "title": "Podłoże zgodnie z ustaleniami", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -12, "title": "Zdjęcie", "type": "photo", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -13, "parentId": -10, "title": "Nośne podłoże", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "Zdjęcie", "type": "photo", "displayInside": -13, "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "Warunki przestrzenne", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -16, "parentId": -15, "title": "<PERSON>rak prz<PERSON><PERSON><PERSON><PERSON><PERSON> budo<PERSON>", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "Zdjęcie", "type": "photo", "displayInside": -16, "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "Drogi transportowe / komunikacyjne", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -19, "parentId": -18, "title": "Średnia długość drogi transportowej (w m)", "type": "float", "required": "true", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -20, "parentId": -18, "title": "Utrudnienia", "type": "combobox-multi", "isRequiredSignature": "0", "value": ["Nieutwardzona droga", "Droga przez trawnik", "Wąskie schody", "Wąski korytarz"], "values": [{"value": "Nieutwardzona droga", "colorCode": null}, {"value": "Droga przez trawnik", "colorCode": null}, {"value": "Wąskie schody", "colorCode": null}, {"value": "Wąski korytarz", "colorCode": null}], "visible": true}, {"id": -21, "title": "Zdjęcie", "type": "photo", "displayInside": -20, "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "Informacje o materiale", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -23, "parentId": -22, "title": "<PERSON><PERSON>ł dostarcz<PERSON>?", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["Tak", "<PERSON><PERSON>", "Opó<PERSON><PERSON><PERSON>"], "values": [{"value": "Tak", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "Opó<PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -24, "parentId": -22, "title": "<PERSON> rusztowania przy dostawie", "type": "combobox-multi", "required": "true", "isRequiredSignature": "0", "value": ["Funkcjonalny", "Uszkodzony", "Zabrudzony"], "values": [{"value": "Funkcjonalny", "colorCode": null}, {"value": "Uszkodzony", "colorCode": null}, {"value": "Zabrudzony", "colorCode": null}], "visible": true}, {"id": -25, "title": "<PERSON> r<PERSON>", "type": "photo", "displayInside": -24, "isRequiredSignature": "0", "visible": true}, {"id": -26, "parentId": -22, "title": "Pozostały materiał dos<PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -27, "parentId": -22, "title": "Zdjęcia pozostałego materiału", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -28, "parentId": -22, "title": "Stopień wstępnego sortowania (materiał rusztowania)", "type": "combobox", "required": "true", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON>", "Średni", "<PERSON><PERSON> (skrzynie transportowe)"], "values": [{"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Średni", "colorCode": null}, {"value": "<PERSON><PERSON> (skrzynie transportowe)", "colorCode": null}], "visible": true}, {"id": -29, "title": "Zdjęcie", "type": "photo", "displayInside": -28, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "Informacje o urządzeniach", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -31, "parentId": -30, "title": "Pomocnicze środki zgodnie z zamówieniem", "type": "checkbox", "required": "true", "isRequiredSignature": "0", "visible": true}, {"id": -32, "parentId": -30, "title": "Awaria urządzenia", "type": "combobox-multi", "isRequiredSignature": "0", "value": ["W<PERSON>zek paletowy", "<PERSON><PERSON><PERSON>k widłowy", "Ładowarka teleskopowa", "<PERSON><PERSON> b<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Wciągarka linowa"], "values": [{"value": "W<PERSON>zek paletowy", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>k widłowy", "colorCode": null}, {"value": "Ładowarka teleskopowa", "colorCode": null}, {"value": "<PERSON><PERSON> b<PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Wciągarka linowa", "colorCode": null}], "visible": true}, {"id": -33, "title": "Zakłócenia", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -34, "parentId": -33, "title": "Opis zakłócenia", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -35, "title": "Zdjęcie", "type": "photo", "displayInside": -34, "isRequiredSignature": "0", "visible": true}, {"id": -36, "parentId": -33, "title": "Opóźnienie spowodowane zakłóceniem (w godzinach)", "type": "float", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false}, "visible": true}, {"id": -37, "title": "Wykonane prace <PERSON>e", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -38, "parentId": -37, "title": "Rusztowanie elewacyjne (w m2)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -39, "parentId": -37, "title": "<PERSON><PERSON><PERSON> (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -40, "parentId": -37, "title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>we (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -41, "parentId": -37, "title": "<PERSON><PERSON><PERSON>a schodowa (w m wysokości)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -42, "parentId": -37, "title": "<PERSON><PERSON><PERSON> (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -43, "parentId": -37, "title": "Poręcz wewnętrzna (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -44, "parentId": -37, "title": "Podest transportowy (w m3 lub sztukach)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -45, "parentId": -37, "title": "Rusztowanie przestrzenne (w m3)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -46, "parentId": -37, "title": "Zdjęcia", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -47, "parentId": -37, "title": "Graficzne pomiary", "type": "graphicalMeasurement", "isRequiredSignature": "0", "visible": true}, {"id": -48, "parentId": -37, "title": "<PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -49, "title": "Wykonane prace <PERSON>e", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -50, "parentId": -49, "title": "Rusztowanie elewacyjne (w m2)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -51, "parentId": -49, "title": "<PERSON><PERSON><PERSON> (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -52, "parentId": -49, "title": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>we (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -53, "parentId": -49, "title": "<PERSON><PERSON><PERSON>a schodowa (w m wysokości)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -54, "parentId": -49, "title": "<PERSON><PERSON><PERSON> (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -55, "parentId": -49, "title": "Poręcz wewnętrzna (w mb)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -56, "parentId": -49, "title": "Podest transportowy (w m3 lub sztukach)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -57, "parentId": -49, "title": "Rusztowanie przestrzenne (w m3)", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -58, "parentId": -49, "title": "Zdjęcia", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -59, "parentId": -49, "title": "Graficzne pomiary", "type": "graphicalMeasurement", "isRequiredSignature": "0", "visible": true}, {"id": -60, "parentId": -49, "title": "<PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -61, "title": "<PERSON><PERSON>", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -62, "parentId": -61, "title": "Regulacja zgodnie z LV", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -63, "parentId": -61, "title": "<PERSON><PERSON><PERSON>", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -64, "parentId": -61, "title": "Szkody", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -65, "parentId": -61, "title": "Zanieczyszczenia", "type": "formula", "isRequiredSignature": "0", "visible": true}, {"id": -66, "parentId": -61, "title": "Zdjęcia", "type": "photo", "isRequiredSignature": "0", "visible": true}]}