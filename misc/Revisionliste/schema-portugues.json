{"name": "Revisionsliste zur Gefährdungsbeurteilung  🇵🇹", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2024-07-01", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["52"], "positions": [{"id": -1, "title": "1. Documentação", "type": "headline", "visible": true}, {"id": -2, "parentId": -1, "title": "1.1 Ordem de serviço escrita disponível", "type": "checkbox", "visible": true}, {"id": -3, "title": "Descrição:", "type": "string", "displayInside": -2, "visible": true}, {"id": -4, "parentId": -1, "title": "1.2 Descrição da viagem disponível - Navegação", "type": "checkbox", "visible": true}, {"id": -5, "title": "Descrição:", "type": "string", "displayInside": -4, "visible": true}, {"id": -6, "parentId": -1, "title": "1.3 Instruções de montagem disponíveis (montagem ou desmontagem).", "type": "checkbox", "visible": true}, {"id": -7, "title": "Descrição:", "type": "string", "displayInside": -6, "visible": true}, {"id": -8, "parentId": -1, "title": "1.4 Desenhos técnicos disponíveis", "type": "checkbox", "visible": true}, {"id": -9, "title": "Descrição:", "type": "string", "displayInside": -8, "visible": true}, {"id": -10, "title": "2. <PERSON><PERSON> <PERSON> rodovi<PERSON> (estradas, passeios, passeios)", "type": "headline", "visible": true}, {"id": -11, "parentId": -10, "title": "2.1 É necessário um guia de estacionamento", "type": "checkbox", "visible": true}, {"id": -12, "title": "Descrição:", "type": "string", "displayInside": -11, "visible": true}, {"id": -13, "parentId": -10, "title": "2.2 É necessário um guia de segurança rodoviária", "type": "checkbox", "visible": true}, {"id": -14, "title": "Descrição:", "type": "string", "displayInside": -13, "visible": true}, {"id": -15, "parentId": -10, "title": "2.3 Implementado o seguro automóvel (sinalizadores, etc.).", "type": "checkbox", "visible": true}, {"id": -16, "title": "Descrição:", "type": "string", "displayInside": -15, "visible": true}, {"id": -17, "parentId": -10, "title": "2.4. <PERSON><PERSON> para carga e transporte diretamente junto a ruas, passagens, ciclovias ou passeios", "type": "checkbox", "visible": true}, {"id": -18, "title": "Descrição:", "type": "string", "displayInside": -17, "visible": true}, {"id": -19, "parentId": -10, "title": "A segurança, barreiras ou marcações (por exemplo, fita de aviso, vedações, etc.) são obrigatórias", "type": "checkbox", "visible": true}, {"id": -20, "title": "Descrição:", "type": "string", "displayInside": -19, "visible": true}, {"id": -21, "title": "3. Instalações/locais", "type": "headline", "visible": true}, {"id": -22, "parentId": -21, "title": "3.1. <PERSON> ou <PERSON> disponível", "type": "checkbox", "visible": true}, {"id": -23, "title": "Descrição:", "type": "string", "displayInside": -22, "visible": true}, {"id": -24, "parentId": -21, "title": "3.2. <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> as m<PERSON><PERSON>", "type": "checkbox", "visible": true}, {"id": -25, "title": "Descrição:", "type": "string", "displayInside": -24, "visible": true}, {"id": -26, "parentId": -21, "title": "3.3. <PERSON><PERSON><PERSON>", "type": "checkbox", "visible": true}, {"id": -27, "title": "Descrição:", "type": "string", "displayInside": -26, "visible": true}, {"id": -28, "parentId": -21, "title": "3.4. Existe espaço de estacionamento suficiente para os nossos veículos", "type": "checkbox", "visible": true}, {"id": -29, "title": "Descrição:", "type": "string", "displayInside": -28, "visible": true}, {"id": -30, "parentId": -21, "title": "3.5. Está disponível espaço suficiente para armazenar materiais", "type": "checkbox", "visible": true}, {"id": -31, "title": "Descrição:", "type": "string", "displayInside": -30, "visible": true}, {"id": -32, "title": "4. Condições do estaleiro de obra", "type": "headline", "visible": true}, {"id": -33, "parentId": -32, "title": "4.1. Superfície de instalação diretamente na rua, passage<PERSON>, ciclovias ou passeios", "type": "checkbox", "visible": true}, {"id": -34, "title": "Descrição:", "type": "string", "displayInside": -33, "visible": true}, {"id": -35, "parentId": -32, "title": "A segurança, barreiras ou marcações (por exemplo, fita de aviso, barreiras, etc.) são obrigatórias", "type": "checkbox", "visible": true}, {"id": -36, "title": "Descrição:", "type": "string", "displayInside": -35, "visible": true}, {"id": -37, "parentId": -32, "title": "4.2. Distribuidor de energia disponível para ligar dispositivos (por exemplo, agregado)", "type": "checkbox", "visible": true}, {"id": -38, "title": "Descrição:", "type": "string", "displayInside": -37, "visible": true}, {"id": -39, "parentId": -32, "title": "Eletricidade necessária através da ligação doméstica", "type": "checkbox", "visible": true}, {"id": -40, "title": "Descrição:", "type": "string", "displayInside": -39, "visible": true}, {"id": -41, "parentId": -32, "title": "Distribuidor de proteção (PRCD) incluído.", "type": "checkbox", "visible": true}, {"id": -42, "title": "Descrição:", "type": "string", "displayInside": -41, "visible": true}, {"id": -43, "parentId": -32, "title": "4.3. l<PERSON><PERSON> elétricas aéreas perto da área de montagem", "type": "checkbox", "visible": true}, {"id": -44, "title": "Descrição:", "type": "string", "displayInside": -43, "visible": true}, {"id": -45, "parentId": -32, "title": "• Distância de segurança suficiente contra contacto acidental, mesmo com material de montagem", "type": "checkbox", "visible": true}, {"id": -46, "title": "Descrição:", "type": "string", "displayInside": -45, "visible": true}, {"id": -47, "parentId": -32, "title": "Linha aérea protegida (des<PERSON>da, coberta ou vedada para evitar o contacto)", "type": "checkbox", "visible": true}, {"id": -48, "title": "Descrição:", "type": "string", "displayInside": -47, "visible": true}, {"id": -49, "parentId": -32, "title": "4.4 Estradas de trânsito (passadeiras) adequadas para o transporte de materiais", "type": "checkbox", "visible": true}, {"id": -50, "title": "Descrição:", "type": "string", "displayInside": -49, "visible": true}, {"id": -51, "parentId": -32, "title": "Segurança na condução sobre condutores de energia (subterrâneos, largura, riscos de tropeçar)", "type": "checkbox", "visible": true}, {"id": -52, "title": "Descrição:", "type": "string", "displayInside": -51, "visible": true}, {"id": -53, "parentId": -32, "title": "Aberturas no pavimento, aterros íngremes presentes", "type": "checkbox", "visible": true}, {"id": -54, "title": "Descrição:", "type": "string", "displayInside": -53, "visible": true}, {"id": -55, "parentId": -32, "title": "4.5. Os cabos passam pelas peças de montagem e não estão fixos", "type": "checkbox", "visible": true}, {"id": -56, "title": "Descrição:", "type": "string", "displayInside": -55, "visible": true}, {"id": -57, "parentId": -32, "title": "4.6. <PERSON><PERSON>, chuva forte, vento, tempestade, etc.", "type": "checkbox", "visible": true}, {"id": -58, "title": "Descrição:", "type": "string", "displayInside": -57, "visible": true}, {"id": -59, "parentId": -32, "title": "São necessárias medidas de proteção especiais (por exemplo, reforço imediato, âncoras).", "type": "checkbox", "visible": true}, {"id": -60, "title": "Descrição:", "type": "string", "displayInside": -59, "visible": true}, {"id": -61, "parentId": -32, "title": "4.7. Iluminação suficiente", "type": "checkbox", "visible": true}, {"id": -62, "title": "Descrição:", "type": "string", "displayInside": -61, "visible": true}, {"id": -63, "parentId": -32, "title": "Iluminação adicional necessária", "type": "checkbox", "visible": true}, {"id": -64, "title": "Descrição:", "type": "string", "displayInside": -63, "visible": true}, {"id": -65, "title": "5. Má<PERSON>as e dispositivos (montagem, assistência ao transporte)", "type": "headline", "visible": true}, {"id": -66, "parentId": -65, "title": "5.1. Utilização de guindaste ou outro guindaste móvel", "type": "checkbox", "visible": true}, {"id": -67, "title": "Descrição:", "type": "string", "displayInside": -66, "visible": true}, {"id": -68, "parentId": -65, "title": "5.2. Utilização de empilhador telescópico ou camião industrial (empilhador)", "type": "checkbox", "visible": true}, {"id": -69, "title": "Descrição:", "type": "string", "displayInside": -68, "visible": true}, {"id": -70, "parentId": -65, "title": "5.3. Util<PERSON>ndo uma plataforma de trabalho motorizada", "type": "checkbox", "visible": true}, {"id": -71, "title": "Descrição:", "type": "string", "displayInside": -70, "visible": true}, {"id": -72, "parentId": -65, "title": "5.4. Utilização de outras máquinas de construção ou equipamentos de transporte", "type": "checkbox", "visible": true}, {"id": -73, "title": "Descrição:", "type": "string", "displayInside": -72, "visible": true}, {"id": -74, "title": "6. <PERSON><PERSON><PERSON> socorros/proteção contra incêndios", "type": "headline", "visible": true}, {"id": -75, "parentId": -74, "title": "6.1. Material de penso com frasco para lavagem dos olhos disponível", "type": "checkbox", "visible": true}, {"id": -76, "title": "Descrição:", "type": "string", "displayInside": -75, "visible": true}, {"id": -77, "parentId": -74, "title": "6.2. Primeiros socorros no local", "type": "checkbox", "visible": true}, {"id": -78, "title": "Descrição:", "type": "string", "displayInside": -77, "visible": true}, {"id": -79, "parentId": -74, "title": "6.3 Extintor de incêndio no local", "type": "checkbox", "visible": true}, {"id": -80, "title": "Descrição:", "type": "string", "displayInside": -79, "visible": true}, {"id": -81, "parentId": -74, "title": "6.4. Plano de emergência disponível (número 911, procedimento, etc.).", "type": "checkbox", "visible": true}, {"id": -82, "title": "Descrição:", "type": "string", "displayInside": -81, "visible": true}, {"id": -83, "title": "7. Condições de construção", "type": "headline", "visible": true}, {"id": -84, "parentId": -83, "title": "7.1. Montagem de acordo com a versão standard ou instruções de montagem e utilização do fabricante", "type": "checkbox", "visible": true}, {"id": -85, "title": "Descrição:", "type": "string", "displayInside": -84, "visible": true}, {"id": -86, "parentId": -83, "title": "É necessário um desvio da execução das regras", "type": "checkbox", "visible": true}, {"id": -87, "title": "Descrição:", "type": "string", "displayInside": -86, "visible": true}, {"id": -88, "parentId": -83, "title": "É necessário comprovar a conta", "type": "checkbox", "visible": true}, {"id": -89, "title": "Descrição:", "type": "string", "displayInside": -88, "visible": true}, {"id": -90, "parentId": -83, "title": "7.2. É necessária a utilização de vedações de segurança pré-fabricadas (GMS).", "type": "checkbox", "visible": true}, {"id": -91, "title": "Descrição:", "type": "string", "displayInside": -90, "visible": true}, {"id": -92, "parentId": -83, "title": "7.3. É obrigatório o uso de Equipamento de Proteção Individual adicional contra quedas", "type": "checkbox", "visible": true}, {"id": -93, "title": "Descrição:", "type": "string", "displayInside": -92, "visible": true}, {"id": -94, "parentId": -83, "title": "7.4. <PERSON> obrigató<PERSON> o uso de vestuário de alta visibilidade", "type": "checkbox", "visible": true}, {"id": -95, "title": "Descrição:", "type": "string", "displayInside": -94, "visible": true}, {"id": -96, "parentId": -83, "title": "7.5. <PERSON><PERSON> obrigatório de vestuário de proteção contra intempéries", "type": "checkbox", "visible": true}, {"id": -97, "title": "Descrição:", "type": "string", "displayInside": -96, "visible": true}, {"id": -98, "parentId": -83, "title": "7.6 São necessários outros EPI, por ex. fatos-macaco, máscara contra o pó, etc.", "type": "checkbox", "visible": true}, {"id": -99, "title": "Descrição:", "type": "string", "displayInside": -98, "visible": true}, {"id": -100, "title": "8. <PERSON><PERSON><PERSON><PERSON>, f<PERSON><PERSON><PERSON><PERSON>, acess<PERSON><PERSON><PERSON>", "type": "headline", "visible": true}, {"id": -101, "parentId": -100, "title": "8.1. As máquinas elétricas foram obviamente verificadas quanto a falhas", "type": "checkbox", "visible": true}, {"id": -102, "title": "Descrição:", "type": "string", "displayInside": -101, "visible": true}, {"id": -103, "parentId": -100, "title": "8.2. <PERSON><PERSON> que as linhas elétricas foram verificadas quanto a defeitos", "type": "checkbox", "visible": true}, {"id": -104, "title": "Descrição:", "type": "string", "displayInside": -103, "visible": true}, {"id": -105, "parentId": -100, "title": "8.3. As ferramentas foram obviamente verificadas quanto a danos", "type": "checkbox", "visible": true}, {"id": -106, "title": "Descrição:", "type": "string", "displayInside": -105, "visible": true}, {"id": -107, "parentId": -100, "title": "8.4. <PERSON><PERSON> (devidamente isolado ou feito de plástico) e condutas elétricas (pelo menos HO7...... ou superior) em conformidade com a norma BGI 608", "type": "checkbox", "visible": true}, {"id": -108, "title": "Descrição:", "type": "string", "displayInside": -107, "visible": true}, {"id": -109, "title": "9. <PERSON><PERSON><PERSON><PERSON>", "type": "headline", "visible": true}, {"id": -110, "parentId": -109, "title": "9.1. <PERSON><PERSON><PERSON><PERSON> qualificado disponível para a tarefa de trabalho", "type": "checkbox", "visible": true}, {"id": -111, "title": "Descrição:", "type": "string", "displayInside": -110, "visible": true}, {"id": -112, "parentId": -109, "title": "9.2. <PERSON><PERSON><PERSON><PERSON> suficiente para executar as tare<PERSON>s planeadas", "type": "checkbox", "visible": true}, {"id": -113, "title": "Descrição:", "type": "string", "displayInside": -112, "visible": true}, {"id": -114, "parentId": -109, "title": "9.3 As especificações de tempo (pré-planeamento) para a tarefa de trabalho são realistas", "type": "checkbox", "visible": true}, {"id": -115, "title": "Descrição:", "type": "string", "displayInside": -114, "visible": true}, {"id": -116, "parentId": -109, "title": "9.4. Trabalhadores com especial expertise no local (aprendizes, estagiários, novos colegas na empresa, etc.)", "type": "checkbox", "visible": true}, {"id": -117, "title": "Descrição:", "type": "string", "displayInside": -116, "visible": true}, {"id": -118, "title": "10. <PERSON><PERSON><PERSON>", "type": "headline", "visible": true}, {"id": -119, "title": "Descrição:", "type": "string", "displayInside": -118, "visible": true}, {"id": -120, "title": "11. <PERSON><PERSON><PERSON>:", "type": "headline", "visible": true}, {"id": -121, "parentId": -120, "title": "Cidade:", "type": "string", "visible": true}, {"id": -122, "parentId": -120, "title": "Data:", "type": "date", "visible": true}, {"id": -123, "parentId": -120, "title": "Assinatura:", "type": "signatureField", "visible": true}]}