{"name": "Revisionsliste zur Gefährdungsbeurteilung 🇧🇦", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2024-07-01", "editedBy": null, "editedOn": null, "applicableEntities": [], "printOptions": ["52"], "positions": [{"id": -1, "title": "1. Dokumentacija", "type": "headline", "visible": true}, {"id": -2, "parentId": -1, "title": "1.1 <PERSON><PERSON><PERSON><PERSON> pismeni radni nalog", "type": "checkbox", "visible": true}, {"id": -3, "title": "Opis", "type": "string", "displayInside": -2, "visible": true}, {"id": -4, "parentId": -1, "title": "1.2 Dostupan opis putovanja- Navigacija", "type": "checkbox", "visible": true}, {"id": -5, "title": "Opis", "type": "string", "displayInside": -4, "visible": true}, {"id": -6, "parentId": -1, "title": "1.3 Dostupne upute za montažu (montaža ili demontaža).", "type": "checkbox", "visible": true}, {"id": -7, "title": "Opis", "type": "string", "displayInside": -6, "visible": true}, {"id": -8, "parentId": -1, "title": "1.4 Dostupni tehnički crteži", "type": "checkbox", "visible": true}, {"id": -9, "title": "Opis", "type": "string", "displayInside": -8, "visible": true}, {"id": -10, "title": "2. <PERSON><PERSON><PERSON> bezbjedn<PERSON>ra<PERSON> (putevi, prilaznice, trotoari)", "type": "headline", "visible": true}, {"id": -11, "parentId": -10, "title": "2.1 Potreban je vodič za parkiranje", "type": "checkbox", "visible": true}, {"id": -12, "title": "Opis", "type": "string", "displayInside": -11, "visible": true}, {"id": -13, "parentId": -10, "title": "2.2 Potreban je vodič za sigurnost saobraćaja", "type": "checkbox", "visible": true}, {"id": -14, "title": "Opis", "type": "string", "displayInside": -13, "visible": true}, {"id": -15, "parentId": -10, "title": "2.3 Sp<PERSON>eno osiguranje vozila ( signalni farovi, itd.).", "type": "checkbox", "visible": true}, {"id": -16, "title": "Opis", "type": "string", "displayInside": -15, "visible": true}, {"id": -17, "parentId": -10, "title": "2.4 Prostor za utovar i transport neposredno uz ulice, prolaze, biciklističke staze ili trotoare", "type": "checkbox", "visible": true}, {"id": -18, "title": "Opis", "type": "string", "displayInside": -17, "visible": true}, {"id": -19, "parentId": -10, "title": "<PERSON><PERSON><PERSON><PERSON>, bari<PERSON>e ili <PERSON> (npr. trak<PERSON>, ograde za barijere, itd.) su potrebne", "type": "checkbox", "visible": true}, {"id": -20, "title": "Opis", "type": "string", "displayInside": -19, "visible": true}, {"id": -21, "title": "3. <PERSON><PERSON><PERSON><PERSON> / lokacije", "type": "headline", "visible": true}, {"id": -22, "parentId": -21, "title": "3.1 WC ili mobilni WC dostupan", "type": "checkbox", "visible": true}, {"id": -23, "title": "Opis", "type": "string", "displayInside": -22, "visible": true}, {"id": -24, "parentId": -21, "title": "3.2 Mogućnost pranja ruku", "type": "checkbox", "visible": true}, {"id": -25, "title": "Opis", "type": "string", "displayInside": -24, "visible": true}, {"id": -26, "parentId": -21, "title": "3.3 Prostorija za odmor", "type": "checkbox", "visible": true}, {"id": -27, "title": "Opis", "type": "string", "displayInside": -26, "visible": true}, {"id": -28, "parentId": -21, "title": "3.4 Ima dovoljno parking mjesta za naša vozila", "type": "checkbox", "visible": true}, {"id": -29, "title": "Opis", "type": "string", "displayInside": -28, "visible": true}, {"id": -30, "parentId": -21, "title": "3.5 Na raspolaganju je dovoljno prostora za skladištenje materijala", "type": "checkbox", "visible": true}, {"id": -31, "title": "Opis", "type": "string", "displayInside": -30, "visible": true}, {"id": -32, "title": "4. <PERSON><PERSON><PERSON>", "type": "headline", "visible": true}, {"id": -33, "parentId": -32, "title": "4.1 Montažna površina direktno na ulici, prolazima, biciklisticke staze ili trotoare", "type": "checkbox", "visible": true}, {"id": -34, "title": "Opis", "type": "string", "displayInside": -33, "visible": true}, {"id": -35, "parentId": -32, "title": "<PERSON><PERSON><PERSON><PERSON>, bari<PERSON>e ili <PERSON> (npr. trak<PERSON>, ograde za barijere, itd.) su potrebne", "type": "checkbox", "visible": true}, {"id": -36, "title": "Opis", "type": "string", "displayInside": -35, "visible": true}, {"id": -37, "parentId": -32, "title": "4.2 Dostupan distributer struje za povezivanje uređaja (npr. Agregat )", "type": "checkbox", "visible": true}, {"id": -38, "title": "Opis", "type": "string", "displayInside": -37, "visible": true}, {"id": -39, "parentId": -32, "title": "Potrebna struja preko kućnog priključka", "type": "checkbox", "visible": true}, {"id": -40, "title": "Opis", "type": "string", "displayInside": -39, "visible": true}, {"id": -41, "parentId": -32, "title": "Ukljucen i distributer zaštite (PRCD).", "type": "checkbox", "visible": true}, {"id": -42, "title": "Opis", "type": "string", "displayInside": -41, "visible": true}, {"id": -43, "parentId": -32, "title": "4.3 električni nadzemni vodovi u blizini montažnog prostora", "type": "checkbox", "visible": true}, {"id": -44, "title": "Opis", "type": "string", "displayInside": -43, "visible": true}, {"id": -45, "parentId": -32, "title": "• Dovoljno sigurnosno rastojanje od nenamjernog kontakta, čak i sa montažnim materijalom", "type": "checkbox", "visible": true}, {"id": -46, "title": "Opis", "type": "string", "displayInside": -45, "visible": true}, {"id": -47, "parentId": -32, "title": "   Nadzemni vod osiguran (isključen, pokriven ili ograđen kako bi se spriječio kontakt)", "type": "checkbox", "visible": true}, {"id": -48, "title": "Opis", "type": "string", "displayInside": -47, "visible": true}, {"id": -49, "parentId": -32, "title": "4.4 Saobraćajni putevi (šetališta) pogodni za transport materijala", "type": "checkbox", "visible": true}, {"id": -50, "title": "Opis", "type": "string", "displayInside": -49, "visible": true}, {"id": -51, "parentId": -32, "title": "Sigurnost vožnje preko provoditelja struje  (pod zemljom, širina, opasnosti od spoticanja)", "type": "checkbox", "visible": true}, {"id": -52, "title": "Opis", "type": "string", "displayInside": -51, "visible": true}, {"id": -53, "parentId": -32, "title": "<PERSON><PERSON>i otvori, prisutni strmi nasipi", "type": "checkbox", "visible": true}, {"id": -54, "title": "Opis", "type": "string", "displayInside": -53, "visible": true}, {"id": -55, "parentId": -32, "title": "4.5. <PERSON><PERSON><PERSON><PERSON> prelaze preko montaznih dijelova i nisu pricvrsceni", "type": "checkbox", "visible": true}, {"id": -56, "title": "Opis", "type": "string", "displayInside": -55, "visible": true}, {"id": -57, "parentId": -32, "title": "4.6. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ne<PERSON><PERSON><PERSON><PERSON> itd.", "type": "checkbox", "visible": true}, {"id": -58, "title": "Opis", "type": "string", "displayInside": -57, "visible": true}, {"id": -59, "parentId": -32, "title": "Potrebne su posebne zaštitne mjere (npr. odmah ucvrscivanje, ankeri).", "type": "checkbox", "visible": true}, {"id": -60, "title": "Opis", "type": "string", "displayInside": -59, "visible": true}, {"id": -61, "parentId": -32, "title": "4.7. <PERSON><PERSON><PERSON><PERSON><PERSON>jetlje<PERSON>t", "type": "checkbox", "visible": true}, {"id": -62, "title": "Opis", "type": "string", "displayInside": -61, "visible": true}, {"id": -63, "parentId": -32, "title": "Potrebna dodatna rasvjeta", "type": "checkbox", "visible": true}, {"id": -64, "title": "Opis", "type": "string", "displayInside": -63, "visible": true}, {"id": -65, "title": "5. <PERSON><PERSON><PERSON> i uređaji (montaža, transportna pomoć)", "type": "headline", "visible": true}, {"id": -66, "parentId": -65, "title": "5.1. Upotreba krana ili druge pokretne dizalice", "type": "checkbox", "visible": true}, {"id": -67, "title": "Opis", "type": "string", "displayInside": -66, "visible": true}, {"id": -68, "parentId": -65, "title": "5.2. Upotreba teleskopskog viljuškara ili industrijskog kamiona (viljuškara)", "type": "checkbox", "visible": true}, {"id": -69, "title": "Opis", "type": "string", "displayInside": -68, "visible": true}, {"id": -70, "parentId": -65, "title": "5.3. <PERSON><PERSON><PERSON>tenje pogonske radne platforme", "type": "checkbox", "visible": true}, {"id": -71, "title": "Opis", "type": "string", "displayInside": -70, "visible": true}, {"id": -72, "parentId": -65, "title": "5.4. Upotreba drugih građevinskih mašina ili transportne opreme", "type": "checkbox", "visible": true}, {"id": -73, "title": "Opis", "type": "string", "displayInside": -72, "visible": true}, {"id": -74, "title": "6. <PERSON><PERSON> pomo<PERSON> / Zastita od pozara", "type": "headline", "visible": true}, {"id": -75, "parentId": -74, "title": "6.1. <PERSON><PERSON><PERSON><PERSON> mater<PERSON> za previjanje sa bocom za ispiranje očiju", "type": "checkbox", "visible": true}, {"id": -76, "title": "Opis", "type": "string", "displayInside": -75, "visible": true}, {"id": -77, "parentId": -74, "title": "6.2. <PERSON>rva pomoć na licu mjesta", "type": "checkbox", "visible": true}, {"id": -78, "title": "Opis", "type": "string", "displayInside": -77, "visible": true}, {"id": -79, "parentId": -74, "title": "6.3 Aparat za gašenje požara na licu mjesta", "type": "checkbox", "visible": true}, {"id": -80, "title": "Opis", "type": "string", "displayInside": -79, "visible": true}, {"id": -81, "parentId": -74, "title": "6.4. Do<PERSON><PERSON><PERSON> plan za hitne sluč<PERSON>eve (broj hitne pomo<PERSON>i, procedura, itd.).", "type": "checkbox", "visible": true}, {"id": -82, "title": "Opis", "type": "string", "displayInside": -81, "visible": true}, {"id": -83, "title": "7. <PERSON><PERSON><PERSON>", "type": "headline", "visible": true}, {"id": -84, "parentId": -83, "title": "7.1. Mont<PERSON>ža prema standardnoj verziji ili uputama proizvođača za montažu i upotrebu", "type": "checkbox", "visible": true}, {"id": -85, "title": "Opis", "type": "string", "displayInside": -84, "visible": true}, {"id": -86, "parentId": -83, "title": "Neophodno je odstupanje od izvršenja pravila", "type": "checkbox", "visible": true}, {"id": -87, "title": "Opis", "type": "string", "displayInside": -86, "visible": true}, {"id": -88, "parentId": -83, "title": "Potreban je racunski dokaz", "type": "checkbox", "visible": true}, {"id": -89, "title": "Opis", "type": "string", "displayInside": -88, "visible": true}, {"id": -90, "parentId": -83, "title": "7.2. <PERSON><PERSON><PERSON><PERSON> je upotreba montažnih sigurno<PERSON> og<PERSON> (MSG).", "type": "checkbox", "visible": true}, {"id": -91, "title": "Opis", "type": "string", "displayInside": -90, "visible": true}, {"id": -92, "parentId": -83, "title": "7.3. <PERSON><PERSON><PERSON><PERSON> je upotreba dodatne Licne Zastitne Opreme protiv padova", "type": "checkbox", "visible": true}, {"id": -93, "title": "Opis", "type": "string", "displayInside": -92, "visible": true}, {"id": -94, "parentId": -83, "title": "7.4. <PERSON><PERSON><PERSON><PERSON> je upotreba odeće vis<PERSON> vidlji<PERSON>ti", "type": "checkbox", "visible": true}, {"id": -95, "title": "Opis", "type": "string", "displayInside": -94, "visible": true}, {"id": -96, "parentId": -83, "title": "7.5. <PERSON><PERSON><PERSON><PERSON> upotreba odeće za zaštitu od vremenskih prilika", "type": "checkbox", "visible": true}, {"id": -97, "title": "Opis", "type": "string", "displayInside": -96, "visible": true}, {"id": -98, "parentId": -83, "title": "7.6 <PERSON><PERSON><PERSON><PERSON> je druga OZO, npr. <PERSON><PERSON><PERSON><PERSON><PERSON>, maska za prašinu itd", "type": "checkbox", "visible": true}, {"id": -99, "title": "Opis", "type": "string", "displayInside": -98, "visible": true}, {"id": -100, "title": "8. <PERSON><PERSON><PERSON><PERSON>, al<PERSON>, pri<PERSON>", "type": "headline", "visible": true}, {"id": -101, "parentId": -100, "title": "8.1. Električne mašine su očito provjerene od kvarova", "type": "checkbox", "visible": true}, {"id": -102, "title": "Opis", "type": "string", "displayInside": -101, "visible": true}, {"id": -103, "parentId": -100, "title": "8.2. <PERSON><PERSON><PERSON><PERSON><PERSON> je da su električni vodovi provjereni na kvarove", "type": "checkbox", "visible": true}, {"id": -104, "title": "Opis", "type": "string", "displayInside": -103, "visible": true}, {"id": -105, "parentId": -100, "title": "8.3. Alati su očito provjereni od ostecenja", "type": "checkbox", "visible": true}, {"id": -106, "title": "Opis", "type": "string", "displayInside": -105, "visible": true}, {"id": -107, "parentId": -100, "title": "8.4. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> (odgovarajuće izolovan ili napravljen od plastike) i električni vodovi (najmanje HO7...... ili više) su u skladu sa BGI 608", "type": "checkbox", "visible": true}, {"id": -108, "title": "Opis", "type": "string", "displayInside": -107, "visible": true}, {"id": -109, "title": "9. Personal", "type": "headline", "visible": true}, {"id": -110, "parentId": -109, "title": "9.1. Kvalificira<PERSON> osoblje dostupno za radni zadatak", "type": "checkbox", "visible": true}, {"id": -111, "title": "Opis", "type": "string", "displayInside": -110, "visible": true}, {"id": -112, "parentId": -109, "title": "9.2. <PERSON><PERSON><PERSON><PERSON><PERSON> osoblja za obavljanje planiranih obaveza", "type": "checkbox", "visible": true}, {"id": -113, "title": "Opis", "type": "string", "displayInside": -112, "visible": true}, {"id": -114, "parentId": -109, "title": "9.3 Vremenske specifikacije (predplaniranje) za radni zadatak su realne", "type": "checkbox", "visible": true}, {"id": -115, "title": "Opis", "type": "string", "displayInside": -114, "visible": true}, {"id": -116, "parentId": -109, "title": "9.4. Ra<PERSON>ici sa posebnom strucnoscu na licu mesta (šegrti, pripravnici, nove kolege u kompaniji itd.)", "type": "checkbox", "visible": true}, {"id": -117, "title": "Opis", "type": "string", "displayInside": -116, "visible": true}, {"id": -118, "title": "10. <PERSON><PERSON><PERSON>", "type": "headline", "visible": true}, {"id": -119, "title": "Opis", "type": "string", "displayInside": -118, "visible": true}, {"id": -120, "title": "11. <PERSON><PERSON><PERSON><PERSON><PERSON>:", "type": "headline", "visible": true}, {"id": -121, "parentId": -120, "title": "Grad:", "type": "string", "visible": true}, {"id": -122, "parentId": -120, "title": "Datum:", "type": "date", "visible": true}, {"id": -123, "parentId": -120, "title": "Potpis:", "type": "signatureField", "visible": true}]}