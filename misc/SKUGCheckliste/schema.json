{"name": "Bautagesbericht", "description": "", "status": "active", "createdBy": "1", "printText": null, "createdOn": "2024-10-01T10:00:00Z", "editedBy": null, "editedOn": "2024-10-22T13:48:18Z", "applicableEntities": [], "printOptions": ["36"], "positions": [{"id": -1, "title": "Grund des Arbeitsausfalls", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -2, "title": "Art der Arbeit", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Betonierarbeiten", "Erdarbeiten"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Betonierarbeiten", "colorCode": null}, {"value": "Erdarbeiten", "colorCode": null}], "visible": true}, {"id": -3, "title": "Wetter", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -4, "parentId": -3, "title": "Temperatur (in °C)", "type": "float", "virtualAssistantText": "<PERSON><PERSON>st gibst du nun die Außentemperatur an.", "virtualAssistantImage": "TOP_RIGHT", "isRequiredSignature": "0", "format": {"hasThousandsSeparator": false, "amountOfDecimalPlaces": null, "unit": null}, "visible": true}, {"id": -5, "parentId": -3, "title": "Verhältnis", "type": "combobox-multi", "virtualAssistantText": "Ist es auf der Baustelle trocken, verregnet oder sogar schneebed<PERSON>t?", "virtualAssistantImage": "TOP_RIGHT", "isRequiredSignature": "0", "value": ["<PERSON><PERSON><PERSON><PERSON>", "trocken", "bedeckt", "<PERSON><PERSON><PERSON>", "Regen", "<PERSON><PERSON><PERSON>", "Wind", "Sc<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "values": [{"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "trocken", "colorCode": null}, {"value": "bedeckt", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Regen", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>", "colorCode": null}, {"value": "Wind", "colorCode": null}, {"value": "Sc<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -3, "title": "Wetterbedingte Einschränkungen", "type": "string", "isRequiredSignature": "0", "multipliable": "0", "visible": true}, {"id": -7, "title": "Fotos", "type": "photo", "isRequiredSignature": "0", "visible": true}, {"id": -8, "title": "Sonstiges", "type": "string", "isRequiredSignature": "0", "visible": true}], "visible": true, "group_ids": [1, 2, 3, 4, 5]}