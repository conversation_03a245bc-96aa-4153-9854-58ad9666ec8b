{"name": "Baustellen-Erst-Begehung – Aufnahme des Bau-IST-Zustands", "description": "", "status": "active", "createdBy": "365", "createdOn": "2025-03-10T00:00:00Z", "editedOn": null, "positions": [{"id": 1, "title": "<PERSON><PERSON><PERSON> zur Baustelle", "type": "headline"}, {"id": 2, "parentId": 1, "title": "<PERSON>s gibt keine Zufahrtsmöglichkeiten bis an die Gebäudekante", "type": "headline"}, {"id": 3, "parentId": 2, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 4, "displayInside": 2, "title": "Beschreibung", "type": "string"}, {"id": 5, "displayInside": 2, "title": "Foto", "type": "photo"}, {"id": 6, "displayInside": 2, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 7, "parentId": 1, "title": "Es gibt folgende Verkehrsbeschränkungen", "type": "headline"}, {"id": 8, "parentId": 7, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 9, "displayInside": 7, "title": "Beschreibung", "type": "string"}, {"id": 10, "displayInside": 7, "title": "Foto", "type": "photo"}, {"id": 11, "displayInside": 7, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 12, "parentId": 1, "title": "Wir müssen folgende Flächen freihalten", "type": "headline"}, {"id": 13, "parentId": 12, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 14, "displayInside": 12, "title": "Beschreibung", "type": "string"}, {"id": 15, "displayInside": 12, "title": "Foto", "type": "photo"}, {"id": 16, "displayInside": 12, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 17, "parentId": 1, "title": "Wir dürfen folgende Montageöffnungen nicht nutzen", "type": "headline"}, {"id": 18, "parentId": 17, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 19, "displayInside": 17, "title": "Beschreibung", "type": "string"}, {"id": 20, "displayInside": 17, "title": "Foto", "type": "photo"}, {"id": 21, "displayInside": 17, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 22, "parentId": 1, "title": "Baustromverteiler stehen wie folgt", "type": "headline"}, {"id": 23, "parentId": 22, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 24, "displayInside": 22, "title": "Beschreibung", "type": "string"}, {"id": 25, "displayInside": 22, "title": "Foto", "type": "photo"}, {"id": 26, "displayInside": 22, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 27, "parentId": 1, "title": "Verortung der Schuttcontainer", "type": "headline"}, {"id": 28, "parentId": 27, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 29, "displayInside": 27, "title": "Beschreibung", "type": "string"}, {"id": 30, "displayInside": 27, "title": "Foto", "type": "photo"}, {"id": 31, "displayInside": 27, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 32, "parentId": 1, "title": "Es gibt folgende Hindernisse in unseren Montagebereichen", "type": "headline"}, {"id": 33, "parentId": 32, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 34, "displayInside": 32, "title": "Beschreibung", "type": "string"}, {"id": 35, "displayInside": 32, "title": "Foto", "type": "photo"}, {"id": 36, "displayInside": 32, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 37, "parentId": 1, "title": "Es gibt folgende schadstoffbelastete Arbeitsbereiche", "type": "headline"}, {"id": 38, "parentId": 37, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 39, "displayInside": 37, "title": "Beschreibung", "type": "string"}, {"id": 40, "displayInside": 37, "title": "Foto", "type": "photo"}, {"id": 41, "displayInside": 37, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 42, "parentId": 1, "title": "Folgende Vorarbeiten beeinflussen uns produktivmindernd in der Ausführung", "type": "headline"}, {"id": 43, "parentId": 42, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 44, "displayInside": 42, "title": "Beschreibung", "type": "string"}, {"id": 45, "displayInside": 42, "title": "Foto", "type": "photo"}, {"id": 46, "displayInside": 42, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 47, "parentId": 1, "title": "Folgende Unternehmer arbeiten in gleichen Bereichen wie wir", "type": "headline"}, {"id": 48, "parentId": 47, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 49, "displayInside": 47, "title": "Beschreibung", "type": "string"}, {"id": 50, "displayInside": 47, "title": "Foto", "type": "photo"}, {"id": 51, "displayInside": 47, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 52, "title": "Angaben zur Ausführung", "type": "headline"}, {"id": 53, "parentId": 52, "title": "Folgende Arbeitsunterbrechungen sind vorhanden", "type": "headline"}, {"id": 54, "parentId": 53, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 55, "displayInside": 53, "title": "Beschreibung", "type": "string"}, {"id": 56, "displayInside": 53, "title": "Foto", "type": "photo"}, {"id": 57, "displayInside": 53, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 58, "parentId": 52, "title": "Folgende Arbeitsbeschränkungen sind vorhanden", "type": "headline"}, {"id": 59, "parentId": 58, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 60, "displayInside": 58, "title": "Beschreibung", "type": "string"}, {"id": 61, "displayInside": 58, "title": "Foto", "type": "photo"}, {"id": 62, "displayInside": 58, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 63, "parentId": 52, "title": "Wir müssen separaten Container benutzen (Müll, Holz, Plastik, Metall)", "type": "headline"}, {"id": 64, "parentId": 63, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 65, "displayInside": 63, "title": "Beschreibung", "type": "string"}, {"id": 66, "displayInside": 63, "title": "Foto", "type": "photo"}, {"id": 67, "displayInside": 63, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 68, "parentId": 52, "title": "Gerüste können nicht durchgehend genutzt werden, sondern müssen in folgenden Bereichen ab- und neu aufgebaut werden", "type": "headline"}, {"id": 69, "parentId": 68, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 70, "displayInside": 68, "title": "Beschreibung", "type": "string"}, {"id": 71, "displayInside": 68, "title": "Foto", "type": "photo"}, {"id": 72, "displayInside": 68, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 73, "parentId": 52, "title": "Folgende Unternehmen nutzen unser Gerüst mit", "type": "headline"}, {"id": 74, "parentId": 73, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 75, "displayInside": 73, "title": "Beschreibung", "type": "string"}, {"id": 76, "displayInside": 73, "title": "Foto", "type": "photo"}, {"id": 77, "displayInside": 73, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 78, "parentId": 52, "title": "Folgende Leistungen müssen wir für andere Gewerke vorhalten", "type": "headline"}, {"id": 79, "parentId": 78, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 80, "displayInside": 78, "title": "Beschreibung", "type": "string"}, {"id": 81, "displayInside": 78, "title": "Foto", "type": "photo"}, {"id": 82, "displayInside": 78, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 83, "parentId": 52, "title": "Hier werden vom Auftraggeber beigestellte Materialien zur Abholung bereitgestellt", "type": "headline"}, {"id": 84, "parentId": 83, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 85, "displayInside": 83, "title": "Beschreibung", "type": "string"}, {"id": 86, "displayInside": 83, "title": "Foto", "type": "photo"}, {"id": 87, "displayInside": 83, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 88, "parentId": 52, "title": "Bei der Inbetriebnahme müssen wir zu dem Zeitpunkt mit helfen", "type": "headline"}, {"id": 89, "parentId": 88, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 90, "displayInside": 88, "title": "Beschreibung", "type": "string"}, {"id": 91, "displayInside": 88, "title": "Foto", "type": "photo"}, {"id": 92, "displayInside": 88, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 93, "parentId": 52, "title": "Baubeleuchtung wird nicht bauseits geliefert", "type": "headline"}, {"id": 94, "parentId": 93, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 95, "displayInside": 93, "title": "Beschreibung", "type": "string"}, {"id": 96, "displayInside": 93, "title": "Foto", "type": "photo"}, {"id": 97, "displayInside": 93, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}, {"id": 98, "parentId": 52, "title": "<PERSON>it<PERSON><PERSON><PERSON><PERSON> stehen hier", "type": "headline"}, {"id": 99, "parentId": 98, "title": "Auswahl", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON> zu<PERSON>n"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON> zu<PERSON>n", "colorCode": null}]}, {"id": 100, "displayInside": 98, "title": "Beschreibung", "type": "string"}, {"id": 101, "displayInside": 98, "title": "Foto", "type": "photo"}, {"id": 102, "displayInside": 98, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "GRAPHICALMEASUREMENT"}]}