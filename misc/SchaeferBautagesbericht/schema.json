{"name": "Einflussgrößen", "description": "", "status": "active", "createdBy": "4", "createdOn": "2023-11-27T08:47:19Z", "editedOn": "2023-11-27T10:07:02Z", "positions": [{"id": 1, "title": "Mitarbeiterinformationen", "type": "headline"}, {"id": 2, "parentId": 1, "title": "<PERSON><PERSON><PERSON>", "type": "int"}, {"id": 3, "parentId": 1, "title": "<PERSON><PERSON><PERSON> neuer Mitarbeiter (Einarbeitungseffekt)", "type": "int"}, {"id": 4, "parentId": 1, "title": "Arbeitsmotivation (subjektiv)", "type": "combobox", "value": ["😄", "😊", "🙂", "😐", "😟", "😫", "😭"]}, {"id": 5, "title": "Planungsstand", "type": "headline"}, {"id": 6, "parentId": 5, "title": "Planungsstand - Rüstplanung", "type": "combobox", "value": ["Vollständig", "Unvollständig"]}, {"id": 7, "parentId": 5, "title": "Planungsstand - Rüstplanung (Beschreibung der Fehlenden Unterlagen)", "type": "string"}, {"id": 8, "title": "Gerüstmerkmale", "type": "headline"}, {"id": 9, "parentId": 8, "title": "Breitenklasse", "type": "combobox", "value": ["W06", "W09", "W12", "W15"]}, {"id": 10, "parentId": 8, "title": "<PERSON><PERSON><PERSON> der Wand [m]", "type": "float"}, {"id": 11, "parentId": 8, "title": "Wandabstand [cm]", "type": "float"}, {"id": 12, "parentId": 8, "title": "Unterbrechung der Gerüstflucht", "type": "combobox-multi", "value": ["Balkon", "Anba<PERSON>", "Öffnung Dachbereich", "Gasleitung", "Technikleitung"]}, {"id": 13, "title": "Umweltbedingungen", "type": "headline"}, {"id": 14, "parentId": 13, "title": "<PERSON><PERSON><PERSON>", "type": "combobox-multi", "value": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>t bewölkt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Starker Wind", "Sturm", "<PERSON><PERSON>en", "Regen", "Platzregen", "Sc<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"id": 15, "parentId": 13, "title": "Temperatur [°C]", "type": "float"}, {"id": 16, "title": "Bauwerksbedingungen", "type": "headline"}, {"id": 17, "parentId": 16, "title": "Geometrie des Bauwerks", "type": "combobox", "value": ["<PERSON><PERSON><PERSON>", "Komplex"]}, {"id": 18, "parentId": 16, "title": "Geometrie des Bauwerks (Beschreibung der Komplexen Stellen (ggf. Katalog: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ckige Geometrie, Grünbewuchs an Fassade, Gasleitung, Technikleitung))", "type": "string"}, {"id": 19, "parentId": 16, "title": "Geometrie des Bauwerks", "type": "photo"}, {"id": 20, "parentId": 16, "title": "Dachüberstände", "type": "combobox", "value": ["<PERSON><PERSON><PERSON>", "T<PERSON><PERSON><PERSON>"]}, {"id": 21, "parentId": 16, "title": "Dachüberstände [cm]", "type": "float"}, {"id": 22, "parentId": 16, "title": "Bauzustand", "type": "combobox", "value": ["Bestandsgebäude", "Neubau / Rohbau", "Rohbaubegleitend"]}, {"id": 23, "parentId": 16, "title": "Art des Ankergrunds", "type": "combobox", "value": ["WDVS", "Mauerwerk", "<PERSON><PERSON>", "Leichtmauerziegel", "Blechfassade", "Voll verglaste <PERSON>", "Lager-/ Stoßfugen (Denkmalschutz)", "Vorhandene Verankerungspunkte"]}, {"id": 24, "title": "Aufstellbedingungen", "type": "headline"}, {"id": 25, "parentId": 24, "title": "Topografie", "type": "combobox", "value": ["Flaches Gelände", "Schräges Gelände", "Steiles Gelände", "Unebenes Gelände"]}, {"id": 26, "parentId": 24, "title": "<PERSON><PERSON><PERSON>", "type": "combobox-multi", "value": ["Erde", "Sand", "<PERSON><PERSON>", "Schotterung", "<PERSON><PERSON>", "Pflastersteine", "Gewachsener Boden"]}, {"id": 27, "parentId": 24, "title": "Unterbrechungen der Aufstellfläche", "type": "combobox-multi", "value": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Lichtschacht", "Tiefgarageneinfahrt", "Bewuchs (Sträucher)", "Bereiche für Aufgrabungen für Versorgungsanlagen"]}, {"id": 28, "title": "Platzverhältnisse", "type": "headline"}, {"id": 29, "parentId": 28, "title": "Platzverhältnisse am Aufbauort (Beschreibung der Platzverhältnisse am Aufbauort (inkl. Grundstücksgrenzen falls relevant))", "type": "string"}, {"id": 30, "parentId": 28, "title": "Platzverhältnisse am Aufbauort", "type": "combobox", "value": ["Aufbau entlang Grenzwand", "Aufbau mit Betreten des Nachbargrundstücks", "Aufbau auf Gebäudeteilen vom Nachbar (Flachdachflächen, Wintergarten, Balkone, Zufahrten)"]}, {"id": 31, "parentId": 28, "title": "Platzverhältnisse an der Entladefläche", "type": "string"}, {"id": 32, "title": "Transportwege / Verkehrswege", "type": "headline"}, {"id": 33, "parentId": 32, "title": "Ø-<PERSON>rageweg [m]", "type": "float"}, {"id": 34, "parentId": 32, "title": "Transportweg (Entladefläche-Aufbauort) [m]", "type": "float"}, {"id": 35, "parentId": 32, "title": "Transportweg (Entladefläche-Aufbauort) Höhenmeter [m]", "type": "float"}, {"id": 36, "parentId": 32, "title": "Transportweg (Entladefläche-Aufbauort) Erschwernisse", "type": "combobox-multi", "value": ["Unbefestigter Weg", "Weg über Grünfläche", "<PERSON><PERSON>", "<PERSON><PERSON>", "Transport durch kleine Bauteilöffnungen"]}, {"id": 37, "title": "Durchbruchsicherheit von Bauteilen & Dächern", "type": "headline"}, {"id": 38, "parentId": 37, "title": "Art der Dachdeckung", "type": "combobox-multi", "value": ["Eternitdächer (Faserzementplatten)", "Scobalitdächer", "Plexiglasdächer", "Oberlichter"]}, {"id": 39, "title": "Materialinformationen", "type": "headline"}, {"id": 40, "parentId": 39, "title": "Material angeliefert?", "type": "combobox", "value": ["<PERSON>a", "<PERSON><PERSON>", "Verspätete Anlieferung"]}, {"id": 41, "parentId": 39, "title": "Zustand des Gerüstmaterials", "type": "combobox-multi", "value": ["Defekt", "Verschmutzt", "Funktionstüchtig"]}, {"id": 42, "parentId": 39, "title": "Zustand des Gerüstmaterials", "type": "photo"}, {"id": 43, "parentId": 39, "title": "Restmaterial vorhanden (vom vorherigen Einsatz)", "type": "string"}, {"id": 44, "parentId": 39, "title": "Grad der Vorsortierung (Gerüstmaterial)", "type": "combobox", "value": ["Hoch", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> (Schweineboxen)"]}, {"id": 45, "title": "Geräteinformationen", "type": "headline"}, {"id": 46, "parentId": 45, "title": "Vorhandene Geräte/<PERSON>l", "type": "combobox-multi", "value": ["Hubwagen", "<PERSON><PERSON><PERSON><PERSON>", "Teleskoplader", "Bauaufzug", "Autokran", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"id": 47, "parentId": 45, "title": "Geräteausfall", "type": "combobox-multi", "value": ["Hubwagen", "<PERSON><PERSON><PERSON><PERSON>", "Teleskoplader", "Bauaufzug", "Autokran", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"id": 48, "title": "Störungen", "type": "headline"}, {"id": 49, "parentId": 48, "title": "Verzögerung durch Störung [h]", "type": "float"}, {"id": 50, "title": "Schäden", "type": "headline"}, {"id": 51, "parentId": 50, "title": "Schadensmeldung", "type": "combobox-multi", "value": ["Nachbarbebauung", "Eigentum vom AG", "Eigenschaden", "Fremdschaden"]}, {"id": 52, "parentId": 50, "title": "Schadensmeldung (Beschreibung des jeweiligen Schadens u. der Schadensursache, Unbenutzbare Aufstellfläche)", "type": "string"}, {"id": 53, "title": "Leistungsergebnis", "type": "headline"}, {"id": 54, "parentId": 53, "title": "Tagesbeginn", "type": "string"}, {"id": 55, "parentId": 53, "title": "Tagesende (Inkl. Markierung im Foto der Montierten/Demontierten/Ummontierten Bauteile)", "type": "string"}, {"id": 56, "title": "Leistungen", "type": "headline"}, {"id": 57, "parentId": 56, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (in m2)", "type": "formula"}, {"id": 58, "parentId": 56, "title": "Ko<PERSON>le (in lfdm)", "type": "formula"}, {"id": 59, "parentId": 56, "title": "Gitterträger (in lfdm)", "type": "formula"}, {"id": 60, "parentId": 56, "title": "Treppenturm (in stm)", "type": "formula"}, {"id": 61, "parentId": 56, "title": "Dachdecker (in lfdm)", "type": "formula"}, {"id": 62, "parentId": 56, "title": "Bekleidung (in m2)", "type": "formula"}]}