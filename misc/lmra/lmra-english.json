{"name": "LMRA 🇬🇧", "description": "", "status": "active", "createdBy": "365", "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Block (C) Investigation/determination of the working environment", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Work environment", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "a.) Is there safe access/escape route?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "b.) Environment OK (lighting, ventilation, cleanliness)?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "c.) Is there a particular risk of slipping and stumbling?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) Meeting points, contact persons and emergency facilities are available and known? First  responders on site?  Yes?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Planning and coordination", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) Is the surface solid and stable?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "b.) Is the anchoring base OK?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "c.) Influence by other people/work groups?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) Danger in the area of crane work?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "e.) Is there a danger in the surrounding area from high-pressure cleaning/blasting work?", "type": "combobox", "isRequiredSignature": "0", "value": ["yes", "no, describe the measure"], "values": [{"value": "yes", "colorCode": null}, {"value": "no, describe the measure", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "f.) other", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "action", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "completed", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "action", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "completed", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "action", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "completed", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "action", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "completed", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "e.) other", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "action", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "completed", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "action", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "completed", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "action", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "completed", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "action", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "completed", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "action", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "completed", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}