{"name": "LMRA 🇷🇴", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Bloc (C) Investigarea/determinarea mediului de lucru", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Mediul de lucru", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "a.) Există o cale de acces/de evacuare sigură?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "<PERSON><PERSON>, descrie mă<PERSON>ra", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "b.) Mediu OK (iluminat, ventilație, curățenie)?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "c.) Există un risc deosebit de alunecare și poticnire?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) Sunt disponibile și cunoscute punctele de întâlnire, persoanele de contact și instalațiile de urgență? Primii intervenții la fața locului? Da?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Planificare și coordonare", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) Suprafața este solidă și stabilă?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "b.) Baza de ancorare este OK?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "c.) Influența altor perso<PERSON>/grupuri de muncă?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) Pericol în zona de lucru cu macara?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "e.) Există un pericol în zona înconjurătoare din cauza lucrărilor de curățare/sablare cu înaltă presiune?", "type": "combobox", "isRequiredSignature": "0", "value": ["da", ""], "values": [{"value": "da", "colorCode": null}, {"value": "", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "f.) altele", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "efectuat", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "efectuat", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "efectuat", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "efectuat", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "e.) altele", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "efectuat", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "efectuat", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "efectuat", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "efectuat", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "efectuat", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}