{"name": "LMRA 🇭🇷", "description": "", "status": "active", "createdBy": null, "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Blok (C) Istraživanje/utvrđivanj e radnog okruženja", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Radno okruženje", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "a.) <PERSON><PERSON><PERSON> li siguran pristup/put za bijeg?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "b.) Okruženje u redu (osvjetljenje, ventilacija, čistoća)?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "c.) Postoji li poseban rizik od klizanja i spoticanja?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) Da li su mjesta za sastanke, kontakt osobe i objekti za hitne slučajeve dostupni i poznati? Prvi odgovori na licu mjesta? Da?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Planiranje i koordinacija", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) Da li je površina čvrsta i stabilna?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "b.) Je li osnova za montažu u redu?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "c.) Uticaj drugih ljudi/radnih grupa?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) Opasnost u području rada dizalice?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "e.) Postoji li opasnost u okolini od čišćenja/pjeskarenja pod visokim pritiskom?", "type": "combobox", "isRequiredSignature": "0", "value": ["Da", "<PERSON><PERSON>, opišite mjeru"], "values": [{"value": "Da", "colorCode": null}, {"value": "<PERSON><PERSON>, opišite mjeru", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "f.) ostalo", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "mjera", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "mjera", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "mjera", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "mjera", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "e.) ostalo", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "mjera", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "mjera", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "mjera", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "mjera", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "mjera", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "<PERSON>av<PERSON>š<PERSON>", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}