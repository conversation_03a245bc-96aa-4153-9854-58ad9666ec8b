{"name": "LMRA 🇧🇬", "description": "", "status": "active", "createdBy": null, "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Блок (C) Изследване/ определяне на работната среда", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Работна среда", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "а.) Има ли безопасен път за достъп/евакуация?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "б.) Околната среда е наред (осветление, вентилация, чистота)?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "в.) Има ли особен  риск от подхлъзване и спъване?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) Местата за срещи, лицата за контакт и съоръженията за спешни случаи са налични и известни? Първи реагиращи на място? Да", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Планиране и координация", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) Повърхността твърда и стабилна ли е?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "б.) Основата за закрепване наред ли е?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "в.) Влияние от други хора/работни групи?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) Опасност в зоната на работа на крана?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "д.) Има ли опасност в района наоколо от почистване под високо налягане/ бластиране?", "type": "combobox", "isRequiredSignature": "0", "value": ["да", "Не, опишете мярката"], "values": [{"value": "да", "colorCode": null}, {"value": "Не, опишете мярката", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "е.) друго", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "мярка", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "завършен", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "мярка", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "завършен", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "мярка", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "завършен", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "мярка", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "завършен", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "д.) друго", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "мярка", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "завършен", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "мярка", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "завършен", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "мярка", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "завършен", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "мярка", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "завършен", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "мярка", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "завършен", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}