{"name": "LMRA 🇩🇪", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Block (C) Untersuchung/Feststellung des Arbeitsumfeldes", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Arbeitsumgebung", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "a.) <PERSON><PERSON>er Zugang/Fluchtweg vorhanden?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "b.) Umfeld in Ordnung(Beleuchtung, Belüftung, Sauberkeit)?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "c.) besondere Rutsch und Stolpergefahr vorhanden?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ansprechpartner und Notfalleinrichtungen vorhanden und bekannt? Ersthelfer vor Ort? Ja?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Planung und Koordinierung", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) Untergrund ist fest und tragfähig?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "b.) Verankerungsgrund ist in Ordnung?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "C.) Beeinflussung durch weitere Personen/Arbeitsgruppen?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) Gefährdung im Umfeld von Krantätigkeiten?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "e.) Gefährdung im Umfeld durch Hochdruckreinigung/ Strahlarbeiten?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>a", "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben"], "values": [{"value": "<PERSON>a", "colorCode": null}, {"value": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> be<PERSON>reiben", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "f.) sonstiges", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "Maßnahme(n)", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "Maßnahme(n)", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "Maßnahme(n)", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "Maßnahme(n)", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "e.) sonstiges", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "Maßnahme(n)", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "Maßnahme(n)", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "Maßnahme(n)", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "Maßnahme(n)", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "Maßnahme(n)", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "<PERSON><PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}