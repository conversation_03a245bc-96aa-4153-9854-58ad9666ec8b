{"name": "LMRA 🇵🇹", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Bloco (C) Investigação/determinação do ambiente de trabalho", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Ambiente de trabalho", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "a.) Existe rota de acesso/escape segura?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "b.) Ambiente OK (iluminação, ventilação, limpeza)?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "c.) Existe um risco particular de escorregar e tropeçar?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) Os pontos de encontro, pessoas de contato e instalações de emergência estão disponíveis e são conhecidos? Socorristas no local? Sim?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Planejamento e coordenação", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) A superfície é sólida e estável?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "b.) A base de ancoragem está OK?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "c.) Influência de outras pessoas/grupos de trabalho?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) Perigo na área de trabalho com guindaste?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "e.) Existe perigo na área circundante devido a trabalhos de limpeza/detonação de alta pressão?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON>m", "Não, descreva a medida"], "values": [{"value": "<PERSON>m", "colorCode": null}, {"value": "Não, descreva a medida", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "f.) outro", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "medir", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "medir", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "medir", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "medir", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "e.) outro", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "medir", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "medir", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "medir", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "medir", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "medir", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "conc<PERSON><PERSON><PERSON>", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}