{"name": "LMRA 🇹🇷", "description": "", "status": "active", "createdBy": "4", "printText": null, "createdOn": "2024-03-08T04:47:19Z", "editedBy": null, "editedOn": "2024-03-08T04:53:23Z", "applicableEntities": [], "positions": [{"id": -1, "title": "Blok (C) Çalışma ortamının araştırılması/belirlenmesi", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -2, "parentId": -1, "title": "C.1 Çalışma ortamı", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -3, "parentId": -2, "title": "a.) Gü<PERSON><PERSON> er<PERSON>/kaçış yolu var mı?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -4, "parentId": -2, "title": "b.) <PERSON><PERSON> iyi mi (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, te<PERSON><PERSON><PERSON>)?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -5, "parentId": -2, "title": "c.) Bel<PERSON><PERSON> bir kayma ve tökezleme riski var mı?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -6, "parentId": -2, "title": "d.) B<PERSON>şma noktaları, irtibat kişileri ve acil durum olanakları mevcut ve biliniyor mu? İlk müdahale ekipleri sahada mı? Evet?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -7, "parentId": -1, "title": "C.2 Planlama ve koordinasyon", "type": "headline", "isRequiredSignature": "0", "visible": true}, {"id": -8, "parentId": -7, "title": "a.) <PERSON><PERSON><PERSON>y sağlam ve sağlam mı?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -9, "parentId": -7, "title": "b.) <PERSON><PERSON><PERSON> tabanı iyi durumda mı?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -10, "parentId": -7, "title": "c.) <PERSON><PERSON><PERSON> k<PERSON>/çalış<PERSON> gruplarının etkisi mi?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -11, "parentId": -7, "title": "d.) <PERSON><PERSON> işi alanında tehlike mi var?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -12, "parentId": -7, "title": "e.) Çevrede yüksek basınçlı temizleme/patlatma çalışmaları nedeniyle tehlike var mı?", "type": "combobox", "isRequiredSignature": "0", "value": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>, ölçüyü açıklayın"], "values": [{"value": "<PERSON><PERSON>", "colorCode": null}, {"value": "<PERSON><PERSON><PERSON>, ölçüyü açıklayın", "colorCode": null}], "visible": true}, {"id": -13, "parentId": -7, "title": "f.) <PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -14, "title": "ölçüm", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -15, "title": "tamamlanmış", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -16, "title": "ölçüm", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -17, "title": "tamamlanmış", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -18, "title": "ölçüm", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -19, "title": "tamamlanmış", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -20, "title": "ölçüm", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -21, "title": "tamamlanmış", "type": "checkbox", "isRequiredSignature": "0", "visible": true}, {"id": -22, "title": "e.) <PERSON><PERSON><PERSON>", "type": "string", "isRequiredSignature": "0", "visible": true}, {"id": -23, "title": "ölçüm", "type": "string", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -24, "title": "tamamlanmış", "type": "checkbox", "displayInside": -8, "isRequiredSignature": "0", "visible": true}, {"id": -25, "title": "ölçüm", "type": "string", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -26, "title": "tamamlanmış", "type": "checkbox", "displayInside": -9, "isRequiredSignature": "0", "visible": true}, {"id": -27, "title": "ölçüm", "type": "string", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -28, "title": "tamamlanmış", "type": "checkbox", "displayInside": -10, "isRequiredSignature": "0", "visible": true}, {"id": -29, "title": "ölçüm", "type": "string", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -30, "title": "tamamlanmış", "type": "checkbox", "displayInside": -11, "isRequiredSignature": "0", "visible": true}, {"id": -31, "title": "ölçüm", "type": "string", "displayInside": -12, "isRequiredSignature": "0", "visible": true}, {"id": -32, "title": "tamamlanmış", "type": "checkbox", "displayInside": -12, "isRequiredSignature": "0", "visible": true}]}