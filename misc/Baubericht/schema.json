{"name": "Baubericht", "description": "", "status": "active", "createdBy": "4", "createdOn": "2023-03-30T00:00:00Z", "printOptions": ["8"], "positions": [{"id": 1, "title": "Projektnummer (die Nummerierung von den Bautagebüchern pro Projekt ist fortlaufend, nicht wie jetzt projekt-unabhängig)", "type": "string"}, {"id": 2, "title": "Anschrift Bauvorhaben", "type": "string"}, {"id": 3, "title": "Auftrag", "type": "combobox-multi", "required": "true", "values": [{"value": "Gerüstaufbau"}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"id": 4, "title": "Fassadenseite", "type": "combobox-multi", "required": "true", "values": [{"value": "Nordfassade"}, {"value": "Ostfassade"}, {"value": "Südfassade"}, {"value": "Westfassade"}]}, {"id": 5, "title": "Wie viel qm²", "type": "float"}, {"id": 6, "title": "Welche Position ausgeführt wurde; Freitextfeld für Positionen aus dem Angebot", "type": "string"}, {"id": 7, "title": "Leistungsbeschreibung", "type": "string"}, {"id": 8, "title": "Material (wenn z. B. Ba<PERSON>nschutzmatten, etc. verwendet werden)", "type": "string"}, {"id": 9, "title": "<PERSON><PERSON> dabei waren", "type": "EMPLOYEESELECTOR"}, {"id": 10, "title": "Bilder hochladen", "type": "photo"}, {"id": 11, "title": "Datum", "type": "date"}, {"id": 12, "title": "Unterschrift des Vorarbeiters", "type": "signatureField"}]}