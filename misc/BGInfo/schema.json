{"name": "BGInfo", "description": "", "status": "active", "createdBy": "4", "createdOn": "", "editedOn": "", "positions": [{"id": 1, "title": "Fachkunde", "type": "headline"}, {"id": 2, "parentId": 1, "title": "Wie heißt der zuständige Bauleiter bei diesem Objekt?", "type": "string"}, {"id": 3, "parentId": 1, "title": "Wer erstellt die Gefährungsbeurteilung im Unternehmen?", "type": "string"}, {"id": 4, "parentId": 1, "title": "Wer ist die fachkundige Person vor Ort? (Arbeitsverantwortlicher)", "type": "string"}, {"id": 5, "parentId": 1, "title": "Wer übergibt das Gerüst an den Nutzer als befähigte Person?", "type": "string"}, {"id": 6, "title": "Planungsqualität", "type": "headline"}, {"id": 7, "parentId": 6, "title": "Liegt <PERSON>en eine Montageanweisung vor?", "type": "checkbox"}, {"id": 8, "parentId": 6, "title": "Liegt <PERSON>en die Gerüstplanung vor?", "type": "checkbox"}, {"id": 9, "parentId": 6, "title": "Wie wird die Absturzsicherheit gewährleistet?", "type": "combobox-multi", "required": "false", "values": [{"value": "MSG (Montage-Sicherungsgeländer)"}, {"value": "systemintegrierter voreilender Seitenschutz"}, {"value": "PSAgA"}, {"value": "gar nicht"}]}, {"id": 10, "parentId": 6, "title": "Ist ein Höhenrettungskonzept vorhanden bei Arbeiten mit PSA?", "type": "checkbox"}, {"id": 11, "parentId": 6, "title": "Sind Sie im Besitz einer Gefährdungsbeurteilung?", "type": "checkbox"}, {"id": 12, "parentId": 6, "title": "St<PERSON>t in der Gefährungsbeurteilung ob mit PSA oder MSG gearbeitet wird?", "type": "checkbox"}, {"id": 13, "title": "Schulungen", "type": "headline"}, {"id": 14, "parentId": 13, "title": "Wer ist Ersthelfer?", "type": "string"}, {"id": 15, "parentId": 13, "title": "Sind Sie im Besitz von Ersthelfernachweisen?", "type": "checkbox"}, {"id": 16, "parentId": 13, "title": "Wann war die letzte Unterweisung?", "type": "date"}, {"id": 17, "parentId": 13, "title": "An was er<PERSON><PERSON> da<PERSON>?", "type": "string"}, {"id": 18, "parentId": 13, "title": "<PERSON><PERSON> von <PERSON>en ist ausgebildet für Höhen-, Tiefen-, Wasserrettung?", "type": "string"}, {"id": 19, "parentId": 13, "title": "Wie viele MSG habt Ihr bei dem Objekt dabei?", "type": "int"}, {"id": 20, "parentId": 13, "title": "Wo wird MSG im Fahrzeug gelagert?", "type": "string"}, {"id": 21, "parentId": 13, "title": "Wer erstellt das Verankerungsprotokoll?", "type": "string"}]}