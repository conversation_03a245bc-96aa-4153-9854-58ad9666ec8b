<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
require_once __DIR__ . '/../printout.helper.php';

class BundesInnungGeruestbauHelper
{
    /**
     * @param PrintoutCurl $curl
     * @param int $projectNo
     * @return array<string, mixed>
     */
    static function downloadCommonDataForHeader(PrintoutCurl $curl, int $projectNo): array
    {
        $settings = PrintoutHelper::downloadSettings($curl);
        $data['logo'] = $settings['logo'] ?? '';

        $infoSettings = PrintoutHelper::downloadInfoSettings($curl);
        $data['companyName'] = $infoSettings['companyName'];
        $data['companyAddress'] = $infoSettings['address']['address'];
        $data['companyPostCode'] = $infoSettings['address']['postcode'];
        $data['companyCity'] = $infoSettings['address']['city'];
        $data['companyTitle'] = $infoSettings['address']['title'];
        $data['addressWithPhone'] = PrintoutHelper::formatAddressWithPhoneNumber($infoSettings);

        $projectPartial = "projectName,customerNo,projectSiteAddress,projectSiteZipCode,projectSiteCity,projectValidStartDate";
        $project = PrintoutHelper::downloadProject($projectNo, $projectPartial, $curl);
        $data['projectName'] = $project['projectName'];
        $data['projectSiteAddress'] = $project['projectSiteAddress'];
        $data['projectSiteCity'] = $project['projectSiteCity'];
        $data['projectSiteZipCode'] = $project['projectSiteZipCode'];
        $data['projectValidStartDate'] = $project['projectValidStartDate'];

        $customer = PrintoutHelper::downloadCustomer($project['customerNo'], $curl);
        $data['principalDisplayName'] = $customer["displayName"];
        $data['principalRealAddress'] = $customer["address"];
        $data['principalPostcode'] = $customer["postcode"];
        $data['principalAddressCity'] = $customer["city"];
        $data['principalAddressCountryCode'] = $customer["countryCode"];

        return $data;
    }
}
