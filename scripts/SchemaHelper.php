<?php
/** @noinspection DuplicatedCode */
/** @noinspection PhpMultipleClassDeclarationsInspection */

require_once __DIR__ . '/../PrintoutCurl.php';
require_once __DIR__ . '/../printout.helper.php';

enum RelType: string
{
    case Project = "project";
    case WorkingOrder = "workingOrder";
    case Scaffold = "scaffold";
    case Resource = "resource";
}

class SchemaHelper
{
    // Store those fields here for easier usage in related functions to not pass them always around
    // those are set in fillInDefaultArguments(). They are all initialized for deleteSchemasByIds()
    private static string $principal = "default_test";
    private static string $loginPnr = "365";
    private static string $loginPassword = "1";
    private static ?string $accessToken = null;

    /**
     * Set $accessToken field.
     */
    private static function fillLoginToken(PrintoutCurl $curl): void
    {
        self::$accessToken = $curl->getLoginToken(self::$loginPnr, self::$loginPassword, self::$principal);
    }

    /**
     * @return array{httpheader: string[]}
     */
    private static function getHeaders(): array
    {
        return [
            'httpheader' => [
                'Content-Type: application/json',
                'Principal: ' . self::$principal,
                'Authorization: Bearer ' . self::$accessToken
            ]
        ];
    }

    /**
     * @return string[] with CLI for -project=300 -test=true parameters
     */
    private static function parseCliArguments(): array
    {
        global $argv;
        $arguments = array();

        for ($i = 1; $i < count($argv); $i++) {
            $arg = $argv[$i];

            if (str_starts_with($arg, '-')) {
                $key = ltrim($arg, '-');

                if (isset($argv[$i + 1])) {
                    $arguments[$key] = $argv[$i + 1];
                    $i++;
                }
            }
        }

        return $arguments;
    }

    /**
     * The only reason this is placed in this file and not in SampleDocumentationCreator.php, is to reuse the login token.
     *
     * @param array<string|int> $ids
     */
    public function deleteSchemasByIds(array $ids): void
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        if (self::$accessToken === null) {
            self::fillLoginToken($curl);
        }
        $urls = array_map(fn($id) => "v1/documentation/documentation_schema/$id", $ids);
        $responses = $curl->_multi_call('DELETE', $urls, [], self::getHeaders());
        for ($i = 0; $i < count($responses); $i++) {
            PrintoutHelper::ensureStatusCode($responses[$i]['statusCode'], 'DELETE ' . $urls[$i], $responses[$i]['response']);
        }
    }

    private static function postJsonSchema(string $jsonSchema, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('POST',
            "v1/documentation/documentation_schema",
            $jsonSchema, self::getHeaders()), associative: true);
    }

    private static function getSampleDocument(int $schemaId, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('GET',
            "v1/documentation/sampleDocument/$schemaId",
            [], self::getHeaders()), associative: true);
    }

    private static function downloadAnyWorkingOrderAndGetId(PrintoutCurl $curl): int
    {
        $currentYear = date('Y');
        $workingOrders = json_decode($curl->_simple_call('GET',
            "v3/workingorders?filter[status]=2&startDate=$currentYear-01-01&endDate=$currentYear-12-31&partial=workingOrderNo",
            [], self::getHeaders()), associative: true);
        if (empty($workingOrders)) {
            die("No working orders found for the current year");
        }
        return $workingOrders[0]["workingOrderNo"];
    }

    private static function createVehicle(PrintoutCurl $curl): array
    {
        $requestBody = json_encode([
            "gruppe" => "gruppe",
            "kurzname" => "kurzname",
            "langtext" => "langtext",
            "info" => "info",
            "sort" => 1,
            "lagerort" => "lagerort",
            "lteartikel" => "lteartikel",
            "businessUnit" => "businessUnit",
            "vondat" => "vondat",
            "bisdat" => "bisdat",
            "hu" => "hu",
            "asu" => "asu",
            "fuelConsumption" => 10.5,
            "priceInformation" => 99.99,
            "vin" => "vin"
        ]);
        return json_decode($curl->_simple_call('POST', "v1/vehicles/create", $requestBody, self::getHeaders()), true);
    }

    private static function createScaffold(int $projectNo, PrintoutCurl $curl): array
    {
        $date = date('Y-m-d');
        $requestBody = json_encode([
            "projectNo" => $projectNo,
            "isMontage" => 0,
            "isDemontage" => 0,
            "type" => 1,
            "serviceSheetStatusOption" => 0,
            "orderNumber" => "",
            "detailDescription" => "",
            "date" => $date,
            "beginMontage" => $date,
            "location" => "",
            "shortDescription" => "printouts schema helper",
            "timeWithoutRent" => 0,
            "typeWithoutRent" => 2,
            "typeWithRent" => 2,
            "percentMontagePerf" => 0,
            "percentMontageBill" => 0,
            "percentDemontageBill" => 0,
            "percentProvision" => 0,
            "percentMontSubcontr" => 0,
            "percentDemontSubcontr" => 0,
            "scaffoldingSlip" => 0,
            "isApproved" => 0,
            "subcontractorMounting" => 0,
            "subcontractorUnmounting" => 0,
            "partialFinishedStatus" => 0,
            "subContractorMeasurement" => 1,
            "subContractorTempEmpLawMount" => 0,
            "subContractorTempEmpLawUnmount" => 0,
            "isRent" => 0,
            "requesteeVirtualNo" => 0,
            "payeeVirtualNo" => 0,
            "createdBy" => "365",
            "labelIds" => 0
        ]);

        return json_decode($curl->_simple_call('POST', "v2/scaffoldlists?type=scaffold",
            $requestBody, self::getHeaders()), true);
    }

    /**
     * @return bool true if the schema has photos or signatureField
     */
    private static function hasSchemaPhotos(string $jsonSchema): bool
    {
        foreach (json_decode($jsonSchema, true)['positions'] as $child) {
            if ($child['type'] === 'photo' || $child['type'] == 'signatureField') {
                return true;
            }
        }
        return false;
    }

    /**
     * @return int the fid
     */
    private static function uploadImage(string $imageFile, string $imageMime, int $projectId, PrintoutCurl $curl): int
    {
        $file = new CURLFile($imageFile, $imageMime, basename($imageFile));
        return $curl->uploadFile(
            [
                "rel_type" => "ktr",
                "rel_key" => $projectId,
                "filename" => basename($imageFile),
                "category" => "",
                "description" => "Dummy signature image",
                "group_ids[0]" => "1",
                "group_ids[1]" => "3",
                "file-form-data" => $file,
                "uploader" => self::$loginPnr
            ],
            [
                "Accept: */*",
                "Proxy-Connection: Keep-Alive",
                "Authorization: Bearer " . self::$accessToken,
                "App-lang: de",
                "Principal: " . self::$principal,
                "Timeshift: 2",
            ],
            dieOnError: false
        )['fid'];
    }

    /**
     * @param array<string, mixed> $document
     * @param int $schemaId
     * @param PrintoutCurl $curl
     * @return array<string, mixed>
     */
    private static function createDocument(array $document, int $schemaId, PrintoutCurl $curl): array
    {
        return json_decode($curl->_simple_call('POST',
            "v1/documentation/$schemaId",
            json_encode($document), self::getHeaders()), true);
    }

    /**
     * @param array<string, string> $args
     * @return array<string, string>
     */
    private static function fillInDefaultArguments(array $args): array
    {
        if (!array_key_exists('image', $args)) {
            $args['image'] = __DIR__ . '/image.png';
        }

        if (!array_key_exists('image-mime', $args)) {
            $args['imageMime'] = 'image/png';
        }

        if (!array_key_exists('base-url', $args)) {
            $args['baseUrl'] = require __DIR__ . '/../utils/DefaultTestBaseUrl.php';
        }

        if (!array_key_exists('principal', $args)) {
            $args['principal'] = "default_test";
        }

        if (!array_key_exists('pnr', $args)) {
            $args['pnr'] = "365";
        }

        if (!array_key_exists('password', $args)) {
            $args['password'] = "1";
        }

        if (!array_key_exists('schema', $args)) {
            $args['schema'] = "schema.json";
        }

        self::$principal = $args['principal'];
        self::$loginPnr = $args['pnr'];
        self::$loginPassword = $args['password'];

        return $args;
    }

    /**
     * @param array<string, string> $options
     * @param RelType|null $relType if null, -project from $options is used
     * @return array{schemaId: int, documentId: int} with schemaId and documentId keys
     */
    public static function createSampleDocumentForSchema(array $options = [], RelType|null $relType = null): array
    {
        $args = $options ?: self::parseCliArguments();
        $args = self::fillInDefaultArguments($args);

        foreach ($args as $key => $value) {
            echo "$key: $value\n";
        }
        if (!array_key_exists('project', $args)) {
            echo "project: taken from the sample document\n";
        }
        if ($relType) {
            $relTypeString = $relType->value;
            echo "relType: $relTypeString\n";
        }
        echo "\n";

        $schemaFile = $args['schema'];
        if (!file_exists($schemaFile)) {
            $cwd = getcwd();
            die("Schema file does not exist: $cwd/$schemaFile");
        }
        $schema = file_get_contents($schemaFile);

        $curl = new PrintoutCurl($args['baseUrl']);
        if (self::$accessToken === null) {
            self::fillLoginToken($curl);
        }

        $responsePostJsonSchema = self::postJsonSchema($schema, $curl);
        $schemaId = $responsePostJsonSchema["documentation_schema_id"];
        echo "1. Uploaded schema to API with schema ID: $schemaId\n";

        $sampleDocument = self::getSampleDocument($schemaId, $curl);
        echo "2. Downloaded sample document\n";

        if (array_key_exists('project', $args)) {
            $projectId = (int)$args['project'];
        } else {
            $projectId = $sampleDocument["rel_key1"];
        }

        if ($relType) {
            switch ($relType) {
                case RelType::Project:
                    // do nothing, use the project from the sample document
                    break;
                case RelType::WorkingOrder:
                    $sampleDocument["rel_type"] = 'workingOrder';
                    $sampleDocument["rel_key2"] = self::downloadAnyWorkingOrderAndGetId($curl);
                    break;
                case RelType::Scaffold:
                    // this creates a scaffold to the sample project, even when the sample project is not a frame contract
                    $scaffold = self::createScaffold($projectId, $curl);
                    $sampleDocument["rel_type"] = 'scaffold';
                    $sampleDocument["rel_key1"] = $scaffold["ktr"];
                    $sampleDocument["rel_key2"] = $scaffold["rvaufnr"];
                    echo "    Created scaffold: {$scaffold["ktr"]}-{$scaffold["rvaufnr"]}\n";
                    break;
                case RelType::Resource:
                    $vehicle = self::createVehicle($curl);
                    $sampleDocument["rel_type"] = 'resource';
                    $sampleDocument["rel_key1"] = $vehicle['rnr'];
            }
        }

        if (self::hasSchemaPhotos($schema)) {
            $fid = self::uploadImage($args['image'], $args['imageMime'], $projectId, $curl);
            echo "3. Uploaded image since the schema has type=photo or type=signatureField. It has fid: $fid\n";

            foreach ($sampleDocument["reportedValues"] as &$item) {
                // check for hardcoded fid to replace it with a real one
                if ($item["value"] === "82969") {
                    $item["value"] = $fid;
                }
            }
        }

        $createdDocument = self::createDocument($sampleDocument, $schemaId, $curl);
        $docId = $createdDocument["documentationId"];
        // echo trailing newlines for nicer output in _default_test.php files
        echo (self::hasSchemaPhotos($schema) ? "4" : "3") .
            ". Created document with ID: $docId for schema ID: $schemaId and project ID: $projectId";
        if ($relType == "workingOrder") {
            echo " and working order ID: {$sampleDocument["rel_key2"]}";
        } else if ($relType == "scaffold") {
            echo " and scaffold ID: {$sampleDocument["rel_key1"]}-{$sampleDocument["rel_key2"]}";
        }
        echo "\n\n";

        return [
            'schemaId' => (int)$schemaId,
            'documentId' => $docId
        ];
    }
}

// Arguments for CLI usage. All are optional and always space separated!

// php SchemaHelper.php -schema /path/to/schema.json ...
//
// -project: if not set, the project from GET sampleDocument is used
// -schema: if not set, it uses schema.json in the current path
// -image: if not set, it uses image.png in the current path
// -image-mime: if not set, it uses image/png
// -base-url: if not set, https://api.baubuddy.de/index.php
// -principal: if not set, default_test
// -pnr: if not set, 365
// -password: if not set, 1

// only execute when script is run from CLI
if (PHP_SAPI === 'cli' && basename(__FILE__) === basename($_SERVER['PHP_SELF'])) {
    SchemaHelper::createSampleDocumentForSchema();
}
