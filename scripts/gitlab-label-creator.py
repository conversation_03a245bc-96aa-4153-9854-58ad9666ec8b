import os
import requests
from typing import List
import sys
from pathlib import Path

def read_gitlab_token() -> str:
    """Read GitLab token from .gitlab-token file."""
    token_path = Path(__file__).parent / '.gitlab-token'
    try:
        with open(token_path) as f:
            return f.read().strip()
    except FileNotFoundError:
        print(f"❌ Error: Token file not found at: {token_path}")
        print("Please create the file with your GitLab token.")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error reading token file: {e}")
        sys.exit(1)

GITLAB_TOKEN = read_gitlab_token()
API_ROUTES_PATH = "/Users/<USER>/vero/api-printouts/api-routes/"
GITLAB_PROJECT_ID = "101"
GITLAB_API_URL = f"https://gitlab.baubuddy.de/api/v4/projects/{GITLAB_PROJECT_ID}/labels"
LABEL_COLOR = "#477ab9"

def get_directories() -> List[str]:
    """Get all immediate directories in the specified path."""
    print(f"Reading directories from: {API_ROUTES_PATH}")
    try:
        # Get only directories, not files
        directories = [d for d in os.listdir(API_ROUTES_PATH) 
                      if os.path.isdir(os.path.join(API_ROUTES_PATH, d))]
        print(f"Found directories: {directories}")
        return directories
    except Exception as e:
        print(f"Error reading directories: {e}")
        return []

def create_gitlab_label(label_name: str) -> None:
    """Create a single GitLab label."""
    headers = {
        "PRIVATE-TOKEN": GITLAB_TOKEN
    }
    
    data = {
        "name": label_name,
        "color": LABEL_COLOR,
        "description": f"API route: {label_name}"
    }
    
    print(f"\nAttempting to create label: {label_name}")
    print(f"Label data: {data}")
    
    try:
        response = requests.post(GITLAB_API_URL, headers=headers, json=data)
        if response.status_code == 201:
            print(f"✅ Successfully created label: {label_name}")
        else:
            print(f"⚠️  Failed to create label: {label_name}")
            print(f"Status code: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error creating label {label_name}: {e}")

def main():
    directories = get_directories()
    
    if not directories:
        print("No directories found. Exiting.")
        return
    
    print("\n=== Labels to be created ===")
    for dir_name in directories:
        print(f"- {dir_name} (color: {LABEL_COLOR})")
    
    confirmation = input("\nPress Enter to create these labels in GitLab, or Ctrl+C to cancel: ")
    
    for dir_name in directories:
        create_gitlab_label(dir_name)

if __name__ == "__main__":
    print("GitLab Label Creator Starting...")
    print(f"Using GitLab token: {GITLAB_TOKEN[:4]}...{GITLAB_TOKEN[-4:]}")
    main()
