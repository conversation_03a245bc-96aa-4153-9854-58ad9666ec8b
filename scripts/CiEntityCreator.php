<?php
require_once __DIR__ . '/../PrintoutCurl.php';
require_once __DIR__ . '/SchemaHelper.php';
require_once __DIR__ . '/../printout.helper.php';

class CiEntityCreator
{
    private const PRINCIPAL = 'default_test';
    private const PNR = '365';
    private const PASSWORD = '1';
    private static ?string $cachedToken = null;

    private static function getToken(): string
    {
        if (self::$cachedToken === null) {
            $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
            self::$cachedToken = $curl->getLoginToken(
                self::PNR,
                self::PASSWORD,
                self::PRINCIPAL
            );
        }
        return self::$cachedToken;
    }

    private static function buildHeaders(bool $includeJsonContent = true): array
    {
        $headers = [
            'Principal: ' . self::PRINCIPAL,
            'Authorization: Bearer ' . self::getToken(),
        ];

        if ($includeJsonContent) {
            $headers[] = 'Content-Type: application/json';
        }

        return ['httpheader' => $headers];
    }

    /**
     * For CI (principal=default_test) only:
     * - POST the schema to /v1/documentation/documentation_schema
     * - Return both the schema ID and its positions IDs as an int[]
     *
     * @param string $schemaFile Path to the JSON schema file
     * @return array{schemaId:int, positionIds:int[]}
     */
    public static function createSchemaAndReturnId(string $schemaFile): array
    {
        $json = file_get_contents($schemaFile);
        if ($json === false) {
            throw new RuntimeException("Unable to read schema file: $schemaFile");
        }

        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $resp = json_decode(
            $curl->_simple_call('POST', 'v1/documentation/documentation_schema', $json, self::buildHeaders()), true
        );

        $schemaId = isset($resp['documentation_schema_id']) ? (int)$resp['documentation_schema_id'] : 0;
        $rawList = $resp['documentation_schema_positions_ids'][0] ?? '';
        $positionIds = [];
        if ($rawList !== '') {
            foreach (explode(',', $rawList) as $part) {
                $positionIds[] = (int)trim($part);
            }
        }

        return [
            'schemaId' => $schemaId,
            'positionIds' => $positionIds,
        ];
    }

    /**
     * fetch any working order no for the current year.
     *
     * @return array{projectNo: int, workingOrderNo: int}
     */
    public static function downloadAnyWorkingOrderAndGetId(): array
    {
        $year = date('Y');
        $endpoint = sprintf('v3/workingorders?filter[status]=2&startDate=%s-01-01&endDate=%s-12-31&partial=workingOrderNo,projectNo', $year, $year);
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $response = $curl->_simple_call('GET', $endpoint, [], self::buildHeaders());
        $data = json_decode($response, true);
        return [
            'projectNo' => (int)$data[0]['projectNo'],
            'workingOrderNo' => (int)$data[0]['workingOrderNo'],
        ];
    }

    /**
     * Upload a single image to a working order and return its fid and filename.
     *
     * @param int $projectNo
     * @param int $workingOrderNo
     * @param string $imageFile
     * @param string $imageMime
     *
     * @return array{fid: int, filename: string}
     */
    public static function uploadImageToWorkingOrder(int $projectNo, int $workingOrderNo, string $imageFile, string $imageMime = 'image/png'): array
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $file = new CURLFile($imageFile, $imageMime, basename($imageFile));
        $body = [
            'rel_type' => 'ktr-aanr',
            'rel_key' => sprintf('%d-%d', $projectNo, $workingOrderNo),
            'filename' => basename($imageFile),
            'file-form-data' => $file,
        ];

        $result = $curl->uploadFile($body, self::buildHeaders(false)['httpheader'], dieOnError: false);

        return [
            'fid' => (int)$result['fid'],
            'filename' => basename($imageFile),
        ];
    }

    /**
     * Upload multiple images from local file paths to a working order and return their fids and filenames.
     *
     * @param int $projectNo
     * @param int $workingOrderNo
     * @param array $imageFilePaths Array of local image file paths
     * @return array<int, array{fid: int, filename: string}>
     */
    public static function uploadImagesToWorkingOrder(int $projectNo, int $workingOrderNo, array $imageFilePaths): array
    {
        $results = [];
        foreach ($imageFilePaths as $imageFilePath) {
            $upload = self::uploadImageToWorkingOrder($projectNo, $workingOrderNo, $imageFilePath);
            $results[] = $upload;
        }
        return $results;
    }

    /**
     * Create a scaffold list item using POST scaffoldLists/create
     * @param int $projectNo
     * @return array The created scaffold list item response
     */
    public static function createScaffoldListItem(int $projectNo): array
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $date = date('Y-m-d');
        $body = [
            "isMontage" => 1,
            "isDemontage" => 0,
            "isRent" => 1,
            "ktr" => $projectNo,
            "typ" => 1,
            "aufmtyp" => 1,
            "bestellnr" => "1",
            "datum" => $date,
            "vhbeg" => $date,
            "vhvorlend" => $date,
            "vhend" => $date,
            "einsatzort" => "Test Location",
            "bezeich" => "CI Test Scaffold List Item",
            "gvh" => 2,
            "gvhtyp" => 2,
            "vhtyp" => 2,
            "aufproz" => 50,
            "aufproz2" => 50,
            "abproz2" => 50,
            "vhproz" => 50,
            "nuproz" => 10,
            "nuproz2" => 10,
            "gbs" => 0,
            "aufmassgenehmigt" => 0,
            "nuauf" => 1,
            "nuabb" => 1,
            "aufmstatus" => 0,
            "nu_frei" => 0,
            "nuauf_aug" => 0,
            "nuabb_aug" => 0,
        ];

        $response = $curl->_simple_call('POST', 'v1/scaffoldlists/create', json_encode($body), self::buildHeaders());
        return json_decode($response, true);
    }

    /**
     * Create a working order from a scaffold list item using POST scaffoldLists/wo
     * @param int $ktr Project Number
     * @param int $intnr Internal Number of Scaffold List Item
     * @return array The created working order response (aanr)
     */
    public static function createWorkingOrderFromScaffoldListItem(int $ktr, int $intnr): array
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $date = date('Y-m-d');
        $body = [
            "ktr" => $ktr,
            "intnr" => $intnr,
            "user" => self::PNR,
            "plannedDate" => $date,
            "plannedStartTime" => "08:00",
            "plannedEndTime" => "17:00",
            "taskSelection" => "Aufbau"
        ];

        $response = $curl->_simple_call('POST', 'v1/scaffoldlists/wo', json_encode($body), self::buildHeaders());
        return json_decode($response, true);
    }

    /**
     * Create a scaffold for a given projectNo and return the scaffold info
     * @param int $projectNo
     * @return array
     */
    public static function createScaffold(int $projectNo): array
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $date = date('Y-m-d');
        $body = [
            "projectNo" => $projectNo,
            "isMontage" => 0,
            "isDemontage" => 0,
            "type" => 1,
            "serviceSheetStatusOption" => 0,
            "orderNumber" => "",
            "detailDescription" => "",
            "date" => $date,
            "beginMontage" => $date,
            "location" => "",
            "shortDescription" => "CIEntityCreator dynamic creation",
            "timeWithoutRent" => 0,
            "typeWithoutRent" => 2,
            "typeWithRent" => 2,
            "percentMontagePerf" => 0,
            "percentMontageBill" => 0,
            "percentDemontageBill" => 0,
            "percentProvision" => 0,
            "percentMontSubcontr" => 0,
            "percentDemontSubcontr" => 0,
            "scaffoldingSlip" => 0,
            "isApproved" => 0,
            "subcontractorMounting" => 0,
            "subcontractorUnmounting" => 0,
            "partialFinishedStatus" => 0,
            "subContractorMeasurement" => 1,
            "subContractorTempEmpLawMount" => 0,
            "subContractorTempEmpLawUnmount" => 0,
            "isRent" => 0,
            "requesteeVirtualNo" => 0,
            "payeeVirtualNo" => 0,
            "createdBy" => self::PNR,
            "labelIds" => 0
        ];
        $response = $curl->_simple_call('POST', 'v2/scaffoldlists?type=scaffold', json_encode($body), self::buildHeaders());
        return json_decode($response, true);
    }

    /**
     * Create a new project for default_test principal, using customerNo.
     * @return array The created project response (including projectNo)
     */
    public static function createProject(int $customerNo): array
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $projectName = 'CI Test Project ' . gmdate('Y-m-d H:i:s') . '.' . substr((string)microtime(true), -3);
        $body = [
            "fiscalYearKey" => date('Y'),
            "projectName" => $projectName,
            "projectValidStartDate" => date('Y-m-d'),
            "customerNo" => $customerNo,
            "projectStatus" => 2,
            "appusage" => 1,
            "projectType" => 3
        ];
        $response = $curl->_simple_call(
            'POST', 'v3/projects', json_encode($body), self::buildHeaders());
        $data = json_decode($response, true);
        $data['projectName'] = $projectName;
        return $data;
    }

    public static function uploadImageToProject(int $projectNo, string $imageFile, string $imageMime = 'image/png'): array
    {
        $curl = new PrintoutCurl(require __DIR__ . '/../utils/DefaultTestBaseUrl.php');
        $file = new CURLFile($imageFile, $imageMime, basename($imageFile));
        $body = [
            'rel_type' => 'ktr',
            'rel_key' => $projectNo,
            'filename' => basename($imageFile),
            'file-form-data' => $file,
        ];
        $result = $curl->uploadFile($body, self::buildHeaders(false)['httpheader'], dieOnError: false);
        return [
            'fid' => (int)$result['fid'],
            'filename' => basename($imageFile),
        ];
    }

    public static function uploadImagesToProject(int $projectNo, array $imageFilePaths): array
    {
        $results = [];
        foreach ($imageFilePaths as $imageFilePath) {
            $upload = self::uploadImageToProject($projectNo, $imageFilePath);
            $results[] = $upload;
        }
        return $results;
    }
}