<?php
/** @noinspection PhpMultipleClassDeclarationsInspection */
/** @noinspection PhpUnused */

// load config file for the PRINTOUT_PROXY constant

$configFile = __DIR__ . '/config.php';
if (file_exists($configFile)) {
    require_once($configFile);
}

/**
 * Work with remote servers via cURL much easier than using the native PHP bindings.
 */
class PrintoutCurl
{
    /**
     * @var string|bool|null hold the cURL response for debugging
     */
    private string|bool|null $response = null;

    /**
     * @var CurlHandle
     */
    private CurlHandle $session;

    private string $url;

    /**
     * @var array fills in curl_setopt_array()
     */
    /**
     * @var array<int|string, mixed>
     */
    private array $options = [
        CURLOPT_TIMEOUT => 600,
        CURLOPT_RETURNTRANSFER => true,
    ];

    /**
     * @var array stores extra HTTP headers
     */
    /**
     * @var array<string, mixed>
     */
    public array $headers = [];

    private bool $isMultiPart = false;

    /**
     * @var string the base URL with / at the end
     */
    private string $baseUrl;

    /**
     * @var string|null a string like "localhost" or null when unset
     */
    private ?string $proxyUrl = null;

    /**
     * @var int|null a port like 9090 or null when unset
     */
    private ?int $proxyPort = null;

    /**
     * @param string|null $baseUrl string the base API URL like . It can end with /
     */
    public function __construct(?string $baseUrl = null)
    {
        if ($baseUrl) {
            if (str_ends_with($baseUrl, '/')) {
                $this->baseUrl = $baseUrl;
            } else {
                $this->baseUrl = $baseUrl . '/';
            }
        }

        if (defined("PRINTOUT_PROXY")) {
            list($url, $port) = explode(':', trim(PRINTOUT_PROXY));
            $this->setProxy($url, (int)$port);
        }
    }

    /**
     * @param string $url the API URL like documentation/schemas
     * @return string the URL with the base API URL if it does not contain http
     */
    private function wrapUrlWithBase(string $url): string
    {
        /** @noinspection HttpUrlsUsage */
        if (str_starts_with($url, "http://") || str_starts_with($url, "https://")) {
            return $url;
        }
        if (str_starts_with($url, "/")) {
            return $this->baseUrl . substr($url, 1);
        }
        return $this->baseUrl . $url;
    }

    /**
     * @param string $method a string like "GET" - case-insensitive
     */
    /**
     * @param string $method
     * @param string $url
     * @param array<string, mixed>|string $params
     * @param array<string, mixed> $options
     * @return bool|string
     */
    public function _simple_call(string $method, string $url, array|string $params = [], array $options = []): bool|string
    {
        $url = $this->wrapUrlWithBase($url);
        $method = strtolower($method);

        // Get acts differently, as it doesn't accept parameters in the same way
        if ($method === 'get') {
            // If a URL is provided, create new session
            $this->create($url . ($params ? '?' . http_build_query($params, '', '&') : ''));
        } else {
            // If a URL is provided, create new session
            $this->create($url);

            $this->{$method}($params);
        }

        // Add in the specific options provided
        $this->options($options);

        return $this->execute();
    }

    /**
     * Call this before executing the curl via curl_multi_exec() or curl_exec().
     *
     * @param array $options merge those options into the default ones, used for every request
     */
    /**
     * @param array<string, mixed> $options
     * @return void
     */
    private function setDefaultOptions(array $options = []): void
    {
        if (!isset($this->options[CURLOPT_TIMEOUT])) {
            $this->options[CURLOPT_TIMEOUT] = 600;
        }
        if (!isset($this->options[CURLOPT_RETURNTRANSFER])) {
            $this->options[CURLOPT_RETURNTRANSFER] = true;
        }

        if (!empty($this->headers)) {
            $this->option(CURLOPT_HTTPHEADER, $this->headers);
        }

        // follow redirects for HTTP to HTTPS
        $this->option(CURLOPT_FOLLOWLOCATION, true);

        if ($this->proxyUrl && $this->proxyPort) {
            $this->option(CURLOPT_PROXY, $this->proxyUrl . ':' . $this->proxyPort);
            $this->option(CURLOPT_SSL_VERIFYHOST, false);
            $this->option(CURLOPT_SSL_VERIFYPEER, false);
        }

        $this->options($options);
    }

    /**
     * @param string $method
     * @param array<string> $urls
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return array<int, mixed>
     */
    /**
     * @param string $method
     * @param array<string> $urls
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return array<int, mixed>
     */
    public function _multi_call(string $method, array $urls, array $params = [], array $options = []): array
    {
        // array of curl handles
        $multiCurl = [];

        // multi handle
        $mh = curl_multi_init();

        $method = strtolower($method);
        $this->isMultiPart = $params['isMultiPart'] ?? false;
        if (isset($params['isMultiPart'])) {
            unset($params['isMultiPart']);
        }

        foreach ($urls as $i => $url) {
            $url = $this->wrapUrlWithBase($url);
            // Get acts differently, as it doesn't accept parameters in the same way
            // If a URL is provided, create new session
            if ($method === 'get') {
                $this->create($url . ($params ? '?' . http_build_query(data: $params, arg_separator: '&') : ''));
            } else {
                $this->create($url);
                $this->{$method}($params);
            }
            $this->options($options[$i] ?? []);

            $this->setDefaultOptions($options);

            $multiCurl[$i] = $this->session;
            curl_multi_add_handle($mh, $multiCurl[$i]);
        }

        do {
            $status = curl_multi_exec($mh, $active);
            if ($active) {
                // wait a short time for more activity
                curl_multi_select($mh, 0.1);
            }
        } while ($active && $status == CURLM_OK);

        $result = [];
        foreach ($urls as $i => $url) {
            $error = curl_error($multiCurl[$i]);
            if (!empty($error)) {
                $result[$i]["error"] = $error;
            }
            $result[$i]["response"] = json_decode(curl_multi_getcontent($multiCurl[$i]), true);

            $responseCode = curl_getinfo($multiCurl[$i], CURLINFO_RESPONSE_CODE);
            $result[$i]["statusCode"] = $responseCode;

            curl_multi_remove_handle($mh, $multiCurl[$i]);
        }

        curl_multi_close($mh);
        return $result;
    }

    /**
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return void
     */
    public function post($params = array(), $options = array()): void
    {
        // If it's an array (instead of a query string) then format it correctly
        if (!$this->isMultiPart) {
            $params = http_build_query($params, '', '&');
        }

        // Add in the specific options provided
        $this->options($options);

        $this->http_method('post');

        $this->option(CURLOPT_POST, TRUE);
        $this->option(CURLOPT_POSTFIELDS, $params);
    }

    /**
     * @param array<string, mixed>|string $params
     * @param array<string, mixed> $options
     * @return void
     */
    /**
     * @param array<string, mixed>|string $params
     * @param array<string, mixed> $options
     * @return void
     */
    /**
     * @param array<string, mixed>|string $params
     * @param array<string, mixed> $options
     * @return void
     */
    public function put(array|string $params = [], array $options = []): void
    {
        // If it's an array (instead of a query string) then format it correctly
        if (is_array($params)) {
            $params = json_encode($params);
        }

        // Add in the specific options provided
        $this->options($options);

        $this->http_method('put');
        $this->option(CURLOPT_POSTFIELDS, $params);

        // Override method, I think this overrides $_POST with PUT data but... we'll see eh?
        $this->option(CURLOPT_HTTPHEADER, ['X-HTTP-Method-Override: PUT']);
    }

    /**
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return void
     */
    /**
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return void
     */
    public function patch(array $params = [], array $options = []): void
    {
        $params = http_build_query(data: $params, arg_separator: '&');

        // Add in the specific options provided
        $this->options($options);

        $this->http_method('patch');
        $this->option(CURLOPT_POSTFIELDS, $params);

        // Override method, I think this overrides $_POST with PATCH data but... we'll see eh?
        $this->option(CURLOPT_HTTPHEADER, ['X-HTTP-Method-Override: PATCH']);
    }

    /**
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return void
     */
    /**
     * @param array<string, mixed> $params
     * @param array<string, mixed> $options
     * @return void
     */
    public function delete(array $params = [], array $options = []): void
    {
        $params = http_build_query(data: $params, arg_separator: '&');

        // Add in the specific options provided
        $this->options($options);

        $this->http_method('delete');

        $this->option(CURLOPT_POSTFIELDS, $params);
    }

    public function http_method(string $method): void
    {
        $this->options[CURLOPT_CUSTOMREQUEST] = strtoupper($method);
    }

    /** @noinspection PhpUnused */
    /**
     * @param string $url a string like localhost
     * @param int $port like 80 or 8080
     */
    public function setProxy(string $url, int $port): void
    {
        $this->proxyUrl = $url;
        $this->proxyPort = $port;
    }

    /**
     * @param array<string, mixed> $options
     * @return void
     */
    public function options(array $options = []): void
    {
        // Merge options in with the rest - done as array_merge() does not overwrite numeric keys
        foreach ($options as $option_code => $option_value) {
            $this->option($option_code, $option_value);
        }

        // Set all options provided
        curl_setopt_array($this->session, $this->options);
    }

    public function option(string|int $code, mixed $value, string $prefix = 'opt'): void
    {
        if (is_string($code) && !is_numeric($code)) {
            $code = constant('CURL' . strtoupper($prefix) . '_' . strtoupper($code));
        }

        $this->options[$code] = $value;
    }

    // Start a session from a URL
    public function create(string $url): void
    {
        $url = $this->wrapUrlWithBase($url);
        $this->url = $url;
        $this->session = curl_init($this->url);
    }

    // End a session and return the results
    public function execute(): bool|string
    {
        $this->setDefaultOptions();

        $this->response = curl_exec($this->session);
        $this->dieOnError();

        curl_close($this->session);
        $response = $this->response;
        $this->set_defaults();
        return $response;
    }

    public function set_defaults(): void
    {
        $this->response = '';
        $this->headers = [];
        $this->options = [];
    }

    /**
     * Call this after curl_exec() to validate if the response failed.
     */
    private function dieOnError(string|null $url = null): void
    {
        $info = curl_getinfo($this->session);
        if ($info === false) {
            curl_close($this->session);
            $errorMessage = ($url === null)
                ? "Failed to get curl info"
                : "Failed to get curl info for $url";
            $this->terminate_with_error($errorMessage);
        }
        $statusCode = $info['http_code'];

        if ($this->response === false || $statusCode >= 400) {
            curl_close($this->session);
            /** @phpstan-ignore-next-line Offset 'effective_method' does not exist, but it does on PHP 8.2. Seems like a PHPStan bug */
            $method = $info['effective_method'] . " ";
            if ($url) {
                $this->terminate_with_error("Curl error on $method" . $url . ' with status code ' . $statusCode . ': ' . $this->response);
            }
            if ($this->url) {
                $this->terminate_with_error("Curl error on $method" . $this->url . ' with status code ' . $statusCode . ': ' . $this->response);
            }
            $this->terminate_with_error("Curl error on $method API call with status code " . $statusCode . ': ' . $this->response);
        }
    }

    /**
     * @param array<string, mixed> $data
     * @param array<string, string> $headers
     * @param bool $dieOnError added because there is/was a time when POST files returned 500, although the response body
     * contained the correct data
     * @return array<string, mixed> the API call response
     */
    public function uploadFile(array $data, array $headers, bool $dieOnError = true): array
    {
        if (!$this->baseUrl) {
            $this->terminate_with_error('BaseURL is not set in PrintoutCurl for fetching the login token!');
        }

        $url = $this->baseUrl . "v1/files";
        $this->session = curl_init($url);
        curl_setopt($this->session, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($this->session, CURLOPT_HTTPHEADER, array_values($headers));
        curl_setopt($this->session, CURLOPT_POST, true);
        curl_setopt($this->session, CURLOPT_POSTFIELDS, $data);

        // follow redirects for HTTP to HTTPS
        curl_setopt($this->session, CURLOPT_FOLLOWLOCATION, true);

        if ($this->proxyUrl && $this->proxyPort) {
            curl_setopt($this->session, CURLOPT_PROXY, $this->proxyUrl . ':' . $this->proxyPort);
            curl_setopt($this->session, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($this->session, CURLOPT_SSL_VERIFYPEER, false);
        }
        $this->response = curl_exec($this->session);
        if ($dieOnError) {
            $this->dieOnError($url);
        }
        curl_close($this->session);
        return json_decode($this->response, true);
    }

    /**
     * @return string the login token without Bearer or Basic
     */
    public function getLoginToken(string $loginPnr, string $loginPassword, string $principal): string
    {
        if (!$this->baseUrl) {
            $this->terminate_with_error('BaseURL is not set in PrintoutCurl for fetching the login token!');
        }

        $response = self::_simple_call('POST', 'v1/login',
            json_encode([
                "username" => $loginPnr,
                "password" => $loginPassword
            ]),
            [
                'httpheader' => [
                    'principal: ' . $principal,
                    'Authorization: Basic dGVzdHdlYnBvcnRhbDp0ZXN0cGFzczRwb3J0YWw=',
                    'Content-Type: application/json'
                ]
            ]
        );

        try {
            $response_json = json_decode($response, true);
            if ($response_json === null) {
                $this->terminate_with_error("Error decoding response JSON: " . $response);
            }
        } catch (Exception $e) {
            $this->terminate_with_error("Error decoding response JSON: " . $response . ' ' . $e);
        }

        if (!isset($response_json["oauth"])) {
            $this->terminate_with_error("Error fetching oauth JSON key from POST login response: \n\n" . print_r($response_json, true));
        }

        return $response_json["oauth"]["access_token"];
    }

    /**
     * Terminates the script with an error message, setting the appropriate
     * exit code for CLI or HTTP status code for web requests.
     *
     * @param string $message The error message to output.
     * @param int $exitCode The exit code to use for CLI (standard is 1 for generic error).
     * @param int $httpStatusCode The HTTP status code for web requests (e.g., 500 for server error).
     * @noinspection PhpSameParameterValueInspection
     */
    private function terminate_with_error(string $message, int $exitCode = 1, int $httpStatusCode = 500): never
    {
        if (php_sapi_name() === 'cli') {
            // For CLI, print message to STDERR and exit with a non-zero code.
            // Using STDERR is best practice for errors so they don't pollute STDOUT.
            fwrite(STDERR, $message . PHP_EOL);
            exit($exitCode);
        } else {
            // For web requests, set the HTTP response code and then die with the message.
            http_response_code($httpStatusCode);
            die($message);
        }
    }
}
