<?php
// disable because there are some debug functions in here
/** @noinspection PhpUnused */

require_once dirname(__FILE__) . '/PrintoutCurl.php';
require_once dirname(__FILE__) . '/core/Response.php';
require_once dirname(__FILE__) . '/core/functions.php';

class PrintoutHelper
{
    private static string $apiBaseUrl = '';

    /**
     * @var array|null set to the document children to merge to a PDF in index.php after generating the PDF.
     *  Check all document positions for type=photo children.
     *  If any contain PDF files, they are all appended at the end to the created PDF.
     */
    public static array|null $documentChildrenForPdfMerging = null;

    /**
     * To avoid downloading files again when $documentToMergePdf is not null, this array is used to keep track
     * of downloaded files.
     *
     * This array contains the fid as the key and another array as the value with those keys:
     * - filePath
     * - mimeType
     */
    public static array $downloadedFilesForDocumentMerging = [];

    /**
     * @var bool Set to true to set the children array of the document to an empty one for testing.
     * Don't set to true and leave in production.
     *
     * Keep this in sync with wkhtmltopdf.py
     */
    public static bool $testEmptyDocChildren = false;

    /**
     * @var bool Set the reported value of some types in the document to a dummy value
     * based on the title.
     *
     * Only use either $testEmptyDocChildren or $testWithDebugChildren.
     */
    public static bool $testWithDebugChildren = false;

    /**
     * Modify this in the model when a template needs a different URL.
     */
    public static bool $injectDummyImages = false;

    /**
     * @return string the base URL starting with http. It should not end with a /, but is stripped away
     * if present.
     */
    public static function getApiBaseUrl(): string
    {
        if (empty(self::$apiBaseUrl)) {
            $principalData = self::fetchPrincipalData();

            if (empty($principalData['api_url'])) {
                $fromEnv = getenv('VERO_API_URL');
                // ksbg principal
//                 $fromEnv = "http://intern.digithebutler.at:8080/api/index.php/";
//                 $fromEnv = "https://api.baubuddy.de/dev/index.php/";
                if (empty($fromEnv)) {
                    $url = require __DIR__ . '/utils/DefaultTestBaseUrl.php';
                } else {
                    $url = $fromEnv;
                }
            } else {
                $url = $principalData['api_url'];
            }

            if (str_ends_with($url, '/')) {
                $url = rtrim($url, '/');
            }

            if (!str_ends_with($url, '/index.php')) {
                // Append if not present for certain API cases, see
                // https://gitlab.baubuddy.de/BauBuddy/scaffoldingAPI/-/merge_requests/3312#note_472925
                $url .= '/index.php';
            }

            self::$apiBaseUrl = $url;
        }

        return self::$apiBaseUrl;
    }

    private static function fetchPrincipalData(): array
    {
        $principal = self::getPrincipal();
        $url = "v1/principals/$principal";
        $baseUrl = 'https://api.baubuddy.de/index.php/';
        /** @noinspection SpellCheckingInspection */
        $options = [
            'httpheader' => [
                'Authorization: Basic dGVzdHdlYnBvcnRhbDp0ZXN0cGFzczRwb3J0YWw',
                'Content-Type: application/json',
                'Principal: master_principal',
                'Content-Language: en'
            ]
        ];

        $curl = new PrintoutCurl($baseUrl);
        $response = $curl->_simple_call('GET', $url, [], $options);

        return json_decode($response, true) ?? [];
    }

    public static function validateApiBaseUrl(): void
    {
        $baseUrl = self::getApiBaseUrl();
        $url = parse_url($baseUrl);

        if (empty($url['scheme'])) {
            http_response_code(500);
            die("URL ($baseUrl) does not contain a scheme like http or https");
        }
    }

    /**
     * Use this when the file URLs in the document are broken or do not exist.
     *
     * @return array update array with https://picsum.photos URL
     */
    public static function injectDummyImages(array $document): array
    {
        $children = [];
        foreach ($document['children'] as $d) {
            if (strtoupper($d['type']) === SchemaTypes::SIGNATURE_FIELD) {
                // use same pixel size as signatures uploaded from the portal
                $url = 'https://picsum.photos/568/400';
                $d['filePath'] = $url;
                $d['thumbPath'] = $url;
            }
            $children[] = $d;
        }
        $document['children'] = $children;
        return $document;
    }

    public static function getIdForTitle(array $document, string $title): int
    {
        foreach ($document['children'] as $d) {
            if ($d['title'] === $title) {
                return $d['id'];
            }
        }

        http_response_code(500);
        die('Did not find title: ' . $title . ' in  document!');
    }

    public static function getDisplayInside(array $document, string $title): array
    {
        $id = self::getIdForTitle($document, $title);
        $result = [];
        foreach ($document['children'] as $d) {
            if (isset($d['displayInside']) && $d['displayInside'] === (string)$id) {
                $uniqueKey = self::getUniqueKey($result, $d['title']);
                $result[$uniqueKey] = self::getValueForDocumentChild($d);
            }
        }
        return $result;
    }

    public static function getChildrenForParentFromDocument(array $document, string $title): array
    {
        $id = self::getIdForTitle($document, $title);
        $result = [];
        foreach ($document['children'] as $d) {
            if (isset($d['parentId']) && $d['parentId'] === (string)$id) {
                $uniqueKey = self::getUniqueKey($result, $d['title']);
                $result[$uniqueKey] = self::getValueForDocumentChild($d);
            }
        }
        return $result;
    }

    /**
     * @return string|array return array for type=photo
     */
    public static function getValueForDocumentChild($child): array|string
    {
        $type = $child['type'];
        if ($type === 'signatureField') {
            return $child['thumbPath'];
        }

        if ($type === 'photo') {
            return [
                'thumbPath' => $child['thumbPath'],
                'filePath' => $child['filePath']
            ];
        }

        if ($type === 'combobox-multi') {
            return $child['reportedValues'];
        }

        return $child['reportedValue'];
    }

    private static function getUniqueKey(array $arr, string $key): string
    {
        $newKey = $key;
        $count = 2;
        while (array_key_exists($newKey, $arr)) {
            $newKey = $key . $count;
            $count++;
        }
        return $newKey;
    }

    /**
     * @param array $child the child from the document
     * @param PrintoutCurl $curl for fetching the image paths when type=photo contains multiple photos
     * @return true|array|string|null return null when no reportedValue key is in the child
     */
    private static function mapToUsableReportedValue(array $child, PrintoutCurl $curl): true|array|string|null
    {
        $type = strtoupper($child['type']);
        if ($type === SchemaTypes::HEADLINE) {
            return null;
        }
        if ($type === SchemaTypes::CHECKBOX) {
            $checked = self::isCheckboxTicked($child);
            if ($checked) {
                return true;
            }
            return null;
        }
        if ($type === SchemaTypes::SIGNATURE_FIELD) {
            if (PrintoutHelper::$testWithDebugChildren) {
                return $child['reportedValues'][0];
            }
            $fid = $child['reportedValues'][0];
            $base = PrintoutHelper::getApiBaseUrl();
            $response = json_decode($curl->_simple_call('get', "$base/files/single/" . $fid,
                [], PrintoutHelper::getHeadersForApiCalls()), true);
            // use the original file since the thumbs are too pixelated
            return $response['filepath'];
        }
        if ($type === SchemaTypes::PHOTO) {
            $photos = [[
                'thumbPath' => $child['thumbPath'],
                'filePath' => $child['filePath']
            ]];
            // do not load first file in array since it is already set above
            foreach (array_slice($child['reportedValues'], 1) as $fid) {
                $base = PrintoutHelper::getApiBaseUrl();
                $response = json_decode($curl->_simple_call('get',
                    "$base/files/single/" . $fid,
                    [], PrintoutHelper::getHeadersForApiCalls()), true);
                $photos[] = [
                    'thumbPath' => $response['thumb_path'],
                    'filePath' => $response['filepath'],
                ];
            }
            return $photos;
        }
        if ($type === SchemaTypes::COMBOBOX_MULTI || $type === SchemaTypes::MEASUREMENT) {
            return $child['reportedValues'];
        }
        if (!isset($child['reportedValue'])) {
            return null;
        }
        return $child['reportedValue'];
    }

    /**
     * @param array $schema the result of downloadSchema()
     * @param array $hierarchicalDocument the result of downloadHierarchicalDocument()
     * @param PrintoutCurl $curl for fetching the image paths when type=photo contains multiple photos
     * @return array an array with integers as its keys (starting at 1) and an array of arrays with 2 keys: value and all.
     *              value contains the result of mapToUsableReportedValue() if the documentation has its value set.
     *              all contains the raw documentation array child if the documentation has it set. Otherwise, the
     *              schema child is inserted.
     */
    public static function mapDocumentChildrenToStableNumberedIndices(
        array $schema, array $hierarchicalDocument, PrintoutCurl $curl): array
    {
        $mapped = [];
        $idCounter = 1;
        foreach ($schema['children'] as $schemaChild) {
            $mappedDocChild = null;
            foreach ($hierarchicalDocument['fullDocument']['children'] as $docChild) {
                if ($schemaChild['id'] === $docChild['id']) {
                    $mappedDocChild = $docChild;
                    break;
                }
            }
            if ($mappedDocChild) {
                $mapped[$idCounter] = [
                    "value" => strtoupper($mappedDocChild['type']) === SchemaTypes::HEADLINE
                        ? ""
                        : PrintoutHelper::mapToUsableReportedValue($mappedDocChild, $curl),
                    "all" => $mappedDocChild
                ];
            } else {
                $mapped[$idCounter] = [
                    "value" => null,
                    "all" => $schemaChild
                ];
            }
            $idCounter++;
        }
        return $mapped;
    }

    /**
     * @param array $document the full unprocessed document from the API with the children key.
     * @param bool $injectParentTitle if true, add the parent title to the output array
     * @param array $schema if not an empty array, inject the parent title from that and not from the document
     */
    public static function mapDocumentChildrenToValues(
        array $document, bool $injectParentTitle = false, array $schema = []): array
    {
        $mapped = [];
        // load Curl for fetching the image paths when type=photo contains multiple photos
        $curl = new PrintoutCurl();

        foreach ($document['children'] as $child) {
            if (!isset($child['reportedValue'])) {
                continue;
            }

            if ($injectParentTitle && $child['parentId']) {
                $found = null;
                if (empty($schema)) {
                    foreach ($document['children'] as $inner) {
                        if ($inner['id'] === $child['parentId']) {
                            $found = $inner['title'];
                            break;
                        }
                    }

                } else {
                    foreach ($schema as $x) {
                        if ($x['id'] === $child['parentId']) {
                            $found = $x['title'];
                            break;
                        }
                    }
                }
                if ($found) {
                    $key = $found . ' ' . $child['title'];
                } else {
                    $key = $child['title'];
                }

            } else {
                $key = $child['title'];
            }
            $uniqueKey = self::getUniqueKey($mapped, $key);
            $mapped[$uniqueKey] = self::mapToUsableReportedValue($child, $curl);
        }
        return $mapped;
    }

    public static function downloadAddressWithPhoneNumber(PrintoutCurl $curl): string
    {
        $infoSettings = PrintoutHelper::downloadInfoSettings($curl);
        $address = PrintoutHelper::formatAddress(
            $infoSettings['address']['address'], $infoSettings['address']['postcode'], $infoSettings['address']['city']);
        if (empty($infoSettings['address']['phoneNumber'])) {
            return $address;
        }
        return $address . ', Tel. ' . $infoSettings['address']['phoneNumber'];
    }

    /**
     * @param array $infoSettingsResponse the result of downloadInfoSettings()
     */
    public static function formatAddressWithPhoneNumber(array $infoSettingsResponse): string
    {
        $address = PrintoutHelper::formatAddress(
            $infoSettingsResponse['address']['address'],
            $infoSettingsResponse['address']['postcode'],
            $infoSettingsResponse['address']['city']);
        if (empty($infoSettingsResponse['address']['phoneNumber'])) {
            return $address;
        }
        return $address . ', Tel. ' . $infoSettingsResponse['address']['phoneNumber'];
    }

    /**
     * Return a string with a comma after address, but not between postal code and city.
     *
     * @return string empty string if all 3 passed parameters are falsy.
     */
    public static function formatAddress(string|null $address, string|null $postalCode, string|null $city): string
    {
        if ($address) {
            if ($postalCode && $city) {
                return $address . ", " . $postalCode . " " . $city;
            }
            if ($postalCode) {
                return $address . ", " . $postalCode;
            }
            if ($city) {
                return $address . ", " . $city;
            }
            return $address;
        }

        if ($postalCode && $city) {
            return $postalCode . " " . $city;
        }
        if ($postalCode) {
            return $postalCode;
        }
        return "";
    }

    public static function getPrincipal(): string
    {
        $headers = getallheaders();
        if (isset($headers['Principal'])) {
            return $headers['Principal'];
        }
        if (isset($headers['principal'])) {
            return $headers['principal'];
        }
        if (isset($_GET['Principal'])) {
            return $_GET['Principal'];
        }
        if (isset($_GET['principal'])) {
            return $_GET['principal'];
        }
        http_response_code(500);
        die("principal header is not set and is also missing from the URL");
    }

    /**
     * @return array[] An array containing the 'httpheader' key with an array value of 2 keys: 'principal' and 'Authorization'.
     */
    public static function getHeadersForApiCalls(): array
    {
        $headers = getallheaders();
        $principal = null;
        $authorization = null;

        // Check headers first (case-insensitive)
        $normalizedHeaders = array_change_key_case($headers);
        if (isset($normalizedHeaders['principal'])) {
            $principal = $normalizedHeaders['principal'];
        }
        if (isset($normalizedHeaders['authorization'])) {
            $authorization = $normalizedHeaders['authorization'];
        }

        // Fall back to GET parameters if headers not found
        if (!$principal) {
            $normalizedGet = array_change_key_case($_GET);
            $principal = $normalizedGet['principal'] ?? null;
        }
        if (!$authorization) {
            $normalizedGet = array_change_key_case($_GET);
            $authorization = $normalizedGet['authorization'] ?? null;
        }

        if ($principal && $authorization) {
            return [
                'httpheader' => [
                    'principal: ' . $principal,
                    'Authorization: ' . $authorization
                ]
            ];
        }

        die_with_response_code(Response::BAD_REQUEST,
            message: "principal and authorization headers are not set and they are also missing from the URL");
    }

    /**
     * Dies when there is no working order for the specified parameters.
     */
    public static function downloadWorkingOrder(
        int $projectId, int $workingOrderId, string $partial, PrintoutCurl $curl): array
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get',
            "$base/v3/workingorders?" .
            "filter[projectNo][eq]=$projectId&filter[workingOrderNo][eq]=$workingOrderId&partial=$partial",
            [], PrintoutHelper::getHeadersForApiCalls());
        $decoded = json_decode($json, true);
        if (empty($decoded)) {
            http_response_code(500);
            die("There is no working order with project ID: $projectId and working order ID: $workingOrderId");
        }
        return $decoded[0];
    }

    public static function downloadSchema(string|int $schemaId, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get', "$base/v1/documentation/schemas/$schemaId",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($json, true);
    }

    public static function downloadSchemaWithDescription(int $schemaId, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get',
            "$base/v1/documentation/schemas?status=active&schemaId=$schemaId",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($json, true);
    }

    public static function downloadCustomer(int $knr, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $json = $curl->_simple_call('get', "$base/v1/addresses/$knr",
            [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($json, true);
    }

    /**
     * @param string|int $projectNo
     * @param string|null $partial if null, partial is not appended
     * @param PrintoutCurl $curl
     * @return mixed|void
     */
    public static function downloadProject(string|int $projectNo, string|null $partial, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $url = "$base/v3/projects?filter[projectNo][eq]=$projectNo";
        if ($partial) {
            $url .= "&partial=$partial";
        }
        $projectJSON = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        $project = json_decode($projectJSON, true);
        if (empty($project)) {
            http_response_code(500);
            die("No project found for projectNo " . $projectNo);
        }
        return $project[0];
    }

    public static function downloadDocumentById(
        int $documentId, PrintoutCurl $curl, bool $considerForPdfMerging = true): array
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $document = json_decode($curl->_simple_call('get', "$base/v1/documentation/$documentId",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        $prepared = PrintoutHelper::prepareDownloadedDocument($document);
        if ($considerForPdfMerging) {
            PrintoutHelper::$documentChildrenForPdfMerging = $document['children'];
        }
        return $prepared;
    }

    public static function downloadHierarchicalDocument(
        string|int $schemaId, string|int $documentId, PrintoutCurl $curl, bool $considerForPdfMerging = true): array
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $document = json_decode($curl->_simple_call('get', "$base/v1/documentation/schemas/$schemaId/$documentId",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
        $prepared = PrintoutHelper::prepareDownloadedDocument($document);
        if ($considerForPdfMerging) {
            PrintoutHelper::$documentChildrenForPdfMerging = $document['children'];
        }
        return $prepared;
    }

    private static function prepareDownloadedDocument(array $document): array
    {
        $document['children'] = PrintoutHelper::adjustTypeTimeChildrenByOffsetHeader($document['children'], getallheaders());

        if (self::$injectDummyImages) {
            $document = self::injectDummyImages($document);
        }

        if (self::$testEmptyDocChildren) {
            $document['children'] = [];

            set_error_handler(function ($errorCode, $errorString): bool {
                if ($errorCode == E_WARNING && str_contains($errorString, "Undefined array key")) {
                    return true;
                }
                // handle other warnings as usual
                return false;
            });

        } else if (self::$testWithDebugChildren) {

            // start at 1 for parity to the schema.json once SchemaIdSorter.php has been run
            $counter = 1;
            $startDate = '1000-01-01T00:00:00Z';

            foreach ($document['children'] as &$child) {
                $type = strtoupper($child['type']);
                // skip to not increase counter which is a bit strange otherwise
                if ($type === SchemaTypes::HEADLINE) {
                    continue;
                }

                $newValue = null;
                $modified = false;

                if ($type === SchemaTypes::STRING) {
                    $newValue = 'STRING: ' . $child['title'] . ' [' . $counter . ']';
                    $modified = true;

                } else if ($type === SchemaTypes::INT) {
                    $newValue = $counter;
                    $modified = true;

                } else if ($type === SchemaTypes::FLOAT) {
                    $newValue = floatval('0.' . $counter);
                    $modified = true;

                } else if ($type === SchemaTypes::TIME) {
                    $hours = floor($counter / 60);
                    $minutes = $counter % 60;
                    $newValue = sprintf('%02d:%02d', $hours, $minutes);
                    $modified = true;

                } else if ($type === SchemaTypes::DATE) {
                    /** @noinspection PhpUnhandledExceptionInspection */
                    $date = new DateTime($startDate);
                    /** @noinspection PhpUnhandledExceptionInspection */
                    $date->modify("+$counter years");

                    $newValue = $date->format('Y-m-d\TH:i:s\Z');
                    $modified = true;

                } else if ($type === SchemaTypes::PHOTO || $type === SchemaTypes::SIGNATURE_FIELD) {
                    $width = rand(100, 3000);
                    $height = rand(100, 3000);
                    $url = 'https://dummyimage.com/';
                    $text = $counter . ' - ' . $width . 'x' . $height . ' - ' . $child['title'];
                    $newValue = $url . $width . 'x' . $height . '?text=' . urlencode($text);
                    $child['filePath'] = $newValue;
                    $child['thumbPath'] = $newValue;
                    $modified = true;

                } else if ($type === SchemaTypes::COMBOBOX) {
                    $newValue = 'COMBOBOX: ' . $child['title'] . ' [' . $counter . ']';
                    $modified = true;

                } else if ($type === SchemaTypes::COMBOBOX_MULTI) {
                    foreach ($child['reportedValues'] as &$value) {
                        $value = 'COMBOBOX-MULTI: ' . $child['title'] . ' [' . $counter . ']';
                    }
                }

                if ($modified) {
                    $child['reportedValue'] = $newValue;
                    $child['reportedValues'] = [$child['reportedValue']];
                }

                $counter++;
            }
        }

        // this needs to be added on top for the groupChildrenUnderParentRecursive function to work
        $main = [];
        $main['id'] = $document['id'];
        $main['title'] = $document['title'];
        $main['type'] = $document['type'];
        $main['required'] = $document['required'];
        $main['reportedValues'] = [];
        array_unshift($document['children'], $main);

        // arranges the document positions in a hierarchical order, based on id <> parentId relations
        return [
            'fullDocument' => $document,
            'document' => PrintoutHelper::groupChildrenUnderParentRecursive($document['children'], 'id', 'parentId'),
            'documentRelKey1' => $document['documentRelKey1']
        ];
    }

    /**
     * Adjusts all type=time children if the X-BauBuddy-UTC-Offset header is set.
     * The X-BauBuddy-UTC-Offset header contains an hour value like 2 which means +2 hour UTC offset.
     *
     * @param array $children all children from the document downloaded from GET v1/documentation/...
     * @param array $headers the headers from the request, usually from getallheaders(). Added for testing
     * @return array the same array if the header is not set. Otherwise, all with type=time are adjusted
     * @noinspection PhpDocMissingThrowsInspection new DateTime('now', ...) can never throw an exception
     */
    public static function adjustTypeTimeChildrenByOffsetHeader(array $children, array $headers): array
    {
        if (isset($headers['X-BauBuddy-UTC-Offset'])) {
            $offset = floatval($headers['X-BauBuddy-UTC-Offset']);
        } else {
            $berlinTimeZone = new DateTimeZone('Europe/Berlin');
            /** @noinspection PhpUnhandledExceptionInspection */
            $now = new DateTime('now', $berlinTimeZone);
            // convert seconds to hours
            $offset = $berlinTimeZone->getOffset($now) / 3600;
        }
        $data = [];
        foreach ($children as $child) {
            if ($child['type'] === 'time') {
                $timeString = explode(':', $child['reportedValue']);
                $hours = intval($timeString[0]);
                $minutes = intval($timeString[1]);
                $reportedMinutes = $hours * 60 + $minutes;
                $adjustedMinutes = $reportedMinutes + $offset * 60;
                $dailyMinutes = ($adjustedMinutes + 1440) % 1440;

                // convert adjusted minutes to hours and minutes
                $hours = floor($dailyMinutes / 60);
                $minutes = $dailyMinutes % 60;
                $time = sprintf('%02d:%02d', $hours, $minutes);
                $child['reportedValue'] = $time;
                $child['reportedValues'][0] = $time;
            }
            $data[] = $child;
        }
        return $data;
    }

    /**
     * @throws Exception
     */
    private static function adjustTime($timeString, $offset): string
    {
        $time = new DateTime($timeString);
        $time->modify($offset . ' minutes');
        return $time->format('H:i');
    }

    /**
     * @throws Exception
     */
    private static function convertToUtc($timeString, $offset): string
    {
        $utc = new DateTime($timeString, new DateTimeZone('UTC'));
        $utc->modify($offset . ' hours');
        return $utc->format('Y-m-d\TH:i:s\Z');
    }

    /**
     * @throws Exception
     */
    public static function adjustHoursByOffsetHeader(array $hours): array
    {
        $headers = getallheaders();
        if (!isset($headers['X-BauBuddy-UTC-Offset'])) {
            return $hours;
        }

        $timeKeys = ['start', 'end', 'startBreak', 'endBreak', 'startDriveTo', 'endDriveTo', 'startDriveFrom', 'endDriveFrom'];
        $utcKeys = ['startUTC', 'endUTC', 'startBreakUTC', 'endBreakUTC', 'startDriveToUTC', 'endDriveToUTC',
            'startDriveFromUTC', 'endDriveFromUTC'];

        $offset = floatval($headers['X-BauBuddy-UTC-Offset']);

        foreach ($hours as &$hour) {
            foreach ($utcKeys as $key) {
                if (!empty($hour[$key])) {
                    $hour[$key] = self::convertToUtc($hour[$key], $offset);
                }
            }
            foreach ($timeKeys as $key) {
                if (!empty($hour[$key])) {
                    $hour[$key] = self::adjustTime($hour[$key], $offset * 60);
                }
            }

            if (isset($hour['breaks'])) {
                for ($i = 0; $i < count($hour['breaks']); $i++) {
                    $hour['breaks'][$i]['start'] = self::adjustTime($hour['breaks'][$i]['start'], $offset * 60);
                    $hour['breaks'][$i]['end'] = self::adjustTime($hour['breaks'][$i]['end'], $offset * 60);
                }
            }
        }

        return $hours;
    }

    public static function convertDecimalToTime(string|float $decimalTime): string
    {
        // Normalize decimal separator and ensure numeric type
        $normalized = (float)str_replace(',', '.', (string)$decimalTime);
        $hours = (int)floor($normalized);
        $minutes = (int)round(($normalized - $hours) * 60);

        // Handle cases where rounding pushes minutes to 60
        if ($minutes === 60) {
            $hours += 1;
            $minutes = 0;
        }
        return sprintf('%02d:%02d', $hours, $minutes);
    }

    public static function getRandomImageUrl(): string
    {
        $base = 'https://picsum.photos/';
        $random1 = rand(100, 3000);
        $random2 = rand(100, 3000);
        return $base . $random1 . '/' . $random2;
    }

    public static function mapOddEven($oddEvenCounter): string
    {
        return ($oddEvenCounter % 2 == 0) ? "even" : "odd";
    }

    /**
     * @return string a string like 44.937,46 with the passed decimal places
     */
    public static function formatTimeNumberWithThousands(float|string $string, int $decimals = 2): string
    {
        return number_format($string, $decimals, ',', '.');
    }

    // TODO remove this and only use formatTimeNumberWithThousands above
    public static function formatTimeNumber(float|string $string): string
    {
        return number_format((float)$string, 2, ',', '.');
    }

    /**
     * @param $index int month starting at 1
     */
    public static function getMonth(string $locale, int $index): false|string
    {
        $formatter = new IntlDateFormatter($locale, IntlDateFormatter::FULL, IntlDateFormatter::NONE);
        $formatter->setPattern('MMMM');
        return $formatter->format(mktime(0, 0, 0, $index, 1));
    }

    /**
     * Note that the passed path is case-sensitive!
     */
    public static function translateLocalPathToServerPathFromRoot(string $pathFromRoot): string
    {
        return $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . '/' . $pathFromRoot;
    }

    /**
     * Do not use paths with /../ in it because they work only locally, but not on server.
     */
    public static function translateLocalPathToServerPath(string $localPath): array|string
    {
        if (str_contains($localPath, "../") || str_contains($localPath, "/..")) {
            die_with_response_code(Response::SERVER_ERROR,
                "The passed path to translateLocalPathToServerPath() contains .. which will not work when deployed!");
        }
        if ($_SERVER['HTTP_HOST'] === 'localhost') {
            $documentRoot = $_SERVER['DOCUMENT_ROOT'];
            // append trailing slash so the URL does not look like http://localhost//...
            if (!str_ends_with($documentRoot, DIRECTORY_SEPARATOR)) {
                $documentRoot .= DIRECTORY_SEPARATOR;
            }
            return
                $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . '/' .
                substr($localPath, strlen($documentRoot));
        }
        $apiFolder = str_replace('/index.php', '', $_SERVER['SCRIPT_NAME']);
        return str_replace(
            getcwd(),
            $_SERVER['REQUEST_SCHEME'] . '://' . $_SERVER['HTTP_HOST'] . $apiFolder,
            $localPath);
    }

    private static function setChildToParent(
        &$branch, $child, $idFieldName, $parentIdFieldName, $childrenArrayFieldName)
    {
        $setChild = null;
        foreach ($branch as $branchProjectIndex => &$branchProject) {
            if ($branchProjectIndex == $child[$parentIdFieldName]) {
                $branchProject[$childrenArrayFieldName][$child[$idFieldName]] = $child;
                $setChild = true;

            } elseif (isset($branchProject[$childrenArrayFieldName]) &&
                $branchProject[$childrenArrayFieldName]) {

                $setChild = self::setChildToParent($branchProject[$childrenArrayFieldName], $child, $idFieldName, $parentIdFieldName, $childrenArrayFieldName);
            }
        }
        return $setChild;
    }

    private static function getChildrenUnderRoot(array $projects, $idFieldName): array
    {
        $children = [];
        foreach ($projects as $project) {
            if ($project[$idFieldName] !== null)
                $children[] = $project;
        }
        return $children;
    }

    private static function groupChildrenUnderParent(
        $result, $idFieldName, $parentIdFieldName, $childrenArrayFieldName)
    {
        if (self::getChildrenUnderRoot($result, $idFieldName)) {
            foreach ($result as $id => $item) {
                if (isset($item[$parentIdFieldName])) {
                    // find parent recursively and add child
                    $setChild = self::setChildToParent(
                        $result, $result[$id], $idFieldName, $parentIdFieldName, $childrenArrayFieldName);
                    if ($setChild) {
                        // remove child from root
                        unset($result[$id]);
                    }
                }
            }
        }
        return $result;
    }

    /**
     * Transform flat array of parents and children into hierarchical tree
     *
     * @param $array array one level depth array containing parents and children
     * @param $idFieldName string name of the field containing the unique id in the array
     * @param $parentIdFieldName string name of the field containing the parent id in the array
     * @param $childrenArrayFieldName string name of the field in which the children will be added (children, subprojects, ...)
     * @return array hierarchical version of the array passed as param
     */
    public static function groupChildrenUnderParentRecursive(
        array  $array, string $idFieldName, string $parentIdFieldName,
        string $childrenArrayFieldName = 'children'): array
    {
        if (!$array[0][$idFieldName]) {
            return $array;
        }

        if ($array) {
            $temp = [];
            foreach ($array as $item) {
                $temp[$item[$idFieldName]] = $item;
            }

            $temp = PrintoutHelper::groupChildrenUnderParent(
                $temp, $idFieldName, $parentIdFieldName, $childrenArrayFieldName);
            $r = [];
            PrintoutHelper::removeProjectKeysRecursive($temp, $r, $childrenArrayFieldName);
            return $r;
        }
        return [];
    }

    private static function removeProjectKeysRecursive($array, &$result, $childrenArrayFieldName): void
    {
        foreach ($array as $value) {
            if (isset($value[$childrenArrayFieldName]) && $value[$childrenArrayFieldName]) {
                $subResult = [];
                self::removeProjectKeysRecursive(
                    $value[$childrenArrayFieldName], $subResult, $childrenArrayFieldName);
                $value[$childrenArrayFieldName] = $subResult;
            }
            $result[] = $value;
        }
    }

    /**
     * Recursive Function to build up the nested data.
     * Used mostly in the documentation models for nested schema positions.
     *
     * @return array flat array containing $keyFieldName and $valueFieldName pairs
     */
    public static function getDeepValueFromField(
        $keyFieldName, $valueFieldName, $node, $childrenFieldName, $includeDisplayInside = false): array
    {
        if (isset($node[$valueFieldName]) && $node[$valueFieldName]) {
            return [$node[$keyFieldName] => $node[$valueFieldName]];
        }

        if (isset($node[$childrenFieldName]) && $node[$childrenFieldName]) {
            $paths = [];
            foreach ($node[$childrenFieldName] as $child) {
                if ($includeDisplayInside) {
                    $value = PrintoutHelper::getDeepValueFromField(
                        $keyFieldName, $valueFieldName,
                        PrintoutHelper::setPathAsValue($child), $childrenFieldName, true)[$child[$keyFieldName]];
                    if ($child['type'] == 'headline') {
                        $paths[$node[$keyFieldName]][$child[$keyFieldName]] = $value;
                    } // once we find a position we also look for displayInside content
                    else {
                        // store the value of displayInside, no path needed here
                        if ($child['displayInside']) {
                            $paths[] = $value;
                        } // store reportedValue for the position itself
                        else {
                            $paths[$node[$keyFieldName]][$child[$keyFieldName]][$valueFieldName] = $value;
                        }

                        // look for displayInside
                        if ($child['children']) {
                            // drop the already processed reportedValue to make the recursion look for the children array
                            unset($child[$valueFieldName]);
                            $displayInsideValue = PrintoutHelper::getDeepValueFromField(
                                $keyFieldName, $valueFieldName,
                                PrintoutHelper::setPathAsValue($child), $childrenFieldName, true);
                            $paths[$node[$keyFieldName]][$child[$keyFieldName]]['displayInside'] =
                                $displayInsideValue[0];
                        }
                    }
                } else {
                    $paths[$node[$keyFieldName]][$child[$keyFieldName]] =
                        PrintoutHelper::getDeepValueFromField(
                            $keyFieldName, $valueFieldName,
                            PrintoutHelper::setPathAsValue($child), $childrenFieldName)[$child[$keyFieldName]] ?? [];
                }
            }
            return $paths;
        }
        return [];
    }

    private static function setPathAsValue($child)
    {
        if ($child['type'] == 'formula') {
            $child['reportedValue'] = eval('return ' . $child['reportedValue'] . ';');
        }

        if (isset($child['filePath'])) {
            $child['reportedValue'] = $child['filePath'];
            $child['reportedValues'][0] = $child['filePath'];
        }
        return $child;
    }

    //When the eval function is used all formulas have to be treated with this function because of leading zeroes
    //sometimes and issues with PHP 7
    public static function treatFormulaStructure($formula): array|int|string|null
    {
        if (empty($formula)) {
            return 0;
        }
        return preg_replace('/(?<![\d.,])0+(?=\d)/', '', $formula);
    }

    /**
     * This should be used to validate the status code for multi curl sequences.
     *
     * @param int $statusCode HTTP status we got back
     * @param string $context A short description of what call failed (e.g. "GET /v1/…")
     * @param mixed $response The raw response body or array
     * @param int $dieCode HTTP code to return to the client (default 500)
     */
    public static function ensureStatusCode(int $statusCode, string $context, mixed $response, int $dieCode = Response::SERVER_ERROR): void
    {
        if ($statusCode !== 200) {
            $message = sprintf('%s failed with %d%s%s', $context, $statusCode, "\n",
                is_string($response) ? $response : print_r($response, true)
            );
            die_with_response_code($dieCode, $message);
        }
    }

    /**
     * Works with associative array to build dynamically HTML table with two columns - key on the left and value on the right
     * @param string $header the header of the table
     */

    public static function buildTable($data, string $header): void
    {
        /** @noinspection HtmlDeprecatedAttribute */
        echo '<table class="genericTable" cellpadding="2" cellspacing="0" width="100%">';
        echo '<tr><th colspan="2">' . $header . '</th></tr>';
        foreach ($data as $k => $v) {
            if (is_array($v)) {
                echo '<tr class="content"><td colspan="2">' . $k . '</td></tr>';
                foreach ($v as $key => $val) {
                    /** @noinspection HtmlDeprecatedAttribute */
                    echo '<tr class="content"><td>' . $key . '</td>' . '<td align="right">' . $val . '</td></tr>';
                }
            } else {
                /** @noinspection HtmlDeprecatedAttribute */
                echo '<tr class="content"><td>' . $k . '</td>' . '<td align="right">' . $v . '</td></tr>';
            }

        }
        echo '</table>';
    }

    public static function downloadSettings(PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get', "$base/v1/settings/common", [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    public static function downloadInfoSettings(PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get', "$base/v1/settings/info", [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    public static function downloadFile(int $fid, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get',
            "$base/v1/files/single/$fid",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    /**
     * Downloads partner information.
     *
     * @param int|string $knr
     * @param int|string $lfdnr
     * @param PrintoutCurl $curl
     * @return mixed
     */
    public static function downloadPartner(int|string $knr, int|string $lfdnr, PrintoutCurl $curl): mixed
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get',
            "$base/v1/partners/select/$knr?lfdnr=$lfdnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    public static function downloadResource(string|int $rnr, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        return json_decode($curl->_simple_call('get',
            "$base/v1/vehicles/select_resource/$rnr",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    public static function downloadEmployee(string|int $pnr, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        // use ?showRelatedProject=false for better performance
        return json_decode($curl->_simple_call('get',
            "$base/v3/employees/$pnr?showRelatedProject=false",
            [], PrintoutHelper::getHeadersForApiCalls()), true);
    }

    public static function downloadPermissions(string|int $pnr, PrintoutCurl $curl)
    {
        $base = PrintoutHelper::getApiBaseUrl();
        $url = "$base/v1/authorize/user/perm/$pnr";
        $response = $curl->_simple_call('get', $url, [], PrintoutHelper::getHeadersForApiCalls());
        return json_decode($response, true)['permissions'] ?? [];
    }

    public static function dateConverter($date, $dateFormat): string
    {
        try {
            return (new DateTime($date))->format($dateFormat);
        } catch (Exception) {
            http_response_code(500);
            die('Can not convert: ' . $date . ' to ' . $dateFormat);
        }
    }

    /**
     * @return string a string like "en" or "de". "en" is default
     */
    public static function getLanguage(): string
    {
        return $_SERVER['HTTP_CONTENT_LANGUAGE'] ?? 'en';
    }

    /**
     * @return string return the language value from the global $printoutLang array, or if not found, the key parameter
     */
    public static function lang($key): string
    {
        global $printoutLang;
        if (!isset($printoutLang)) {
            $language = self::getLanguage();

            $lang_file = dirname(__FILE__) . "/lang/$language.php";
            if (file_exists($lang_file)) {
                require_once($lang_file);
            } else {
                require_once dirname(__FILE__) . "/lang/en.php";
            }
        }

        if (isset($printoutLang[$key])) {
            return $printoutLang[$key];
        }
        return 'MISS ' . $key;
    }

    /**
     * @param bool $condition if true echo the checked checkbox, otherwise the unchecked one
     */
    public static function echoCheckbox(bool $condition): void
    {
        echo $condition ? "&#9745;" : "&#9744;";
    }

    /**
     * Echo the checked checkbox, otherwise the unchecked one, if the $needle is in $array.
     */
    public static function echoCheckboxIfContained($array, $needle): void
    {
        if ($array && is_array($array)) {
            echo in_array($needle, $array) ? "&#9745;" : "&#9744;";
        } else {
            echo "&#9744;";
        }
    }

    /**
     * @param int $dayOfWeek 1 for Monday, 7 for Sunday
     * @param string|null $locale a locale like "en" or "de"
     * @return false|string
     */
    public static function getWeekDayName(int $dayOfWeek, string|null $locale = null): false|string
    {
        if (is_null($locale)) {
            $locale = self::getLanguage();
        }
        $formatter = new IntlDateFormatter($locale, IntlDateFormatter::FULL, IntlDateFormatter::NONE);
        $formatter->setPattern('EEEE');
        $timestamp = strtotime("next Monday") + ($dayOfWeek - 1) * 24 * 60 * 60;
        return $formatter->format($timestamp);
    }

    /**
     * Will crash if $child is not of type=checkbox!
     *
     * @return bool true when the type=checkbox child is checked
     */
    public static function isCheckboxTicked(array $child): bool
    {
        // double check type, just to be very sure!
        if (strtoupper($child['type']) !== SchemaTypes::CHECKBOX) {
            http_response_code(500);
            die("isCheckboxTicked() was passed a non type=checkbox child: " . print_r($child, true));
        }

        // there are so many edge cases here, especially when type=checkbox is required=true,
        // so the code tries to handle all cases

        if (isset ($child['reportedValue'])) {
            return self::isCheckboxTickedByReportedValue($child['reportedValue']);
        }

        if (isset ($child['reportedValues']) && count($child['reportedValues']) >= 1) {
            return self::isCheckboxTickedByReportedValue($child['reportedValues'][0]);
        }

        // if this is reached, something is really wrong with the API response
        return false;
    }

    /**
     * Use self::isCheckboxTicked() if you have a document child.
     * @param $reportedValue bool|int|string
     * @return bool
     */
    public static function isCheckboxTickedByReportedValue(bool|int|string $reportedValue): bool
    {
        // Portal sends: "on"
        // Android sends: "1"
        // iOS sends:  true
        // I noticed for MediaPrintV3 that the English names are also sent
        // check for the 1 integer just to be very sure
        return $reportedValue === "1" || $reportedValue === "on" || $reportedValue === 1
            || $reportedValue === true || strtolower($reportedValue) === 'ja' || strtolower($reportedValue) === 'yes';
    }

    /**
     * Echo the checked checkbox Unicode, otherwise the unchecked one checkbox when $reportedValue is considered checked.
     *
     * @param $reportedValue bool|int|string the checkbox data from $data
     */
    public static function echoCheckboxByReportedValue(bool|int|string $reportedValue): void
    {
        echo self::isCheckboxTickedByReportedValue($reportedValue) ? "&#9745;" : "&#9744;";
    }

    /**
     * @param string $hexColor a string in format "#RRGGBB" or "#RGB"
     * @return array an array with the red, green and blue values
     */
    static public function convertHexToRGB(string $hexColor): array
    {
        if ($hexColor[0] == '#') {
            $hexColor = substr($hexColor, 1);
        }

        if (strlen($hexColor) == 3) {
            $hexColor = $hexColor[0] . $hexColor[0] . $hexColor[1] . $hexColor[1] . $hexColor[2] . $hexColor[2];
        }

        $r = hexdec($hexColor[0] . $hexColor[1]);
        $g = hexdec($hexColor[2] . $hexColor[3]);
        $b = hexdec($hexColor[4] . $hexColor[5]);

        return array($r, $g, $b);
    }

    /**
     * The YIQ algorithm from https://24ways.org/2010/calculating-color-contrast
     *
     * @param string $hexColor a string in format "#RRGGBB" or "#RGB"
     * @return bool return true when the color is dark
     */
    static public function isDarkColor(string $hexColor): bool
    {
        $colors = self::convertHexToRGB($hexColor);
        $yiq = (($colors[0] * 299) + ($colors[1] * 587) + ($colors[2] * 114)) / 1000;
        return $yiq < 128;
    }

    /**
     * @return array an empty array should the file not exist, is not readable or when the {@link TempsQueryParameter} parameter is not set
     */
    public static function readGetDataResultFromTempsFile(): array
    {
        if (!isset($_GET[self::TempsQueryParameter])) {
            return [];
        }
        $tmpFile = $_GET[self::TempsQueryParameter];
        if (!file_exists($tmpFile)) {
            return [];
        }
        $tempsContent = file_get_contents($tmpFile);
        if ($tempsContent === false) {
            return [];
        }
        return json_decode($tempsContent, true);
    }

    public static function doesUrlReturn200(string $url): bool
    {
        try {
            // encode URL path only since using rawurlencode() also messes with the scheme characters which always fails get_headers()
            $parsedUrl = parse_url($url);
            $scheme = isset($parsedUrl['scheme']) ? $parsedUrl['scheme'] . '://' : '';
            $host = $parsedUrl['host'] ?? '';
            if (isset($parsedUrl['port']) && $host !== "") {
                $host .= ':' . $parsedUrl['port'];
            }
            $path = isset($parsedUrl['path']) ? implode('/', array_map('rawurlencode', explode('/', $parsedUrl['path']))) : '';
            $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
            $encodedUrl = $scheme . $host . $path . $query;

            $headers = get_headers($encodedUrl);
            return str_contains($headers[0], "200 OK");
        } catch (Exception) {
            return false;
        }
    }

    /**
     * @param string $url the URL to probe
     * @return string|null uses convertBytesToHumanString to convert the bytes to a human-readable string
     */
    public static function probeUrlContentLength(string $url): ?string
    {
        $headers = get_headers($url, true);
        if ($headers === false) {
            return null;
        }
        if (!str_contains($headers["0"], "200 OK")) {
            return null;
        }
        $contentLengthBytes = $headers["Content-Length"];
        return PrintoutHelper::convertBytesToHumanString($contentLengthBytes);
    }

    public static function convertBytesToHumanString(int $bytes): string
    {
        $units = [' B', ' KB', ' MB', ' GB'];
        $unitIndex = 0;

        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }

        return number_format($bytes) . $units[$unitIndex];
    }

    /**
     * This works for local file system paths like translateLocalPathToServerPath() or URLs from the internet.
     */
    public static function getImageBase64String($imageUrl): string
    {
        if (!self::doesUrlReturn200($imageUrl)) {
            return '';
        }

        $imageData = @file_get_contents($imageUrl);
        if ($imageData === false) {
            return '';
        }

        $imageData = file_get_contents($imageUrl);
        return 'data:image/' . pathinfo($imageUrl, PATHINFO_EXTENSION) . ';base64,' . base64_encode($imageData);
    }

    /**
     * Optional. When set, it should be an absolute file path to a JSON file.
     *
     * The file will be in a temporary OS file directory and should be cleaned up by the OS.
     */
    const TempsQueryParameter = "tempsFilePath";

    /**
     * If "true", check all document positions for type=photo children.
     * If any contain PDF files, they are all appended at the end to the created PDF.
     */
    const AppendPdfsToPrintoutQueryParameter = "appendPdfsToPrintout";

    /**
     * Formats a numeric value with locale-specific separators and optional trimming of trailing zeros.
     *
     * - Accepts text input that may use either comma or dot as decimal separator
     * - Rounds to the specified number of decimal places
     * - Always uses the thousand separators every 3 digits
     * - Uses German (de) or English (en) locale semantics for separators
     * - Trims trailing zeros and removes dangling decimal separator
     */
    public static function formatNumberLocale(float|string $input, int $decimalPlaces, NumberLocale $locale): string
    {
        // Normalize input to float: allow both comma and dot as decimal separator
        if (is_string($input)) {
            $normalized = str_replace([' ', '\u{00A0}'], '', $input); // strip spaces incl. nbsp
            // If both separators exist, assume last separator is decimal; remove others as thousands
            $hasComma = str_contains($normalized, ',');
            $hasDot = str_contains($normalized, '.');
            if ($hasComma && $hasDot) {
                $lastComma = strrpos($normalized, ',');
                $lastDot = strrpos($normalized, '.');
                if ($lastComma > $lastDot) {
                    // comma is decimal, dots are thousands
                    $normalized = str_replace('.', '', $normalized);
                    $normalized = str_replace(',', '.', $normalized);
                } else {
                    // dot is decimal, commas are thousands
                    $normalized = str_replace(',', '', $normalized);
                }
            } elseif ($hasComma) {
                // Treat comma as decimal
                $normalized = str_replace('.', '', $normalized); // safety: remove any dot thousands
                $normalized = str_replace(',', '.', $normalized);
            } else {
                // Only dot or plain number
                $normalized = str_replace(',', '', $normalized); // safety: remove stray commas
            }
            // casting after normalization
            $value = (float)$normalized;
        } else {
            $value = $input;
        }

        // Round to desired decimals avoiding scientific notation
        $rounded = round($value, $decimalPlaces);

        // Determine separators by locale
        $isGerman = $locale === NumberLocale::DE;
        $decSep = $isGerman ? ',' : '.';
        $thouSep = $isGerman ? '.' : ',';

        // Use number_format to force thousands separator always; then trim decimals if needed
        $formatted = number_format($rounded, $decimalPlaces, '.', ''); // temporary with dot decimal, no thousands

        // Split integer/decimal to inject thousands
        $parts = explode('.', $formatted, 2);
        $intPart = $parts[0];
        $decPart = $parts[1] ?? '';

        // Add the thousand separators to integer part every 3 digits (handle negatives)
        $sign = '';
        if (str_starts_with($intPart, '-')) {
            $sign = '-';
            $intPart = substr($intPart, 1);
        }
        $intPartWithSep = preg_replace('/\B(?=(\d{3})+(?!\d))/', $thouSep, $intPart);

        // Trim trailing zeros from decimal part
        if ($decimalPlaces > 0 && $decPart !== '') {
            $decPart = rtrim($decPart, '0');
        }

        // Build final string
        if ($decPart === '') {
            return $sign . $intPartWithSep;
        }
        return $sign . $intPartWithSep . $decSep . $decPart;
    }
}

class ColorHelper
{
    /**
     * Calculates the relative luminosity of a given color based on WCAG 2.0.
     *
     * @param string $color A color in "#RRGGBB" format.
     * @return float The calculated luminosity.
     */
    public static function calculateLuminosity(string $color): float
    {
        $color = ltrim($color, '#');
        $r = hexdec(substr($color, 0, 2)) / 255;
        $g = hexdec(substr($color, 2, 2)) / 255;
        $b = hexdec(substr($color, 4, 2)) / 255;

        $r = ($r <= 0.03928) ? ($r / 12.92) : pow((($r + 0.055) / 1.055), 2.4);
        $g = ($g <= 0.03928) ? ($g / 12.92) : pow((($g + 0.055) / 1.055), 2.4);
        $b = ($b <= 0.03928) ? ($b / 12.92) : pow((($b + 0.055) / 1.055), 2.4);

        return (0.2126 * $r + 0.7152 * $g + 0.0722 * $b);
    }

    /**
     * Calculates the luminosity ratio between two colors.
     *
     * @param string $color1 A color in "#RRGGBB" format.
     * @param string $color2 A color in "#RRGGBB" format.
     * @return float The luminosity ratio.
     */
    public static function calculateLuminosityRatio(string $color1, string $color2): float
    {
        $l1 = self::calculateLuminosity($color1);
        $l2 = self::calculateLuminosity($color2);
        return ($l1 > $l2) ? (($l1 + 0.05) / ($l2 + 0.05)) : (($l2 + 0.05) / ($l1 + 0.05));
    }

    /**
     * Returns the contrast ratio between two colors.
     *
     * @param string $color1 A color in "#RRGGBB" format.
     * @param string $color2 A color in "#RRGGBB" format.
     * @return float The contrast ratio.
     */
    public static function contrastRatio(string $color1, string $color2): float
    {
        $color1 = ltrim($color1, '#');
        $color2 = ltrim($color2, '#');
        return self::calculateLuminosityRatio($color1, $color2);
    }

    /**
     * Returns a readable text color (black or white) for the given background color.
     *
     * @param string $backgroundColor A color in "#RRGGBB" format.
     * @return string Either "#000000" or "#ffffff".
     */
    public static function getReadableTextColor(string $backgroundColor): string
    {
        $backgroundColor = ltrim($backgroundColor, '#');
        $contrastWithBlack = self::calculateLuminosityRatio('000000', $backgroundColor);
        $contrastWithWhite = self::calculateLuminosityRatio('ffffff', $backgroundColor);
        return ($contrastWithBlack > $contrastWithWhite) ? '#000000' : '#ffffff';
    }


    /**
     * Projects the given hexadecimal color towards white by the specified percentage.
     *
     * This function accepts a hexadecimal color value and a percentage,
     * then calculates a lighter shade of the color by moving its RGB values
     * closer to 255 (white). If the provided hex code is not in a 6-character format,
     * the original hex code (prefixed with '#') is returned.
     *
     * @param string $hexColor The hexadecimal color code (with or without a leading '#').
     * @param float|int $percent The percentage (0-100) by which to lighten the color.
     *
     * @return string The resulting hexadecimal color code, prefixed with '#'.
     */
    public static function projectColor(string $hexColor, float|int $percent): string
    {
        $hexColor = ltrim($hexColor, '#');
        if (strlen($hexColor) !== 6) {
            return '#' . $hexColor;
        }
        $r = hexdec(substr($hexColor, 0, 2));
        $g = hexdec(substr($hexColor, 2, 2));
        $b = hexdec(substr($hexColor, 4, 2));

        $r = round($r + ($percent / 100 * (255 - $r)));
        $g = round($g + ($percent / 100 * (255 - $g)));
        $b = round($b + ($percent / 100 * (255 - $b)));

        return '#' . sprintf('%02x%02x%02x', $r, $g, $b);
    }
}

enum NumberLocale: string
{
    case EN = 'en';
    case DE = 'de';
}

class SchemaTypes
{
    const  HEADLINE = 'HEADLINE';
    const  PHOTO = 'PHOTO';
    const  INFO = 'INFO';
    const  DATE = 'DATE';
    const  STRING = 'STRING';
    const  CHECKBOX = 'CHECKBOX';
    const  SIGNATURE_FIELD = 'SIGNATUREFIELD';
    const  INVOICE_SELECTOR = 'INVOICESELECTOR';
    const  EMPLOYEE_SELECTOR = 'EMPLOYEESELECTOR';
    const  PARTNER_SELECTOR = 'PARTNERSELECTOR';
    const  CUSTOMER_SELECTOR = 'CUSTOMERSELECTOR';
    const  RESOURCE_SELECTOR = 'RESOURCESELECTOR';
    const  GRAPHICAL_MEASUREMENT = 'GRAPHICALMEASUREMENT';
    const  SUPPLIER_SELECTOR = 'SUPPLIERSELECTOR';
    const  TASK_SELECTOR = 'TASKSELECTOR';
    const  MEASUREMENT = 'MEASUREMENT';
    const  FORMULA = 'FORMULA';
    const  COMBOBOX = 'COMBOBOX';
    const  COMBOBOX_MULTI = 'COMBOBOX-MULTI';
    const  COMBOBOX_TAP = 'COMBOBOX-TAP';
    const  INT = 'INT';
    const  FLOAT = 'FLOAT';
    const  TIME = 'TIME';
    const  MATRIX_MEASUREMENT = 'MATRIXMEASUREMENT';
    const  EXPAND_SUB_PROJECT_STRUCTURE = 'EXPAND-SUB-PROJECT-STRUCTURE';
}
