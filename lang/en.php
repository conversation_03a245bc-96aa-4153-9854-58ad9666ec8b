<?php
$printoutLang["approved_total"] = "Approved total";
$printoutLang["approver"] = "Approver";
$printoutLang["bookings"] = "Bookings";
$printoutLang["break_time"] = "Break time";
$printoutLang["days"] = "Days";
$printoutLang["drive_from_time"] = "Drive time from";
$printoutLang["drive_to_time"] = "Drive time to";
$printoutLang["hours"] = "Hours";
$printoutLang["hours_total"] = "Total";
$printoutLang["hours|d"] = "Hours";
$printoutLang["notes"] = "Notes";
$printoutLang["timeTrackEvent"] = "Time track event";
$printoutLang["total_drive_time"] = "Drive time";
$printoutLang["total_working_time_brackets"] = "Working time (Total)";
$printoutLang["unapproved_total"] = "Unapproved Total";
$printoutLang["view_your_hours"] = "List";
$printoutLang["work_and_drive_time"] = "(Work+Drive)";
$printoutLang["working_time"] = "Working time";
$printoutLang["x_hours"] = "hours";
$printoutLang["year"] = "Year";
$printoutLang["working_order"] = "Working order";
$printoutLang["total_printable"] = "(Total)";
$printoutLang["total_working_time"] = "Working time";
$printoutLang["sum"] = "Sum";
$printoutLang["title"] = "Title";
$printoutLang["author"] = "Author";
$printoutLang["uploaded_at"] = "Uploaded At";
$printoutLang["created_at"] = "Created At";
$printoutLang["customer_name"] = "Customer Name";
$printoutLang["comments"] = "Comments";
$printoutLang["project_number"] = "Project Number";
$printoutLang["project_description"] = "Project Description";
$printoutLang["date"] = "Date";
$printoutLang["working_order_description"] = "Working Order Description";
$printoutLang["construction_site_address"] = "Address of construction site";
$printoutLang["site_supervisor"] = "Responsible person on site (AvO)";
$printoutLang["risk_management_list"] = "Revision list for risk assessment";
$printoutLang["intro_text"] = "This checklist must be worked through on the construction site before work begins. This documents the current situation on the construction/assembly site, analyzes the hazards and enables the person responsible for the job on site (AvO) to initiate the necessary measures to largely eliminate the risk of accidents and injuries for your trade or to reduce them to a minimum.";
$printoutLang["outro_text"] = "The information in the revision list corresponds to the facts, they were carried out to the best of our knowledge and belief by the person responsible for the order on site (AvO). Based on the results, the necessary measures could be initiated, the employees on site instructed and explicit danger points pointed out.";
$printoutLang["documentation"] = "Documentation";
$printoutLang["yes"] = "Yes";
$printoutLang["no"] = "No";
$printoutLang["remark_measures"] = "Remark / Measures";
$printoutLang["general_traffic_safety"] = "General traffic safety (roads, thoroughfares, sidewalks)";
$printoutLang["site_equipment"] = "Site equipment";
$printoutLang["site_conditions"] = "Site conditions";
$printoutLang["machines_devices"] = "Machines and devices (assembly, transport aid)";
$printoutLang["first_aid"] = "First Aid / Fire Protection";
$printoutLang["assembly_conditions"] = "Assembly Conditions";
$printoutLang["tools"] = "Hand machines, tools, accessories";
$printoutLang["employee"] = "Employee";
$printoutLang["miscellaneous"] = "Miscellaneous";
$printoutLang["location"] = "Location";
$printoutLang["time"] = "Time";
$printoutLang["site_supervisor_signature"] = "Signature of the person responsible on site (AvO)";
$printoutLang["test"] = "This is a text to test translation";
$printoutLang["uzbekistan"] = "Uzbekistan";
$printoutLang["oversize_no."] = "Oversize No.";
$printoutLang["project"] = "Project";
$printoutLang["client"] = "Client";
$printoutLang["order"] = "Order";
$printoutLang["location"] = "Location";
$printoutLang["description"] = "Description";
$printoutLang["set_up"] = "Set Up";
$printoutLang["basic_rent"] = "Basic rent";
$printoutLang["serial_number"] = "serno";
$printoutLang["item_number"] = "item no.";
$printoutLang["specification_of_service"] = "specification of service";
$printoutLang["pc"] = "pc";
$printoutLang["length"] = "length";
$printoutLang["depth"] = "depth";
$printoutLang["height"] = "height";
$printoutLang["extent"] = "extent";
$printoutLang["amount"] = "amount";
$printoutLang["unit"] = "unit";
$printoutLang["percentage"] = "percentage";
$printoutLang["unit_price"] = "UP";
$printoutLang["total"] = "total";
$printoutLang["service confirmed -no handwritten changes-"] = "service confirmed -no handwritten changes-";
$printoutLang["contractor"] = "Contractor";
$printoutLang["name"] = "name";
$printoutLang["stamp"] = "stamp";
$printoutLang["signature"] = "signature";
$printoutLang["subtotal"] = "subtotal";
$printoutLang["since there was transport available from AP and the order had to be completed as quickly as possible, we transported the material from U10 to U60 using manpower.Time commitment 2 hour per man"] = "Since there was transport available from AP and the order had to be completed as quickly as possible, we transported the material from U10 to U60 using manpower.Time commitment 2 hour per man";
$printoutLang["page"] = "Page";
$printoutLang["customer"] = "Customer";
$printoutLang["location"] = "Location";
$printoutLang["level"] = "Level";
$printoutLang["coordinates"] = "Coordinates";
$printoutLang["requester"] = "Requester";
$printoutLang["execution_team"] = "Execution team";
$printoutLang["type_of_scaffold"] = "Type of scaffold";
$printoutLang["work_scaffold"] = "Work scaffold";
$printoutLang["load_scaffold"] = "Load scaffold";
$printoutLang["protective_scaffold"] = "Protective scaffold";
$printoutLang["suspended_scaffold"] = "Suspended scaffold";
$printoutLang["mobile_scaffold_tower"] = "Mobile scaffold tower";
$printoutLang["other"] = "Other";
$printoutLang["safety_instructions"] = "Safety instructions";
$printoutLang["modifications_to_the_scaffolding_only_by_the_scaffolding_manufacturer"] = "Modifications to the scaffolding only by the scaffolding manufacturer";
$printoutLang["leave_sufficient_space_for_material_storage_in_the_passageway"] = "Leave sufficient space for material storage in the passageway";
$printoutLang["no_material_storage_on_protective_and_safety_scaffolding"] = "No material storage on protective and safety scaffolding";
$printoutLang["do_not_overload_scaffolding_decks"] = "Do not overload scaffolding decks";
$printoutLang["do_not_work_on_top_of_each_other"] = "Don`t work on top of each other";
$printoutLang["ascent_and_descent_only_via_existin_stairs_or_ladders"] = "Ascent and descent only via existing stairs or ladders";
$printoutLang["keep_access_doors_closed"] = "Keep access doors closed";
$printoutLang["do_not_jump_onto_scaffolding_decks_or_drop_material"] = "Do not jump onto scaffolding decks or drop material";
$printoutLang["do_not_jeopardize_the_stability_of_the_scaffolding_by_excavating"] = "Do not jeopardize the stability of the scaffolding by excavating";
$printoutLang["load_class"] = "Load class";
$printoutLang["uniformly_distributed_load"] = "uniformly distributed load";
$printoutLang["width_class"] = "Width class";
$printoutLang["approval_extension"] = "Approval + extension";
$printoutLang["acceptance_by_scaffolding_users"] = "Acceptance by scaffolding users";
$printoutLang["unauthorized_modifications_to_the_scaffolding_are_prohibited"] = "UNAUTHORIZED MODIFICATIONS TO THE SCAFFOLDING ARE PROHIBITED!";
$printoutLang["the_accident_prevention_regulations_and_legal_regulations_must_be_observed"] = "The accident prevention regulations and legal regulations must be observed!";
$printoutLang["scaffolding_closed"] = "SCAFFOLDING CLOSED";
$printoutLang["attention"] = "Attention";
$printoutLang["do_not_use_the_scaffolding"] = "do not use the scaffolding!";
$printoutLang["any_unauthorized_use_of_the_scaffolding_is_prohibited"] = "ANY UNAUTHORIZED USE OF THE SCAFFOLDING IS PROHIBITED!";
$printoutLang["reason_for_blocking"] = "Reason for blocking";
$printoutLang["detection_and_blocking_by"] = "Detection and blocking by";
$printoutLang["remedy_defects"] = "Remedy defects";
$printoutLang["short_description_of_the_defect"] = "Short description of the defect";
$printoutLang["abbreviation"] = "Abbreviation";
$printoutLang["change_scaffold_only_by_manufacturer"] = "Change scaffold only by manufacturer";
$printoutLang["allow_space_for_material_storage"] = "Allow space for material storage";
$printoutLang["no_material_storage_on_protective_scaffolding"] = "No material storage on protective and safety scaffolding";
$printoutLang["use_existing_stairs_or_ladders"] = "Use existing stairs or ladders";
$printoutLang["do_not_jump_or_drop_material_on_scaffold_decks"] = "Do not jump or drop material on scaffold decks";
$printoutLang["do_not_jeopardize_scaffold_stability"] = "Do not jeopardize the stability of the scaffolding by excavating";