<?php /**
 * @noinspection PhpUnused
 * @noinspection HttpUrlsUsage
 */
$configFile = __DIR__ . '/config.php';
if (file_exists($configFile)) {
    require_once($configFile);
}

/**
 * This does nothing when not on macOS.
 */
function openInMacOs(string $path): void
{
    if (isMacOs()) {
        $process = popen("open " . escapeshellarg($path), "r");
        if (!$process) {
            echo "Error executing open: Unable to open process\n";
            return;
        }
        pclose($process);
    }
}

/**
 * This only works on Windows.
 */
function openInWindows(string $path): void
{
    if (isWindows()) {
        exec('start "" ' . escapeshellarg($path));
    }
}

/**
 * This only works on macOS when Hammerspoon is installed!
 *
 * @param string $title - The title of the notification.
 * @param string|null $subtitle - Optional subtitle for the notification.
 * @param int $timeVisible - Duration in seconds for how long the notification should be visible
 */
function notifyViaHammerspoon(string $title, string|null $subtitle = null, int $timeVisible = 1): void
{
    if ($subtitle === null) {
        echo $title . "\n";
    } else {
        echo $title . ": " . $subtitle . "\n";
    }

    if (!isMacOs()) {
        return;
    }

    $fullTitle = $title . ($subtitle ? "\n" . $subtitle : "");
    $command = sprintf(
        'hs -c "hs.alert.show([===[%s]===], { textStyle = { paragraphStyle = { alignment = \'center\' } } }, hs.screen.mainScreen(), %d)"',
        $fullTitle, $timeVisible);
    $output = [];
    $status = null;

    exec($command, $output, $status);

    if ($status !== 0) {
        echo "hs -c failed with code $status\n";
    }
}

/**
 * Check if the OS is macOS.
 */
function isMacOs(): bool
{
    return PHP_OS === "Darwin";
}

/**
 * Check if the OS is Windows.
 */
function isWindows(): bool
{
    return str_contains(PHP_OS, "WIN");
}

class PrintoutPrincipal
{
    protected string $caseSensitivePrincipal;
    protected string $apiUrl;
    protected string $username;
    protected string $password;

    /**
     * @param string $caseSensitive The string for use in the API
     * @param string $apiUrl Ends with /index.php/
     */
    public function __construct(string $caseSensitive, string $apiUrl, string $username, string $password)
    {
        $this->caseSensitivePrincipal = $caseSensitive;
        $this->apiUrl = $apiUrl;
        $this->username = $username;
        $this->password = $password;
    }

    public function getApiUrl(): string
    {
        return $this->apiUrl;
    }

    public function getCaseSensitivePrincipal(): string
    {
        return $this->caseSensitivePrincipal;
    }

    /**
     * @throws Exception on any errors
     */
    public function loginAndGetToken(PrintoutPrincipal $principal): string
    {
        if (!str_ends_with($principal->getApiUrl(), '/index.php/')) {
            throw new Exception('apiUrl should end with /index.php/');
        }
        $url = "{$principal->apiUrl}login";
        echo "Logging in user $principal->username via POST $url on {$this->getCaseSensitivePrincipal()}\n\n";

        $data = json_encode([
            "username" => $principal->username,
            "password" => $principal->password
        ]);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        /** @noinspection SpellCheckingInspection */
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Principal: ' . $this->getCaseSensitivePrincipal(),
            'Authorization: Basic dGVzdHdlYnBvcnRhbDp0ZXN0cGFzczRwb3J0YWw'
        ]);
        if (defined('PRINTOUT_PROXY')) {
            $proxy = PRINTOUT_PROXY;
            curl_setopt($ch, CURLOPT_PROXY, "http://$proxy");
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode < 200 || $httpCode >= 300) {
            throw new Exception("POST login is not successful. Status code: $httpCode. Response body:\n\n$response\n");
        }

        $jsonResponse = json_decode($response, true);
        return $jsonResponse['oauth']['access_token'];
    }
}

class Principal
{
    public static function DEFAULT_TEST(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "default_test",
            require __DIR__ . '/utils/DefaultTestBaseUrl.php',
            "365",
            "1");
    }

    public static function HOST_GERUESTBAU_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "host_gerstbau_gmbh",
            "https://api.baubuddy.de/index.php/",
            "58",
            "ZIZhxGx6");
    }

    public static function HIRSCH_MALEREI_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "hirsch_malerei_gmbh",
            "https://api.baubuddy.de/index.php/",
            "1",
            "hsm4562!");
    }

    public static function VERO(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "vero",
            "http://portal-sofia.vero.bg:8090/api/index.php/",
            "314",
            "Etteln123");
    }

    public static function HAKA_KUECHE(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "haka_kueche",
            "https://api.baubuddy.de/index.php/",
            "1",
            "byg4wje6RZQ*hjm*ezh");
    }

    public static function VERO_SCAFFOLDING_EOOD_HEADER(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "vero_scaffolding_eood_header",
            "http://portal-sofia.vero.bg:8090/api/index.php/",
            "1",
            "Etteln123");
    }

    public static function SCHAEFER_GERUESTBAU_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "schaefer_geruestbau_gmbh",
            "https://api.baubuddy.de/index.php/",
            "6000",
            "schaefer");
    }

    public static function PADERTOR_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "padertor_gmbh",
            "https://api.baubuddy.de/index.php/",
            "1",
            "1HDPFJUy");
    }

    public static function GERUESTBAU_A_SCHLEIPFER_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "geruestbau_a_schleipfer_gmbh",
            "https://api.baubuddy.de/index.php/",
            "1099",
            "lieferSchein");
    }

    public static function GUETERSLOHER_BAUELEMENTE_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "guetersloher_bauelemente_gmbh",
            "https://api.baubuddy.de/index.php/",
            "1",
            "1");
    }

    public static function HEINEN_GRUPPE_GERUESTBAU_GMBH_CO_KG(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "heinen_gruppe_geruestbau_gmbh_co_kg",
            "https://api.baubuddy.de/dev/index.php/",
            "121",
            "heinen2020");
    }


    public static function MOLIN(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "molin",
            "https://api.baubuddy.de/index.php/",
            "3",
            "roS9513§");
    }

    public static function DIGI(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "digi",
            "https://api.baubuddy.de/index.php/",
            "1",
            "2023");
    }

    public static function ZB2_ONLINE_TEST(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "zb2_online_test",
            "https://api.baubuddy.de/index.php/",
            "1",
            "zb2021");
    }

    public static function LAMONICA(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "lamonica",
            "https://api.baubuddy.de/index.php/",
            "1",
            "KA2197");
    }

    public static function TEST_SCHNITTSTELLE_1000(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "test_schnittstelle_1000",
            "https://api.baubuddy.de/index.php/",
            "1",
            "123456789");
    }

    public static function KANZLER_DACH_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "kanzler_dach_gmbh",
            "https://api.baubuddy.de/index.php/",
            "1",
            "kzl0825!");
    }

    public static function KERAMIKO_2020(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "keramiko_2020",
            "https://api.baubuddy.de/index.php/",
            "1",
            "2020");
    }

    public static function KSBG(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "ksbg",
            "https://intern.digithebutler.at:8443/api/index.php/",
            "1",
            "ksbg2019");
    }

    public static function KSBG_USER_3127(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "ksbg",
            "https://intern.digithebutler.at:8443/api/index.php/",
            "3127",
            "ksbg2019");
    }

    public static function KERAMIKO_2020_USER_125(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "keramiko_2020",
            "https://api.baubuddy.de/index.php/",
            "125",
            "2020");
    }


    public static function KSBG_TEST(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "ksbg_test",
            "https://intern-test.digithebutler.at:8443/api/index.php/",
            "1",
            "ksbg2019");
    }

    public static function MACGYVER(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "macgyver",
            "https://api.baubuddy.de/index.php/",
            "1",
            "2023");
    }

    public static function TS_LEARNING_TEST(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "tslearning_test",
            "https://api.baubuddy.de/index.php/",
            "1",
            "Leimer1981$$");
    }

    /** @noinspection SpellCheckingInspection */
    public static function ZWICKER_GERUESTE_AG_GOLDACH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "zwicker_gerueste_ag_goldach",
            "https://api.baubuddy.de/index.php/",
            "1",
            "Zwicker2023");
    }

    public static function LUCKEY_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "luckey_gmbh",
            "https://api.baubuddy.de/index.php/",
            "63",
            "luckey2023");
    }

    public static function LUCKEY_ONLINE(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "luckeyonline",
            "https://api.baubuddy.de/index.php/",
            "29",
            "Luckey2023");
    }

    public static function VERO_SCAFFOLDING_LLC_UZBEKISTAN(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "vero_scaffolding_llc_uzbekistan",
            // "https://api.baubuddy.de/index.php/",
            "http://portal-sofia.vero.bg:8090/api/index.php/",
            "5",
            "Qarshi2024");
    }

    public static function AMBIGUS_GMBH(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "ambigus_gmbh",
            "https://api.baubuddy.de/index.php/",
            "2",
            "Ek/b4kqS");
    }

    public static function WALDGARTEN_SERVICE(): PrintoutPrincipal
    {
        return new PrintoutPrincipal(
            "waldgartenservice",
            "https://api.baubuddy.de/index.php/",
            "1",
            "wgs3078!");
    }
}

/**
     * @param string $command
     * @param array<string> $args
     * @param bool $echoCommand
     * @return array{output: string, resultCode: int}
     */
    function executeCommand(string $command, array $args, bool $echoCommand): array
{
    $commandLine = $command . ' ' . implode(' ', array_map('escapeshellarg', $args));
    $resultCode = 0;
    if ($echoCommand) {
        echo "\nExecuting via exec():\n$commandLine\n";
    }
    exec($commandLine, $outputLines, $resultCode);
    $output = implode("\n", $outputLines);
    return ['output' => $output, 'resultCode' => $resultCode];
}

enum Route: string
{
    case Arbeitszeiterfassung = "Arbeitszeiterfassung";
    case MediaPrint = "MediaPrint";
    case MediaPrintV2 = "MediaPrintV2";
    case MediaPrintV3 = "MediaPrintV3";
    case MediaProject = "MediaProject";
    case MediaProjectV2 = "MediaProjectV2";
    case MediaWorkingOrder = "MediaWorkingOrder";
    case MediaWorkingOrderV2 = "MediaWorkingOrderV2";
    case LuckeyCheckliste = "LuckeyCheckliste";
    case Montagerapport = "Montagerapport";
    case KsbgChecklist = "KsbgChecklist";
    case KsbgChecklistHideEpGp = "KsbgChecklistHideEpGp";
    case KsbgChecklistOversizes = "KsbgChecklistOversizes";
    case PrintRevisionListe = "PrintRevisionListe";
    case UnfallMeldung = "UnfallMeldung";
    case PaderTor = "PaderTor";
    case UnfallanzeigeBgBau = "UnfallanzeigeBgBau";
    case Stundenzettel = "Stundenzettel";
    case RiskAnalysis = "RiskAnalysis";
    case Schadenmeldeformular = "Schadenmeldeformular";
    case ErsteHilfeMassnahme = "ErsteHilfeMassnahme";
    case Stundenlohnarbeiten = "Stundenlohnarbeiten";
    case PrintHoursV3 = "printHoursV3";
    case PrintHoursVersionThree = "PrintHoursVersionThree";
    case PrintHoursVersionTwo = "PrintHoursVersionTwo";
    case PrintHours = "printHours";
    case PrintHoursV2 = "printHoursV2";
    case BauberichtNerisoV1 = "BauberichtNerisoV1";
    case BauberichtNerisoV2 = "BauberichtNerisoV2";
    case ScaffoldListItem = "ScaffoldListItem";
    case ScaffoldListItemStandby = "ScaffoldListItemStandby";
    case GeruestFreigabeSchein = "GeruestFreigabeSchein";
    case Mindestlohnerklaerung = "Mindestlohnerklaerung";
    case Pruefprotokoll = "Pruefprotokoll";
    case TeamPlanningReport = "TeamPlanningReport";
    case AcceptanceProtocol = "AcceptanceProtocol";
    case ScaffoldingInvoice = "ScaffoldingInvoice";
    case Bestellung = "Bestellung";
    case ScaffoldingOffer = "ScaffoldingOffer";
    case ScaffoldListOversizes = "ScaffoldListOversizes";
    case Oversizes = "Oversizes";
    case ScaffoldListItemRental = "ScaffoldListItemRental";
    case TaetigkeitsbezogeneGefaehrdungsbeurteilung = "TaetigkeitsbezogeneGefaehrdungsbeurteilung";
    case Bautagesbericht = "Bautagesbericht";
    case BautagesberichtRustemeier = "BautagesberichtRustemeier";
    case ArbeitszeiterfassungV2 = "ArbeitszeiterfassungV2";
    case GeruestFreigabe = "GeruestFreigabe";
    case AufzugdokumenteUebergabeprotokoll = "AufzugdokumenteUebergabeprotokoll";
    case AufzugdokumenteRueckgabeprotokoll = "AufzugdokumenteRueckgabeprotokoll";
    case AufzugdokumenteUvvWartungCheckliste = "AufzugdokumenteUvvWartungCheckliste";
    case Behinderungsanzeige = "Behinderungsanzeige";
    case AnzeigeStundenlohnarbeiten = "AnzeigeStundenlohnarbeiten";
    case MitteilungFertigstellung = "MitteilungFertigstellung";
    case VerguetungZusaetzlicheLeistungen = "VerguetungZusaetzlicheLeistungen";
    case ZulassungsbescheinigungTeil2 = "ZulassungsbescheinigungTeil2";
    case ZwickerGeruestabnahmeProtokoll = "ZwickerGeruestabnahmeProtokoll";
    case Bedenkenmeldung = "Bedenkenmeldung";
    case AbnahmeUndUebergabeProtokoll = "AbnahmeUndUebergabeProtokoll";
    case Baustellenplan = "Baustellenplan";
    case ChecklisteAufzuge = "ChecklisteAufzuge";
    case GreenTag = "GreenTag";
    case Kalkulationsdatenblatt = "Kalkulationsdatenblatt";
    case Kolonneneinteilung = "Kolonneneinteilung";
    case Mangelanzeige = "Mangelanzeige";
    case NkjiMinutesCapInspection = "nkji_minutes_cap_inspection";
    case NkjiTechnicalExamination = "nkji_technical_examination";
    case ZB2DruckZuordnung = "ZB2Druckzuordnung";
    case WerkstattdokumenteChecklisteUVVPruefung = "WerkstattdokumenteChecklisteUVVPruefung";
    case Abfahrtskontrolle = "Abfahrtskontrolle";
    case Inspektionsprotokoll = "Inspektionsprotokoll";
    case PadertorServicebericht = "PadertorServicebericht";
}

enum PrintoutFileType: string
{
    case Pdf = '.pdf';
    case PdfNoExtension = '';
    case Html = '.html';
    case Json = '.json';
}

enum RequestMethod: string
{
    case GET = 'GET';
    case POST = 'POST';
}

function trimBranchMiddle(string $branch, int $maxLen = 60): string
{
    $branch = trim($branch);
    if (strlen($branch) <= $maxLen) {
        return "[$branch]";
    }
    $keep = $maxLen - 5; // 2 left, 3 for '...', 2 right
    $left = intdiv($keep, 2);
    $right = $keep - $left;
    return '[' . substr($branch, 0, $left) . '...' . substr($branch, -$right) . ']';
}

/**
 * @throws Exception
 */
function fullUrl(PrintoutPrincipal $principal, bool $useBaseUrlFromConfigOrLocalhost, string|null $branch): string
{
    if ($useBaseUrlFromConfigOrLocalhost) {
        if (defined('COMPILER_API_BASE_URL')) {
            $url = COMPILER_API_BASE_URL;
        } else {
            $url = 'http://localhost/index.php/';
        }
        if (!str_ends_with($url, '/index.php/')) {
            throw new Exception('apiBaseUrl in the config should end with /index.php/');
        }
        return $url;
    }
    return $branch ? "https://api.baubuddy.de/branches/api-$branch/index.php/" : $principal->getApiUrl();
}

function getExtensionForApi(PrintoutFileType $fileType, bool $useBaseUrlFromConfigOrLocalhost): string
{
    return ($fileType === PrintoutFileType::Html && $useBaseUrlFromConfigOrLocalhost)
        ? '.html' : $fileType->value;
}

function replaceCharsForFileSystem(string $str): string
{
    return preg_replace('/[^A-Za-z0-9]/', '-', $str);
}

function getBaseFileName(string|null $baseFileName, Route $apiRoute, bool $appendGitBranchToFileName): string
{
    $printoutDirectory = null;
    if (defined('COMPILER_PRINTOUT_DIRECTORY')) {
        $printoutDirectory = COMPILER_PRINTOUT_DIRECTORY;
    }
    if ($appendGitBranchToFileName && $printoutDirectory != null) {
        $output = executeCommand("git", ["-C", $printoutDirectory, "rev-parse", "--abbrev-ref", "HEAD"], echoCommand: false);
        if ($output['resultCode'] == 0) {
            $sanitizedBranch = trimBranchMiddle(replaceCharsForFileSystem(trim($output['output'])));
            if ($baseFileName == null) {
                return replaceCharsForFileSystem($apiRoute->value) . " $sanitizedBranch";
            }
            return replaceCharsForFileSystem($apiRoute->value) . " $baseFileName " . $sanitizedBranch;
        }
    }
    return $baseFileName ?? replaceCharsForFileSystem($apiRoute->value);
}

/**
     * @param array<string> $args
     * @param string|null $requestBodyForPost
     * @param array<string> $proxyArgs
     * @return string
     */
    function getMarkdownFileContent(array $args, string|null $requestBodyForPost, array $proxyArgs): string
{
    $escapedCmd = implode(' ', array_map(function ($item) {
        return empty($item) ? '' : escapeshellarg($item);
    }, $args));

    $escapedCmd = "http " . $escapedCmd;
    $requestBody = ($requestBodyForPost === null) ? '' : "\n\nJSON request body\n\n```json\n$requestBodyForPost\n```";

    return empty($proxyArgs)
        ? sprintf("```bash\n%s\n```%s", $escapedCmd, $requestBody)
        : sprintf("```bash\n%s\n```$requestBody", trim(str_replace(' ' . implode(' ', $proxyArgs), '', $escapedCmd)));
}

/**
 * @param string|null $baseFileName the base file name without an extension like "Padertor"
 * @noinspection PhpDocMissingThrowsInspection no need to catch those, just let them crash
 */
/**
     * @param PrintoutPrincipal $principal
     * @param Route $apiRoute
     * @param string|null $apiRouteSuffix
     * @param string|null $branch
     * @param PrintoutFileType $fileType
     * @param array<string, int|string> $headers
     * @param bool $useBaseUrlFromConfigOrLocalhost
     * @param bool $appendGitBranchToFileName
     * @param string|null $baseFileName
     * @param RequestMethod $method
     * @param string|null $requestBodyForPost
     * @param array<string, int|string> $params
     * @param callable|null $cleanupCallback
     * @return void
     */
    function printoutGenerator(
    PrintoutPrincipal $principal,
    Route             $apiRoute,
    string|null       $apiRouteSuffix = null,
    string|null       $branch = null,
    PrintoutFileType  $fileType = PrintoutFileType::Pdf,
    array             $headers = [],
    bool              $useBaseUrlFromConfigOrLocalhost = true,
    bool              $appendGitBranchToFileName = true,
    string|null       $baseFileName = null,
    RequestMethod     $method = RequestMethod::GET,
    string|null       $requestBodyForPost = null,
    array             $params = [],
    callable|null     $cleanupCallback = null,
): void
{
    if (defined('COMPILER_LOGIN_TOKEN')) {
        $token = COMPILER_LOGIN_TOKEN;
    } else {
        /** @noinspection PhpUnhandledExceptionInspection */
        $token = $principal->loginAndGetToken($principal);
    }

    if (isWindows()) {
        // Do not use http_build_query() on Windows since the comma will be encoded as %2C.
        // Later, escapeshellarg() is used on the argument and since it behaves differently on Windows, it breaks,
        // see https://gitlab.baubuddy.de/BauBuddy/api-printouts/-/merge_requests/335#note_646822
        $urlQuery = '?';
        foreach ($params as $key => $value) {
            $urlQuery .= $key . '=' . $value . '&';
        }
        $urlQuery = rtrim($urlQuery, '&');
    } else {
        $urlQuery = empty($params) ? '' : '?' . http_build_query($params);
    }

    $fullApiRoute = $useBaseUrlFromConfigOrLocalhost ? $apiRoute->value : sprintf("v1/printouts/%s", $apiRoute->value);
    if ($apiRouteSuffix != null) {
        $fullApiRoute .= $apiRouteSuffix;
    }

    /** @noinspection PhpUnhandledExceptionInspection */
    $fullUrlString = fullUrl($principal, $useBaseUrlFromConfigOrLocalhost, $branch) . $fullApiRoute .
        getExtensionForApi($fileType, $useBaseUrlFromConfigOrLocalhost) . $urlQuery;

    $sanitizedBaseFileName = getBaseFileName($baseFileName, $apiRoute, $appendGitBranchToFileName);
    if (defined('COMPILER_OUTPUT_DIRECTORY')) {
        $outputDirWithSlash = rtrim(COMPILER_OUTPUT_DIRECTORY, DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
    } else {
        $outputDirWithSlash = rtrim(getenv('HOME'), DIRECTORY_SEPARATOR) . DIRECTORY_SEPARATOR;
    }
    $filePath = sprintf("%s%s%s", $outputDirWithSlash, $sanitizedBaseFileName, $fileType->value);

    $proxyArgs = [];
    if (defined('PRINTOUT_PROXY')) {
        $proxy = PRINTOUT_PROXY;
        $proxyArgs = [
            // from https://httpie.io/docs/cli/server-ssl-certificate-verification
            "--verify=no",
            "--proxy=http://$proxy",
            "--proxy=https://$proxy",
        ];
    }
    $httpHeaders = array_map(fn($key, $value) => "$key:$value", array_keys($headers), $headers);
    $xdebugCookie = 'Cookie:XDEBUG_SESSION=PHPSTORM;path=/';

    $args = array_merge(['--ignore-stdin', '--check-status'], $proxyArgs, [
        $method->value,
        $fullUrlString,
        $xdebugCookie,
        ...$httpHeaders,
        "Authorization:Bearer $token",
        "Principal:{$principal->getCaseSensitivePrincipal()}",
    ]);

    if ($requestBodyForPost !== null) {
        $tempFile = tempnam(sys_get_temp_dir(), 'printout-compiler-request-body-stdin');
        file_put_contents($tempFile, $requestBodyForPost);
        $args[] = "@" . $tempFile;
    }
    $args[] = '-o';
    $args[] = $filePath;

    $argsWithoutLastOutputFile = array_slice($args, 0, count($args) - 1);
    $plainArgs = array_merge(
        array_filter($argsWithoutLastOutputFile, function ($item) use ($xdebugCookie) {
            return !empty($item) &&
                $item !== '--ignore-stdin' &&
                $item !== '--verify=no' &&
                $item !== $xdebugCookie &&
                $item !== '--check-status' &&
                !str_starts_with($item, '--proxy=');
        }),
        // replace last -o argument
        [$sanitizedBaseFileName . $fileType->value]
    );
    $plainArgs = implode(' ', array_map('escapeshellarg', $plainArgs));

    echo "Plain invocation without proxy:\nhttp " . $plainArgs . "\n";

    if (defined('PRINTOUT_COMPILER_CREATE_MARKDOWN_FILES') && PRINTOUT_COMPILER_CREATE_MARKDOWN_FILES) {
        $markdownFilePath = sprintf("%s%s.md", $outputDirWithSlash, $sanitizedBaseFileName);
        file_put_contents($markdownFilePath, getMarkdownFileContent($args, $requestBodyForPost, $proxyArgs));
    }

    $out = executeCommand("http", $args, echoCommand: true);
    if ($out['resultCode'] === 0) {
        if (in_array($fileType, [PrintoutFileType::Pdf, PrintoutFileType::PdfNoExtension])) {
            if (defined('COMPILER_OPEN_CREATED_PDFS') && !COMPILER_OPEN_CREATED_PDFS) {
                $fileName = $sanitizedBaseFileName . $fileType->value;
                notifyViaHammerspoon("\nFinished PDF printout generation for:\n$fileName");
            } else {
                if (isMacOs()) {
                    openInMacOs($filePath);
                } else if (isWindows()) {
                    openInWindows($filePath);
                    echo "PDF generated and opened.\n";
                }
            }
        } elseif ($fileType === PrintoutFileType::Html) {
            notifyViaHammerspoon("Finished HTML printout generation for $apiRoute->value");
        } elseif ($fileType === PrintoutFileType::Json) {
            notifyViaHammerspoon("Finished JSON printout generation for $apiRoute->value");
        }
    } else {
        echo "httpie failed with status code: {$out['resultCode']}: " . trim($out['output']) . "\n";
        $response = file_get_contents($filePath);
        rename($filePath, "$filePath-error");
        if (trim($response) == "") {
            echo "\nEmpty response body";
        } else {
            echo "\nResponse body:\n$response\n";
        }
        if (getenv('CI') || php_sapi_name() === 'cli') {
            // abort the CI job, but keep going for local development
            if ($cleanupCallback !== null) {
                $cleanupCallback();
            }
            exit(1);
        }
    }
    if ($cleanupCallback !== null) {
        $cleanupCallback();
    }
}