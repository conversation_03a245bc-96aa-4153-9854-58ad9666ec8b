# Optional config.php

If you need to set any constants below, create a `config.php` in the root directory.
You can set the following constants. All are optional.

- `PRINTOUT_PROXY`: the proxy for all network requests for man in the middle proxies
- `PLAYWRIGHT_SERVER_PORT`: the server port for the `localhost` server. If undefined, 3000.
- `COMPILER_OUTPUT_DIRECTORY`: **you most likely need to set this**. The directory where the PDF, JSON and Markdown
  files are saved. If null, the home directory is used, but take care that often times the PHP environment has no access
  to it
- `COMPILER_PRINTOUT_DIRECTORY`: the directory where the printouts are located to fetch the git branch from.
- `COMPILER_API_BASE_URL`: the API base URL to fire the requests to. If undefined, the default localhost URL on port 80
  is used.
- `COMPILER_LOGIN_TOKEN` : can be set to a hardcoded token to not fetch a fresh one from `POST login`
- `COMPILER_OPEN_CREATED_PDFS`: set to `false` to disable opening the created PDFs after generation. If unset, it is assumed `true`

```php
<?php
const PRINTOUT_PROXY = "localhost:9090";
const PRINTOUT_WKHTMLTOPDF_PATH = "/usr/local/bin/wkhtmltopdf";
const PLAYWRIGHT_SERVER_PORT = 3000;

const COMPILER_OUTPUT_DIRECTORY = "/Users/<USER>/";
const COMPILER_LOGIN_TOKEN = "5d14afb3246991b6279c76a6d6f6f544da353d8d";
const COMPILER_PRINTOUT_DIRECTORY = "/Users/<USER>/vero/api-printouts/";
const COMPILER_OPEN_CREATED_PDFS = false;

const COMPILER_API_BASE_URL = "http://localhost/index.php/";
//const COMPILER_API_BASE_URL = "http://localhost:50000/index.php/";
//const COMPILER_API_BASE_URL = "https://printout.baubuddy.de/index.php/";
//const COMPILER_API_BASE_URL = "https://printout.baubuddy.de:8443/index.php/";
//const COMPILER_API_BASE_URL = "https://printout-dev.baubuddy.de/index.php/";
```

# About PHPStan

The configuration is in `phpstan.neon`. Run via `composer lint`.

We started with level 3 and should strive to get the level as high as possible.

---

Using level 6 shows many warnings about unspecified types.
In the future, it would still be amazing to provide the value types for good type inference.

```
 ------ -------------------------------------------------- 
  Line   utils/BundesInnungGeruestbauHelper.php            
 ------ -------------------------------------------------- 
  7      <USER>                                            
         <GROUP>::downloadCommonData  
         ForHeader() return type has no value type         
         specified in iterable type array.                 
         🪪  missingType.iterableValue                     
         💡  See:                                          
         https://phpstan.org/blog/solving-phpstan-no-valu  
         e-type-specified-in-iterable-type                 
 ------ -------------------------------------------------- 
```
